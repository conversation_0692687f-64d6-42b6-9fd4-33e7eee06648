package cn.need.framework.common.core.entity;

import cn.need.framework.common.core.annotation.TestAnnotation;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单元测试用类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Demo extends TestUser {

    @TestAnnotation(value = "a", enable = true)
    private String code;

    @TestAnnotation(value = "b", enable = true)
    private String name;

    private Integer age;

    private Integer studentId;
}
