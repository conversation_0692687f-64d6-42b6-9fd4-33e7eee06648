package cn.need.framework.common.core.bean.closure;

import cn.need.framework.common.core.bean.PropertyUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import org.apache.commons.collections4.Closure;

/**
 * <AUTHOR>
 */
public class BeanPropertyChangeClosure<T> implements Closure<T> {

    /**
     * 属性名
     */
    private final String property;

    /**
     * 属性值
     */
    private final Object value;

    public BeanPropertyChangeClosure(String property, Object value) {
        Validate.notEmpty(property, "property can't be blank!");
        this.property = property;
        this.value = value;
    }

    @Override
    public void execute(T bean) {
        if (ObjectUtil.isEmpty(bean)) {
            return;
        }
        PropertyUtil.setProperty(bean, property, value);
    }
}
