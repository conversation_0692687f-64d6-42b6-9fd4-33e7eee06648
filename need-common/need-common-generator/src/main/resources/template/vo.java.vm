package ${voPackage};

#if(${superVoClassPackage})
import ${superVoClassPackage};
#end
#if(${springdoc})
import io.swagger.v3.oas.annotations.media.Schema;
#elseif(${swagger})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
    #if(${superVoClass})
    import lombok.EqualsAndHashCode;
    #end
#end

#if(${entitySerialVersionUID})
    #if($javaVersion > 14)
    import java.io.Serial;
    #end
    #if(!${superVoClass})
    #end
#end

/**
 * $!{table.comment} VO对象
 *
 * <AUTHOR>
 * @since ${date}
 */
    #if(${entityLombokModel})
    @Data
        #if(${superVoClass})
        @EqualsAndHashCode(callSuper = true)
        #end
    #end
    #if(${springdoc})
    @Schema(description = "$!{table.comment} VO对象")
    #elseif(${swagger})
    @ApiModel(value = "${voName}", description = "${table.comment} VO对象")
    #end
    #if(${superVoClass})
    public class ${voName} extends ${superVoClass} {
    #else
            public class ${voName} implements Serializable {
    #end

    #if(${entitySerialVersionUID})
        #if($javaVersion > 14)
        @Serial
        #end
    private static final long serialVersionUID = 1L;
    #end
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    ## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if("$!field.comment" != "")
            #set($propertyVal=${field.comment})
        #else
            #set($propertyVal=${field.propertyName})
        #end
        /**
         * ${propertyVal}
         */
        #if(${springdoc})
        @Schema(description = "${propertyVal}")
        #elseif(${swagger})
        @ApiModelProperty(value = "${propertyVal}")
        #end
    private ${field.propertyType} ${field.propertyName};

    #end
    ## ----------  END 字段循环遍历  ----------
    #if(!${entityLombokModel})

        ## ----------  字段循环遍历  ----------
        #foreach($field in ${table.fields})
            #if(${field.propertyType.equals("boolean")})
                #set($getprefix="is")
            #else
                #set($getprefix="get")
            #end
            public ${field.propertyType} ${getprefix}${field.capitalName}() {
            return this.${field.propertyName};
        }

            public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
            this.${field.propertyName} = ${field.propertyName};
        }
        #end

        @Override
        public String toString() {
        return "${voName} {" +
                "id=" + id +
            #foreach($field in ${table.fields})
                    ", ${field.propertyName}=" + ${field.propertyName} +
            #end
                "} ";
    }
    #end

}