package ${queryPackage};

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;

import java.util.List;
#if(${superQueryClassPackage})
import ${superQueryClassPackage};
#end
#if(${springdoc})
import io.swagger.v3.oas.annotations.media.Schema;
#elseif(${swagger})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
    #if(${superQueryClass})
    import lombok.EqualsAndHashCode;
    #end
#end

#if(${entitySerialVersionUID})
    #if($javaVersion > 14)
    import java.io.Serial;
    #end
    #if(!${superQueryClass})
    #end
#end

/**
 * $!{table.comment} Query对象
 *
 * <AUTHOR>
 * @since ${date}
 */
    #if(${entityLombokModel})
    @Data
        #if(${superQueryClass})
        @EqualsAndHashCode(callSuper = true)
        #end
    #end
    #if(${springdoc})
    @Schema(description = "$!{table.comment} Query对象")
    #elseif(${swagger})
    @ApiModel(value = "${queryName}", description = "${table.comment} Query对象")
    #end
    #if(${superQueryClass})
    public class ${queryName} extends ${superQueryClass} {
    #else
            public class ${queryName} implements Serializable {
    #end

    #if(${entitySerialVersionUID})
        #if($javaVersion > 14)
        @Serial
        #end
    private static final long serialVersionUID = 1L;
    #end
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    ## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if("$!field.comment" != "")
            #set($propertyVal=${field.comment})
        #else
            #set($propertyVal=${field.propertyName})
        #end
        /**
         * ${propertyVal}
         */
        #if(${springdoc})
        @Schema(description = "${propertyVal}")
        #elseif(${swagger})
        @ApiModelProperty(value = "${propertyVal}")
        #end
    private ${field.propertyType} ${field.propertyName};
        #if(${field.propertyName.endsWith("RefNum")}
        || ${field.propertyName.equals("refNum")}
        || ${field.propertyName.endsWith("Status")}
        || ${field.propertyName.equals("status")}
        || ${field.propertyName.endsWith("Type")}
        || ${field.propertyName.equals("type")}
        || ${field.propertyName.endsWith("Upc")}
        || ${field.propertyName.equals("upc")}
        || ${field.propertyName.endsWith("SupplierSku")}
        || ${field.propertyName.equals("supplierSku")}
        || ${field.propertyName.endsWith("ProductBarcode")}
        || ${field.propertyName.equals("productBarcode")}
        || ${field.propertyName.endsWith("Channel")}
        || ${field.propertyName.equals("channel")}
        || ${field.propertyName.endsWith("TransactionPartnerId")}
        || ${field.propertyName.equals("transactionPartnerId")}
        || ${field.propertyName.endsWith("TransactionPartnerId")}
        || ${field.propertyName.equals("transactionPartnerId")}
        || ${field.propertyName.endsWith("Station")}
        || ${field.propertyName.equals("station")}
        || ${field.propertyName.endsWith("BolNum")}
        || ${field.propertyName.equals("bolNum")}
        || ${field.propertyName.endsWith("Ssccnum")}
        || ${field.propertyName.equals("ssccnum")}
        || ${field.propertyName.endsWith("TrackingNum")}
        || ${field.propertyName.equals("trackingNum")}
        || ${field.propertyName.endsWith("ShipCarrier")}
        || ${field.propertyName.equals("shipCarrier")}
        || ${field.propertyName.endsWith("ShipMethod")}
        || ${field.propertyName.equals("shipMethod")}
        || ${field.propertyName.endsWith("Id")}
        || ${field.propertyName.endsWith("Type")}
        || ${field.propertyName.endsWith("Status")}
        )
            /**
             * ${propertyVal}
             */
            #if(${springdoc})
            @Schema(description = "${propertyVal}集合")
            #elseif(${swagger})
            @ApiModelProperty(value = "${propertyVal}集合")
            #end
        @Condition(value = Keyword.IN, fields = {"${field.propertyName}"})
        private List<${field.propertyType}> ${field.propertyName}List;
        #end
        #if(${field.propertyType.equals("LocalDateTime")})
            /**
             * ${propertyVal}开始
             */
            #if(${springdoc})
            @Schema(description = "${propertyVal}开始")
            #elseif(${swagger})
            @ApiModelProperty(value = "${propertyVal}开始")
            #end
        @Condition(Keyword.GE)
        private ${field.propertyType} ${field.propertyName}Start;
            /**
             * ${propertyVal}结束
             */
            #if(${springdoc})
            @Schema(description = "${propertyVal}结束")
            #elseif(${swagger})
            @ApiModelProperty(value = "${propertyVal}结束")
            #end
        @Condition(Keyword.LE)
        private ${field.propertyType} ${field.propertyName}End;
        #end

    #end
    ## ----------  END 字段循环遍历  ----------
    #if(!${entityLombokModel})

        ## ----------  字段循环遍历  ----------
        #foreach($field in ${table.fields})
            #if(${field.propertyType.equals("boolean")})
                #set($getprefix="is")
            #else
                #set($getprefix="get")
            #end
            public ${field.propertyType} ${getprefix}${field.capitalName}() {
            return this.${field.propertyName};
        }

            public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
            this.${field.propertyName} = ${field.propertyName};
        }
        #end

        @Override
        public String toString() {
        return "${queryName} {" +
                "id=" + id +
            #foreach($field in ${table.fields})
                    ", ${field.propertyName}=" + ${field.propertyName} +
            #end
                "} ";
    }
    #end

    @Override
    public Set<String> getAvailableFields () {
    java.util.Set<String> fields = new java.util.HashSet<>();
    #foreach($field in ${table.commonFields})
        fields.add("${field.propertyName}");
    #end
    #foreach($field in ${table.fields})
        fields.add("${field.propertyName}");
    #end
    return fields;
}
}