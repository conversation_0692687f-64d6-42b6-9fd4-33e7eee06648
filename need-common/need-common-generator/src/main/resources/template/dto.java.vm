package ${dtoPackage};

import java.time.LocalDateTime;
import java.math.BigDecimal;
#if(${superDtoClassPackage})
import ${superDtoClassPackage};
#end
#if(${entityLombokModel})
import lombok.Data;
#if(${superDtoClass})
import lombok.EqualsAndHashCode;
#end
#end

#if(${entitySerialVersionUID})
    #if($javaVersion > 14)
import java.io.Serial;
    #end
    #if(!${superDtoClass})
import java.io.Serializable;
    #end
#end

/**
 * $!{table.comment} dto对象
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${entityLombokModel})
@Data
    #if(${superDtoClass})
@EqualsAndHashCode(callSuper = true)
    #end
#end
#if(${superDtoClass})
public class ${dtoName} extends ${superDtoClass} {
#else
public class ${dtoName} implements Serializable {
#end

#if(${entitySerialVersionUID})
    #if($javaVersion > 14)
    @Serial
    #end
    private static final long serialVersionUID = 1L;
#end
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if("$!field.comment" != "")
        #set($propertyVal=${field.comment})
    #else
        #set($propertyVal=${field.propertyName})
    #end

    /**
     * ${propertyVal}
     */
    private ${field.propertyType} ${field.propertyName};
#end
## ----------  END 字段循环遍历  ----------
#if(!${entityLombokModel})

    ## ----------  字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if(${field.propertyType.equals("boolean")})
            #set($getprefix="is")
        #else
            #set($getprefix="get")
        #end
    public ${field.propertyType} ${getprefix}${field.capitalName}() {
        return this.${field.propertyName};
    }

    public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
        this.${field.propertyName} = ${field.propertyName};
    }
    #end

    @Override
    public String toString() {
        return "${dtoName} {" +
        "id=" + id +
    #foreach($field in ${table.fields})
        ", ${field.propertyName}=" + ${field.propertyName} +
    #end
        "} ";
    }
#end

}