package ${package.ServiceImpl};

import java.util.List;
import javax.annotation.Resource;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import ${voPackage}.${voName};
import ${queryPackage}.${queryName};
import ${createParamPackage}.${createParamName};
import ${updateParamPackage}.${updateParamName};
import ${converterPackage}.${converterName};
import ${package.Mapper}.${table.mapperName};
import ${package.Entity}.${entity};
import ${pageVoPackage}.${pageVoName};
import ${package.Service}.${table.serviceName};
import ${converterPackage}.${converterName};
import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.framework.common.core.lang.NumberUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.util.ApiUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import org.springframework.transaction.annotation.Transactional;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import cn.need.framework.common.support.convert.Converters;
#if(${superServiceImplClassPackage})
import ${package.Mapper}.${table.mapperName};
import ${package.Entity}.${entity};
    #if(${table.serviceInterface})
import ${package.Service}.${table.serviceName};
    #end
import ${superServiceImplClassPackage};
#else
    #if(${table.serviceInterface})
import ${package.Service}.${table.serviceName};
    #end
#end
import java.util.Collections;
import org.springframework.stereotype.Service;

/**
 * <p>
 * $!{table.comment} service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
#if(${superServiceImplClass})
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}>#if(${table.serviceInterface}) implements ${table.serviceName}#end {
#else
public class ${table.serviceImplName}#if(${table.serviceInterface}) implements ${table.serviceName}#end {
#end

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if(${field.propertyName.equals("refNum")})
    @Resource
    private NumberGenerateClient numberGenerateClient;
    #end
#end
## ----------  END 字段循环遍历  ----------


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(${entity}CreateParam createParam) {
        // 检查传入$!{table.comment}参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 将$!{table.comment}参数对象转换为实体对象并初始化
        ${entity} entity = init${entity}(createParam);

        // 插入$!{table.comment}实体对象到数据库
        super.insert(entity);

        // 返回$!{table.comment}ID
        return entity.getId();
    }


    /**
     * 初始化$!{table.comment}对象
     * 此方法用于设置$!{table.comment}对象的必要参数，确保其处于有效状态
     *
     * @param createParam $!{table.comment} 新增对象，不应为空
     * @throws BusinessException 如果传入的$!{table.comment}为空，则抛出此异常
     */
    private ${entity} init${entity}(${entity}CreateParam createParam){

        // 检查传入的配置对象是否为空
        if(ObjectUtil.isEmpty(createParam)){
            throw new BusinessException("${entity}CreateParam cannot be empty");
        }

        // 获取$!{table.comment}转换器实例，用于将$!{table.comment}参数对象转换为实体对象
        ${entity}Converter converter = Converters.get(${entity}Converter.class);

        // 将$!{table.comment}参数对象转换为实体对象并初始化
        ${entity} entity = converter.toEntity(createParam);

    ## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if(${field.propertyName.equals("refNum")})
        // 生成RefNum
        entity.setRefNum(ApiUtil.getResultData(numberGenerateClient.generateNumber("${entity}")));
        #end
    #end
    ## ----------  END 字段循环遍历  ----------

        ##     init${entity}(entity);

        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(${entity}UpdateParam updateParam) {
        // 检查传入$!{table.comment}参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam)|| ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 将$!{table.comment}参数对象转换为实体对象
        ${entity} entity = init${entity}(updateParam);

        // 执行更新$!{table.comment}操作
        return super.update(entity);

    }

    /**
     * 初始化$!{table.comment}对象
     * 此方法用于设置$!{table.comment}对象的必要参数，确保其处于有效状态
     *
     * @param updateParam $!{table.comment} 修改对象，不应为空
     * @throws BusinessException 如果传入的$!{table.comment}为空，则抛出此异常
     */
    private ${entity} init${entity}(${entity}UpdateParam updateParam){

        // 检查传入的配置对象是否为空
        if(ObjectUtil.isEmpty(updateParam)){
            throw new BusinessException("${entity}UpdateParam cannot be empty");
        }

        // 获取$!{table.comment}转换器实例，用于将$!{table.comment}参数对象转换为实体对象
        ${entity}Converter converter = Converters.get(${entity}Converter.class);

        // 将$!{table.comment}参数对象转换为实体对象并初始化
        ${entity} entity = converter.toEntity(updateParam);

        ##    init${entity}(entity);

        // 返回初始化后的配置对象
        return entity;
    }

##    /**
##     * 初始化$!{table.comment}对象
##     * 此方法用于设置$!{table.comment}对象的必要参数，确保其处于有效状态
##     *
##     * @param entity $!{table.comment}对象，不应为空
##     * @throws BusinessException 如果传入的$!{table.comment}为空，则抛出此异常
##     */
##    private void init${entity}(${entity} entity){
##
##        // 检查传入的配置对象是否为空
##        if(ObjectUtil.isEmpty(entity)){
##            throw new BusinessException("${entity} cannot be empty");
##        }
##
##    }

    @Override
    public List<${entity}PageVO> listByQuery(${entity}Query query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<${entity}PageVO> pageByQuery(PageSearch<${entity}Query> search) {
        Page<${entity}> page = Conditions.page(search, entityClass);
        List<${entity}PageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ${entity}VO detailById(Long id) {
        ${entity} entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in ${entity}");
        }
        return build${entity}VO(entity);
    }

    ## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})
        #if(${field.propertyName.equals("refNum")})
    @Override
    public ${entity}VO detailByRefNum(String refNum) {
        ${entity} entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in ${entity}");
        }
        return build${entity}VO(entity);
    }
        #end
    #end
    ## ----------  END 字段循环遍历  ----------

    #if(${parentEntityName})
    @Override
    public List<${entity}VO> listBy${parentEntityName}Id(Long ${lowerParentEntityName}Id) {
        List<${entity}> list = lambdaQuery().eq(${entity}::get${parentEntityName}Id, ${lowerParentEntityName}Id).list();
        return Converters.get(${entity}Converter.class).toVO(list);
    }
    #end

    /**
     * 构建$!{table.comment}VO对象
     * @param entity $!{table.comment}对象
     * @return 返回包含详细信息的$!{table.comment}VO对象
     */
    private ${entity}VO build${entity}VO(${entity} entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的$!{table.comment}VO对象
        return Converters.get(${entity}Converter.class).toVO(entity);
    }

}
