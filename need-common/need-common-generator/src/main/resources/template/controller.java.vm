package ${package.Controller};

#if(${superControllerClassPackage})
import ${converterPackage}.${converterName};
import ${package.Entity}.${entity};
import ${voPackage}.${voName};
import ${package.Service}.${table.serviceName};
import ${queryPackage}.${queryName};
import ${createParamPackage}.${createParamName};
import ${updateParamPackage}.${updateParamName};
import ${pageVoPackage}.${pageVoName};
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.cloud.upms.cache.util.UserCacheUtil;
import ${superControllerClassPackage};
    #if(${springdoc})
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
    #elseif(${swagger})
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
    #end
import org.springframework.web.bind.annotation.*;
#if(${controllerImport})
import org.springframework.web.multipart.MultipartFile;
#end

import javax.validation.Valid;
#else
    #if(${restControllerStyle})
import org.springframework.web.bind.annotation.RestController;
    #else
import org.springframework.stereotype.Controller;
    #end
#end

/**
 * <p>
 * ${table.comment} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${restControllerStyle})
@RestController
#else
@Controller
#end
#set($requestBasePath="#if(${requestPrefix})/${requestPrefix}#end#if(${package.ModuleName})/${controllerMappingModule}#end/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end")
@RequestMapping("${requestBasePath}")
#if(${springdoc})
@Tag(name = "${table.comment}")
#elseif(${swagger})
@Api(tags = {"${table.comment}"})
#end
#if(${superControllerClass})
public class ${table.controllerName} extends ${superControllerClass}<${table.serviceName}, ${entity}, ${converterName}, ${voName}> {
    #if(${controllerInsert})

        #if(${springdoc})
    @Operation(summary = "新增${table.comment}", description = "接收${table.comment}的传参对象，将该对象持久化到数据库中表")
        #elseif(${swagger})
    @ApiOperation(value = "新增${table.comment}", notes = "接收${table.comment}的传参对象，将该对象持久化到数据库中表")
        #end
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody #if(${springdoc})@Parameter(description = "数据对象", required = true)#elseif(${swagger})@ApiParam(value = "数据对象", required = true)#end ${entity}CreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }
    #end
    #if(${controllerUpdate})

        #if(${springdoc})
    @Operation(summary = "修改${table.comment}", description = "接收${table.comment}的传参对象，将该对象持久化到数据库中表")
        #elseif(${swagger})
    @ApiOperation(value = "修改${table.comment}", notes = "接收${table.comment}的传参对象，将该对象持久化到数据库中表")
        #end
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody #if(${springdoc})@Parameter(description = "数据对象", required = true)#elseif(${swagger})@ApiParam(value = "数据对象", required = true)#end ${entity}UpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }
    #end
    #if(${controllerRemove})

        #if(${springdoc})
    @Operation(summary = "根据id删除${table.comment}", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
        #elseif(${swagger})
    @ApiOperation(value = "根据id删除${table.comment}", notes = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
        #end
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody #if(${springdoc})@Parameter(description = "数据主键id", required = true)#elseif(${swagger})@ApiParam(value = "数据主键id", required = true)#end DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }
    #end
    #if(${controllerActive})

        #if(${springdoc})
    @Operation(summary = "设置数据有效性接口，返回受影响行数", description = "根据数据主键id，设置对应数据的有效性")
        #elseif(${swagger})
    @ApiOperation(value = "设置数据有效性接口，返回受影响行数", notes = "根据数据主键id，设置对应数据的有效性")
        #end
    @PostMapping(value = "/active")
    public Result<Integer> active(@RequestBody #if(${springdoc})@Parameter(description = "数据主键id", required = true)#elseif(${swagger})@ApiParam(value = "数据主键id", required = true)#end IdCondition id) {
        Validate.notNull(id.getId(), "The id value cannot be null.");
        return super.active(id.getId(), null);
    }
    #end
    #if(${controllerDetail})

        #if(${springdoc})
    @Operation(summary = "根据id获取${table.comment}详情", description = "根据数据主键id，从数据库中获取其对应的${table.comment}详情")
        #elseif(${swagger})
    @ApiOperation(value = "根据id获取${table.comment}详情", notes = "根据数据主键id，从数据库中获取其对应的${table.comment}详情")
        #end
    @GetMapping(value = "/detail/{id}")
    public Result<${voName}> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

#foreach($field in ${table.fields})
    #if(${field.propertyName.equals("refNum")})
    #if(${springdoc})
    @Operation(summary = "根据RefNum获取${table.comment}详情", description = "根据数据RefNum，从数据库中获取其对应的${table.comment}详情")
    #elseif(${swagger})
    @ApiOperation(value = "根据RefNum获取${table.comment}详情", notes = "根据数据RefNum，从数据库中获取其对应的${table.comment}详情")
    #end
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
        public Result<${voName}> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }
    #end
#end

    #end

    #if(${controllerList})

        #if(${springdoc})
    @Operation(summary = "获取${table.comment}分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的${table.comment}列表")
        #elseif(${swagger})
    @ApiOperation(value = "获取${table.comment}分页列表", notes = "根据传入的搜索条件参数，从数据库中获取分页后的${table.comment}列表")
        #end
    @PostMapping(value = "/list")
    public Result<PageData<${entity}PageVO>> list(@RequestBody #if(${springdoc})@Parameter(description = "搜索条件参数", required = true)#elseif(${swagger})@ApiParam(value = "搜索条件参数", required = true)#end PageSearch<${entity}Query> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
    #end
    #if(${controllerImport})

    /**
     * <p>
     * 接收一个excel文件，将数据导入到系统中
     * </p>
     *
     * @param file excel文件
     * @return 返回导入成功的数量
     */
        #if(${springdoc})
    @Operation(summary = "数据导入接口，返回导入成功的数量", description = "该接口接收一个excel文件，将文件中的数据导入到系统中")
        #elseif(${swagger})
    @ApiOperation(value = "数据导入接口，返回导入成功的数量", notes = "该接口接收一个excel文件，将文件中的数据导入到系统中")
        #end
    @PostMapping(value = "/import")
    public Result<Integer> importExcel(@RequestParam("file") #if(${springdoc})@Parameter(description = "excel文件", required = true)#elseif(${swagger})@ApiParam(value = "excel文件", required = true)#end MultipartFile file) {
        return super.importExcel(file, super.voClass, null);
    }
    #end
    #if(${controllerExport})

    /**
     * <p>
     * 将全量数据导出至一个excel文件中
     * </p>
     */
        #if(${springdoc})
    @Operation(summary = "数据导出接口", description = "将全量数据导出，并生成一个excel文件")
        #elseif(${swagger})
    @ApiOperation(value = "数据导出接口", notes = "将全量数据导出，并生成一个excel文件")
        #end
    @GetMapping(value = "/export")
    public void exportExcel() {
        super.exportExcel(super.getExportName("${table.comment}"), super.voClass, null);
    }
    #end
}
#else
public class ${table.controllerName} {

}
#end