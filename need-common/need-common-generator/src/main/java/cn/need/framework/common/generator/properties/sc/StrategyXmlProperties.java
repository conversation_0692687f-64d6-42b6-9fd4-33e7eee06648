package cn.need.framework.common.generator.properties.sc;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.core.constant.StringPool.FALSE;
import static cn.need.framework.common.core.lang.BooleanUtil.toBoolean;
import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 策略配置 - xml配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class StrategyXmlProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = 7599658118768656057L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * vo文件路径
     */
    private String filePath;

    /**
     * 格式化Xml文件名称
     */
    private String formatFileName;

    /**
     * 是否开启BaseResultMap（默认 false）
     */
    private boolean baseResultMap;

    /**
     * 是否开启baseColumnList（默认 false）
     */
    private boolean baseColumnList;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public StrategyXmlProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.filePath = properties.getProperty(SC_XML_FILE_PATH);
        this.formatFileName = properties.getProperty(SC_XML_FORMAT_FILE_NAME, SC_XML_FORMAT_FILE_NAME_VALUE);
        this.baseResultMap = toBoolean(properties.getProperty(SC_XML_BASE_RESULT_MAP, FALSE));
        this.baseColumnList = toBoolean(properties.getProperty(SC_XML_BASE_COLUMN_LIST, FALSE));
    }
}
