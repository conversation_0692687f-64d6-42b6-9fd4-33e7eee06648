package cn.need.framework.common.generator.properties;

import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.generator.properties.cfg.InjectionProperties;
import cn.need.framework.common.generator.properties.db.DataSourceProperties;
import cn.need.framework.common.generator.properties.gc.GlobalProperties;
import cn.need.framework.common.generator.properties.pc.PackageProperties;
import cn.need.framework.common.generator.properties.sc.StrategyProperties;
import cn.need.framework.common.generator.properties.tp.TemplateProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class NeedProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = 7982155655876106876L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * 数据配置
     */
    private final DataSourceProperties dataSourceProperties = new DataSourceProperties();

    /**
     * 全局配置
     */
    private final GlobalProperties globalProperties = new GlobalProperties();

    /**
     * 策略配置
     */
    private final StrategyProperties strategyProperties = new StrategyProperties();

    /**
     * 包路径配置
     */
    private final PackageProperties packageProperties = new PackageProperties();

    /**
     * 模版配置
     */
    private final TemplateProperties templateProperties = new TemplateProperties();

    /**
     * 注入配置
     */
    private final InjectionProperties injectionProperties = new InjectionProperties();

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     *
     * @param properties 配置信息
     */
    public NeedProperties(Properties properties) {
        setDataSourceProperties(new DataSourceProperties(properties));
        setGlobalProperties(new GlobalProperties(properties));
        setStrategyProperties(new StrategyProperties(properties));
        setPackageProperties(new PackageProperties(properties));
        setTemplateProperties(new TemplateProperties(properties));
        setInjectionProperties(new InjectionProperties(properties));
    }

    ////////////////////////// 功能方法 ///////////////////////////////////

    /**
     * 获取数据源配置
     *
     * @return 数据源配置
     */
    public DataSourceProperties db() {
        return this.dataSourceProperties;
    }

    /**
     * 获取全局配置
     *
     * @return 全局配置
     */
    public GlobalProperties gc() {
        return this.globalProperties;
    }

    /**
     * 获取策略配置
     *
     * @return 策略配置
     */
    public StrategyProperties sc() {
        return this.strategyProperties;
    }

    /**
     * 获取包路径配置
     *
     * @return 包路径配置
     */
    public PackageProperties pc() {
        return this.packageProperties;
    }

    /**
     * 获取模版配置
     *
     * @return 模版配置
     */
    public TemplateProperties tp() {
        return this.templateProperties;
    }

    /**
     * 获取注入配置
     *
     * @return 注入配置
     */
    public InjectionProperties cfg() {
        return this.injectionProperties;
    }

    ////////////////////////// 重写赋值方法 ///////////////////////////////////

    /**
     * 设置数据源配置
     *
     * @param dataSourceProperties 数据源配置
     */
    public void setDataSourceProperties(DataSourceProperties dataSourceProperties) {
        if (isNotNull(dataSourceProperties)) {
            BeanUtil.copy(dataSourceProperties, this.dataSourceProperties);
        }
    }

    /**
     * 设置全局配置
     *
     * @param globalProperties 全局配置
     */
    public void setGlobalProperties(GlobalProperties globalProperties) {
        if (isNotNull(globalProperties)) {
            BeanUtil.copy(globalProperties, this.globalProperties);
        }
    }

    /**
     * 设置策略配置
     *
     * @param strategyProperties 策略配置
     */
    public void setStrategyProperties(StrategyProperties strategyProperties) {
        if (isNotNull(strategyProperties)) {
            BeanUtil.copy(strategyProperties, this.strategyProperties);
        }
    }

    /**
     * 设置包路径配置
     *
     * @param packageProperties 包路径配置
     */
    public void setPackageProperties(PackageProperties packageProperties) {
        if (isNotNull(packageProperties)) {
            BeanUtil.copy(packageProperties, this.packageProperties);
        }
    }

    /**
     * 设置模版配置
     *
     * @param templateProperties 模版配置
     */
    public void setTemplateProperties(TemplateProperties templateProperties) {
        if (isNotNull(templateProperties)) {
            BeanUtil.copy(templateProperties, this.templateProperties);
        }
    }

    /**
     * 设置注入配置
     *
     * @param injectionProperties 注入配置
     */
    public void setInjectionProperties(InjectionProperties injectionProperties) {
        if (isNotNull(injectionProperties)) {
            BeanUtil.copy(injectionProperties, this.injectionProperties);
        }
    }
}
