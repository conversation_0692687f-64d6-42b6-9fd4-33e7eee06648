package cn.need.framework.common.generator.properties.sc;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 策略配置 - controller配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class StrategyServiceImplProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = -1140901075026789805L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * 自定义继承的ServiceImpl类全称，带包名
     */
    private String superClass;

    /**
     * 格式化service实现类文件名称
     */
    private String formatFileName;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public StrategyServiceImplProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.superClass = properties.getProperty(SC_SERVICE_IMPL_SUPER_CLASS, SC_SERVICE_IMPL_SUPER_CLASS_VALUE);
        this.formatFileName = properties.getProperty(SC_SERVICE_IMPL_FORMAT_FILE_NAME, SC_SERVICE_IMPL_FORMAT_FILE_NAME_VALUE);
    }
}

