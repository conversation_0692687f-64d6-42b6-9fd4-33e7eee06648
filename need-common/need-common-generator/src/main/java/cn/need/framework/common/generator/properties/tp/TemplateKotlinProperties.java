package cn.need.framework.common.generator.properties.tp;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 模版配置 - kotlin模版配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class TemplateKotlinProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = -8292539087914714537L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * entity 模版
     */
    private String entity;

    /**
     * controller 模版
     */
    private String controller;

    /**
     * mapper 模版
     */
    private String mapper;

    /**
     * service 模版
     */
    private String service;

    /**
     * serviceImpl 模版
     */
    private String serviceImpl;

    /**
     * vo 模版
     */
    private String vo;

    /**
     * createParam 模版
     */
    private String createParam;


    /**
     * updateParam 模版
     */
    private String updateParam;

    /**
     * pageVo 模版
     */
    private String pageVo;

    /**
     * query 模版
     */
    private String query;

    /**
     * dto 模版
     */
    private String dto;

    /**
     * converter 模版
     */
    private String converter;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public TemplateKotlinProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.entity = properties.getProperty(TP_KOTLIN_ENTITY, TP_KOTLIN_ENTITY_VALUE);
        this.controller = properties.getProperty(TP_KOTLIN_CONTROLLER, TP_KOTLIN_CONTROLLER_VALUE);
        this.mapper = properties.getProperty(TP_KOTLIN_MAPPER, TP_KOTLIN_MAPPER_VALUE);
        this.service = properties.getProperty(TP_KOTLIN_SERVICE, TP_KOTLIN_SERVICE_VALUE);
        this.serviceImpl = properties.getProperty(TP_KOTLIN_SERVICE_IMPL, TP_KOTLIN_SERVICE_IMPL_VALUE);
        this.vo = properties.getProperty(TP_KOTLIN_VO, TP_KOTLIN_VO_VALUE);
        this.query = properties.getProperty(TP_KOTLIN_QUERY, TP_KOTLIN_QUERY_VALUE);
        this.createParam = properties.getProperty(TP_KOTLIN_CREATE_PARAM, TP_KOTLIN_CREATE_PARAM_VALUE);
        this.updateParam = properties.getProperty(TP_KOTLIN_UPDATE_PARAM, TP_KOTLIN_UPDATE_PARAM_VALUE);
        this.dto = properties.getProperty(TP_KOTLIN_DTO, TP_KOTLIN_DTO_VALUE);
        this.converter = properties.getProperty(TP_KOTLIN_CONVERTER, TP_KOTLIN_CONVERTER_VALUE);
    }
}
