package cn.need.framework.common.generator.utils;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.stream.Streams;
import com.baomidou.mybatisplus.annotation.DbType;
import org.springframework.lang.NonNull;

import java.util.stream.Collectors;

/**
 * 代码生成器工具类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class GeneratorUtil {

    /**
     * 私有化构造函数，禁止实例化该类
     */
    private GeneratorUtil() {
        throw new AssertionError("No " + getClass().getName() + " instances for you!");
    }

    /**
     * 判断是否mysql数据库
     *
     * @param url 数据库连接url
     * @return boolean
     */
    public static boolean isMysql(final String url) {
        return isMysql(getDbType(url));
    }

    /**
     * 判断是否mysql数据库
     *
     * @param dbType 数据库类型
     * @return boolean
     */
    public static boolean isMysql(final DbType dbType) {
        return dbType == DbType.MYSQL || dbType == DbType.MARIADB;
    }

    /**
     * 根据数据库连接url获取数据库类型
     *
     * @param url 数据库连接url
     * @return 数据库类型
     */
    @SuppressWarnings("SpellCheckingInspection")
    public static DbType getDbType(@NonNull final String url) {
        Validate.notEmpty(url, "The url must not be empty.");
        if (url.contains(":mysql:") || url.contains(":cobar:")) {
            return DbType.MYSQL;
        } else if (url.contains(":oracle:")) {
            return DbType.ORACLE;
        } else if (url.contains(":postgresql:")) {
            return DbType.POSTGRE_SQL;
        } else if (url.contains(":sqlserver:")) {
            return DbType.SQL_SERVER;
        } else if (url.contains(":db2:")) {
            return DbType.DB2;
        } else if (url.contains(":mariadb:")) {
            return DbType.MARIADB;
        } else if (url.contains(":sqlite:")) {
            return DbType.SQLITE;
        } else if (url.contains(":h2:")) {
            return DbType.H2;
        } else if (url.contains(":lealone:")) {
            return DbType.LEALONE;
        } else if (url.contains(":kingbase:") || url.contains(":kingbase8:")) {
            return DbType.KINGBASE_ES;
        } else if (url.contains(":dm:")) {
            return DbType.DM;
        } else if (url.contains(":zenith:")) {
            return DbType.GAUSS;
        } else if (url.contains(":oscar:")) {
            return DbType.OSCAR;
        } else if (url.contains(":firebird:")) {
            return DbType.FIREBIRD;
        } else if (url.contains(":xugu:")) {
            return DbType.XU_GU;
        } else if (url.contains(":clickhouse:")) {
            return DbType.CLICK_HOUSE;
        } else if (url.contains(":sybase:")) {
            return DbType.SYBASE;
        } else {
            return DbType.OTHER;
        }
    }

    /**
     * 根据分隔符，连接字符串
     *
     * @param delimiter 分隔符
     * @param params    需要连接的字符串
     * @return 连接之后的字符串
     */
    public static String joining(String delimiter, String... params) {
        return Streams.of(params).filter(ObjectUtil::isNotEmpty).collect(Collectors.joining(delimiter));
    }
}
