package cn.need.framework.common.generator.properties.pc;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.core.constant.StringPool.EMPTY;
import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 包路径配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class PackageProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = 4554181300252896149L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * 父包名。如果为空，下面子包名必须写全部， 否则就只需写子包名
     */
    private String parent;

    /**
     * 父包模块名
     */
    private String moduleName;

    /**
     * Entity包名
     */
    private String entity;

    /**
     * Service包名
     */
    private String service;

    /**
     * Service Impl包名
     */
    private String serviceImpl;

    /**
     * Mapper包名
     */
    private String mapper;

    /**
     * Mapper XML包名
     */
    private String xml;

    /**
     * Controller包名
     */
    private String controller;

    /**
     * VO包名
     */
    private String vo;

    /**
     * CreateParam包名
     */
    private String createParam;

    /**
     * UpdateParam包名
     */
    private String updateParam;

    /**
     * PageVo包名
     */
    private String pageVo;

    /**
     * Query包名
     */
    private String query;

    /**
     * DTO包名
     */
    private String dto;

    /**
     * Converter包名
     */
    private String converter;

    /**
     * 连接父子包名
     */
    private String joinPackage;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public PackageProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.parent = properties.getProperty(PC_PARENT, PC_PARENT_VALUE);
        this.moduleName = properties.getProperty(PC_MODULE_NAME, EMPTY);
        this.entity = properties.getProperty(PC_ENTITY, PC_ENTITY_VALUE);
        this.service = properties.getProperty(PC_SERVICE, PC_SERVICE_VALUE);
        this.serviceImpl = properties.getProperty(PC_SERVICE_IMPL, PC_SERVICE_IMPL_VALUE);
        this.mapper = properties.getProperty(PC_MAPPER, PC_MAPPER_VALUE);
        this.xml = properties.getProperty(PC_XML, PC_XML_VALUE);
        this.controller = properties.getProperty(PC_CONTROLLER, PC_CONTROLLER_VALUE);
        this.vo = properties.getProperty(PC_VO, PC_VO_VALUE);
        this.createParam = properties.getProperty(PC_CREATE_PARAM, PC_CREATE_PARAM_VALUE);
        this.updateParam = properties.getProperty(PC_UPDATE_PARAM, PC_UPDATE_PARAM_VALUE);
        this.query = properties.getProperty(PC_QUERY, PC_QUERY_VALUE);
        this.pageVo = properties.getProperty(PC_PAGE_VO, PC_PAGE_VO_VALUE);
        this.dto = properties.getProperty(PC_DTO, PC_DTO_VALUE);
        this.converter = properties.getProperty(PC_CONVERTER, PC_CONVERTER_VALUE);
        this.joinPackage = properties.getProperty(PC_JOIN_PACKAGE, EMPTY);
    }

}
