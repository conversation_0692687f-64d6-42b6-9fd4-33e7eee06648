package cn.need.framework.common.generator.properties.sc;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.core.constant.StringPool.TRUE;
import static cn.need.framework.common.core.lang.BooleanUtil.toBoolean;
import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 策略配置 - vo配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class StrategyDtoProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = 2856844884026647629L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * 自定义继承的DTO类全称，带包名
     */
    private String superClass;

    /**
     * dto文件路径
     */
    private String filePath;

    /**
     * dto文件名称
     */
    private String fileName;

    /**
     * 是否覆盖已有文件（默认 true）
     */
    private boolean fileOverride = true;

    /**
     * 是否开启dto生成策略（默认 true）
     */
    private boolean enable = true;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public StrategyDtoProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.superClass = properties.getProperty(SC_DTO_SUPER_CLASS, SC_DTO_SUPER_CLASS_VALUE);
        this.filePath = properties.getProperty(SC_DTO_FILE_PATH);
        this.fileName = properties.getProperty(SC_DTO_FILE_NAME, SC_DTO_FILE_NAME_VALUE);
        this.fileOverride = toBoolean(properties.getProperty(SC_DTO_FILE_OVERRIDE, TRUE));
        this.enable = toBoolean(properties.getProperty(SC_DTO_ENABLE, TRUE));
    }
}
