package cn.need.framework.common.generator.properties.sc;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.core.constant.StringPool.TRUE;
import static cn.need.framework.common.core.lang.BooleanUtil.toBoolean;
import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 策略配置 - serviceImpl配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class StrategyControllerProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = -1140901075026789805L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * 自定义继承的Controller类全称，带包名
     */
    private String superClass;

    /**
     * 格式化controller文件名称
     */
    private String formatFileName;

    /**
     * 开启生成@RestController控制器（默认 true）
     */
    private boolean restStyle = true;

    /**
     * 开启驼峰转连字符（默认 true）
     */
    private boolean hyphenStyle = true;

    /**
     * 是否覆盖已有文件（默认 true）
     */
    private boolean fileOverride = true;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public StrategyControllerProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.superClass = properties.getProperty(SC_CONTROLLER_SUPER_CLASS, SC_CONTROLLER_SUPER_CLASS_VALUE);
        this.formatFileName = properties.getProperty(SC_CONTROLLER_FORMAT_FILE_NAME, SC_CONTROLLER_FORMAT_FILE_NAME_VALUE);
        this.restStyle = toBoolean(properties.getProperty(SC_CONTROLLER_REST_STYLE, TRUE));
        this.hyphenStyle = toBoolean(properties.getProperty(SC_CONTROLLER_HYPHEN_STYLE, TRUE));
        this.fileOverride = toBoolean(properties.getProperty(SC_CONTROLLER_FILE_OVERRIDE, TRUE));
    }
}
