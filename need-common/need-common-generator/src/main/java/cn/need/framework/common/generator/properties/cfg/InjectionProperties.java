package cn.need.framework.common.generator.properties.cfg;

import cn.need.framework.common.core.constant.CharPool;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.map.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Map;
import java.util.Properties;

import static cn.need.framework.common.core.constant.StringPool.*;
import static cn.need.framework.common.core.lang.BooleanUtil.toBoolean;
import static cn.need.framework.common.core.lang.ObjectUtil.*;
import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 注入配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class InjectionProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = 6349189248858279138L;

    ////////////////////////// 属性字段 ///////////////////////////////////
    /**
     * 自定义参数配置map集合
     */
    private final Map<String, Object> propertiesMap = Maps.hashMap();
    /**
     * java版本
     */
    private int javaVersion;
    /**
     * 项目前缀名，用于输出目录生成项目名用
     */
    private String projectPrefix;
    /**
     * server项目后缀
     */
    private String serverSuffix;
    /**
     * client项目后缀
     */
    private String clientSuffix;
    /**
     * 这里自定了Controller类上的请求路径前缀，路径为前缀+模块名称+请求路径，如：@RequestMapping("/api/item/unit")
     */
    private String requestPrefix;
    /**
     * 占位文件名字
     */
    private String placeholderFileName;
    /**
     * 是否开启生成占位文件（默认 true）
     */
    private boolean enablePlaceholder = true;
    /**
     * 标记controller是否生成新增数据的方法（默认 true）
     */
    private boolean controllerInsert = true;
    /**
     * 标记controller是否生成修改数据的方法（默认 true）
     */
    private boolean controllerUpdate = true;
    /**
     * 标记controller是否生成删除数据的方法（默认 true）
     */
    private boolean controllerRemove = true;
    /**
     * 标记controller是否生成设置数据有效性的方法（默认 false）
     */
    private boolean controllerActive;
    /**
     * 标记controller是否生成获取数据详情的方法（默认 true）
     */
    private boolean controllerDetail = true;
    /**
     * 标记controller是否生成获取数据列表的方法（默认 true）
     */
    private boolean controllerList = true;
    /**
     * 标记controller是否生成通用导入方法（默认 false）
     */
    private boolean controllerImport;
    /**
     * 标记controller是否生成通用导出方法（默认 false）
     */
    private boolean controllerExport;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public InjectionProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.javaVersion = convert(properties.getProperty(CFG_JAVA_VERSION, CFG_JAVA_VERSION_VALUE), Integer.class);
        injectionProperties(CFG_JAVA_VERSION, this.javaVersion);
        this.projectPrefix = properties.getProperty(CFG_PROJECT_PREFIX, null);
        injectionProperties(CFG_PROJECT_PREFIX, this.projectPrefix);
        this.serverSuffix = properties.getProperty(CFG_SERVER_SUFFIX, CFG_SERVER_SUFFIX_VALUE);
        injectionProperties(CFG_SERVER_SUFFIX, this.serverSuffix);
        this.clientSuffix = properties.getProperty(CFG_CLIENT_SUFFIX, CFG_CLIENT_SUFFIX_VALUE);
        injectionProperties(CFG_CLIENT_SUFFIX, this.clientSuffix);
        this.requestPrefix = properties.getProperty(CFG_REQUEST_PREFIX, CFG_REQUEST_PREFIX_VALUE);
        injectionProperties(CFG_REQUEST_PREFIX, this.requestPrefix);
        this.placeholderFileName = properties.getProperty(CFG_PLACEHOLDER_FILE_NAME, CFG_PLACEHOLDER_FILE_NAME_VALUE);
        injectionProperties(CFG_PLACEHOLDER_FILE_NAME, this.placeholderFileName);
        this.enablePlaceholder = toBoolean(properties.getProperty(CFG_ENABLE_PLACEHOLDER, TRUE));
        injectionProperties(CFG_ENABLE_PLACEHOLDER, this.enablePlaceholder);
        this.controllerInsert = toBoolean(properties.getProperty(CFG_CONTROLLER_INSERT, TRUE));
        injectionProperties(CFG_CONTROLLER_INSERT, this.controllerInsert);
        this.controllerUpdate = toBoolean(properties.getProperty(CFG_CONTROLLER_UPDATE, TRUE));
        injectionProperties(CFG_CONTROLLER_UPDATE, this.controllerUpdate);
        this.controllerRemove = toBoolean(properties.getProperty(CFG_CONTROLLER_REMOVE, TRUE));
        injectionProperties(CFG_CONTROLLER_REMOVE, this.controllerRemove);
        this.controllerActive = toBoolean(properties.getProperty(CFG_CONTROLLER_ACTIVE, FALSE));
        injectionProperties(CFG_CONTROLLER_ACTIVE, this.controllerActive);
        this.controllerDetail = toBoolean(properties.getProperty(CFG_CONTROLLER_DETAIL, TRUE));
        injectionProperties(CFG_CONTROLLER_DETAIL, this.controllerDetail);
        this.controllerList = toBoolean(properties.getProperty(CFG_CONTROLLER_LIST, TRUE));
        injectionProperties(CFG_CONTROLLER_LIST, this.controllerList);
        this.controllerImport = toBoolean(properties.getProperty(CFG_CONTROLLER_IMPORT, FALSE));
        injectionProperties(CFG_CONTROLLER_IMPORT, this.controllerImport);
        this.controllerExport = toBoolean(properties.getProperty(CFG_CONTROLLER_EXPORT, FALSE));
        injectionProperties(CFG_CONTROLLER_EXPORT, this.controllerExport);
        // 注入参数到map集合中
        injectionProperties(properties);
    }

    ////////////////////////// 功能方法 ///////////////////////////////////

    /**
     * 将参数key、值value注入到参数map集合中，不处理value为空的数据
     */
    public void injectionProperties(String key, Object value) {
        if (isNotNull(value)) {
            this.propertiesMap.putIfAbsent(handleKey(key), value);
        }
    }

    ////////////////////////// 重写赋值方法 ///////////////////////////////////

    /**
     * 设置自定义参数配置map集合
     *
     * @param propertiesMap 自定义参数配置map集合
     */
    public void setPropertiesMap(Map<String, Object> propertiesMap) {
        if (isNotEmpty(propertiesMap)) {
            propertiesMap.forEach(this::injectionProperties);
        }
    }

    ////////////////////////// 私有方法 ///////////////////////////////////

    /**
     * 将cfg.开头的参数注入到参数map集合中
     */
    private void injectionProperties(Properties properties) {
        //获取配置信息中所有参数名
        Enumeration<?> enumeration = properties.propertyNames();
        while (enumeration.hasMoreElements()) {
            // 获取key值，并过滤非cfg.前缀的配置
            String key = enumeration.nextElement().toString();
            if (key.startsWith(CFG_PREFIX_VALUE)) {
                // 将参数注入到propertiesMap中
                injectionProperties(key, properties.getProperty(key));
            }
        }
    }

    /**
     * 处理key值
     * 去前缀、转驼峰的优化处理，如：cfg.dto.superClassPackage 处理后为 dtoSuperClassPackage
     */
    private String handleKey(String key) {
        // 获取"."的位置
        int index = key.indexOf(CharPool.DOT);
        // 获取前缀
        String prefix = index < 0 ? EMPTY : key.substring(0, index + 1);
        // 判断前缀是否在前缀集合中
        String newKey = REMOVE_PREFIX_VALUES.contains(prefix) ? key.substring(prefix.length()) : key;
        // 处理key值
        return StringUtil.characterToCamel(newKey, CharPool.DOT);
    }

}
