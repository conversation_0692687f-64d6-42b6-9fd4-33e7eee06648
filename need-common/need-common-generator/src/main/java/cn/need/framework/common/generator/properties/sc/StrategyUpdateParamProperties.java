package cn.need.framework.common.generator.properties.sc;

import cn.need.framework.common.core.lang.Validate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Properties;

import static cn.need.framework.common.core.constant.StringPool.TRUE;
import static cn.need.framework.common.core.lang.BooleanUtil.toBoolean;
import static cn.need.framework.common.generator.constant.ConfigConstants.*;

/**
 * 策略配置 - vo配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
public class StrategyUpdateParamProperties implements Serializable {

    @Serial
    private static final long serialVersionUID = -2220295230390055170L;

    ////////////////////////// 属性字段 ///////////////////////////////////

    /**
     * 自定义继承的updateParam类全称，带包名
     */
    private String superClass;

    /**
     * updateParam文件路径
     */
    private String filePath;

    /**
     * updateParam文件名称
     */
    private String fileName;

    /**
     * 是否覆盖已有文件（默认 true）
     */
    private boolean fileOverride = true;

    /**
     * 是否开启updateParam生成策略（默认 true）
     */
    private boolean enable = true;

    ////////////////////////// 构造函数 ///////////////////////////////////

    /**
     * 构造函数
     */
    public StrategyUpdateParamProperties(Properties properties) {
        Validate.notNull(properties, "The properties must not be null.");
        this.superClass = properties.getProperty(SC_UPDATE_PARAM_SUPER_CLASS, SC_UPDATE_PARAM_SUPER_CLASS_VALUE);
        this.filePath = properties.getProperty(SC_UPDATE_PARAM_FILE_PATH);
        this.fileName = properties.getProperty(SC_UPDATE_PARAM_FILE_NAME, SC_UPDATE_PARAM_FILE_NAME_VALUE);
        this.fileOverride = toBoolean(properties.getProperty(SC_UPDATE_PARAM_FILE_OVERRIDE, TRUE));
        this.enable = toBoolean(properties.getProperty(SC_UPDATE_PARAM_ENABLE, TRUE));
    }
}
