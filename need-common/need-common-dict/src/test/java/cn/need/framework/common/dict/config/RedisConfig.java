package cn.need.framework.common.dict.config;

import cn.need.framework.common.dict.api.AreaRepertory;
import cn.need.framework.common.dict.api.ConfigRepertory;
import cn.need.framework.common.dict.api.DictRepertory;
import cn.need.framework.common.dict.impl.RedisAreaRepertory;
import cn.need.framework.common.dict.impl.RedisConfigRepertory;
import cn.need.framework.common.dict.impl.RedisDictRepertory;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.TimeZone;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    @Bean
    public RedisConnectionFactory factory() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration("106.55.101.5", 16000);
        configuration.setPassword("uneed2020");
        configuration.setDatabase(15);
        return new LettuceConnectionFactory(configuration);
    }

    /**
     * redisTemplate 序列化使用自定义序列化类
     *
     * @param factory redis连接工厂类
     * @return RedisTemplate
     */
    @Bean
    @SuppressWarnings("ALL")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);

        // 设置key的序列化规则
        StringRedisSerializer keySerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(keySerializer);
        redisTemplate.setHashKeySerializer(keySerializer);
        // 设置value的序列化规则，使用Jackson2JsonRedisSerialize 替换默认序列化
        Jackson2JsonRedisSerializer valueSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.WRAPPER_ARRAY);
        mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        valueSerializer.setObjectMapper(mapper);
        redisTemplate.setValueSerializer(valueSerializer);
        redisTemplate.setHashValueSerializer(valueSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    /**
     * 注入系统配置缓存操作工具，可以直接使用DictUtil从redis缓存中获取数据字典信息
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    public DictRepertory dictRepertory(RedisTemplate redisTemplate) {
        return new RedisDictRepertory(redisTemplate);
    }

    /**
     * 注入系统配置缓存操作工具，可以直接使用DictUtil从redis缓存中获取数据字典信息
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    public AreaRepertory areaRepertory(RedisTemplate redisTemplate) {
        return new RedisAreaRepertory(redisTemplate, true);
    }

    /**
     * 注入系统配置缓存操作工具，可以直接使用ConfigUtil从redis缓存中获取系统配置信息
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public ConfigRepertory configRepertory(RedisTemplate redisTemplate) {
        return new RedisConfigRepertory(redisTemplate);
    }
}
