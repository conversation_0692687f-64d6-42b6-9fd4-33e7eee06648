package cn.need.framework.common.dict.api;

import org.springframework.data.redis.core.BoundHashOperations;

import java.util.Map;

/**
 * 配置缓存操作接口，包含了在缓存中对字典数据进行增、删、改、查等操作
 *
 * <AUTHOR>
 */
public interface ConfigRepertory {

    /**
     * 根据配置编号从缓存获取配置值
     *
     * @param key 配置编号
     * @return String 配置值
     */
    String get(String key);

    /**
     * 根据配置编号从缓存获取配置值
     *
     * @param key      配置编号
     * @param grouping 分组
     * @return String 配置值
     * @since 1.3.0
     */
    String get(String key, String grouping);

    /**
     * 根据分组信息从缓存获取配置值集合
     *
     * @param grouping 分组信息
     * @return Map<String, String> 配置值
     * @since 1.3.0
     */
    Map<String, String> grouping(String grouping);

    /**
     * 将配置信息添加至缓存中，允许添加空值
     *
     * @param key   配置编号
     * @param value 配置值
     */
    void add(String key, String value);

    /**
     * 将配置信息添加至缓存中，允许添加空值
     *
     * @param key      配置编号
     * @param value    配置值
     * @param grouping 分组
     * @since 1.3.0
     */
    void add(String key, String value, String grouping);

    /**
     * 批量添加配置信息至缓存中，允许添加空值
     *
     * @param configMap 配置信息map集合
     * @since 1.1.0
     */
    void add(Map<String, String> configMap);

    /**
     * 批量添加配置信息至缓存中，允许添加空值
     *
     * @param configMap 配置信息map集合
     * @param grouping  分组信息
     * @since 1.3.0
     */
    void add(Map<String, String> configMap, String grouping);

    /**
     * 根据配置编号，从缓存中删除配置信息
     *
     * @param key 配置编号
     */
    void del(String key);

    /**
     * 根据配置编号，从缓存中删除配置信息
     *
     * @param key      配置编号
     * @param grouping 分组信息
     * @since 1.3.0
     */
    void del(String key, String grouping);

    /**
     * 根据分组信息，从缓存中删除配置信息
     *
     * @param grouping 分组信息
     * @since 1.3.0
     */
    void delGrouping(String grouping);

    /**
     * 清空所有配置信息
     *
     * @since 1.1.0
     */
    void clear();

    /**
     * 初始化配置信息
     *
     * @param configMap 配置信息map集合，key为分组、value为分组下的配置集合
     */
    void initialization(Map<String, Map<String, String>> configMap);

    /**
     * 获取RedisTemplate的BoundHashOperations对象
     *
     * @param grouping 分组信息
     * @return BoundHashOperations<String, String, String>
     * @since 1.1.0
     */
    BoundHashOperations<String, String, String> getHash(String grouping);

}
