package cn.need.framework.common.dict.api;

import org.springframework.data.redis.core.BoundHashOperations;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/20
 */
public interface ExceptionLangRepertory {

    /**
     * 清空所有字段语言配置信息
     *
     * @since 1.1.0
     */
    void clear();

    /**
     * 初始化字段语言配置信息
     *
     * @param langFieldMap 怨言信息map集合，key为语言集合、value为分组下的多语言集合
     */
    void initialization(Map<String, Map<String, String>> langFieldMap);

    /**
     * 获取RedisTemplate的BoundHashOperations对象
     *
     * @param lang 语言信息
     * @return BoundHashOperations<String, String, String>
     * @since 1.1.0
     */
    BoundHashOperations<String, String, String> getHash(String lang);

    /**
     * 将语言信息添加至缓存中，允许添加空值
     *
     * @param key   字段属性
     * @param value 字段翻译
     * @param lang  语言编码
     * @since 1.3.0
     */
    void add(String key, String value, String lang);

    /**
     * 将语言信息添加至缓存中，允许添加空值
     *
     * @param key   字段属性
     * @param value 字段翻译
     * @since 1.3.0
     */
    default void add(String key, String value) {
        add(key, value, "zh");
    }

    /**
     * 根据配置编号，从缓存中删除配置信息
     *
     * @param key 配置编号
     */
    default void del(String key) {
        del(key, "zh");
    }

    /**
     * 根据字段属性，从缓存中删除字段语言翻译
     *
     * @param key  字段属性
     * @param lang 语言编码
     * @since 1.3.0
     */
    void del(String key, String lang);


    /**
     * 批量字段多语言翻译信息至缓存中，允许添加空值
     *
     * @param langFieldMap 字段多语言翻译信息map集合
     * @since 1.1.0
     */
    default void add(Map<String, String> langFieldMap) {
        add(langFieldMap, "zh");
    }

    /**
     * 批量字段多语言翻译信息至缓存中，允许添加空值
     *
     * @param langFieldMap 字段多语言翻译信息map集合
     * @param lang         语言信息
     * @since 1.3.0
     */
    void add(Map<String, String> langFieldMap, String lang);


    /**
     * 根据语言信息从缓存获取配置值集合
     *
     * @param lang 语言信息
     * @return Map<String, String> 配置值
     * @since 1.3.0
     */
    Map<String, String> listLand(String lang);

    /**
     * 删除语言下的所素有配置
     *
     * @param lang 语言信息
     */
    void delLang(String lang);

    /**
     * 获取搜有多语言配置的缓存数据
     *
     * @return 多语言配置结果
     */
    Map<String, Map<String, String>> allLand();

    /**
     * 根据多语言获取缓存数据
     *
     * @param langList 语言编码数组
     * @return 多语言配置结果
     */
    Map<String, Map<String, String>> listByMulLang(List<String> langList);

    /**
     * 获取异常信息
     *
     * @param key  编码
     * @param lang 语言
     * @return 描述信息
     */
    String get(String key, String lang);

    String get(String key, String lang, Object... arguments);

    default String get(String key) {
        return get(key, null);
    }
}
