package cn.need.framework.common.dict.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户缓存信息.
 *
 * <AUTHOR>
 */
@Data
public class UserCache implements Serializable {

    private static final long serialVersionUID = -7922341152503567987L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 用户类型，取值数据字典[USER_TYPE]
     */
    private String userType;

    /**
     * 用户来源，取值数据字典[USER_SOURCE]
     */
    private String userSource;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 个性签名
     */
    private String signature;

    /**
     * 数据状态，取值0/1，1表示数据有效
     */
    private Integer state;

    /**
     * 用户角色集合
     */
    private List<String> roles;

    /**
     * 用户权限集合
     */
    private List<String> permissions;

    /**
     * 二维码
     */
    @Deprecated
    private String qrCode;

    /**
     * 性别
     */
    @Deprecated
    private String gender;

    /**
     * 在职状态
     */
    @Deprecated
    private String jobStatus;

    /**
     * 关联siebelId
     */
    @Deprecated
    private String siebelId;

    /**
     * 直属领导id
     */
    @Deprecated
    private Long leaderId;

    /**
     * 用户组织编码集合，第一个永远为主组织
     */
    @Deprecated
    private List<String> organizations;

    /**
     * 用户岗位编码集合
     */
    @Deprecated
    private List<String> positions;

}
