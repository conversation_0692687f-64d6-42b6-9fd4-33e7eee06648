package cn.need.framework.common.dict.attribute;

import cn.need.framework.common.annotation.enums.AreaDepth;
import cn.need.framework.common.annotation.enums.AreaProperty;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Data;
import lombok.NonNull;

import java.io.Serial;
import java.io.Serializable;
import java.lang.reflect.Method;

import static cn.need.framework.common.core.bean.BeanUtil.executeGetter;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotEmpty;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * 行政区域属性对象
 *
 * <AUTHOR>
 */
@Data
public class AreaAttribute implements Serializable {

    @Serial
    private static final long serialVersionUID = 5102748669015871086L;

    /**
     * 行政区域注解映射的字段名称
     */
    private String fieldName;

    /**
     * 行政区域注解上配置的行政区域code值来源字段名称
     */
    private String codeField;

    /**
     * 行政区域注解上配置的行政区域编号
     */
    private String code;

    /**
     * 行政区域的根级编码
     */
    private AreaDepth depth;

    /**
     * 指定显示行政区域全链名称时，中间的间隔符号，默认为""
     */
    private String space;

    /**
     * 行政区域注解上配置的获取行政区域属性类型
     */
    private AreaProperty property;

    /**
     * 用来取行政区域编号的类方法
     */
    private Method getter;

    /**
     * 用来赋值行政区域的方法
     */
    private Method setter;

    /**
     * 构建行政区域的key值
     *
     * @param bean 数据对象
     * @param <T>  数据对象泛型
     * @return String 行政区域key
     */
    public <T extends Serializable> String buildCode(@NonNull T bean) {
        // 优先取注解配置的key值
        if (isNotEmpty(code)) {
            return code;
        }
        // 其次通过获取的getter方法获取key值
        if (isNotNull(getGetter())) {
            return ObjectUtil.toString(executeGetter(bean, getGetter()));
        }
        return null;
    }

}
