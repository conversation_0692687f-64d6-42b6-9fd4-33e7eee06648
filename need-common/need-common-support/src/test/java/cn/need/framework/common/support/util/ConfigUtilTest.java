package cn.need.framework.common.support.util;

import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.dict.api.ConfigRepertory;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Map;
import java.util.Set;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {"classpath:spring-redis.xml"})
@Slf4j
class ConfigUtilTest {

    @Autowired
    private ConfigRepertory repertory;

    /**
     * 初始化数据字典
     */
    @Test
    @Order(0)
    public void init() {
        TimeMeter meter = new TimeMeter();
        Map<String, Map<String, String>> map = buildConfigData();
        log.warn(" >>>>>>>> test build data finished. time=[{}]ms", meter.sign("one"));
        repertory.initialization(map);
        log.warn(" >>>>>>>> test initialization finished. time=[{}]ms", meter.sign("one", "two"));
    }

    @Test
    @Order(1)
    void get() {
        Assertions.assertEquals("1400050520", ConfigUtil.get("SMS_APP_ID"));
        Assertions.assertEquals(0, ConfigUtil.get("SMS_TEST", Integer.class));
        Assertions.assertTrue(ObjectUtil.equals(Boolean.FALSE, ConfigUtil.get("SMS_TEST", Boolean.class)));
    }

    @Test
    @Order(2)
    void testGet() {
        Assertions.assertEquals("abc", ConfigUtil.get("SMS_APP_ID", "PRODUCT"));
        Assertions.assertEquals(1, ConfigUtil.get("SMS_TEST", "PRODUCT", Integer.class));
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, ConfigUtil.get("SMS_TEST", "PRODUCT", Boolean.class)));

        Assertions.assertEquals("xyz", ConfigUtil.get("SMS_APP_ID", "ORDER"));
        Assertions.assertEquals(1, ConfigUtil.get("SMS_TEST", "ORDER", Integer.class));
        Assertions.assertTrue(ObjectUtil.equals(Boolean.TRUE, ConfigUtil.get("SMS_TEST", "ORDER", Boolean.class)));
    }

    @Test
    @Order(3)
    void getHash() {
        Set<String> keys = ObjectUtil.nullToDefault(ConfigUtil.getHash("PRODUCT").keys(), Lists.hashSet());
        Assertions.assertEquals(4, keys.size());
    }

    /**
     * 清空数据字典
     */
    @Test
    @Order(99)
    public void clear() {
        TimeMeter meter = new TimeMeter();
        repertory.clear();
        log.warn(" >>>>>>>> test clear finished. time=[{}]ms", meter.sign());
    }

    private Map<String, Map<String, String>> buildConfigData() {
        Map<String, Map<String, String>> confMap = Maps.hashMap();
        Map<String, String> map = Maps.hashMap();
        map.put("SMS_APP_ID", "1400050520");
        map.put("SMS_APP_KEY", "79848e433575514607cef61d1055d01e");
        map.put("SMS_TEST", "0");
        map.put("TEST", null);
        confMap.put("", map);

        Map<String, String> map2 = Maps.hashMap();
        map2.put("SMS_APP_ID", "abc");
        map2.put("SMS_APP_KEY", "123");
        map2.put("SMS_TEST", "1");
        map2.put("TEST", "");
        confMap.put("PRODUCT", map2);

        Map<String, String> map3 = Maps.hashMap();
        map3.put("SMS_APP_ID", "xyz");
        map3.put("SMS_APP_KEY", "789");
        map3.put("SMS_TEST", "1");
        map3.put("TEST", "test");
        confMap.put("ORDER", map3);
        return confMap;
    }
}