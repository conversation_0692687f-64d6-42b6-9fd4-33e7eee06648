package cn.need.framework.common.support.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class IdListCondition extends IdCondition {
    @Serial
    private static final long serialVersionUID = -464365845645578745L;

    @NotEmpty(message = "idList 不能为空")
    private List<Long> idList;
}
