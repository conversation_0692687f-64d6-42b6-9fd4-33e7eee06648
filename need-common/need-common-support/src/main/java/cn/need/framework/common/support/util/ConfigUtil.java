package cn.need.framework.common.support.util;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.dict.api.ConfigRepertory;
import org.springframework.data.redis.core.BoundHashOperations;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * 系统配置工具类
 *
 * <AUTHOR>
 */
public class ConfigUtil {

    /**
     * 用来缓存系统配置的工具对象
     */
    private static ConfigRepertory repertory;

    /**
     * 私有化构造函数
     */
    private ConfigUtil() {
        throw new AssertionError("No " + getClass().getName() + " instances for you!");
    }

    /**
     * 通过配置编号获取配置
     *
     * @param key 配置编号
     * @return String 配置值
     */
    public static String get(String key) {
        return getRepertory().get(key);
    }

    /**
     * 通过配置编号获取配置
     *
     * @param key      配置编号
     * @param grouping 分组信息
     * @return String 配置值
     * @since 1.3.0
     */
    public static String get(String key, String grouping) {
        return getRepertory().get(key, grouping);
    }

    /**
     * 通过配置编号获取配置值，会自动将获取的值转换为参数传递的类型，转换失败会抛异常
     *
     * @param key  配置编号
     * @param type 需要转换的值类型
     * @param <T>  值类型泛型参数
     * @return T 转换后的值
     * @since 1.1.0
     */
    public static <T> T get(String key, Class<T> type) {
        return ObjectUtil.convert(get(key), type);
    }

    /**
     * 通过配置编号获取配置值，会自动将获取的值转换为参数传递的类型，转换失败会抛异常
     *
     * @param key      配置编号
     * @param grouping 分组信息
     * @param type     需要转换的值类型
     * @param <T>      值类型泛型参数
     * @return T 转换后的值
     * @since 1.3.0
     */
    public static <T> T get(String key, String grouping, Class<T> type) {
        return ObjectUtil.convert(get(key, grouping), type);
    }

    /**
     * 获取RedisTemplate的BoundHashOperations对象，系统会为每一个字典集合构建一个hash对象
     *
     * @param grouping 分组
     * @return BoundHashOperations<String, String, String>
     * @since 1.1.0
     */
    public static BoundHashOperations<String, String, String> getHash(String grouping) {
        return getRepertory().getHash(grouping);
    }

    /**
     * spring容器中获取系统配置工具对象
     */
    private static ConfigRepertory getRepertory() {
        if (isNotNull(repertory)) {
            return repertory;
        }
        repertory = SpringUtil.getBean(ConfigRepertory.class);
        Validate.notNull(repertory, "Cannot get ConfigRepertory object from spring container!");
        return repertory;
    }
}
