package cn.need.framework.common.support.thread;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serial;
import java.io.Serializable;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "uneed.thread-pool")
public class ThreadPoolConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = -7662578934856121373L;

    /**
     * 池中所保存的线程数，包括空闲线程
     */
    private Integer corePoolSize;

    /**
     * 池中允许的最大线程数
     */
    private Integer maximumPoolSize;

    /**
     * 当线程数大于核心时，此为终止前多余的空闲线程等待新任务的最长时间（秒）
     */
    private Long keepAliveTime;

    /**
     * 任务队列最大容量
     */
    private Integer workQueue;

    /**
     * 线程名前缀
     */
    private String threadNamePrefix;

}
