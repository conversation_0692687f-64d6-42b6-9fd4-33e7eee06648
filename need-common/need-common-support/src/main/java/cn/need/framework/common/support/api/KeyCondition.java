package cn.need.framework.common.support.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * key值条件信息
 *
 * <AUTHOR>
 * @since 3。0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeyCondition implements Serializable {

    @Serial
    private static final long serialVersionUID = -1659383732575527074L;

    /**
     * key值
     */
    private String key;

}
