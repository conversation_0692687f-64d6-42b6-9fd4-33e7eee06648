package cn.need.framework.common.support.redis;


import cn.need.framework.common.core.exception.unchecked.UncheckedException;
import cn.need.framework.common.core.http.HttpCode;

import java.io.Serial;

/**
 * redis加锁功能异常.
 *
 * <AUTHOR>
 */
public class TryLockException extends UncheckedException {

    @Serial
    private static final long serialVersionUID = -340611234487220247L;

    private static final HttpCode HTTP_CODE = HttpCode.BUSINESS_HANDLER_FAILED;

    public TryLockException() {
        super(HTTP_CODE.getCode(), HTTP_CODE.getMessage());
    }

    public TryLockException(String message) {
        super(HTTP_CODE.getCode(), message);
    }

    public TryLockException(String message, Throwable throwable) {
        super(HTTP_CODE.getCode(), message, throwable);
    }

    public TryLockException(Throwable throwable) {
        super(HTTP_CODE.getCode(), HTTP_CODE.getMessage(), throwable);
    }

    public TryLockException(Integer code) {
        super(code, HTTP_CODE.getMessage());
    }

    public TryLockException(Integer code, String message) {
        super(code, message);
    }

    public TryLockException(Integer code, Throwable throwable) {
        super(code, HTTP_CODE.getMessage(), throwable);
    }

    public TryLockException(Integer code, String message, Throwable throwable) {
        super(code, message, throwable);
    }
}
