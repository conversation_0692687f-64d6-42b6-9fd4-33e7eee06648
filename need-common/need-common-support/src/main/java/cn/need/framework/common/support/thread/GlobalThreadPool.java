package cn.need.framework.common.support.thread;

import cn.need.framework.common.support.util.SpringUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
public class GlobalThreadPool {

    /**
     * 私有化实例
     */
    private static volatile GlobalThreadPool INSTANCE = null;
    /**
     * 线程池处理对象
     */
    private final ExecutorService executorService;
    /**
     * 池中所保存的线程数，包括空闲线程
     */
    private int corePoolSize = ThreadConstant.DEFAULT_CORE_POOL_SIZE;
    /**
     * 池中允许的最大线程数
     */
    private int maximumPoolSize = ThreadConstant.DEFAULT_MAXIMUM_POOL_SIZE;
    /**
     * 当线程数大于核心时，此为终止前多余的空闲线程等待新任务的最长时间
     */
    private long keepAliveTime = ThreadConstant.DEFAULT_KEEP_ALIVE_TIME;
    /**
     * 任务队列最大容量
     */
    private int workQueue = ThreadConstant.DEFAULT_WORK_QUEUE;
    /**
     * 线程名前缀
     */
    private String threadNamePrefix = ThreadConstant.DEFAULT_THREAD_NAME_PREFIX;

    /////////////////////////////////////////////////// 私有方法 //////////////////////////////////////////////////////
    /**
     * 阻塞队列
     */
    private BlockingQueue<Runnable> blockingQueue = null;

    /**
     * 私有化构造函数
     */
    private GlobalThreadPool() {
        //初始化线程池参数
        initThreadPoolParams();
        //初始化阻塞队列
        initBlockingQueue();
        //初始化线程参数
        executorService = TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                this.corePoolSize,
                this.maximumPoolSize,
                this.keepAliveTime,
                TimeUnit.SECONDS,
                this.blockingQueue,
                buildThreadFactory(),
                buildRejectedExecutionHandler()
        ));
    }

    /**
     * 双重校验锁模式获取单例的GlobalThreadPool对象
     *
     * @return GlobalThreadPool
     */
    public static GlobalThreadPool instance() {
        if (INSTANCE == null) {
            synchronized (GlobalThreadPool.class) {
                if (INSTANCE == null) {
                    INSTANCE = new GlobalThreadPool();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 获取全局通用的线程池
     *
     * @return ExecutorService
     */
    public ExecutorService getThreadPool() {
        return this.executorService;
    }

    /////////////////////////////////////////////////// 私有属性 //////////////////////////////////////////////////////

    /**
     * 对外的线程池执行器，不带返回值
     *
     * @param runnable 接收一个多线程处理对象
     */
    public void execute(Runnable runnable) {
        this.executorService.execute(runnable);
    }

    /**
     * 对外的线程池执行器，带返回值
     *
     * @param task 接收一个多线程任务对象
     */
    public <T> Future<T> submit(Callable<T> task) {
        return this.executorService.submit(task);
    }

    /**
     * 对外开放获取队列的方法
     *
     * @return ArrayBlockingQueue<Runnable> 线程池队列
     */
    public BlockingQueue<Runnable> getQueue() {
        return this.blockingQueue;
    }

    /**
     * 初始化线程池相关参数信息
     */
    private void initThreadPoolParams() {
        //容器中获取线程池配置信息
        ThreadPoolConfig config = SpringUtil.getBean(ThreadPoolConfig.class);
        if (isNull(config)) {
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug("-------------->>> ThreadPoolConfig={}", config);
        }
        if (isNotNull(config.getCorePoolSize())) {
            this.corePoolSize = config.getCorePoolSize();
        }
        if (isNotNull(config.getMaximumPoolSize())) {
            this.maximumPoolSize = config.getMaximumPoolSize();
        }
        if (isNotNull(config.getKeepAliveTime())) {
            this.keepAliveTime = config.getKeepAliveTime();
        }
        if (isNotNull(config.getWorkQueue())) {
            this.workQueue = config.getWorkQueue();
        }
        if (isNotEmpty(config.getThreadNamePrefix())) {
            this.threadNamePrefix = config.getThreadNamePrefix();
        }
    }

    /**
     * 初始化阻塞队列
     */
    private void initBlockingQueue() {
        this.blockingQueue = this.workQueue > 0 ? new ArrayBlockingQueue<>(this.workQueue) : new LinkedBlockingQueue<>();
    }

    /**
     * 线程池创建工厂，这里会设置自定义线程的前缀名
     *
     * @return ThreadFactory
     */
    private ThreadFactory buildThreadFactory() {
        return new CustomizableThreadFactory(this.threadNamePrefix);
    }

    /**
     * 构建线程的阻塞策略
     *
     * @return RejectedExecutionHandler
     */
    private RejectedExecutionHandler buildRejectedExecutionHandler() {
        return new ThreadPoolExecutor.AbortPolicy();
    }
}
