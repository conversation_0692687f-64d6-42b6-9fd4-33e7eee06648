package cn.need.framework.common.support.thread;

import cn.need.framework.common.core.exception.ExceptionUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Slf4j
public class ThreadUtil {

    /**
     * 私有化构造函数
     */
    private ThreadUtil() {
        throw new AssertionError("No " + getClass().getName() + " instances for you!");
    }

    /**
     * 接收一个异步多任务，并收集所有异步任务的结果，返回结果集
     *
     * @param futures 多线程异步任务
     * @param <T>     任务数据集泛型
     * @return List<T> 任务数据集
     * @deprecated 该方法不会转换异常，推荐使用{@link ThreadUtil#supplyAllOf(List)}
     */
    @Deprecated
    public static <T> List<T> futureAllOf(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allFuture.thenApply(e -> futures.stream().map(CompletableFuture::join).filter(ObjectUtil::isNotNull).collect(Collectors.toList())).join();
    }

    /**
     * 接收一个异步多任务集合，并收集所有异步任务的结果，返回结果集，会将异常转换为本地异常
     *
     * @param futures 多线程异步任务
     * @param <T>     任务数据集泛型
     * @return List<T> 任务数据集
     */
    public static <T> List<T> supplyAllOf(List<CompletableFuture<T>> futures) {
        try {
            CompletableFuture<Void> allFuture = CompletableFuture.allOf(futures.stream().filter(ObjectUtil::isNotNull).toArray(CompletableFuture[]::new));
            return allFuture.thenApply(e -> futures.stream().map(CompletableFuture::join).filter(ObjectUtil::isNotNull).collect(Collectors.toList())).join();
        } catch (Exception e) {
            log.error("收集有返回结果的异步任务异常：{}", e.getMessage(), e);
            throw ExceptionUtil.wrapUnchecked(e.getCause());
        }
    }

    /**
     * 接收一个异步多任务集合，并收集所有异步任务的结果，不返回结果集，会将异常转换为本地异常
     *
     * @param futures 多线程异步任务
     */
    public static void runAllOf(List<CompletableFuture<Void>> futures) {
        try {
            CompletableFuture.allOf(futures.stream().filter(ObjectUtil::isNotNull).toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            log.error("收集无返回结果的异步任务异常：{}", e.getMessage(), e);
            throw ExceptionUtil.wrapUnchecked(e.getCause());
        }
    }

    public static CompletableFuture<Void> buildRunFuture(Consumer<Void> consumer) {
        return buildRunFuture(consumer, globalThreadPool());
    }

    public static CompletableFuture<Void> buildRunFuture(Consumer<Void> consumer, ExecutorService executorService) {
        return buildRunFuture(consumer, executorService, RequestContextHolder.getRequestAttributes());
    }

    public static CompletableFuture<Void> buildRunFuture(Consumer<Void> consumer, RequestAttributes attributes) {
        return buildRunFuture(consumer, globalThreadPool(), attributes);
    }

    public static CompletableFuture<Void> buildRunFuture(Consumer<Void> consumer, ExecutorService executorService, RequestAttributes attributes) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return CompletableFuture.runAsync(() -> {
            if (ObjectUtil.isNotEmpty(contextMap)) {
                MDC.setContextMap(contextMap);
            }

            if (ObjectUtil.isNotNull(attributes)) {
                RequestContextHolder.setRequestAttributes(attributes);
            }

            consumer.accept(null);
        }, TtlExecutors.getTtlExecutorService(executorService));
    }

    public static <T> CompletableFuture<T> buildSupplyFuture(Supplier<T> supplier) {
        return buildSupplyFuture(supplier, globalThreadPool());
    }

    public static <T> CompletableFuture<T> buildSupplyFuture(Supplier<T> supplier, ExecutorService executorService) {
        return buildSupplyFuture(supplier, executorService, RequestContextHolder.getRequestAttributes());
    }

    public static <T> CompletableFuture<T> buildSupplyFuture(Supplier<T> supplier, RequestAttributes attributes) {
        return buildSupplyFuture(supplier, globalThreadPool(), attributes);
    }

    public static <T> CompletableFuture<T> buildSupplyFuture(Supplier<T> supplier, ExecutorService executorService, RequestAttributes attributes) {
        return CompletableFuture.supplyAsync(() -> {
            if (ObjectUtil.isNotNull(attributes)) {
                RequestContextHolder.setRequestAttributes(attributes);
            }

            return supplier.get();
        }, TtlExecutors.getTtlExecutorService(executorService));
    }

    /**
     * 多线程事务处理，适用于需要在多线程环境下执行多个数据库操作，并且这些操作要么全部成功，要么全部失败的场景
     *
     * @param platformTransactionManager Spring的事务管理器，用于控制事务的提交和回滚
     * @param taskList                   一个可变数量的Runnable任务，每个任务代表一个数据库操作
     * @return 如果所有任务都成功完成，则返回true；否则返回false
     */
    public static boolean executeTransaction(PlatformTransactionManager platformTransactionManager, List<Runnable> taskList) {
        if (ObjectUtil.isEmpty(taskList)) {
            throw new IllegalArgumentException("taskList is empty");
        }
        // 任务数量
        int taskSize = taskList.size();
        // 任务成功数量计数器
        AtomicInteger taskSuccessAccount = new AtomicInteger(0);

        List<Future<?>> taskFutureList = new ArrayList<>(taskSize);

        // 循环屏障，用于让多线程事务一起提交 || 一起回滚
        CyclicBarrier cyclicBarrier = new CyclicBarrier(taskSize);
        int i = 1;
        // 定义了一个线程池，线程池核心线程数大小和任务大小必须一致，因为里面的任务都必须同时去执行，否则会死锁
        ExecutorService executorService = Executors.newFixedThreadPool(taskSize);
        try {
            //使用线程池执行循环处理任务，每个任何会交给线程池中的一个线程执行
            for (Runnable task : taskList) {
                final int taskIndex = i;
                Future<?> future = executorService.submit(() -> {
                    TransactionStatus transactionStatus = null;
                    try {
                        // 使用spring编程式事务，开启事务
                        transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionAttribute());
                        // 执行任务
                        task.run();
                        // 成功数量+1
                        taskSuccessAccount.incrementAndGet();
                        log.debug("task：{} 等待提交事务", taskIndex);
                    } catch (Throwable e) {
                        log.error("task：{}，执行异常，异常原因：{}", taskIndex, e.getMessage());
                    } finally {
                        // 走到这里，会阻塞，直到当前线程池中所有的任务都执行到这个位置后，才会被唤醒，继续向下走
                        try {
                            cyclicBarrier.await();
                        } catch (Exception e) {
                            log.error("cyclicBarrier.await error:{}", e.getMessage(), e);
                        }
                    }
                    if (transactionStatus != null) {
                        // 如果所有任务都成功（successAccount的值等于任务总数），则一起提交事务，如果有任何任务失败，则一起回滚事务
                        if (taskSuccessAccount.get() == taskSize) {
                            // 成功，提交事务
                            log.debug("task：{} 提交事务", taskIndex);
                            platformTransactionManager.commit(transactionStatus);
                        } else {
                            //失败，回滚事务
                            log.debug("task：{} 回滚事务", taskIndex);
                            platformTransactionManager.rollback(transactionStatus);
                        }
                    }
                });
                taskFutureList.add(future);
                i++;
            }
            for (Future<?> future : taskFutureList) {
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("future.get error:{}", e.getMessage(), e);
                }
            }
        } finally {
            //关闭线程池
            executorService.shutdown();
        }
        //如果所有任务都成功完成，则返回true；否则返回false
        return taskSuccessAccount.get() == taskSize;
    }

    /**
     * 获取全局定义的单例线程池，可以通过在spring-boot来配置线程池大小、任务队列数、线程池名称等
     *
     * @return ExecutorService 线程池
     */
    public static ExecutorService globalThreadPool() {
        return GlobalThreadPool.instance().getThreadPool();
    }

    /**
     * 获取单线程的线程池，通过{@link Executors#newSingleThreadExecutor()}获取
     *
     * @return ExecutorService 线程池
     */
    @SuppressWarnings("AlibabaThreadPoolCreation")
    public static ExecutorService singleThreadPool() {
        return Executors.newSingleThreadExecutor();
    }

    /**
     * 获取缓存线程池，通过{@link Executors#newCachedThreadPool()}获取
     *
     * @return ExecutorService 线程池
     */
    @SuppressWarnings("AlibabaThreadPoolCreation")
    public static ExecutorService cachedThreadPool() {
        return Executors.newCachedThreadPool();
    }

    /**
     * 获取固定大小线程的线程池，通过{@link Executors#newFixedThreadPool(int)}获取
     *
     * @param poolSize 线程池固定大小
     * @return ExecutorService 线程池
     */
    @SuppressWarnings("AlibabaThreadPoolCreation")
    public static ExecutorService fixedThreadPool(int poolSize) {
        return Executors.newFixedThreadPool(poolSize);
    }

}
