package cn.need.framework.common.mybatis.condition;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GeneralCondition implements Serializable {

    @Serial
    private static final long serialVersionUID = 7425858528546839625L;
    /**
     * 通用编号条件，适用classCode、teaCode、stuCode
     */
    @Condition(value = Keyword.LIKE, fields = {"classCode", "teaCode", "stuCode"})
    private String code;

    /**
     * 通用名称条件，适用teaName、stuName
     */
    @Condition(value = Keyword.LIKE, fields = {"teaName", "stuName"})
    private String name;
}
