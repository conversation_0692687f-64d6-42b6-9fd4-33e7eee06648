package cn.need.framework.common.mybatis.service.impl;

import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.entity.ExamSubject;
import cn.need.framework.common.mybatis.mapper.ExamSubjectMapper;
import cn.need.framework.common.mybatis.service.ExamSubjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Service
public class ExamSubjectServiceImpl extends SuperServiceImpl<ExamSubjectMapper, ExamSubject> implements ExamSubjectService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteAll() {
        return mapper.deleteAll();
    }
}
