package cn.need.framework.common.mybatis.entity;

import cn.need.framework.common.mybatis.model.TenantModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 考试科目信息
 * </p>
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName("ud_exam_subject")
@Data
public class ExamSubject extends TenantModel {

    @Serial
    private static final long serialVersionUID = -1650551427858188871L;

    /**
     * 科目编号
     */
    @TableField("subject_code")
    private String subjectCode;
    /**
     * 科目名称
     */
    @TableField("subject_name")
    private String subjectName;
}
