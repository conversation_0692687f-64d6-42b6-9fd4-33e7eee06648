package cn.need.framework.common.mybatis.condition;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeacherCondition {

    /**
     * 教师编号条件
     */
    @Condition(value = Keyword.LIKE, fields = {"teaCode"})
    private String code;

    /**
     * 教师姓名条件
     */
    @Condition(value = Keyword.LIKE, fields = {"teaName"})
    private String name;

    /**
     * 教师性别条件
     */
    @Condition(fields = {"teaSex"})
    private String sex;

}
