package cn.need.framework.common.mybatis.cache;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.ArrayUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.mybatis.info.UniqueField;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 唯一校验字段的缓存类
 *
 * <AUTHOR>
 */
@Slf4j
public class UniqueFieldCache {

    /**
     * 用来缓存唯一校验的字段集合
     */
    private static Map<String, List<UniqueField>> cache;

    /**
     * 单例初始化对象
     */
    private static UniqueFieldCache instance;

    /**
     * 私有化构造函数
     */
    private UniqueFieldCache() {

    }

    /**
     * 使用单例构造缓存类
     *
     * @return ConditionCache
     */
    public static UniqueFieldCache getInstance() {
        if (instance == null) {
            instance = new UniqueFieldCache();
            cache = Maps.concurrentMap();
        }
        return instance;
    }

    /**
     * 根据实体类，获取缓存的唯一校验字段集合
     *
     * @param bean 条件对象java类型
     * @return List<UniqueField> 唯一校验的字段集合
     */
    public List<UniqueField> get(Object bean) {
        return ObjectUtil.isNull(bean) ? Lists.arrayList() : get(bean.getClass());
    }

    /**
     * 根据实体类类java类型，获取缓存的唯一校验字段集合
     *
     * @param beanClass 条件对象java类型
     * @return List<UniqueField> 唯一校验的字段集合
     */
    public List<UniqueField> get(Class<?> beanClass) {
        if (!cache.containsKey(getKey(beanClass))) {
            if (log.isDebugEnabled()) {
                log.debug(" ==> {}缓存获取条件对象getter方法集合为空，开始重新构造条件对象getter方法集合", UniqueFieldCache.class);
            }
            put(beanClass, buildUniqueFields(beanClass));
        }
        return cache.get(getKey(beanClass));
    }

    /**
     * 缓存中设置唯一校验的字段集合
     *
     * @param beanClass 条件对象类类型
     * @param fields    唯一校验的字段集合
     */
    public void put(Class<?> beanClass, List<UniqueField> fields) {
        cache.put(getKey(beanClass), fields);
    }

    /**
     * 缓存中删除唯一校验的字段集合
     *
     * @param beanClass 条件对象类型
     */
    public void remove(Class<?> beanClass) {
        cache.remove(getKey(beanClass));
    }

    /**
     * 构建key名称
     *
     * @param beanClass 条件对象类型
     * @return String 缓存key名称
     */
    private String getKey(Class<?> beanClass) {
        return beanClass.getName();
    }

    /**
     * 构建唯一校验字段集合
     */
    private List<UniqueField> buildUniqueFields(Class<?> beanClass) {
        return BeanUtil.getFields(beanClass).stream().map(this::buildUniqueField).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    /**
     * 构建唯一校验字段
     */
    private UniqueField buildUniqueField(Field field) {
        Unique unique = field.getAnnotation(Unique.class);
        if (ObjectUtil.isNull(unique)) {
            return null;
        }
        UniqueField uniqueField = new UniqueField();
        uniqueField.setProperty(field.getName());
        uniqueField.setFields(combinedProperty(unique.combined(), uniqueField.getProperty()));
        uniqueField.setMessage(unique.message());
        return uniqueField;
    }

    /**
     * 组合属性
     */
    private String[] combinedProperty(String[] combined, String property) {
        return ArrayUtil.contains(combined, property) ? combined : ArrayUtil.insert(combined, 0, property);
    }
}
