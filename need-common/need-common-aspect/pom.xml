<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.need.framework</groupId>
        <artifactId>need-parent</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <groupId>cn.need.framework.common</groupId>
    <artifactId>need-common-aspect</artifactId>
    <packaging>jar</packaging>
    <name>need-common-aspect</name>
    <description>Uneed common util for aspect</description>

    <!-- 版本参数 -->
    <properties>

    </properties>

    <!-- 依赖配置 -->
    <dependencies>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>

    </build>

    <developers>
        <developer>
            <name>diablo</name>
            <email><EMAIL></email>
        </developer>
    </developers>
</project>
