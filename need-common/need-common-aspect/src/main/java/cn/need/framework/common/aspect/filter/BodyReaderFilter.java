package cn.need.framework.common.aspect.filter;

import cn.need.framework.common.aspect.constant.AspectConstant;
import cn.need.framework.common.aspect.wrapper.BodyReaderRequestWrapper;
import cn.need.framework.common.core.lang.ObjectUtil;
import org.springframework.http.HttpMethod;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class BodyReaderFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        //只有request对象是httpRequest，并且requestMethod是POST，contentType是"application/json"的情况下，才将request转换为自定义body转换request对象
        if (request instanceof HttpServletRequest && isPostAndApplicationJson((HttpServletRequest) request)) {
            chain.doFilter(new BodyReaderRequestWrapper((HttpServletRequest) request), response);
        } else {
            chain.doFilter(request, response);
        }
    }

    private boolean isPostAndApplicationJson(HttpServletRequest request) {
        return isPost(request) && isApplicationJson(request);
    }

    private boolean isApplicationJson(HttpServletRequest request) {
        String contentType = request.getContentType();
        return ObjectUtil.equals(contentType, AspectConstant.CONTENT_TYPE_JSON)
                || ObjectUtil.equals(contentType, AspectConstant.CONTENT_TYPE_JSON_UTF8);
    }

    private boolean isPost(HttpServletRequest request) {
        return ObjectUtil.equals(request.getMethod(), HttpMethod.POST.name());
    }
}
