package cn.need.framework.common.oss.template;

import cn.need.framework.common.oss.model.AttachFile;
import cn.need.framework.common.oss.model.OssFile;
import cn.need.framework.common.oss.props.OssProperties;
import cn.need.framework.common.oss.rule.OssRule;
import lombok.RequiredArgsConstructor;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.ObjectIdentifier;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.List;

/**
 * Description: Amazon s3 ossTemplate impl
 *
 * <AUTHOR>
 * @since 2025/5/14
 *
 * <p>
 * </p>
 *
 */
@RequiredArgsConstructor
public class AmazonS3Template implements OssTemplate {

    private final S3Client s3Client;

    private final OssProperties ossProperties;

    private final OssRule ossRule;

    @Override
    public void makeBucket(String bucketName) {
        s3Client.createBucket(builder -> builder.bucket(bucketName));
    }

    @Override
    public void removeBucket(String bucketName) {
        s3Client.deleteBucket(builder -> builder.bucket(bucketName));
    }

    @Override
    public boolean bucketExists(String bucketName) {
        return s3Client.listBuckets().buckets().stream().anyMatch(bucket -> bucket.name().equals(bucketName));
    }

    @Override
    public void copyFile(String bucketName, String fileName, String destBucketName) {
        s3Client.copyObject(builder ->
                builder.sourceBucket(bucketName).sourceKey(fileName).destinationBucket(destBucketName).destinationKey(fileName).build());
    }

    @Override
    public void copyFile(String bucketName, String fileName, String destBucketName, String destFileName) {
        s3Client.copyObject(builder ->
                builder.sourceBucket(bucketName).sourceKey(fileName).destinationBucket(destBucketName).destinationKey(destFileName).build());
    }

    @Override
    public OssFile statFile(String fileName) {
        return statFile(ossProperties.getBucketName(), fileName);
    }

    @Override
    public OssFile statFile(String bucketName, String fileName) {
        GetObjectResponse response = s3Client.getObject(builder -> builder.bucket(ossProperties.getBucketName()).key(fileName)).response();
        URL url = s3Client.utilities().getUrl(builder -> builder.bucket(ossProperties.getBucketName()).key(fileName));
        OssFile ossFile = new OssFile();
        ossFile.setContentType(response.contentType());
        ossFile.setHash(response.eTag());
        ossFile.setLength(response.contentLength());
        ossFile.setPutTime(Date.from(response.lastModified()));
        ossFile.setName(fileName);
        ossFile.setLink(url.toString());
        return ossFile;
    }

    @Override
    public String filePath(String fileName) {
        return filePath(ossProperties.getBucketName(), fileName);
    }

    @Override
    public String filePath(String bucketName, String fileName) {
        return s3Client.utilities().getUrl(builder -> builder.bucket(bucketName).key(fileName))
                .getPath();
    }

    @Override
    public String fileLink(String fileName) {
        return fileLink(ossProperties.getBucketName(), fileName);
    }

    @Override
    public String fileLink(String bucketName, String fileName) {
        return s3Client.utilities().getUrl(builder -> builder.bucket(bucketName).key(fileName)).toString();
    }

    @Override
    public AttachFile putFile(MultipartFile file) {
        return putFile(ossProperties.getBucketName(), file.getOriginalFilename(), file);
    }

    @Override
    public AttachFile putFile(String fileName, MultipartFile file) {
        return putFile(ossProperties.getBucketName(), fileName, file);
    }

    @Override
    public AttachFile putFile(String bucketName, String fileName, MultipartFile file) {
        try {
            return putFile(bucketName, "", fileName, file.getOriginalFilename(), file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public AttachFile putFile(String fileName, InputStream stream) {
        return putFile(ossProperties.getBucketName(), fileName, stream);
    }

    @Override
    public AttachFile putFile(String bucketName, String fileName, InputStream stream) {
        return putFile(ossProperties.getBucketName(), "", fileName, fileName, stream);
    }

    private AttachFile putFile(String bucketName, String moduleName, String fileName, String originalFileName, InputStream stream) {

        String fullFileName = ossRule.fileName(moduleName, fileName);

        try {
            s3Client.putObject(PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fullFileName)
                    .build(), RequestBody.fromInputStream(stream, stream.available()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        URL url = s3Client.utilities().getUrl(builder -> builder.bucket(bucketName).key(fullFileName));

        AttachFile attachFile = new AttachFile();
        attachFile.setOriginalName(originalFileName);
        attachFile.setName(fileName);
        attachFile.setDomain(url.getHost());
        attachFile.setLink(url.toString());
        attachFile.setModule(moduleName);
        return attachFile;
    }

    @Override
    public AttachFile putModelFile(String fileName, String module, InputStream stream) {
        return putFile(ossProperties.getBucketName(), module, fileName, fileName, stream);
    }

    @Override
    public void removeFile(String fileName) {
        removeFile(ossProperties.getBucketName(), fileName);
    }

    @Override
    public void removeFile(String bucketName, String fileName) {
        s3Client.deleteObject(builder -> builder.bucket(bucketName).key(fileName));
    }

    @Override
    public void removeFiles(List<String> fileNames) {
        removeFiles(ossProperties.getBucketName(), fileNames);
    }

    @Override
    public void removeFiles(String bucketName, List<String> fileNames) {
        s3Client.deleteObjects(builder -> builder.bucket(bucketName).delete(deleteBuilder -> deleteBuilder.objects(
                fileNames.stream().map(fileName -> ObjectIdentifier.builder().key(fileName).build()).toList()
        )));
    }
}
