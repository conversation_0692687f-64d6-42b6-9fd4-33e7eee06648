package cn.need.framework.common.annotation.enums;

import lombok.Getter;

import java.util.List;

/**
 * 字典的属性常量
 *
 * <AUTHOR>
 */
@Getter
public enum DictProperty {

    /**
     * 字典编号
     */
    CODE("code", "字典编号", String.class),

    /**
     * 字典名称
     */
    NAME("name", "字典名称", String.class),

    /**
     * 字典值
     */
    VALUE("value", "字典值", String.class),

    /**
     * 字典描述
     */
    DESCRIPTION("description", "字典描述", String.class),

    /**
     * 字典排序值
     */
    SORTING("sorting", "字典排序值", Integer.class),

    /**
     * 字典状态
     */
    ENABLE("enable", "字典状态", Boolean.class),

    /**
     * 父级字典编号
     */
    PARENT("parent", "父级字典编号", String.class),

    /**
     * 字典根级编号
     */
    ROOT("root", "字典根级编号", String.class),

    /**
     * 字典子集编号集合
     */
    SUBSET("subset", "字典子集编号集合", List.class);

    private final String code;
    private final String description;
    private final Class<?> type;

    DictProperty(String code, String description, Class<?> type) {
        this.code = code;
        this.description = description;
        this.type = type;
    }

}
