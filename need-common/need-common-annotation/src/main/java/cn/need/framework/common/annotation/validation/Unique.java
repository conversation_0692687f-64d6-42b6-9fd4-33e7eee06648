package cn.need.framework.common.annotation.validation;

import java.lang.annotation.*;

/**
 * 唯一校验标记
 * <p>
 * 注：注解在实体类型，调用service的insert、update、updateForAll、insertOrUpdate、validateUnique(T entity)方法时，会自动校验数据唯一性
 *
 * <AUTHOR>
 */
@Documented
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Unique {

    /**
     * 提示信息，效果同{@link this#message()}，message()值为空的情况下取value()的值
     * <p>
     * field data values [{}] are duplicated
     */
    String value() default "";


    /**
     * 组合校验的字段集合
     */
    String[] combined() default {};

    /**
     * 提示信息
     */
    String message() default "";
}
