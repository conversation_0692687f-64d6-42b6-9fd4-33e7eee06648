package cn.need.framework.common.screw;


import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.File;
import java.io.IOException;
import java.util.Properties;

/**
 * 数据库文档生成工具
 *
 * <AUTHOR>
 **/
@Slf4j
public abstract class BaseGenerator {

    @Setter
    protected String configPath;

    protected Properties props;

    protected String output = "D:\\data";

    protected void initProperties() throws IOException {
        Resource resource = getResource(configPath);
        props = PropertiesLoaderUtils.loadProperties(resource);
        //需要对windows路径进行转换
        output = props.getProperty("gc.output", output).replaceAll("\\\\", "/");
    }

    private Resource getResource(String configPath) {
        if (ObjectUtil.isEmpty(configPath)) {
            configPath = "config/generator.properties";
        }
        File file = new File(configPath);
        if (file.exists()) {
            return new FileSystemResource(file);
        }
        return new ClassPathResource(configPath);
    }

    /**
     * 具体业务执行器
     */
    protected abstract void execute();

    public void generate() {
        try {
            //初始化配置文件的配置
            initProperties();
            execute();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
