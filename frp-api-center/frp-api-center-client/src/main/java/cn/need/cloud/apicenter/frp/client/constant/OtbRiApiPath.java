package cn.need.cloud.apicenter.frp.client.constant;

/**
 * 库位库存路径
 *
 * <AUTHOR>
 */
public class OtbRiApiPath {
    /**
     * 前缀
     */
    public static final String PREFIX = "/api/open/otb-ri";

    /**
     * 创建或更新
     */
    public static final String CREATE_OR_UPDATE = "/createOrUpdate";

    /**
     * 删除
     */
    public static final String DELETE = "/delete";

    /**
     * 详情
     */
    public static final String DETAIL = "/detail";

    /**
     * 审批
     */
    public static final String AUDIT = "/audit";

    /**
     * 列表
     */
    public static final String LIST = "/list";
}
