package cn.need.cloud.apicenter.frp.client.constant;

/**
 * 库位库存路径
 *
 * <AUTHOR>
 */
public class BinLocationApiPath {
    /**
     * 前缀
     */
    public static final String PREFIX = "/api/open/exBinLocation";

    /**
     * 重置库位
     */
    public static final String RESET_BIN_LOCATION_EMPTY = "/resetBinLocationEmpty";

    /**
     * 创建或更新
     */
    public static final String CREATE_OR_UPDATE = "/createOrUpdate";

    /**
     * 删除
     */
    public static final String DELETE = "/delete";

    /**
     * 详情
     */
    public static final String DETAIL = "/detail";

    /**
     * 列表
     */
    public static final String LIST = "/list";
}
