<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>cn.need.framework</groupId>
        <artifactId>need-parent</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>cn.need.cloud</groupId>
    <artifactId>frp-api-center</artifactId>
    <version>frp-dev.41-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>frp-api-center-boot</module>
        <module>frp-api-center-client</module>
        <module>frp-api-center-server</module>
    </modules>

    <!--分为打client和service两种打包反应堆profile -->
    <profiles>
        <profile>
            <id>build-client</id>
            <modules>
                <module>frp-api-center-client</module>
            </modules>
        </profile>
        <profile>
            <id>build-service</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <modules>
                <module>frp-api-center-client</module>
                <module>frp-api-center-server</module>
                <module>frp-api-center-boot</module>
            </modules>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>

            <!-- install时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>