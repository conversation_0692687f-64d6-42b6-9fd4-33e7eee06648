package cn.need.cloud.apicenter.frp.service.otb;

import cn.need.cloud.biz.client.api.path.OtbRequestPath;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestAuditReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbRequestCreateOrUpdateWithWarehouseReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbRequestQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbRequestPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbRequestRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * otb入库请求管理 Feign 客户端
 * <p>
 * 该接口提供otbRequest相关的远程调用功能，包括入库请求的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@FeignClient(value = "${serviceId.frp-business}", contextId = "OtbRequestClient")
public interface OtbRequestService {

    /**
     * 新增编辑入库请求
     * <p>
     * 新增编辑入库请求
     * </p>
     *
     * @param reqDTO 库位数据传输对象，包含库位的详细信息
     * @return Result<Boolean> 包含重置库位的结果
     */
    @PostMapping(value = OtbRequestPath.CREATE_OR_UPDATE)
    Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(@RequestBody OtbRequestCreateOrUpdateWithWarehouseReqDTO reqDTO);

    /**
     * 获取入库详情
     * <p>
     * 获取入库详情
     * </p>
     *
     * @param query 请求数据传输对象，包含请求的详细信息
     * @return Result<OtbRequestRespDTO> 包含请求详情的结果
     */
    @PostMapping(value = OtbRequestPath.DETAIL)
    Result<OtbRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query);

    /**
     * 请求列表
     * <p>
     * 请求列表
     * </p>
     *
     * @param search 请求单数据传输对象，包含库位的详细信息
     * @return Result<PageData<OtbRequestPageRespDTO>> 包含入库列表的结果
     */
    @PostMapping(value = OtbRequestPath.LIST)
    Result<PageData<OtbRequestPageRespDTO>> list(@RequestBody PageSearch<OtbRequestQueryReqDTO> search);

    /**
     * 删除请求
     * <p>
     * 根据提供的请求信息删除请求记录
     * </p>
     *
     * @param baseDeleteReqDTO 包含要删除请求信息的数据传输对象
     * @return Result<Integer> 包含删除影响行数的结果对象
     */
    @PostMapping(value = OtbRequestPath.DELETE)
    Result<Integer> remove(@RequestBody BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO);

    /**
     * 取消请求
     * <p>
     * 根据提供的请求信息取消请求记录
     * </p>
     *
     * @param baseDeleteReqDTO 包含要取消请求信息的数据传输对象
     * @return Result<Integer> 包含取消影响行数的结果对象
     */
    @PostMapping(value = OtbRequestPath.CANCEL)
    Result<Integer> cancel(@RequestBody BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO);

    /**
     * 审批请求
     * <p>
     * 根据提供的请求信息审批请求记录
     * </p>
     *
     * @param baseAuditReqDTO 包含要审批请求信息的数据传输对象
     * @return Result<Integer> 包含审批影响行数的结果对象
     */
    @PostMapping(value = OtbRequestPath.AUDIT)
    Result<Integer> audit(@RequestBody BaseRequestAuditReqDTO baseAuditReqDTO);
}
