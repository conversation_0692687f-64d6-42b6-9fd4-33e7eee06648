package cn.need.cloud.apicenter.frp.service.impl;

import cn.need.cloud.apicenter.frp.mapper.ThirdRecordResponseMapper;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordResponse;
import cn.need.cloud.apicenter.frp.service.ThirdRecordResponseService;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 第三方接口调用记录的响应信息 服务实现
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Service
public class ThirdRecordResponseServiceImpl extends SuperServiceImpl<ThirdRecordResponseMapper, ThirdRecordResponse> implements ThirdRecordResponseService {

}
