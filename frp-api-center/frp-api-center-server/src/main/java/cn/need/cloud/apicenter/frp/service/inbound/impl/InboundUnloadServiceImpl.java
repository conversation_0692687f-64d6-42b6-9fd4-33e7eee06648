package cn.need.cloud.apicenter.frp.service.inbound.impl;

import cn.need.cloud.apicenter.frp.service.inbound.InboundUnloadService;
import cn.need.cloud.biz.client.api.inbound.InboundUnloadClient;
import cn.need.cloud.biz.client.dto.req.inbound.InboundUnloadQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundUnloadRespDTO;
import cn.need.framework.common.support.api.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 入库请求管理 Feign 客户端
 * <AUTHOR>
 */
@Service
public class InboundUnloadServiceImpl implements InboundUnloadService {
    @Resource
    private InboundUnloadClient inboundUnloadClient;

    @Override
    public Result<List<InboundUnloadRespDTO>> listByRequest(InboundUnloadQueryReqDTO reqDTO) {
        return inboundUnloadClient.listByRequest(reqDTO);
    }
}
