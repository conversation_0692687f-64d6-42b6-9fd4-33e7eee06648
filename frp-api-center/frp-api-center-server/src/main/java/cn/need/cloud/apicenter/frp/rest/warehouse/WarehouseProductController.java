package cn.need.cloud.apicenter.frp.rest.warehouse;

import cn.need.cloud.apicenter.frp.client.constant.WarehouseProductApiPath;
import cn.need.cloud.apicenter.frp.service.warehouse.WarehouseProductRestService;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseProductWithTransactionPartnerReqDTO;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 仓库产品控制器
 * </p>
 *
 * <p>
 * 该控制器提供了仓库产品相关的API接口，用于处理仓库产品的各种操作请求。
 * 主要功能包括标记产品为Slap and Go等操作，支持外部系统通过REST API进行调用。
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@RestController
@Tag(name = "外部接口-warehouse product api")
@Slf4j
@RequestMapping(WarehouseProductApiPath.PREFIX)
public class WarehouseProductController extends AbstractController {

    /**
     * 仓库产品REST服务
     * <p>
     * 提供仓库产品相关的业务逻辑实现，包括产品状态的标记和管理。
     * </p>
     */
    @Resource
    private WarehouseProductRestService warehouseProductRestService;

    /**
     * 将产品标记为Slap and Go状态
     * <p>
     * 该方法将指定的产品标记为Slap and Go状态，这将影响产品在仓库中的处理方式。
     * Slap and Go产品通常会跳过某些常规处理流程，直接进入发货环节。
     * </p>
     *
     * @param param 包含仓库产品和交易伙伴信息的请求参数
     * @return 操作结果，成功返回true，失败返回false
     */
    @Operation(summary = "标记产品为Slap and Go", description = "接收产品的传参对象，将指定产品标记为Slap and Go状态")
    @PostMapping(value = WarehouseProductApiPath.MARK_PRODUCT_AS_SLAP_AND_GO)
    public Result<Boolean> markProductAsSlapAndGo(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BaseWarehouseProductWithTransactionPartnerReqDTO param) {
        log.info("markProductAsSlapAndGo  reqDTO: {}", param);

        // 返回结果
        return Result.ok(warehouseProductRestService.markProductAsSlapAndGo(param));
    }

}
