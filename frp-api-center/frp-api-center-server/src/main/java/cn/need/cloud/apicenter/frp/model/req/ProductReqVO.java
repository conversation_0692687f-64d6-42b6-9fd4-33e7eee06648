package cn.need.cloud.apicenter.frp.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品 vo对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@Schema(description = "产品 vo对象")
public class ProductReqVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * 参考编码
     */
    @Schema(description = "参考编码 refNum 和  supplierSku 两个传一个")
    private String refNum;

    /**
     * 供应商编码
     */
    @Schema(description = "参考编码 refNum 和  supplierSku 两个传一个")
    private String supplierSku;

    @JsonIgnore
    private Long productId;
}
