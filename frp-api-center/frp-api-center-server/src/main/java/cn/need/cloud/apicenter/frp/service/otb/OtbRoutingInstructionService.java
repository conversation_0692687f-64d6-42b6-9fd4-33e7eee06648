package cn.need.cloud.apicenter.frp.service.otb;

import cn.need.cloud.biz.client.dto.req.otb.*;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;

/**
 * 入库请求管理 服务类
 * <p>
 * 该接口提供库位库存相关的远程调用功能，包括库位库存的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
public interface OtbRoutingInstructionService {

    /**
     * ri新增或编辑
     * <p>
     * ri新增或编辑
     * </p>
     *
     * @param reqDTO ri数据传输对象，包含ri的详细信息
     * @return Result<Boolean> 包含ri的结果
     */
    Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(OtbRoutingInstructionCreateOrUpdateReqDTO reqDTO);

    /**
     * 获取ri详情
     * <p>
     * 获取ri详情
     * </p>
     *
     * @param query ri数据传输对象，包含ri的详细信息
     * @return Result<BinLocationRespDTO> 包含ri的结果
     */
    Result<OtbRIRespDTO> detail( RIQueryReqDTO query);

    /**
     * 库位列表
     * <p>
     * 库位列表
     * </p>
     *
     * @param search 库位数据传输对象，包含库位的详细信息
     * @return Result<Boolean> 包含重置库位的结果
     */
    Result<PageData<OtbRoutingInstructionPageRespDTO>> list(PageSearch<OtbRoutingInstructionQueryReqDTO> search);

    /**
     * ri审批
     * @param query ri数据传输对象，包含ri的详细信息
     * @return Result<Boolean> 包含ri的结果
     */
    Result<Boolean> audit(RIQueryReqDTO query);
}
