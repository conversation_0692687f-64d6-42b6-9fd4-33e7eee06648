package cn.need.cloud.apicenter.frp.service.inbound.impl;

import cn.need.cloud.apicenter.frp.service.inbound.InboundRequestService;
import cn.need.cloud.biz.client.api.inbound.InboundRequestClient;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.inbound.*;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundRequestRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 入库请求管理 服务类
 * <p>
 * 该接口提供库位库存相关的远程调用功能，包括库位库存的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@Service
public class InboundRequestServiceImpl implements InboundRequestService {
    @Resource
    private InboundRequestClient inboundRequestClient;

    @Override
    public Result<Boolean> createOrUpdate(InboundRequestCreateOrUpdateWithWarehouseReqDTO reqDTO){

        //返回结果
        return inboundRequestClient.createOrUpdate(reqDTO);
    }

    @Override
    public Result<Integer> remove( BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO){

        //返回结果
        return inboundRequestClient.remove(baseDeleteReqDTO);
    }

    @Override
    public Result<Integer> cancel(BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO){

        //返回结果
        return inboundRequestClient.cancel(baseDeleteReqDTO);
    }

    @Override
    public Result<Boolean> audit(InboundRequestAuditWithWarehouseReqDTO inboundRequestAuditReqDTO){

        //返回结果
        return inboundRequestClient.audit(inboundRequestAuditReqDTO);
    }

    @Override
    public Result<InboundRequestRespDTO> detail(BaseDetailQueryReqDTO reqDTO){

        //返回结果
        return inboundRequestClient.detail(reqDTO);
    }

    @Override
    public Result<PageData<InboundRequestPageReqDTO>> list(PageSearch<InboundRequestQueryReqDTO> search){

        //返回结果
        return inboundRequestClient.list(search);
    }


}
