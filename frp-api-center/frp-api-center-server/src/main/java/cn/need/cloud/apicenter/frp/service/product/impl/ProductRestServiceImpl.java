package cn.need.cloud.apicenter.frp.service.product.impl;

import cn.need.cloud.apicenter.frp.service.product.ProductRestService;
import cn.need.cloud.biz.client.api.product.ProductClient;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.product.*;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithSupplierSkuRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductVersionRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductPageRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.util.ApiUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 产品服务实现类
 * </p>
 *
 * <p>
 * 该类实现了产品相关的REST服务，为外部系统提供产品的增删改查功能。
 * 通过调用ProductClient实现与业务层的交互，封装了底层的复杂性。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductRestServiceImpl implements ProductRestService {

    /**
     * 产品客户端
     * <p>
     * 用于调用产品服务的远程接口，实现与产品业务层的交互。
     * 通过Feign实现微服务间的通信。
     * </p>
     */
    @Resource
    private ProductClient productClient;

    /**
     * 根据参数新增产品
     * <p>
     * 调用产品客户端的insert方法创建新产品，并返回新创建产品的ID。
     * </p>
     *
     * @param createParam 产品创建参数，包含产品的基本信息
     * @return 新创建产品的ID
     */
    @Override
    public Long insertByParam(ProductCreateReqDTO createParam) {
        return ApiUtil.getResultData(productClient.insert(createParam));
    }

    /**
     * 创建或更新产品
     * <p>
     * 调用产品客户端的createOrUpdate方法，如果产品已存在则更新，否则创建新产品。
     * </p>
     *
     * @param reqDTO 产品创建或更新参数
     * @return 包含产品唯一标识和供应商SKU的响应对象
     */
    @Override
    public RefNumWithSupplierSkuRespDTO createOrUpdate(ProductCreateOrUpdateReqDTO reqDTO) {
        return ApiUtil.getResultData(productClient.createOrUpdate(reqDTO));
    }

    /**
     * 获取产品尺寸变更列表
     * <p>
     * 调用产品客户端的getDimensionChangeProduct方法，获取产品尺寸变更的历史记录。
     * </p>
     *
     * @param query 产品尺寸变更查询参数
     * @return 产品尺寸变更版本列表
     */
    @Override
    public List<ProductVersionRespDTO> getDimensionChangeProduct(ProductDimensionChangeQueryReqDTO query) {
        return ApiUtil.getResultData(productClient.getDimensionChangeProduct(query));
    }

    /**
     * 删除产品
     * <p>
     * 调用产品客户端的remove方法，删除指定的产品。
     * </p>
     *
     * @param deleteParam 删除参数，包含要删除的产品标识
     * @return 受影响的行数，通常为1表示删除成功
     */
    @Override
    public Integer removeByParam(BaseDeleteReqDTO deleteParam) {
        return ApiUtil.getResultData(productClient.remove(deleteParam));
    }

    /**
     * 更新产品
     * <p>
     * 调用产品客户端的update方法，更新指定的产品信息。
     * </p>
     *
     * @param updateParam 更新参数，包含要更新的产品信息
     * @return 受影响的行数，通常为1表示更新成功
     */
    @Override
    public Integer updateByParam(ProductUpdateReqDTO updateParam) {
        return ApiUtil.getResultData(productClient.update(updateParam));
    }

    /**
     * 获取产品详情
     * <p>
     * 调用产品客户端的detail方法，获取指定产品的详细信息。
     * </p>
     *
     * @param query 产品查询参数，包含产品的标识信息
     * @return 产品详情响应对象
     */
    @Override
    public ProductDetailRespDTO detail(BaseProductQueryReqDTO query) {
        return ApiUtil.getResultData(productClient.detail(query));
    }

    /**
     * 分页查询产品列表
     * <p>
     * 调用产品客户端的list方法，根据查询条件和分页参数获取产品列表。
     * 在调用前会先将查询参数转换为客户端需要的格式。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的产品列表
     */
    @Override
    public PageData<ProductPageRespDTO> pageByQuery(PageSearch<ProductQueryReqDTO> search) {
        //组装传参
        PageSearch<ProductQueryReqDTO> pageSearch = PageUtil.convert(search, dto -> BeanUtil.copyNew(dto, ProductQueryReqDTO.class));
        //返回结果
        return ApiUtil.getResultData(productClient.list(pageSearch));
    }
}