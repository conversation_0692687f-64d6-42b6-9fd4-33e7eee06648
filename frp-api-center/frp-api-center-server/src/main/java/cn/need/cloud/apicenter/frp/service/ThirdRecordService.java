package cn.need.cloud.apicenter.frp.service;

import cn.need.cloud.apicenter.frp.model.entity.ThirdRecord;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordRequest;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordResponse;
import cn.need.framework.common.mybatis.base.SuperService;

/**
 * 第三方接口调用记录  服务接口
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
public interface ThirdRecordService extends SuperService<ThirdRecord> {

    /**
     * 新增第三方接口请求信息
     *
     * @param request 第三方接口请求信息数据对象
     * @return int 受影响行数
     */
    @SuppressWarnings("UnusedReturnValue")
    int insertThirdRecordRequest(ThirdRecordRequest request);

    /**
     * 新增第三方接口响应信息
     *
     * @param response 第三方接口响应信息数据对象
     * @return int 受影响行数
     */
    @SuppressWarnings("UnusedReturnValue")
    int insertThirdRecordResponse(ThirdRecordResponse response);

}