package cn.need.cloud.apicenter.frp.model.param.product.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品多箱详情 新建参数")
public class MultiboxDetailParam implements Serializable {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    @NotNull(message = "product cannot be empty")
    private Long productId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    @NotNull(message = "lineNum must not be null")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

    /**
     * 箱内产品数量
     */
    @Schema(description = "箱内产品数量")
    @NotNull(message = "qty cannot be empty")
    @Min(value = 1, message = "qty cannot be less than 1")
    private Integer qty;


    public MultiboxDetailParam(Long productId, Integer qty) {
        this.productId = productId;
        this.qty = qty;
    }
}
