// package cn.need.cloud.apicenter.frp.service.product.impl;
//
// import cn.need.cloud.apicenter.frp.service.product.*;
// import cn.need.cloud.biz.cache.ProductCacheRepertory;
// import cn.need.cloud.biz.cache.ProductVersionCacheRepertory;
// import cn.need.cloud.biz.cache.bean.ProductCache;
// import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
// import cn.need.cloud.biz.client.constant.enums.product.ProductConfigTypeEnum;
// import cn.need.cloud.biz.client.constant.enums.product.ProductGroupTypeEnum;
// import cn.need.cloud.biz.client.constant.enums.product.ProductLogStatusEnum;
// import cn.need.cloud.biz.model.entity.log.AuditShowLog;
// import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
// import cn.need.cloud.biz.service.helper.auditshowlog.product.ProductAuditLogHelper;
// import cn.need.cloud.biz.service.inbound.InboundRequestDetailService;
// import cn.need.cloud.biz.util.ProductCacheUtil;
// import cn.need.cloud.biz.util.log.AuditLogHolder;
// import cn.need.cloud.biz.util.log.AuditLogUtil;
// import cn.need.framework.common.core.exception.unchecked.BusinessException;
// import cn.need.framework.common.core.lang.ObjectUtil;
// import cn.need.framework.common.core.lang.StringUtil;
// import cn.need.framework.common.core.lang.Validate;
// import org.apache.commons.math3.stat.descriptive.summary.Product;
// import org.springframework.context.annotation.Lazy;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
//
// import javax.annotation.Resource;
// import java.util.ArrayList;
// import java.util.List;
//
// /**
//  * <p>
//  * 产品 special service 实现类
//  * </p>
//  *
//  * <AUTHOR>
//  * @since 2024-10-28
//  */
// @Service
// public class ProductSpecialServiceImpl implements ProductSpecialService {
//
//     @Resource
//     @Lazy
//     private ProductMultiboxService productMultiboxService;
//
//     @Resource
//     @Lazy
//     private ProductGroupService productGroupService;
//
//     @Resource
//     @Lazy
//     private ProductComponentService productComponentService;
//
//     @Resource
//     private ProductVersionService productVersionService;
//
//     @Resource
//     private ProductCacheRepertory productCacheRepertory;
//
//     @Resource
//     private ProductScanService productScanService;
//
//     @Resource
//     private ProductService productService;
//
//     @Resource
//     private InboundRequestDetailService inboundRequestDetailService;
//
//     @Resource
//     private BinLocationDetailService binLocationDetailService;
//
//     @Resource
//     private ProductVersionCacheRepertory productVersionCacheRepertory;
//
//     @Resource
//     private ProductMultiboxDetailService productMultiboxDetailService;
//
//
//     @Override
//     public Integer getProductVersionInt(Long productId, ProductConfigTypeEnum productConfigTypeEnum) {
//
//         switch (productConfigTypeEnum) {
//             case MULTIBOX -> {
//                 final ProductMultibox multibox = productMultiboxService.getFirstProductMultiboxByProductId(productId);
//                 if (ObjectUtil.isNull(multibox)) {
//                     throw new BusinessException("id: " + productId + " not found in ProductMultibox");
//                 }
//                 return multibox.getMultiboxVersionInt();
//             }
//             case ASSEMBLY -> {
//                 final ProductComponent assembly = productComponentService.getFirstByAssemblyProductId(productId);
//                 if (ObjectUtil.isNull(assembly)) {
//                     throw new BusinessException("id: " + productId + " not found in ProductComponent");
//                 }
//                 return assembly.getComponentVersionInt();
//             }
//             case GROUP -> {
//                 final ProductGroup group = productGroupService.getFirstByProductId(productId);
//                 if (ObjectUtil.isNull(group)) {
//                     throw new BusinessException("id: " + productId + " not found in ProductGroup");
//                 }
//                 return group.getGroupVersionInt();
//             }
//             default -> throw new IllegalStateException("Unexpected value: " + productConfigTypeEnum);
//         }
//     }
//
//     @Override
//     @Transactional(rollbackFor = Exception.class)
//     public Integer checkAndDelete(Long id, String deletedNote) {
//         // 产品版本存在，无法删除
//         if (productVersionService.existByProductId(id)) {
//             throw new BusinessException("Product version exists, cannot be deleted");
//         }
//         // 根据ID获取产品信息，如果不存在，抛出异常
//         Product product = productService.getById(id);
//         if (ObjectUtil.isEmpty(product)) {
//             throw new BusinessException("Parameter cannot be empty");
//         }
//
//         // 检查产品是否为多箱产品或组件产品，如果是，抛出异常，不允许删除
//         if (product.getMultiboxFlag()) {
//             throw new BusinessException("Product is Multibox ,cannot be deleted");
//         }
//         // 检查产品是否有分组，如果有，抛出异常，不允许删除
//         boolean isGroup = product.getGroupType().equals(ProductGroupTypeEnum.CHILD.getType())
//                 || product.getGroupType().equals(ProductGroupTypeEnum.PARENT.getType());
//         if (isGroup) {
//             throw new BusinessException("Product has group, cannot be deleted");
//         }
//         //
//         if (product.getAssemblyProductFlag() || productComponentService.isComponent(id)) {
//             throw new BusinessException("Product is Assembly or Component, cannot be deleted");
//         }
//         // 删除产品并记录删除备注
//         int removed = productService.removeAndNote(id, deletedNote);
//         // 删除相关产品扫描数据
//         productScanService.deleteByProductId(id);
//         //清除产品缓存
//         productCacheRepertory.delProduct(id.toString());
//
//         // 记录到日志
//         ProductAuditLogHelper.recordLog(
//                 product,
//                 ProductLogStatusEnum.DELETE.getStatus(),
//                 BaseTypeLogEnum.BASE_INFO.getType(),
//                 deletedNote,
//                 null
//         );
//
//         // 调用父类方法执行删除操作并记录删除备注
//         return removed;
//     }
//
//     @Override
//     @Transactional(rollbackFor = Exception.class)
//     public Integer removeScan(Long id, String deletedNote) {
//         ProductScan productScan = productScanService.getById(id);
//         if (productScan.getDefaultFlag()) {
//             throw new BusinessException("The system does not permit deletion by default.");
//         }
//         ProductCache productCache = ProductCacheUtil.getById(productScan.getProductId());
//
//         // 记录到log日志
//         AuditShowLog showLog = AuditLogUtil.baseLog(productScan)
//                 // 关联表refNum
//                 .with(AuditShowLog::setRefTableRefNum, productCache.getRefNum())
//                 // 关联表展示refNum
//                 .with(AuditShowLog::setRefTableShowRefNum, productCache.getRefNum())
//                 // 设置日志的状态为产品扫描编号增加
//                 .with(AuditShowLog::setEvent, ProductLogStatusEnum.PRODUCT_SCAN_NUM_DELETE.getStatus())
//                 // 设置审计日志的描述为产品扫描编号增加成的具体内容
//                 .with(AuditShowLog::setDescription, StringUtil.format("ScanNum:{},ProductType:{}", productScan.getScanNum(), productScan.getProductType())).build();
//         AuditLogHolder.record(showLog);
//         // 调用父类的方法进行删除
//         return productScanService.removeAndNote(id, deletedNote);
//     }
//
//     @Override
//     public Integer removeVersion(Long id, String deletedNote) {
//         ProductVersion entity = productVersionService.getById(id);
//         //  获取不到返回异常信息
//         if (ObjectUtil.isEmpty(entity)) {
//             throw new BusinessException("id: " + id + " not found in ProductVersion");
//         }
//         //  校验是否存在入库单
//         if (inboundRequestDetailService.existInboundRequestByProductVersionId(id)) {
//             throw new BusinessException("ProductVersion has InBound, cannot be deleted");
//         }
//         // 校验库位下是否有库存
//         if (binLocationDetailService.existInStockQtyByProductVersionId(id)) {
//             throw new BusinessException("ProductVersion has InStockQty, cannot be deleted");
//         }
//
//         // 记录到log日志
//         AuditShowLog build = AuditLogUtil.commonLog(entity).with(AuditShowLog::setEvent, ProductLogStatusEnum.DELETE.getStatus())
//                 .with(AuditShowLog::setDescription, AuditLogUtil.PRODUCT_VERSION_PREFIX + entity.getProductVersionInt()).build();
//         AuditLogHolder.record(build);
//
//         //清缓存
//         productVersionCacheRepertory.delProductVersion(id.toString());
//         return productVersionService.removeAndNote(id, deletedNote);
//     }
//
//     @Override
//     @Transactional(rollbackFor = Exception.class)
//     public Integer removeMultibox(Long productId, String deletedNote) {
//         Product entity = productService.getById(productId);
//         Validate.notNull(entity, "Parameter cannot be empty");
//         //获取对应的多箱集合
//         List<Long> idList = productMultiboxService.getProductMultiboxListByProductId(productId)
//                 //转换成多箱的idList
//                 .stream().map(ProductMultibox::getId).toList();
//         if (ObjectUtil.isEmpty(idList)) {
//             throw new BusinessException("No multiBox data found");
//         }
//         // 删除detailList
//         productMultiboxDetailService.deleteByProductMultiboxIds(idList);
//
//         // 修改产品多箱标记
//         productService.updateMultiboxFlagById(productId, Boolean.FALSE);
//
//         // 删除扫描中数据
//         productScanService.deleteByMultiboxIds(idList);
//
//         //记录日志
//         ProductAuditLogHelper.recordLog(
//                 entity,
//                 ProductLogStatusEnum.DELETE.getStatus(),
//                 BaseTypeLogEnum.MULTI_BOX.getType(),
//                 deletedNote,
//                 null
//         );
//         // 删除多箱信息
//         return productMultiboxService.deleteByProductId(productId, deletedNote);
//     }
//
//     @Override
//     @Transactional(rollbackFor = Exception.class)
//     public Integer removeGroup(Long productId, String deletedNote) {
//         Product entity = productService.getById(productId);
//         Validate.notNull(entity, "Parameter cannot be empty");
//         List<ProductGroup> listByParentProductId = productGroupService.getListByParentProductId(productId);
//         if (ObjectUtil.isEmpty(listByParentProductId)) {
//             throw new BusinessException("ProductGroup: " + productId + " is not parent");
//         }
//         List<Long> idList = new ArrayList<>(productGroupService.getChildIdList(listByParentProductId));
//         // 修改产品Group父子关系
//         idList.add(productId);
//         productService.updateGroupTypeByIdList(idList, ProductGroupTypeEnum.NONE.getType());
//
//         //记录日志
//         ProductAuditLogHelper.recordLog(
//                 entity,
//                 ProductLogStatusEnum.DELETE.getStatus(),
//                 BaseTypeLogEnum.GROUP.getType(),
//                 deletedNote,
//                 null
//         );
//         // 删除产品Group
//         return productGroupService.deleteByParentProductId(productId, deletedNote);
//     }
//
//     @Override
//     @Transactional(rollbackFor = Exception.class)
//     public Integer removeComponent(Long productId, String deletedNote) {
//         Product entity = productService.getById(productId);
//         //校驗
//         if (ObjectUtil.isEmpty(entity)) {
//             throw new BusinessException("Parameter cannot be empty");
//         }
//         if (entity.getMultiboxFlag() || ObjectUtil.notEqual(entity.getGroupType(), ProductGroupTypeEnum.NONE.getType())) {
//             throw new BusinessException("Product is Multibox or Group, cannot be deleted");
//         }
//
//         // 修改产品组装标记
//         productService.updateAssemblyFlagById(productId, Boolean.FALSE);
//         // 修改产品配件标记
//         List<ProductComponent> list = productComponentService.list(productId);
//         if (ObjectUtil.isEmpty(list)) {
//             throw new BusinessException("No component data found");
//         }
//         //记录日志
//         ProductAuditLogHelper.recordLog(
//                 entity,
//                 ProductLogStatusEnum.DELETE.getStatus(),
//                 BaseTypeLogEnum.COMPONENTS.getType(),
//                 deletedNote,
//                 null
//         );
//         // 删除
//         return productComponentService.deleteByAssemblyProductId(productId, deletedNote);
//     }
// }
