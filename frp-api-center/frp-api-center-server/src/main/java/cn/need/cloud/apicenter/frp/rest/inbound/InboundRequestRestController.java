package cn.need.cloud.apicenter.frp.rest.inbound;

import cn.need.cloud.apicenter.frp.client.constant.InboundRequestApiPath;
import cn.need.cloud.apicenter.frp.service.inbound.InboundRequestService;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.inbound.*;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundRequestRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(InboundRequestApiPath.PREFIX)
@Tag(name = "外部接口- inbound request API")
@Slf4j
public class InboundRequestRestController {
    @Resource
    private InboundRequestService inboundRequestService;


    @Operation(summary = "创建或更新")
    @PostMapping(value = InboundRequestApiPath.CREATE_OR_UPDATE)

    public Result<Boolean> createOrUpdate(@RequestBody InboundRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        log.info("inbound-createOrUpdate reqDTO {}",reqDTO);
        //返回结果
        return inboundRequestService.createOrUpdate(reqDTO);
    }

    @Operation(summary = "删除")
    @PostMapping(value = InboundRequestApiPath.DELETE)

    public Result<Integer> remove(@RequestBody BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO) {

        //返回结果
        return inboundRequestService.remove(baseDeleteReqDTO);
    }


    @Operation(summary = "详情")
    @PostMapping(value = InboundRequestApiPath.DETAIL)

    public Result<InboundRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query) {

        //构建返回参数
        return inboundRequestService.detail(query);
    }

    @Operation(summary = "列表")
    @PostMapping(value = InboundRequestApiPath.LIST)

    public Result<PageData<InboundRequestPageReqDTO>> list(@RequestBody PageSearch<InboundRequestQueryReqDTO> search) {

        //返回结果
        return inboundRequestService.list(search);
    }

    @Operation(summary = "取消")
    @PostMapping(value = InboundRequestApiPath.CANCEL)

    public Result<Integer> cancel(BaseRequestDeleteAndCancelReqDTO reqDTO) {

        //返回结果
        return inboundRequestService.cancel(reqDTO);
    }

    @Operation(summary = "审核")
    @PostMapping(value = InboundRequestApiPath.AUDIT)

    public Result<Boolean> audit(InboundRequestAuditWithWarehouseReqDTO reqDTO) {

        //返回结果
        return inboundRequestService.audit(reqDTO);
    }
}
