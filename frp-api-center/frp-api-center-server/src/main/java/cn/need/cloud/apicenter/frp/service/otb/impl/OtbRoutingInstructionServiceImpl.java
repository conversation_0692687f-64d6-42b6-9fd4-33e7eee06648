package cn.need.cloud.apicenter.frp.service.otb.impl;

import cn.need.cloud.apicenter.frp.service.otb.OtbRoutingInstructionService;
import cn.need.cloud.biz.client.api.ri.OtbRoutingInstructionClient;
import cn.need.cloud.biz.client.dto.req.otb.*;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 入库请求管理 服务实现类类
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@Service
public class OtbRoutingInstructionServiceImpl implements OtbRoutingInstructionService {
    @Resource
    private OtbRoutingInstructionClient otbRoutingInstructionClient;

    @Override
    public Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(OtbRoutingInstructionCreateOrUpdateReqDTO reqDTO) {
        return otbRoutingInstructionClient.createOrUpdate(reqDTO);
    }

    @Override
    public Result<OtbRIRespDTO> detail(RIQueryReqDTO query){
        return otbRoutingInstructionClient.detail(query);
    }

    @Override
    public Result<PageData<OtbRoutingInstructionPageRespDTO>> list(PageSearch<OtbRoutingInstructionQueryReqDTO> search){
        return otbRoutingInstructionClient.list(search);
    }

    @Override
    public Result<Boolean> audit(RIQueryReqDTO query) {
        return otbRoutingInstructionClient.audit(query);
    }
}
