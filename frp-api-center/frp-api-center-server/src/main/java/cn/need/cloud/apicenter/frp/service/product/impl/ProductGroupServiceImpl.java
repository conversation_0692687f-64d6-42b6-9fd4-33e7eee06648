package cn.need.cloud.apicenter.frp.service.product.impl;

import cn.need.cloud.apicenter.frp.service.product.ProductGroupService;
import cn.need.cloud.biz.client.api.product.ProductGroupClient;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductGroupCreateOrUpdateReqDTO;
import cn.need.framework.common.support.api.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 产品group管理 服务实现类
 * <p>
 * 该接口提供产品group相关的远程调用功能，包括产品group组合的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@Service
public class ProductGroupServiceImpl implements ProductGroupService {
    @Resource
    private ProductGroupClient productGroupClient;


    @Override
    public Result<Integer> createOrUpdate(ProductGroupCreateOrUpdateReqDTO reqDTO){
        return productGroupClient.createOrUpdate(reqDTO);
    }

    @Override
    public Result<Integer> remove(BaseDeleteReqDTO baseDeleteReqDTO){
        return productGroupClient.remove(baseDeleteReqDTO);
    }
}
