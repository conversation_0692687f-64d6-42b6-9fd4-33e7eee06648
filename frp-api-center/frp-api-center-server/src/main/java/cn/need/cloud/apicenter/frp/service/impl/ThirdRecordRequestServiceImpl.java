package cn.need.cloud.apicenter.frp.service.impl;

import cn.need.cloud.apicenter.frp.mapper.ThirdRecordRequestMapper;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordRequest;
import cn.need.cloud.apicenter.frp.service.ThirdRecordRequestService;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 第三方接口调用记录的请求信息 服务实现
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Service
public class ThirdRecordRequestServiceImpl extends SuperServiceImpl<ThirdRecordRequestMapper, ThirdRecordRequest> implements ThirdRecordRequestService {

}
