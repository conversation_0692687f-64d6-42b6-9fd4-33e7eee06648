package cn.need.cloud.apicenter.frp.model.req;

import cn.need.cloud.apicenter.frp.model.req.base.BaseTransactionPartnerReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.math.BigDecimal;

/**
 * 产品 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品 create对象")
public class ProductCreateReqVO extends BaseTransactionPartnerReqVO {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    @NotBlank(message = "supplierSku cannot be empty")
    private String supplierSku;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    @NotBlank(message = "upc cannot be empty")
    private String upc;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;


    /**
     * 净长度
     */
    @Schema(description = "净长度")
    @NotNull(message = "netLength must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "netLength must be greater than 0")
    private BigDecimal netLength;

    /**
     * 净宽度
     */
    @Schema(description = "净宽度")
    @NotNull(message = "netWidth must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "netWidth must be greater than 0")
    private BigDecimal netWidth;

    /**
     * 净高度
     */
    @Schema(description = "净高度")
    @NotNull(message = "netHeight must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "netHeight must be greater than 0")
    private BigDecimal netHeight;

    /**
     * 净重量
     */
    @Schema(description = "净重量")
    @NotNull(message = "netWeight must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "netWeight must be greater than 0")
    private BigDecimal netWeight;

    /**
     * 发货长度
     */
    @Schema(description = "发货长度")
    @NotNull(message = "shipLength must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "shipLength must be greater than 0")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @Schema(description = "发货宽度")
    @NotNull(message = "shipWidth must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "shipWidth must be greater than 0")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @Schema(description = "发货高度")
    @NotNull(message = "shipHeight must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "shipHeight must be greater than 0")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @Schema(description = "发货重量")
    @NotNull(message = "shipWeight must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "shipWeight must be greater than 0")
    private BigDecimal shipWeight;

    /**
     * 纸箱长度
     */
    @Schema(description = "纸箱长度")
    @NotNull(message = "cartonLength must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "cartonLength must be greater than 0")
    private BigDecimal cartonLength;

    /**
     * 纸箱宽度
     */
    @Schema(description = "纸箱宽度")
    @NotNull(message = "cartonWidth must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "cartonWidth must be greater than 0")
    private BigDecimal cartonWidth;

    /**
     * 纸箱高度
     */
    @Schema(description = "纸箱高度")
    @NotNull(message = "cartonHeight must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "cartonHeight must be greater than 0")
    private BigDecimal cartonHeight;

    /**
     * 纸箱重量
     */
    @Schema(description = "纸箱重量")
    @NotNull(message = "cartonWeight must not be null")
    @DecimalMin(value = "0", inclusive = false, message = "cartonWeight must be greater than 0")
    private BigDecimal cartonWeight;

    /**
     * 每箱数量
     */
    @Schema(description = "每箱数量")
    @NotNull(message = "pcsPerCarton must not be null")
    @Min(value = 1, message = "pcsPerCarton must be greater than 0")
    private Integer pcsPerCarton;
}
