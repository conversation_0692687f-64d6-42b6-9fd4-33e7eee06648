
package cn.need.cloud.apicenter.frp.service.otb;

import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbShipmentPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbShipmentRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.RequestProductRespDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;

import java.util.List;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
public interface OtbShipmentService {

    /**
     *  详情
     * @param query 详情查询参数
     * @return 返回结果
     */
    Result<OtbShipmentRespDTO> detail(OtbShipmentDetailQueryReqDTO query);

    /**
     * 列表
     * @param search 列表查询参数
     * @return 返回结果
     */
    Result<PageData<OtbShipmentPageRespDTO>> list(PageSearch<OtbShipmentQueryReqDTO> search);

    /**
     * 获取实际商品信息
     * @param query 查询参数
     * @return 返回结果
     */
    Result<List<RequestProductRespDTO>> requestActualProduct(BaseDetailQueryReqDTO query);
}
