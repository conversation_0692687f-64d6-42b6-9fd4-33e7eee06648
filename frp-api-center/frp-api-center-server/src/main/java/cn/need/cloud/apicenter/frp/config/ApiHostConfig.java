package cn.need.cloud.apicenter.frp.config;

import cn.need.cloud.apicenter.frp.client.constant.enums.ThirdSystem;
import cn.need.framework.common.core.constant.StringPool;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serial;
import java.io.Serializable;

/**
 * 接口同步host配置（从nacos获取）
 *
 * <AUTHOR>
 * @since 2024/3/22
 */
@Data
@Component
@ConfigurationProperties(prefix = "api.host")
public class ApiHostConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 5268125453843396177L;
    private String channelUnify;

    private String frp;

    private String fba;

    private String usps;

    public String getHost(ThirdSystem thirdSystem) {
        switch (thirdSystem) {
            case CHANNEL_UNIFY -> {
                return channelUnify;
            }
            case FRP -> {
                return frp;
            }
            case FBA -> {
                return fba;
            }
            case USPS -> {
                return usps;
            }
            default -> {
                return StringPool.EMPTY;
            }
        }
    }
}
