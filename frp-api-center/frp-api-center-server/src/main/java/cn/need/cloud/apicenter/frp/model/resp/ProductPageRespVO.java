package cn.need.cloud.apicenter.frp.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "产品 vo对象")
public class ProductPageRespVO implements Serializable {

    /**
     * 供应商对象
     */
    @Schema(description = "供应商对象")
    private BasePartnerRespVO transactionPartner;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 组装产品标志
     */
    @Schema(description = "组装产品标志")
    private Boolean assemblyProductFlag;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;


    /**
     * 多箱标志
     */
    @Schema(description = "多箱标志")
    private Boolean multiboxFlag;

    /**
     * 组类型
     */
    @Schema(description = "组类型")
    private String groupType;


}