package cn.need.cloud.apicenter.frp.service.impl;

import cn.need.cloud.apicenter.frp.mapper.ThirdRecordMapper;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecord;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordRequest;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordResponse;
import cn.need.cloud.apicenter.frp.service.ThirdRecordRequestService;
import cn.need.cloud.apicenter.frp.service.ThirdRecordResponseService;
import cn.need.cloud.apicenter.frp.service.ThirdRecordService;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 第三方接口调用记录 服务实现
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Service
@RequiredArgsConstructor
public class ThirdRecordServiceImpl extends SuperServiceImpl<ThirdRecordMapper, ThirdRecord> implements ThirdRecordService {

    private final ThirdRecordRequestService thirdRecordRequestService;

    private final ThirdRecordResponseService thirdRecordResponseService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertThirdRecordRequest(ThirdRecordRequest request) {
        return thirdRecordRequestService.insert(request);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertThirdRecordResponse(ThirdRecordResponse response) {
        return thirdRecordResponseService.insert(response);
    }

}
