package cn.need.cloud.apicenter.frp.service.warehouse.impl;

import cn.need.cloud.apicenter.frp.service.warehouse.WarehouseProductRestService;
import cn.need.cloud.biz.client.api.warehouse.WarehouseProductClient;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseProductWithTransactionPartnerReqDTO;
import cn.need.framework.common.support.util.ApiUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 产品 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class WarehouseProductRestServiceImpl implements WarehouseProductRestService {

    @Resource
    private WarehouseProductClient warehouseProductClient;

    @Override
    public Boolean markProductAsSlapAndGo(BaseWarehouseProductWithTransactionPartnerReqDTO param) {
        return  ApiUtil.getResultData(warehouseProductClient.markProductAsSlapAndGo(param));
    }
}