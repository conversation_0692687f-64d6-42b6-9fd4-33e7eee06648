package cn.need.cloud.apicenter.frp.model.param.product.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品扫描 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品扫描 vo对象")
public class ProductScanCreateParam implements Serializable {


    /**
     * 扫描编号
     */
    @Schema(description = "扫描编号")
    private String scanNum;

    /**
     * 产品版本id
     */
    @Schema(description = "产品id")
    private Long productId;


    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 产品属性
     */
    @Schema(description = "产品类型")
    private String productAttribute;


}