package cn.need.cloud.apicenter.frp.model.req;

import cn.need.cloud.apicenter.frp.annotation.ValidProductReqVO;
import cn.need.cloud.apicenter.frp.model.req.base.BaseTransactionPartnerReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * 产品 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品 vo对象")
public class ProductUpdateReqVO extends BaseTransactionPartnerReqVO {

    /**
     * 产品信息
     */
    @Schema(description = "产品信息")
    @NotNull(message = "product can not null")
    @ValidProductReqVO(message = "refNum and supplierSku cannot both be null.")
    private ProductReqVO product;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;


}