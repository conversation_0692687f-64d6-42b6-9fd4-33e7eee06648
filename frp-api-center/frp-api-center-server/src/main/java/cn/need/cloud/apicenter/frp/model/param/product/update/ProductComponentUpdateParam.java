package cn.need.cloud.apicenter.frp.model.param.product.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品组装 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品组装 vo对象")
public class ProductComponentUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 组装产品ID
     */
    @Schema(description = "组装产品ID")
    private Long assemblyProductId;

    /**
     * 组件产品ID
     */
    @Schema(description = "组件产品ID")
    private Long componentProductId;

    /**
     * 组装说明备注
     */
    @Schema(description = "组装说明备注")
    private String assemblyInstructionNote;

    /**
     * componentQty
     */
    @Schema(description = "componentQty")
    private Integer componentQty;

    /**
     * 组装产品版本号
     */
    @Schema(description = "组装产品版本号")
    private Integer componentVersionInt;

}