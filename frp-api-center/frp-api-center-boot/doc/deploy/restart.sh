#!/bin/bash
source /etc/profile

# create directory if not exist
if [ ! -d "/app/frp-api-center" ];then
  mkdir -p /app/frp-api-center
fi

# clear application log
echo Clearing application.log ...
rm -rf logs
mkdir -p logs/frp-api-center
touch logs/frp-api-center/application.log

# stop service
echo Stopping frp-api-center ...
docker compose -f frp-api-center.yaml down

# start service
echo Starting frp-api-center ...
docker compose -f frp-api-center.yaml up -d

# print log, auto abort when 'JVM running for' is encountered(start successfully)
tail -200f logs/frp-api-center/application.log | sed '/JVM running for/ q'
