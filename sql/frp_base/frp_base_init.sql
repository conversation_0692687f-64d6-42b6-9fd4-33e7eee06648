/*
 Navicat Premium Data Transfer

 Source Server         : chengdu  frp
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : ***************:30201
 Source Schema         : frp_base_test

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 28/04/2025 14:27:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `app_access_config`;
CREATE TABLE `app_access_config`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键ID',
    `app_id`              varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `name`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `service_type`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`         tinyint(1)                                             NOT NULL DEFAULT 1 COMMENT '是否激活',
    `note`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '备注',
    `version`             bigint                                                 NOT NULL DEFAULT 0 COMMENT '版本',
    `deleted_note`        varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '删除备注',
    `default_operator_id` bigint                                                 NULL     DEFAULT NULL COMMENT '默认操作员ID',
    `default_tenant_id`   bigint                                                 NULL     DEFAULT NULL COMMENT '默认租户ID',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`         timestamp                                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`           bigint                                                 NULL     DEFAULT NULL COMMENT '更新者ID',
    `update_time`         timestamp                                              NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = 'app权限配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_access_config
-- ----------------------------

-- ----------------------------
-- Table structure for dfs_attach
-- ----------------------------
DROP TABLE IF EXISTS `dfs_attach`;
CREATE TABLE `dfs_attach`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键',
    `link`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附件地址',
    `domain`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附件域名',
    `name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附件名称',
    `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附件原名',
    `extension`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '附件拓展名',
    `attach_size`   bigint                                                 NULL DEFAULT NULL COMMENT '附件大小',
    `create_by`     bigint                                                 NULL DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 NULL DEFAULT NULL COMMENT '最后更新人',
    `update_time`   datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '模块',
    `form_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '表单名称',
    `order_no`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '单据号',
    `file_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '原始名称',
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '附件'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dfs_attach
-- ----------------------------

-- ----------------------------
-- Table structure for dfs_template_file
-- ----------------------------
DROP TABLE IF EXISTS `dfs_template_file`;
CREATE TABLE `dfs_template_file`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键',
    `template_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '文件模板编号',
    `function_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '功能名称',
    `template_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '导出文件名称',
    `path`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '导入模板路径',
    `api`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '导入API',
    `state`         tinyint(1)                                             NULL DEFAULT 1 COMMENT '状态 1:有效 0:无效 ',
    `create_by`     bigint                                                 NULL DEFAULT NULL COMMENT '创建人',
    `update_by`     bigint                                                 NULL DEFAULT NULL COMMENT '最后修改人',
    `create_time`   datetime                                               NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime                                               NULL DEFAULT NULL COMMENT '最后修改时间',
    `remove_flag`   tinyint(1)                                             NULL DEFAULT NULL COMMENT '逻辑删除标记',
    `file_url`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模板下载地址',
    `attach_id`     bigint                                                 NULL DEFAULT NULL COMMENT '附件表主键id',
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '导入导出模板配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dfs_template_file
-- ----------------------------

-- ----------------------------
-- Table structure for dict_config
-- ----------------------------
DROP TABLE IF EXISTS `dict_config`;
CREATE TABLE `dict_config`
(
    `id`          bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `conf_code`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置编号',
    `conf_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称',
    `conf_value`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '配置值',
    `state`       tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `conf_group`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '分组信息，同一分组下的配置编号唯一',
    `create_by`   bigint                                                 NOT NULL COMMENT '创建人',
    `create_time` datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`     bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `C_INX_CONF_CODE` (`conf_code` ASC, `conf_group` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1820756045492916227
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '系统配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_config
-- ----------------------------

-- ----------------------------
-- Table structure for dict_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `dict_dictionary`;
CREATE TABLE `dict_dictionary`
(
    `id`          bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `dict_code`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段编码',
    `dict_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典名称',
    `dict_value`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '字典值',
    `dict_type`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '字典类型',
    `state`       tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `parent_id`   bigint                                                 NOT NULL COMMENT '父级id',
    `depth`       int                                                    NOT NULL COMMENT '深度，用来记录树结构的层级关系',
    `path`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路径，用来记录树结构数据id的路径，用\'.\'分隔',
    `sorting`     int                                                    NOT NULL COMMENT '排序字段',
    `create_by`   bigint                                                 NULL DEFAULT NULL COMMENT '创建人',
    `create_time` datetime                                               NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NULL DEFAULT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NULL DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NULL DEFAULT 0 COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`     bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INX_PARENT_ID` (`parent_id` ASC) USING BTREE,
    INDEX `INX_PATH` (`path` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1863851819267854339
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '数据字典'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_dictionary
-- ----------------------------
INSERT INTO `dict_dictionary`
VALUES (1746740036289212417, 'STATE', '状态', 'STATE', 'SYSTEM', 1, '状态', 0, 1, '1746740036289212417,', 0, 0,
        '2024-01-15 03:44:12', 1, '2024-11-04 10:54:16', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1746740121693630466, 'Active', '有效', '1', 'SYSTEM', 1, '1', 1746740036289212417, 2,
        '1746740036289212417,1746740121693630466,', 1, 0, '2024-01-15 03:44:33', 1, '2024-06-14 08:26:27', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1746740203654524930, 'Inactive', '无效', '0', 'SYSTEM', 1, '无效', 1746740036289212417, 2,
        '1746740036289212417,1746740203654524930,', 1, 0, '2024-01-15 03:44:52', 0, '2024-01-19 10:42:20', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747820568259375105, 'OPERATION_FUNCTION', '操作功能', 'OPERATION_FUNCTION', 'SYSTEM', 1, '', 0, 1,
        '1747820568259375105,', 6, 0, '2024-01-18 03:17:51', 1, '2024-11-04 10:54:53', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1747820705694134274, 'LOGIN', '登录', 'LOGIN', 'SYSTEM', 1, '', 1747820568259375105, 2,
        '1747820568259375105,1747820705694134274,', 1, 0, '2024-01-18 03:18:24', 0, '2024-01-18 03:18:24', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747820765177753602, 'USER', '用户管理', 'USER', 'SYSTEM', 1, '', 1747820568259375105, 2,
        '1747820568259375105,1747820765177753602,', 2, 0, '2024-01-18 03:18:38', 0, '2024-01-18 03:18:38', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747820843300859906, 'ROLE', '角色管理', 'ROLE', 'SYSTEM', 1, '', 1747820568259375105, 2,
        '1747820568259375105,1747820843300859906,', 3, 0, '2024-01-18 03:18:57', 0, '2024-01-18 03:18:57', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747820897604513793, 'API_CENTER', '接口中心', 'API_CENTER', 'SYSTEM', 1, '', 1747820568259375105, 2,
        '1747820568259375105,1747820897604513793,', 4, 0, '2024-01-18 03:19:10', 0, '2024-01-18 03:19:10', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823205222494209, 'OPERATION_MODULE', '操作模块', 'OPERATION_MODULE', 'SYSTEM', 1, '', 0, 1,
        '1747823205222494209,', 3, 0, '2024-01-18 03:28:20', 1, '2024-11-04 10:54:34', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1747823270699773954, 'PLATFORM_BASIC', '基础服务', 'PLATFORM_BASIC', 'SYSTEM', 1, '', 1747823205222494209, 2,
        '1747823205222494209,1747823270699773954,', 1, 0, '2024-01-18 03:28:35', 0, '2024-01-18 03:28:35', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823337196269570, 'PLATFORM_DICT', '字典服务', 'PLATFORM_DICT', 'SYSTEM', 1, '', 1747823205222494209, 2,
        '1747823205222494209,1747823337196269570,', 2, 0, '2024-01-18 03:28:51', 0, '2024-01-18 03:28:51', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823390321324034, 'PLATFORM_LOG', '日志服务', 'PLATFORM_LOG', 'SYSTEM', 1, '', 1747823205222494209, 2,
        '1747823205222494209,1747823390321324034,', 3, 0, '2024-01-18 03:29:04', 0, '2024-01-18 03:29:04', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823459888050178, 'PLATFORM_MESSAGE', '消息服务', 'PLATFORM_MESSAGE', 'SYSTEM', 1, '', 1747823205222494209,
        2, '1747823205222494209,1747823459888050178,', 4, 0, '2024-01-18 03:29:20', 0, '2024-01-18 03:29:20', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823626922012674, 'OPERATION_TYPE', '操作类型', 'OPERATION_TYPE', 'SYSTEM', 1, '', 0, 1,
        '1747823626922012674,', 4, 0, '2024-01-18 03:30:00', 1, '2024-11-04 10:54:41', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1747823690608324610, 'INSERT', '新增', 'INSERT', 'SYSTEM', 1, '', 1747823626922012674, 2,
        '1747823626922012674,1747823690608324610,', 1, 0, '2024-01-18 03:30:15', 0, '2024-01-18 03:30:15', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823738758934530, 'UPDATE', '更新', 'UPDATE', 'SYSTEM', 1, '', 1747823626922012674, 2,
        '1747823626922012674,1747823738758934530,', 2, 0, '2024-01-18 03:30:27', 0, '2024-01-18 03:30:27', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823789149302785, 'DELETE', '删除', 'DELETE', 'SYSTEM', 1, '', 1747823626922012674, 2,
        '1747823626922012674,1747823789149302785,', 3, 0, '2024-01-18 03:30:39', 0, '2024-01-18 03:30:39', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823840336588801, 'QUERY', '查询', 'QUERY', 'SYSTEM', 1, '', 1747823626922012674, 2,
        '1747823626922012674,1747823840336588801,', 4, 0, '2024-01-18 03:30:51', 0, '2024-01-18 03:30:51', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747823956246179841, 'LOGIN', '登录', 'LOGIN', 'SYSTEM', 1, '', 1747823626922012674, 2,
        '1747823626922012674,1747823956246179841,', 5, 0, '2024-01-18 03:31:19', 0, '2024-01-18 03:31:19', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747824118876123138, 'LOGGER_SOURCE', '请求来源', 'LOGGER_SOURCE', 'SYSTEM', 1, '', 0, 1,
        '1747824118876123138,', 5, 0, '2024-01-18 03:31:58', 1, '2024-11-04 10:54:48', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1747824174773612545, 'PC', 'PC端', 'PC', 'SYSTEM', 1, '', 1747824118876123138, 2,
        '1747824118876123138,1747824174773612545,', 1, 0, '2024-01-18 03:32:11', 0, '2024-01-18 03:32:11', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747824233263181826, 'THIRD', '第三方系统', 'THIRD', 'SYSTEM', 1, '', 1747824118876123138, 2,
        '1747824118876123138,1747824233263181826,', 2, 0, '2024-01-18 03:32:25', 0, '2024-01-18 03:32:25', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1747880269923790850, 'PLATFORM_UPMS', '用户权限服务', 'PLATFORM_UPMS', 'SYSTEM', 1, '', 1747823205222494209, 2,
        '1747823205222494209,1747880269923790850,', 4, 0, '2024-01-18 07:15:05', 0, '2024-01-18 07:15:05', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1748276430736240641, 'PERMISSION_TYPE', '资源类型', 'PERMISSION_TYPE', 'SYSTEM', 1, '资源类型', 0, 1,
        '1748276430736240641,', 2, 0, '2024-01-19 09:29:17', 1, '2024-11-04 10:54:26', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1748276543126810625, 'MENU', 'Menu', 'MENU', 'SYSTEM', 1, '菜单', 1748276430736240641, 2,
        '1748276430736240641,1748276543126810625,', 1, 0, '2024-01-19 09:29:44', 1, '2024-12-09 02:34:16', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1748276621325414401, 'BUTTON', 'Button', 'BUTTON', 'SYSTEM', 1, '按钮', 1748276430736240641, 2,
        '1748276430736240641,1748276621325414401,', 2, 0, '2024-01-19 09:30:03', 1, '2024-12-09 02:34:24', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1748276725025386498, 'INVISIBLE_MENU', 'InvisibleMenu', 'INVISIBLE_MENU', 'SYSTEM', 1, '隐藏的菜单',
        1748276430736240641, 2, '1748276430736240641,1748276725025386498,', 3, 0, '2024-01-19 09:30:27', 1,
        '2024-12-09 02:34:37', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1751892551989243906, 'TENANT_TYPE', '租户类型', 'TENANT_TYPE', 'SYSTEM', 1, '', 0, 1, '1751892551989243906,',
        10, 0, '2024-01-29 08:58:28', 1, '2024-11-04 10:55:24', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1751892692083191810, 'OPERATION', 'Operation', 'Operation', 'SYSTEM', 1, '', 1751892551989243906, 2,
        '1751892551989243906,1751892692083191810,', 1, 0, '2024-01-29 08:59:01', 1, '2024-10-29 20:07:47', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1751892892264738818, 'SUPPLY', 'Supply', 'Supply', 'SYSTEM', 1, '', 1751892551989243906, 2,
        '1751892551989243906,1751892892264738818,', 2, 0, '2024-01-29 08:59:49', 1, '2024-10-29 20:08:17', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1789941500189085697, 'IS', '是否', 'IS', 'SYSTEM', 1, '是否', 0, 1, '1789941500189085697,', 1, 1,
        '2024-05-13 16:51:24', 1, '2024-05-13 16:51:24', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1789941627905642498, 'YES', '是', '1', 'SYSTEM', 1, '是', 1789941500189085697, 2,
        '1789941500189085697,1789941627905642498,', 1, 1, '2024-05-13 16:51:54', 1, '2024-05-13 16:52:23', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1789941720440377345, 'NO', '否', '0', 'SYSTEM', 1, '否', 1789941500189085697, 2,
        '1789941500189085697,1789941720440377345,', 2, 1, '2024-05-13 16:52:16', 1, '2024-05-13 16:52:16', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1790231332073246722, 'OCR', 'OCR', 'OCR', 'SYSTEM', 1, '1', 1747824118876123138, 2,
        '1747824118876123138,1790231332073246722,', 1, 1, '2024-05-14 12:03:05', 1, '2024-05-14 12:03:05', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1820347293233057794, 'ICON_TYPE', '图标类型', 'ICON_TYPE', 'SYSTEM', 1, NULL, 0, 1, '1820347293233057794,', 7,
        1, '2024-08-05 14:33:10', 1, '2024-11-04 10:54:59', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1820347440876752898, 'ALI_ICON', '阿里图标', 'ALI_ICON', 'SYSTEM', 1, NULL, 1820347293233057794, 2,
        '1820347293233057794,1820347440876752898,', 1, 1, '2024-08-05 14:33:45', 1, '2024-08-05 14:33:45', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1820348403368857602, 'CUSTOMIZE_UPLOAD_ICON', '自定义上传图标', 'CUSTOMIZE_UPLOAD_ICON', 'SYSTEM', 1, NULL,
        1820347293233057794, 2, '1820347293233057794,1820348403368857602,', 2, 1, '2024-08-05 14:37:34', 1,
        '2024-08-05 14:37:34', 0, 0);
INSERT INTO `dict_dictionary`
VALUES (1820727586649542658, 'FUNCTION', 'Function', 'FUNCTION', 'SYSTEM', 1, NULL, 1748276430736240641, 2,
        '1748276430736240641,1820727586649542658,', 4, 1, '2024-08-06 15:44:19', 1, '2024-12-09 02:34:49', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1827950413674024961, 'ENABLE_DISABLE', '启用禁用', 'ENABLE_DISABLE', 'SYSTEM', 1, '启用禁用', 0, 1,
        '1827950413674024961,', 11, 1, '2024-08-26 14:05:15', 1, '2024-11-04 10:55:29', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1827950572491345921, 'ENABLE', 'Enable', '1', 'SYSTEM', 1, '启用', 1827950413674024961, 2,
        '1827950413674024961,1827950572491345921,', 1, 1, '2024-08-26 14:05:53', 1, '2024-12-02 14:27:03', 0, 2);
INSERT INTO `dict_dictionary`
VALUES (1827950782181380097, 'FORBIDDEN', 'Disable', '0', 'SYSTEM', 1, '禁用', 1827950413674024961, 2,
        '1827950413674024961,1827950782181380097,', 2, 1, '2024-08-26 14:06:43', 1, '2024-12-02 14:27:18', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1833411134717267970, 'START_UP', '启用状态布尔值', 'START_UP', 'SYSTEM', 1, NULL, 0, 1, '1833411134717267970,',
        8, 1, '2024-09-10 15:44:12', 1, '2024-11-04 10:55:05', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1833411270633689090, 'TRUE', 'Enable', 'true', 'SYSTEM', 1, NULL, 1833411134717267970, 2,
        '1833411134717267970,1833411270633689090,', 1, 1, '2024-09-10 15:44:45', 1, '2024-12-02 14:27:51', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1833411357199929346, 'FALSE', 'Disable', 'false', 'SYSTEM', 1, NULL, 1833411134717267970, 2,
        '1833411134717267970,1833411357199929346,', 2, 1, '2024-09-10 15:45:05', 1, '2024-12-02 14:27:59', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1846377998411952129, 'OTC_REQUEST_STATUS', 'OTC请求状态', 'OTC_REQUEST_STATUS', 'SYSTEM', 1, NULL, 0, 1,
        '1846377998411952129,', 9, 1, '2024-10-16 10:29:54', 1, '2024-11-04 10:55:17', 1, 1);
INSERT INTO `dict_dictionary`
VALUES (1853269433857929218, 'SERVICETYPE', '服务类型', 'SERVICETYPE', 'SYSTEM', 1, NULL, 0, 1, '1853269433857929218,',
        12, 1, '2024-11-04 10:54:00', 1, '2024-11-04 10:55:36', 0, 2);
INSERT INTO `dict_dictionary`
VALUES (1853269946854862849, 'All', 'All', 'All', 'SYSTEM', 1, NULL, 1853269433857929218, 2,
        '1853269433857929218,1853269946854862849,', 1, 1, '2024-11-04 10:56:02', 1, '2024-11-04 10:56:02', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853270014970359810, 'User', 'User', 'User', 'SYSTEM', 1, NULL, 1853269433857929218, 2,
        '1853269433857929218,1853270014970359810,', 2, 1, '2024-11-04 10:56:18', 1, '2024-11-04 10:56:18', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853270055269232642, 'FRP', 'FRP', 'FRP', 'SYSTEM', 1, NULL, 1853269433857929218, 2,
        '1853269433857929218,1853270055269232642,', 3, 1, '2024-11-04 10:56:28', 1, '2024-11-04 10:56:28', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853270102824251394, 'SRP', 'SRP', 'SRP', 'SYSTEM', 1, NULL, 1853269433857929218, 2,
        '1853269433857929218,1853270102824251394,', 4, 1, '2024-11-04 10:56:39', 1, '2024-11-04 10:56:39', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853270162102349825, 'SHIP', 'Ship', 'Ship', 'SYSTEM', 1, NULL, 1853269433857929218, 2,
        '1853269433857929218,1853270162102349825,', 5, 1, '2024-11-04 10:56:53', 1, '2024-11-04 10:57:07', 0, 2);
INSERT INTO `dict_dictionary`
VALUES (1853270396371005441, 'ChANNELUNIFY', 'ChannelUnify', 'ChannelUnify', 'SYSTEM', 1, NULL, 1853269433857929218, 2,
        '1853269433857929218,1853270396371005441,', 6, 1, '2024-11-04 10:57:49', 1, '2024-11-04 10:57:49', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853280079357997057, 'VALUETYPE', '值类型', 'VALUETYPE', 'SYSTEM', 1, NULL, 0, 1, '1853280079357997057,', 13, 1,
        '2024-11-04 11:36:18', 1, '2024-11-04 11:36:18', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853280156113760257, 'INT', 'Int', 'Int', 'SYSTEM', 1, NULL, 1853280079357997057, 2,
        '1853280079357997057,1853280156113760257,', 1, 1, '2024-11-04 11:36:36', 1, '2024-11-04 11:36:36', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853281826080092161, 'STRING', 'String', 'String', 'SYSTEM', 1, NULL, 1853280079357997057, 2,
        '1853280079357997057,1853281826080092161,', 2, 1, '2024-11-04 11:43:14', 1, '2024-11-04 11:43:14', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853282261616619521, 'JSON', 'Json', 'Json', 'SYSTEM', 1, NULL, 1853280079357997057, 2,
        '1853280079357997057,1853282261616619521,', 3, 1, '2024-11-04 11:44:58', 1, '2024-11-04 11:44:58', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853282331414032386, 'DATATIME', 'Datetime', 'Datetime', 'SYSTEM', 1, NULL, 1853280079357997057, 2,
        '1853280079357997057,1853282331414032386,', 4, 1, '2024-11-04 11:45:15', 1, '2024-11-04 11:45:15', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853424059857039361, 'WAREHOUSE_ZONE_OPT', '仓库区域类型', 'WAREHOUSE_ZONE_OPT', 'SYSTEM', 1, NULL, 0, 1,
        '1853424059857039361,', 14, 1, '2024-11-04 21:08:25', 1, '2024-11-04 21:08:25', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853424122385723393, 'GROUND', 'Ground', 'Ground', 'SYSTEM', 1, NULL, 1853424059857039361, 2,
        '1853424059857039361,1853424122385723393,', 1, 1, '2024-11-04 21:08:40', 1, '2024-11-04 21:08:40', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1853424169370316802, 'RACK', 'Rack', 'Rack', 'SYSTEM', 1, NULL, 1853424059857039361, 2,
        '1853424059857039361,1853424169370316802,', 2, 1, '2024-11-04 21:08:52', 1, '2024-11-04 21:08:52', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1854101573671645186, 'IS_DEFAULT', '是否(布尔值)', 'IS_DEFAULT', 'SYSTEM', 1, NULL, 0, 1,
        '1854101573671645186,', 15, 1, '2024-11-06 18:00:37', 1, '2024-11-09 16:47:16', 0, 2);
INSERT INTO `dict_dictionary`
VALUES (1854101753301102594, 'TRUE', 'true', 'true', 'SYSTEM', 1, NULL, 1854101573671645186, 2,
        '1854101573671645186,1854101753301102594,', 1, 1, '2024-11-06 18:01:20', 1, '2024-11-06 18:01:20', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1854101840441962498, 'FALES', 'false', 'false', 'SYSTEM', 1, NULL, 1854101573671645186, 2,
        '1854101573671645186,1854101840441962498,', 2, 1, '2024-11-06 18:01:41', 1, '2024-11-06 18:01:41', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1854826680251035649, 'Logistic', 'Logistic', 'Logistic', 'SYSTEM', 1, NULL, 1751892551989243906, 2,
        '1751892551989243906,1854826680251035649,', 3, 1, '2024-11-08 18:01:56', 1, '2024-11-08 18:01:56', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1863850240909316097, 'GROUP_TYPE', '组合类型', 'GROUP_TYPE', 'SYSTEM', 1, NULL, 0, 1, '1863850240909316097,', 1,
        1, '2024-12-03 07:38:21', 1, '2024-12-03 07:38:21', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1863851706868895745, 'PARENT', 'Parent', 'Parent', 'SYSTEM', 1, NULL, 1863850240909316097, 2,
        '1863850240909316097,1863851706868895745,', 1, 1, '2024-12-03 07:44:10', 1, '2024-12-03 07:44:10', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1863851762976100353, 'CHILD', 'Child', 'Child', 'SYSTEM', 1, NULL, 1863850240909316097, 2,
        '1863850240909316097,1863851762976100353,', 2, 1, '2024-12-03 07:44:24', 1, '2024-12-03 07:44:24', 0, 1);
INSERT INTO `dict_dictionary`
VALUES (1863851819267854338, 'NONE', 'None', 'None', 'SYSTEM', 1, NULL, 1863850240909316097, 2,
        '1863850240909316097,1863851819267854338,', 3, 1, '2024-12-03 07:44:37', 1, '2024-12-05 12:00:24', 0, 2);

-- ----------------------------
-- Table structure for dict_lang
-- ----------------------------
DROP TABLE IF EXISTS `dict_lang`;
CREATE TABLE `dict_lang`
(
    `id`          bigint                                                 NOT NULL COMMENT '主键',
    `tenant_id`   bigint                                                 NULL DEFAULT NULL COMMENT '租户ID',
    `module_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '模块名',
    `lang_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言代码(代码中用到的key)',
    `lang_zh_CN`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '简体中文信息',
    `lang_en_US`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '英文信息',
    `params`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数信息',
    `create_by`   bigint                                                 NOT NULL COMMENT '创建人',
    `create_time` datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INX_REGION_CODE` (`module_name` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '平台多语言配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_lang
-- ----------------------------

-- ----------------------------
-- Table structure for dict_lang_field
-- ----------------------------
DROP TABLE IF EXISTS `dict_lang_field`;
CREATE TABLE `dict_lang_field`
(
    `id`                 bigint                                                  NOT NULL COMMENT '主键',
    `lang_field`         varchar(399) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言字段属性',
    `lang_field_desc`    varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '语言字段描述',
    `resource_id`        bigint                                                  NULL DEFAULT NULL COMMENT '资源id',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '备注',
    `create_by`          bigint                                                  NOT NULL COMMENT '创建人',
    `create_time`        datetime                                                NOT NULL COMMENT '创建时间',
    `update_by`          bigint                                                  NOT NULL COMMENT '最后更新人',
    `update_time`        datetime                                                NOT NULL COMMENT '最后更新时间',
    `remove_flag`        tinyint(1)                                              NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `lang_type`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NULL DEFAULT NULL COMMENT '类型：FIELD-字段,EXCEPTION-异常信息',
    `show_type`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NULL DEFAULT NULL COMMENT '显示类型：TIPS：提示类型，FIELD：字段类型',
    `lang_field_tips_id` bigint                                                  NULL DEFAULT NULL COMMENT '提示id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_LANG_FIELD_RESOURCE_ID` (`resource_id` ASC) USING BTREE,
    INDEX `INX_LANG_FIELD_FIELD` (`lang_field` ASC) USING BTREE,
    INDEX `INX_LANG_FIELD_LANG_TYPE` (`lang_type` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '平台多语言字段定义'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_lang_field
-- ----------------------------

-- ----------------------------
-- Table structure for dict_lang_field_translation
-- ----------------------------
DROP TABLE IF EXISTS `dict_lang_field_translation`;
CREATE TABLE `dict_lang_field_translation`
(
    `id`                     bigint                                                  NOT NULL COMMENT '主键',
    `lang_code`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言编码',
    `lang_field_translation` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '语言翻译',
    `lang_field_tips`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '语言提示',
    `lang_field_id`          bigint                                                  NULL DEFAULT NULL COMMENT '语言字段id',
    `lang_field_tips_id`     bigint                                                  NULL DEFAULT NULL COMMENT '语言提示id',
    `create_by`              bigint                                                  NOT NULL COMMENT '创建人',
    `create_time`            datetime                                                NOT NULL COMMENT '创建时间',
    `update_by`              bigint                                                  NOT NULL COMMENT '最后更新人',
    `update_time`            datetime                                                NOT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                              NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INX_LANG_FIELD_LANG_CODE` (`lang_code` ASC) USING BTREE,
    INDEX `IDX_LANG_FIELD_LANG_FIELD_ID` (`lang_field_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '平台多语言字段翻译'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_lang_field_translation
-- ----------------------------

-- ----------------------------
-- Table structure for dict_language
-- ----------------------------
DROP TABLE IF EXISTS `dict_language`;
CREATE TABLE `dict_language`
(
    `id`          bigint                                                 NOT NULL COMMENT '主键',
    `lang_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言编码',
    `module_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '模块编码',
    `title_code`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题编码',
    `title_name`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '标题名称',
    `create_by`   bigint                                                 NOT NULL COMMENT '创建人',
    `create_time` datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `INX_LANG_AND_TITLE_AND_MODULE` (`module_code` ASC, `lang_code` ASC, `title_code` ASC) USING BTREE,
    INDEX `INX_LANG_CODE` (`lang_code` ASC) USING BTREE,
    INDEX `INX_LANG_MODULE_CODE_AND_LANG_CODE` (`module_code` ASC, `lang_code` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '平台多语言配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_language
-- ----------------------------
INSERT INTO `dict_language`
VALUES (1, 'zh_CN', 'route', 'home', '首页', 1, '2024-01-10 16:21:22', 1, '2024-01-10 16:21:27', 0);
INSERT INTO `dict_language`
VALUES (2, 'zh_CN', 'route', 'Dictionary', '数据字典', 1, '2024-01-10 16:21:22', 1, '2024-01-10 16:21:27', 0);
INSERT INTO `dict_language`
VALUES (3, 'en_US', 'route', 'home', 'Home', 1, '2024-01-10 16:21:22', 1, '2024-01-10 16:21:27', 0);
INSERT INTO `dict_language`
VALUES (4, 'en_US', 'route', 'Dictionary', 'Dictionary', 1, '2024-01-10 16:21:22', 1, '2024-01-10 16:21:27', 0);

-- ----------------------------
-- Table structure for dict_region
-- ----------------------------
DROP TABLE IF EXISTS `dict_region`;
CREATE TABLE `dict_region`
(
    `id`                 bigint                                                 NOT NULL,
    `region_code`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '区域编号',
    `region_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '区域名称',
    `parent_region_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父级区域编码',
    `full_region_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域编码全',
    `full_region_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域编码全名称',
    `region_name_py`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域名称拼音',
    `short_name`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '简称',
    `short_name_py`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '短拼音',
    `longitude`          decimal(10, 6)                                         NULL DEFAULT NULL COMMENT '经度',
    `latitude`           decimal(10, 6)                                         NULL DEFAULT NULL COMMENT '纬度',
    `parent_id`          bigint                                                 NULL DEFAULT NULL COMMENT '父级ID',
    `depth`              int                                                    NULL DEFAULT NULL COMMENT '区域深度，记录字典的层级关系，0/国家,1/省，2/市，3/区县，4/乡镇',
    `path`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域路径，用来记录当前类别的id路径，用“.”分隔',
    `sorting`            double(18, 2)                                          NULL DEFAULT NULL COMMENT '区域排序',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`        int                                                    NOT NULL COMMENT '逻辑删除标记',
    `code_path`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域路径，用来记录当前类别的编码路径，用“.”分隔',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注·',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_region
-- ----------------------------

-- ----------------------------
-- Table structure for dict_region_cn
-- ----------------------------
DROP TABLE IF EXISTS `dict_region_cn`;
CREATE TABLE `dict_region_cn`
(
    `id`                 bigint                                                 NOT NULL COMMENT '主键',
    `region_code`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '区域编号',
    `region_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '区域名称',
    `parent_region_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父级区域编码',
    `full_region_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域编码全',
    `full_region_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域编码全名称',
    `region_name_py`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域名称拼音',
    `short_name`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '简称',
    `short_name_py`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '短拼音',
    `longitude`          decimal(10, 6)                                         NULL DEFAULT NULL COMMENT '经度',
    `latitude`           decimal(10, 6)                                         NULL DEFAULT NULL COMMENT '纬度',
    `parent_id`          bigint                                                 NULL DEFAULT NULL COMMENT '父级ID',
    `depth`              int                                                    NULL DEFAULT NULL COMMENT '区域深度，记录字典的层级关系，0/国家,1/省，2/市，3/区县，4/乡镇',
    `path`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域路径，用来记录当前类别的id路径，用“.”分隔',
    `sorting`            double(18, 2)                                          NULL DEFAULT NULL COMMENT '区域排序',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`        int                                                    NOT NULL COMMENT '逻辑删除标记',
    `code_path`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '区域路径，用来记录当前类别的编码路径，用“.”分隔',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `IDX_REGON_CODE` (`region_code` ASC) USING BTREE,
    INDEX `IDX_PARENT_ID` (`parent_id` ASC) USING BTREE,
    INDEX `IDX_REGION_NAME` (`region_name` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '行政区域'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_region_cn
-- ----------------------------

-- ----------------------------
-- Table structure for dict_version
-- ----------------------------
DROP TABLE IF EXISTS `dict_version`;
CREATE TABLE `dict_version`
(
    `id`                   bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `version_no`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '版本号',
    `version_title`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本标题',
    `version_content`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '版本内容描述',
    `version_status`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本状态',
    `version_publisher`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本发布人',
    `terminal`             tinyint                                                NULL DEFAULT NULL COMMENT '终端类型：0: 移动端，1: PC 端，枚举：TerminalEnum',
    `status`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'TO_BE_SUBMITTED' COMMENT '状态：TO_BE_SUBMITTED: 待提交，SUBMITTED: 已提交，枚举：SubmitStatusEnum',
    `server_address`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '服务器地址',
    `publish_announcement` tinyint(1)                                             NULL DEFAULT 0 COMMENT '是否发布公告',
    `announcement_id`      bigint                                                 NULL DEFAULT NULL COMMENT '公告id',
    `popup_flag`           tinyint(1)                                             NULL DEFAULT 0 COMMENT '是否强制弹窗,0：否，1: 是',
    `start_time`           datetime                                               NULL DEFAULT NULL COMMENT '开始时间',
    `end_time`             datetime                                               NULL DEFAULT NULL COMMENT '结束时间',
    `publish_time`         datetime                                               NULL DEFAULT NULL COMMENT '发版日期',
    `publish_scope`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin     NULL COMMENT '发布范围',
    `create_by`            bigint                                                 NOT NULL COMMENT '创建人',
    `create_by_name`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '创建人名称',
    `create_time`          datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`            bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_by_name`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '更新人名称',
    `update_time`          datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`          tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`              bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1821842540484104195
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '系统版本信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dict_version
-- ----------------------------

-- ----------------------------
-- Table structure for duo_white_list
-- ----------------------------
DROP TABLE IF EXISTS `duo_white_list`;
CREATE TABLE `duo_white_list`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键ID',
    `name`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `ipaddress`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'IP地址',
    `note`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `version`      datetime                                               NOT NULL COMMENT '版本',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除备注',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NULL DEFAULT NULL COMMENT '更新者ID',
    `update_time`  datetime                                               NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '二次验证白名单'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of duo_white_list
-- ----------------------------

-- ----------------------------
-- Table structure for frp_app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `frp_app_access_config`;
CREATE TABLE `frp_app_access_config`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键ID',
    `app_id`              varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `name`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `service_type`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`         tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `note`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `version`             timestamp                                              NOT NULL COMMENT '版本',
    `deleted_note`        varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除备注',
    `default_operator_id` bigint                                                 NULL DEFAULT NULL COMMENT '默认操作员ID',
    `default_tenant_id`   bigint                                                 NULL DEFAULT NULL COMMENT '默认租户ID',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`         timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                 NULL DEFAULT NULL COMMENT '更新者ID',
    `update_time`         timestamp                                              NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = 'app权限配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_app_access_config
-- ----------------------------

-- ----------------------------
-- Table structure for frp_duo_white_list
-- ----------------------------
DROP TABLE IF EXISTS `frp_duo_white_list`;
CREATE TABLE `frp_duo_white_list`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键ID',
    `name`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `ipaddress`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'IP地址',
    `note`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `version`      timestamp                                              NOT NULL COMMENT '版本',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除备注',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`  timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NULL DEFAULT NULL COMMENT '更新者ID',
    `update_time`  timestamp                                              NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '二次验证白名单'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_duo_white_list
-- ----------------------------

-- ----------------------------
-- Table structure for frp_partner
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner`;
CREATE TABLE `frp_partner`
(
    `id`                     bigint                                                 NOT NULL COMMENT '合作伙伴ID',
    `name`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴名称',
    `abbr_name`              varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴简称',
    `contact_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '联系人姓名',
    `contact_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人邮箱',
    `address_name`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址名称',
    `address_company`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '公司地址',
    `address_country`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '国家',
    `address_state`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '州/省',
    `address_city`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
    `address_zip_code`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '邮编',
    `address_addr1`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址1',
    `address_addr2`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址2',
    `address_addr3`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址3',
    `address_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址邮箱',
    `address_phone`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '地址电话',
    `address_note`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址备注',
    `partner_type`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴类型',
    `active_flag`            tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `version`                timestamp                                              NOT NULL COMMENT '版本',
    `deleted_note`           varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '删除备注',
    `address_is_residential` tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '是否住宅地址',
    `create_by`              bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`            timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`              bigint                                                 NULL     DEFAULT NULL COMMENT '更新人',
    `update_time`            timestamp                                              NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`            tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '企业伙伴'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_partner
-- ----------------------------

-- ----------------------------
-- Table structure for frp_partner_app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner_app_access_config`;
CREATE TABLE `frp_partner_app_access_config`
(
    `id`              bigint                                                 NOT NULL COMMENT '配置ID',
    `name`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称',
    `partner_user_id` bigint                                                 NOT NULL COMMENT '合作伙伴用户ID',
    `app_id`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`      varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `service_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`     tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `note`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `version`         timestamp                                              NOT NULL COMMENT '版本',
    `deleted_note`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除备注',
    `tenant_id`       bigint                                                 NOT NULL COMMENT '租户ID',
    `create_by`       bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`     timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`       bigint                                                 NULL DEFAULT NULL COMMENT '更新人',
    `update_time`     timestamp                                              NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`     tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_partner_app_access_config_partner_user_partner_user_id` (`partner_user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '企业伙伴app权限配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_partner_app_access_config
-- ----------------------------

-- ----------------------------
-- Table structure for frp_partner_ship
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner_ship`;
CREATE TABLE `frp_partner_ship`
(
    `id`                  bigint                                                 NOT NULL COMMENT '关系ID',
    `provider_partner_id` bigint                                                 NOT NULL COMMENT '提供者合作伙伴ID',
    `client_partner_id`   bigint                                                 NOT NULL COMMENT '客户合作伙伴ID',
    `provider_type`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '提供者类型',
    `active_flag`         tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `note`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `version`             timestamp                                              NOT NULL COMMENT '版本',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`         timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                 NULL DEFAULT NULL COMMENT '更新人',
    `update_time`         timestamp                                              NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_partner_ship_partner_client_partner_id` (`client_partner_id` ASC) USING BTREE,
    INDEX `fk_partner_ship_partner_provider_partner_id` (`provider_partner_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '企业关系'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_partner_ship
-- ----------------------------

-- ----------------------------
-- Table structure for frp_partner_user
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner_user`;
CREATE TABLE `frp_partner_user`
(
    `id`           bigint                                                 NOT NULL COMMENT '用户ID',
    `user_id`      bigint                                                 NOT NULL COMMENT '用户ID',
    `first_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '名字',
    `last_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '姓氏',
    `email`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电子邮件',
    `phone`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '电话号码',
    `active_flag`  tinyint(1)                                             NOT NULL COMMENT '激活标志',
    `version`      timestamp                                              NOT NULL COMMENT '版本',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除备注',
    `tenant_id`    bigint                                                 NOT NULL COMMENT '租户ID',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`  timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NULL DEFAULT NULL COMMENT '更新者ID',
    `update_time`  timestamp                                              NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '企业用户'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_partner_user
-- ----------------------------

-- ----------------------------
-- Table structure for frp_profile_partner
-- ----------------------------
DROP TABLE IF EXISTS `frp_profile_partner`;
CREATE TABLE `frp_profile_partner`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键ID',
    `tenant_id`     bigint                                                 NOT NULL COMMENT '租户ID',
    `version`       timestamp                                              NOT NULL COMMENT '版本时间戳',
    `deleted_note`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '删除备注',
    `service_type`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `category_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分类代码',
    `category_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '分类描述',
    `value_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '值类型',
    `name`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `value`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
    `note`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '备注',
    `sort`          int                                                    NOT NULL COMMENT '排序',
    `active_flag`   tinyint(1)                                             NOT NULL COMMENT '激活标志',
    `code`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '代码',
    `create_by`     bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`   timestamp                                              NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 NULL     DEFAULT NULL COMMENT '更新者ID',
    `update_time`   timestamp                                              NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '企业伙伴档案'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_profile_partner
-- ----------------------------

-- ----------------------------
-- Table structure for frp_tenant_partner
-- ----------------------------
DROP TABLE IF EXISTS `frp_tenant_partner`;
CREATE TABLE `frp_tenant_partner`
(
    `id`          bigint     NOT NULL,
    `tenant_id`   bigint     NULL DEFAULT NULL,
    `partner_id`  bigint     NULL DEFAULT NULL,
    `create_by`   bigint     NOT NULL,
    `create_time` timestamp  NOT NULL,
    `update_by`   bigint     NULL DEFAULT NULL,
    `update_time` timestamp  NULL DEFAULT NULL,
    `remove_flag` tinyint(1) NOT NULL,
    `version`     bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '租户企业对应信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of frp_tenant_partner
-- ----------------------------

-- ----------------------------
-- Table structure for log_alert_record
-- ----------------------------
DROP TABLE IF EXISTS `log_alert_record`;
CREATE TABLE `log_alert_record`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键id',
    `source`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '来源',
    `keyword`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关健字',
    `module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '功能模块',
    `function_desc` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务功能说明',
    `request_url`   varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求url地址',
    `request_body`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin     NULL COMMENT '请求参数内容',
    `alert_info`    longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin     NULL COMMENT '预警信息',
    `create_by`     bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`     bigint                                                 NOT NULL COMMENT '更新人',
    `create_time`   datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`   datetime                                               NOT NULL COMMENT '修改时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_SOURCE` (`source` ASC) USING BTREE,
    INDEX `INDEX_KEYWORD` (`keyword` ASC) USING BTREE,
    INDEX `INDEX_MODULE` (`module` ASC) USING BTREE,
    INDEX `INDEX_REQUEST_URL` (`request_url` ASC) USING BTREE,
    INDEX `INDEX_CREATE_TIME` (`create_time` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '预警日志数据'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_alert_record
-- ----------------------------

-- ----------------------------
-- Table structure for log_error_record
-- ----------------------------
DROP TABLE IF EXISTS `log_error_record`;
CREATE TABLE `log_error_record`
(
    `id`                bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `request_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求用户',
    `request_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求用户角色信息',
    `request_time`      datetime                                               NOT NULL COMMENT '请求时间',
    `request_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求URL',
    `request_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求ip地址',
    `request_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '请求来源',
    `status`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应状态',
    `error_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '错误信息',
    `create_by`         bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`         bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`       datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`       datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`           bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE,
    INDEX `INDEX_request_time` (`request_time` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '错误日志记录信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_error_record
-- ----------------------------

-- ----------------------------
-- Table structure for log_login_record
-- ----------------------------
DROP TABLE IF EXISTS `log_login_record`;
CREATE TABLE `log_login_record`
(
    `id`              bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `login_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '登录用户',
    `login_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户角色信息',
    `login_time`      datetime                                               NOT NULL COMMENT '登录时间',
    `login_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求url',
    `login_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录ip',
    `login_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '登录来源，用来标记是PC端还是小程序',
    `status`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '状态，成功或失败',
    `error_code`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '错误信息',
    `create_by`       bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`       bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`     datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`     datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`     tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`         bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE,
    INDEX `INDEX_login_time` (`login_time` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '登录日志记录'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_login_record
-- ----------------------------

-- ----------------------------
-- Table structure for log_operation_record
-- ----------------------------
DROP TABLE IF EXISTS `log_operation_record`;
CREATE TABLE `log_operation_record`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `operation_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '操作用户',
    `operation_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '操作用户角色',
    `operation_time`      datetime                                               NOT NULL COMMENT '操作时间',
    `operation_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '操作URL',
    `operation_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作ip',
    `operation_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '操作来源，用来标记是PC端还是小程序',
    `operation_module`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'log' COMMENT '操作模块',
    `operation_function`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'default' COMMENT '操作功能',
    `operation_type`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'online' COMMENT '操作类型',
    `status`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '状态，成功或失败',
    `error_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '错误信息',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`           bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`         datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `operation_user_id`   bigint                                                 NULL     DEFAULT NULL COMMENT '操作用户id',
    `version`             bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE,
    INDEX `INDEX_operation_time` (`operation_time` ASC) USING BTREE,
    INDEX `idx_user_id` (`operation_user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '操作日志记录表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_operation_record
-- ----------------------------

-- ----------------------------
-- Table structure for log_record_search
-- ----------------------------
DROP TABLE IF EXISTS `log_record_search`;
CREATE TABLE `log_record_search`
(
    `id`                 bigint                                                 NOT NULL COMMENT '主键id',
    `record_type`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '记录类型：REQUEST,RESPONSE',
    `operation_function` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作功能',
    `operation_module`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作模块',
    `request_path`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求路径',
    `keyword`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '查询关键字',
    `record_id`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`        tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_OPERATION_FUNCTION` (`operation_function` ASC) USING BTREE,
    INDEX `INDEX_KEYWORD` (`keyword` ASC) USING BTREE,
    INDEX `INDEX_REQUEST_PATH` (`request_path` ASC) USING BTREE,
    INDEX `INDEX_REQUEST_MODULE` (`operation_module` ASC) USING BTREE,
    INDEX `INDEX_RECORD_ID` (`record_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '日志记录查询信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_record_search
-- ----------------------------

-- ----------------------------
-- Table structure for log_request_data
-- ----------------------------
DROP TABLE IF EXISTS `log_request_data`;
CREATE TABLE `log_request_data`
(
    `id`                 bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `request_user`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求用户',
    `request_user_role`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求用户角色',
    `request_time`       datetime                                               NOT NULL COMMENT '请求时间',
    `request_schema`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '请求Schema',
    `request_method`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '请求方法',
    `request_media_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求媒介类型',
    `request_url`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求url地址',
    `request_path`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求的api路径',
    `request_ip`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求的真实ip地址',
    `request_source`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '请求来源',
    `request_id`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求id',
    `request_headers`    mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NULL COMMENT '请求头信息',
    `request_body`       longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin     NULL COMMENT '请求参数内容',
    `target_server`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目标服务',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '修改时间',
    `remove_flag`        tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE,
    INDEX `INDEX_request_time` (`request_time` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '请求明细数据'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_request_data
-- ----------------------------

-- ----------------------------
-- Table structure for log_request_path_pattern
-- ----------------------------
DROP TABLE IF EXISTS `log_request_path_pattern`;
CREATE TABLE `log_request_path_pattern`
(
    `id`                   bigint                                                 NOT NULL COMMENT '主键id',
    `request_path`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求路径',
    `request_description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求描述',
    `operation_function`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作功能，数据字典动态值：OPERATION_MODULE',
    `operation_module`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作模块，数据字典动态值：OPERATION_MODULE',
    `operation_type`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '操作类型，数据字典动态值：OPERATION_TYPE',
    `request_source`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '请求来源，取值数据字典：LOGGER_SOURCE',
    `record_response_data` tinyint(1)                                             NOT NULL COMMENT '用来标记是否记录响应数据，0 - 不记录，1 - 记录',
    `state`                tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `request_key`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求信息的搜索key，多个用\',\'隔开',
    `response_key`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '响应信息的搜索key，多个用\',\'隔开',
    `create_by`            bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`            bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`          datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`          datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`          tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`              bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '请求路径匹配规则'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_request_path_pattern
-- ----------------------------

-- ----------------------------
-- Table structure for log_request_used_time
-- ----------------------------
DROP TABLE IF EXISTS `log_request_used_time`;
CREATE TABLE `log_request_used_time`
(
    `id`                bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `request_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求用户',
    `request_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求用户角色信息',
    `request_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求URL',
    `request_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求ip地址',
    `request_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '请求来源',
    `request_time`      datetime                                               NOT NULL COMMENT '请求时间',
    `response_time`     datetime                                               NOT NULL COMMENT '响应时间',
    `used_time`         bigint                                                 NOT NULL COMMENT '请求用时(毫秒)',
    `status`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应状态',
    `error_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '错误信息',
    `create_by`         bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`         bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`       datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`       datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`           bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE,
    INDEX `INDEX_request_time` (`request_time` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '请求时长日志信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_request_used_time
-- ----------------------------

-- ----------------------------
-- Table structure for log_response_data
-- ----------------------------
DROP TABLE IF EXISTS `log_response_data`;
CREATE TABLE `log_response_data`
(
    `id`                     bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `response_time`          datetime                                               NOT NULL COMMENT '响应时间',
    `response_status`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应状态',
    `response_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应编号',
    `response_reason_phrase` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '响应描述简语',
    `response_message`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '响应信息',
    `response_headers`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL COMMENT '响应头信息',
    `response_body`          longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin     NULL COMMENT '响应数据',
    `create_by`              bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`              bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`            datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`            datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`                bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '响应明细数据'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_response_data
-- ----------------------------

-- ----------------------------
-- Table structure for log_third_record
-- ----------------------------
DROP TABLE IF EXISTS `log_third_record`;
CREATE TABLE `log_third_record`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `operation_module`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作模块，数据字典动态值：OPERATION_MODULE',
    `request_path`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求路径',
    `request_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求描述',
    `request_user`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '请求用户',
    `request_ip`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求ip',
    `request_time`        datetime                                               NOT NULL COMMENT '请求时间',
    `used_time`           bigint                                                 NOT NULL COMMENT '用时',
    `response_status`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '状态，成功或失败',
    `response_code`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应编码',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`           bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`         datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`             bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INDEX_record_id` (`record_id` ASC) USING BTREE,
    INDEX `INDEX_REQUEST_DESCRIPTION` (`request_description` ASC) USING BTREE,
    INDEX `IDX_REQUEST_PATH` (`request_path` ASC) USING BTREE,
    INDEX `IDX_REQUEST_TIME` (`request_time` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '第三方请求日志记录'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_third_record
-- ----------------------------

-- ----------------------------
-- Table structure for number_generate_config
-- ----------------------------
DROP TABLE IF EXISTS `number_generate_config`;
CREATE TABLE `number_generate_config`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键',
    `code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '编码',
    `prefix_code`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '编码前缀',
    `date_format`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '时间格式',
    `stream_length` int                                                    NULL     DEFAULT NULL COMMENT '流水号长度',
    `current_num`   bigint                                                 NULL     DEFAULT 0 COMMENT '当前数',
    `step_size`     bigint                                                 NOT NULL DEFAULT 1000 COMMENT '最大可用值步长',
    `reset_flag`    tinyint                                                NULL     DEFAULT 0 COMMENT '是否每天重置',
    `description`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '备注',
    `create_by`     bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`   datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`   datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `tenant_id`     mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NULL,
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INX_REGION_CODE` (`code` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '编码生成配置表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of number_generate_config
-- ----------------------------
INSERT INTO `number_generate_config`
VALUES (1, 'TENANT', 'T', NULL, 3, 100, 1000, 0, '租户', 1, '2024-07-29 14:00:55', 0, '2024-09-27 11:34:19', 0, NULL,
        0);
INSERT INTO `number_generate_config`
VALUES (2, 'WAREHOUSE', 'WH', NULL, 3, 100, 1000, 0, '仓库', 1, '2024-11-15 11:28:14', 1, '2024-11-15 11:28:18', 0,
        NULL, 0);
INSERT INTO `number_generate_config`
VALUES (3, 'INVENTORY_AUDIT', 'INVA', 'yyMMdd', 4, 0, 1000, 1, '库存', 1, '2024-11-15 11:31:23', 1,
        '2024-11-15 11:31:27', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (4, 'PALLET_TEMPLATE', 'PLT', NULL, 4, 9999, 1000, 1, '打托模板', 1, '2024-11-21 14:53:00', 1,
        '2024-11-21 14:53:03', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (5, 'PALLET_EMPTY', 'PLEP', NULL, 4, 9999, 1000, 1, '空托盘', 1, '2024-11-21 14:53:50', 1, '2024-11-21 14:53:54',
        0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (6, 'PRODUCT', 'SKU', NULL, 5, 0, 1000, 0, '产品', 1, '2024-11-22 14:53:50', 1, '2024-11-22 14:53:50', 0, NULL,
        0);
INSERT INTO `number_generate_config`
VALUES (7, 'OTC_REQUEST', 'OCQ', 'yyMMdd', 4, 0, 1000, 1, 'OTC请求', 1, '2024-11-22 14:54:50', 1, '2024-11-22 14:54:50',
        0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (8, 'OTB_ROUTING_INSTRUCTION', 'OBRI', 'yyMMdd', 4, 0, 1000, 1, 'OTB路由', 1, '2024-11-14 22:09:06', 1,
        '2024-11-14 22:09:06', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (9, 'OTB_REQUEST', 'OBQ', 'yyMMdd', 4, 0, 1000, 1, 'OTB请求', 1, '2024-11-14 22:09:06', 1, '2024-11-14 22:09:06',
        0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (10, 'INBOUND_REQUEST', 'IBQ', 'yyMMdd', 4, 0, 1000, 1, '入库申请单', 1, '2024-11-20 20:05:35', 1,
        '2024-11-20 20:05:40', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (11, 'BIN_LOCATION', 'LOC', NULL, 4, 0, 1000, 0, '库位', 1, '2024-11-15 10:53:37', 1, '2024-11-15 10:53:40', 0,
        NULL, 0);
INSERT INTO `number_generate_config`
VALUES (19, 'INBOUND_UNLOAD', 'IBU', 'yyMMdd', 4, 0, 1000, 1, 'InBound卸货单', 1, '2024-11-26 15:38:28', 1,
        '2024-11-26 15:38:33', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (20, 'INBOUND_PUT_AWAY_SLIP', 'IPAS', 'yyMMdd', 4, 0, 1000, 1, 'InBound上架单', 1, '2024-11-26 15:48:28', 1,
        '2024-11-26 15:48:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (21, 'INBOUND_PALLET', 'IPL', 'yyMMdd', 4, 0, 1000, 1, 'InBound打托单', 1, '2024-11-26 17:28:04', 1,
        '2024-11-26 17:28:07', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (22, 'INBOUND_WORKORDER', 'IBW', 'yyMMdd', 4, 0, 1000, 1, 'InBound入库工单', 1, '2024-12-02 11:08:11', 1,
        '2024-12-02 11:08:14', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (24, 'OTC_WORK_ORDER', 'OCW', 'yyMMdd', 4, 0, 1000, 1, 'OTC工单', 1, '2024-11-18 19:30:39', 0,
        '2024-11-18 19:30:45', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (25, 'OTC_PICKING_SLIP', 'OCP', 'yyMMdd', 4, 0, 1000, 1, 'OTC拣货单', 1, '2024-11-19 17:44:26', 0,
        '2024-11-19 17:44:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (26, 'OTC_PACKAGE', 'OCS', 'yyMMdd', 4, 0, 1000, 1, 'OTC包裹', 1, '2024-11-19 17:44:26', 0,
        '2024-11-19 17:44:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (27, 'OTC_SHIP_PALLET', 'OCSPL', 'yyMMdd', 4, 0, 1000, 1, 'OTC 运输托盘', 1, '2024-11-19 17:44:26', 0,
        '2024-11-19 17:44:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (28, 'OTC_PREP_WORK_ORDER', 'OCSW', 'yyMMdd', 4, 0, 1000, 1, 'OTC Prep工单', 1, '2024-11-19 17:44:26', 0,
        '2024-11-19 17:44:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (29, 'OTC_PREP_PICKING_SLIP', 'OCSP', 'yyMMdd', 4, 0, 1000, 1, 'OTC Prep拣货单', 1, '2024-11-19 17:44:26', 0,
        '2024-11-19 17:44:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (30, 'OTB_PICKING_SLIP', 'OBP', 'yyMMdd', 4, 0, 1000, 1, 'OTB拣货单', 1, '2024-12-13 11:17:30', 1,
        '2024-12-13 11:17:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (31, 'OTB_WORK_ORDER', 'OBW', 'yyMMdd', 4, 0, 1000, 1, 'OTB工单', 1, '2024-11-18 19:30:39', 0,
        '2024-11-18 19:30:45', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (32, 'OTB_PALLET', 'OBPL', 'yyMMdd', 4, 0, 1000, 1, 'OTB打托单', 1, '2025-01-02 11:03:11', 1,
        '2025-01-02 11:03:16', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (33, 'OTB_PACKAGE', 'OBPKG', 'yyMMdd', 4, 0, 1000, 1, 'OTB包裹', 1, '2025-01-02 11:08:05', 1,
        '2025-01-02 11:08:09', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (34, 'OTB_PREP_PICKING_SLIP', 'OBSP', 'yyMMdd', 4, 0, 1000, 1, 'OTB Prep拣货单', 1, '2024-12-13 11:17:30', 1,
        '2024-12-13 11:17:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (35, 'OTB_PREP_WORK_ORDER', 'OBSW', 'yyMMdd', 4, 0, 1000, 1, 'OTB Prep工单', 1, '2024-12-13 11:17:30', 1,
        '2024-12-13 11:17:35', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (36, 'OTB_SHIPMENT', 'OBS', 'yyMMdd', 4, 0, 1000, 1, 'otb发货单', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (37, 'FEE_CONFIG_INBOUND', 'FCI', NULL, 4, 0, 1000, 1, '费用配置InBound', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (38, 'FEE_CONFIG_OTB', 'FCB', NULL, 4, 0, 1000, 1, '费用配置OTB', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (39, 'FEE_CONFIG_OTC', 'FCC', NULL, 4, 0, 1000, 1, '费用配置OTC', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (40, 'FEE_CONFIG_STORAGE', 'FCS', NULL, 4, 0, 1000, 1, '费用配置Storage', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (41, 'OTC_PUT_AWAY_SLIP', 'OCPAS', 'yyMMdd', 4, 0, 1000, 1, 'OTC上架单', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (42, 'FEE_QUOTE', 'FQ', NULL, 4, 0, 1000, 1, '费用配置报价', 1, '2025-01-17 13:58:27', 1, '2025-01-17 13:58:31',
        0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (43, 'FEE_SUPPLIER_QUOTE', 'FSQ', NULL, 4, 0, 1000, 1, '费用配置报价', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (44, 'HAZMAT', 'HAZ', NULL, 4, 0, 1000, 0, '危险品版本号', 1, '2025-04-07 10:31:15', 1, '2025-04-07 10:31:17', 0,
        NULL, 0);
INSERT INTO `number_generate_config`
VALUES (45, 'FEE_ORIGINAL_DATA_INBOUND', 'FODI', 'yyMMdd', 4, 0, 1000, 1, '费用原始数据InBound', 1,
        '2025-01-17 13:58:27', 1, '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (46, 'FEE_ORIGINAL_DATA_OTC', 'FODC', 'yyMMdd', 4, 0, 1000, 1, '费用原始数据OTC', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (47, 'FEE_ORIGINAL_DATA_OTB', 'FODB', 'yyMMdd', 4, 0, 1000, 1, '费用原始数据OTB', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (48, 'FEE_ORIGINAL_DATA_STORAGE', 'FODS', 'yyMMdd', 4, 0, 1000, 1, '费用原始数据Storage', 1,
        '2025-01-17 13:58:27', 1, '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (49, 'FEE_INBOUND', 'FI', 'yyMMdd', 4, 0, 1000, 1, '费用InBound', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (50, 'FEE_OTC', 'FC', 'yyMMdd', 4, 0, 1000, 1, '费用OTC', 1, '2025-01-17 13:58:27', 1, '2025-01-17 13:58:31', 0,
        NULL, 0);
INSERT INTO `number_generate_config`
VALUES (51, 'FEE_OTB', 'FB', 'yyMMdd', 4, 0, 1000, 1, '费用OTB', 1, '2025-01-17 13:58:27', 1, '2025-01-17 13:58:31', 0,
        NULL, 0);
INSERT INTO `number_generate_config`
VALUES (52, 'FEE_STORAGE', 'FS', 'yyMMdd', 4, 0, 1000, 1, '费用Storage', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (53, 'FEE_INBOUND_DETAIL', 'FID', 'yyMMdd', 4, 0, 1000, 1, '费用InBound详情', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (54, 'FEE_OTC_DETAIL', 'FCD', 'yyMMdd', 4, 0, 1000, 1, '费用OTC详情', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (55, 'FEE_OTB_DETAIL', 'FBD', 'yyMMdd', 4, 0, 1000, 1, '费用OTB详情', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);
INSERT INTO `number_generate_config`
VALUES (56, 'FEE_STORAGE_DETAIL', 'FSD', 'yyMMdd', 4, 0, 1000, 1, '费用Storage详情', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);

-- ----------------------------
-- Table structure for number_generate_record
-- ----------------------------
DROP TABLE IF EXISTS `number_generate_record`;
CREATE TABLE `number_generate_record`
(
    `id`          bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '编码',
    `prefix_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '编码前缀',
    `max_num`     bigint                                                 NULL     DEFAULT NULL COMMENT '最大数',
    `create_by`   bigint                                                 NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_time` datetime                                               NULL     DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NOT NULL DEFAULT 0 COMMENT '最后更新人',
    `update_time` datetime                                               NULL     DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`     bigint                                                 NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    `tenant_id`   bigint                                                 NULL     DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1916738830516785155
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '编码生成记录'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of number_generate_record
-- ----------------------------
INSERT INTO `number_generate_record`
VALUES (1915286489839828993, 'BIN_LOCATION', '4N1-LOC', 1000, 1, '2025-04-24 06:07:38', 1, '2025-04-24 06:07:38', 0, 0,
        NULL);
INSERT INTO `number_generate_record`
VALUES (1916732222214672386, 'PRODUCT', 'SKU-ADMIN', 1000, 1, '2025-04-28 05:52:28', 1, '2025-04-28 05:52:28', 0, 0,
        NULL);
INSERT INTO `number_generate_record`
VALUES (1916738537938915329, 'BIN_LOCATION', '4N3-LOC', 1000, 1916737477379788802, '2025-04-28 06:17:33',
        1916737477379788802, '2025-04-28 06:17:33', 0, 0, NULL);
INSERT INTO `number_generate_record`
VALUES (1916738830516785154, 'BIN_LOCATION', '4N4-LOC', 1000, 1916737477379788802, '2025-04-28 06:18:43',
        1916737477379788802, '2025-04-28 06:18:43', 0, 0, NULL);

-- ----------------------------
-- Table structure for partner_app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `partner_app_access_config`;
CREATE TABLE `partner_app_access_config`
(
    `id`              bigint                                                 NOT NULL COMMENT '配置ID',
    `name`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称',
    `partner_user_id` bigint                                                 NOT NULL COMMENT '合作伙伴用户ID',
    `app_id`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`      varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `service_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`     tinyint(1)                                             NOT NULL DEFAULT 1 COMMENT '是否激活',
    `note`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '备注',
    `version`         bigint                                                 NOT NULL DEFAULT 0 COMMENT '版本',
    `deleted_note`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '删除备注',
    `tenant_id`       bigint                                                 NOT NULL COMMENT '租户ID',
    `create_by`       bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`     timestamp                                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`       bigint                                                 NULL     DEFAULT NULL COMMENT '更新人',
    `update_time`     timestamp                                              NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`     tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_partner_app_access_config_partner_user_partner_user_id` (`partner_user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '企业伙伴app权限配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of partner_app_access_config
-- ----------------------------

-- ----------------------------
-- Table structure for upms_business_role
-- ----------------------------
DROP TABLE IF EXISTS `upms_business_role`;
CREATE TABLE `upms_business_role`
(
    `id`            bigint                                                NOT NULL COMMENT '主键',
    `business_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务数据类型，可以是用户、组织、职位、分组',
    `business_id`   bigint                                                NOT NULL COMMENT '业务数据id',
    `role_id`       bigint                                                NOT NULL COMMENT '角色id',
    `create_by`     bigint                                                NOT NULL COMMENT '创建人',
    `create_time`   datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                NOT NULL COMMENT '最后更新人',
    `update_time`   datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`       bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_BUSINESS_ID` (`business_id` ASC) USING BTREE,
    INDEX `IDX_BUSINESS_TYPE` (`business_type` ASC) USING BTREE,
    INDEX `IDX_ROLE_ID` (`role_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '业务角色信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_business_role
-- ----------------------------
INSERT INTO `upms_business_role`
VALUES (1857301722792841217, 'USER_TENANT', 1857301722771869698, 1854826444497596417, 1857294809313804290,
        '2024-11-15 13:56:52', 1857294809313804290, '2024-11-15 13:56:52', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1857301821694529538, 'USER_TENANT', 1857301821669363713, 1857300929826115585, 1857294809313804290,
        '2024-11-15 13:57:16', 1857294809313804290, '2024-11-15 13:57:16', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1857352695431155713, 'USER_TENANT', 1857352695418572801, 1, 1, '2024-11-15 17:19:25', 1, '2024-11-15 17:19:25',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1857371346725097473, 'USER_TENANT', 1, 1, 1857360949485883393, '2024-11-15 18:33:32', 1857360949485883393,
        '2024-11-15 18:33:32', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1857371346729291778, 'USER_TENANT', 1, 1854826444497596417, 1857360949485883393, '2024-11-15 18:33:32',
        1857360949485883393, '2024-11-15 18:33:32', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1857371346729291779, 'USER_TENANT', 1, 1856638049274470401, 1857360949485883393, '2024-11-15 18:33:32',
        1857360949485883393, '2024-11-15 18:33:32', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858325545066901505, 'USER_TENANT', 1858325545045929985, 1, 1858321662198362113, '2024-11-18 09:45:11',
        1858321662198362113, '2024-11-18 09:45:11', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858343993419444225, 'USER_TENANT', 1858343993411055618, 1854826444497596417, 1858321662198362113,
        '2024-11-18 10:58:29', 1858321662198362113, '2024-11-18 10:58:29', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858346673349013506, 'USER_TENANT', 1858344404532539394, 1854826444497596417, 1858321662198362113,
        '2024-11-18 11:09:08', 1858321662198362113, '2024-11-18 11:09:08', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858389753410301954, 'USER_TENANT', 1857360949485883393, 1, 1, '2024-11-18 14:00:19', 1, '2024-11-18 14:00:19',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1858424621829664769, 'USER_TENANT', 1858394025610915842, 1, 1, '2024-11-18 16:18:52', 1, '2024-11-18 16:18:52',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1858441487461593090, 'USER_TENANT', 1858422906359328770, 1856638049274470401, 1858399715440144386,
        '2024-11-18 17:25:53', 1858399715440144386, '2024-11-18 17:25:53', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858442142859341826, 'USER_TENANT', 1858390113776513026, 1857372732179206146, 1, '2024-11-18 17:28:30', 1,
        '2024-11-18 17:28:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858444037841694721, 'USER_TENANT', 1858399715440144386, 1856638049274470401, 1858399715440144386,
        '2024-11-18 17:36:01', 1858399715440144386, '2024-11-18 17:36:01', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1858446854392328194, 'USER_TENANT', 1858317415884992514, 1, 1, '2024-11-18 17:47:13', 1, '2024-11-18 17:47:13',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1858703870574669825, 'USER_TENANT', 1858703870562086913, 1, 1, '2024-11-19 10:48:30', 1, '2024-11-19 10:48:30',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1859058757968957442, 'USER_TENANT', 1859058757889265665, 1856638049274470401, 1858394025610915842,
        '2024-11-20 10:18:42', 1858394025610915842, '2024-11-20 10:18:42', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859071192327098370, 'USER_TENANT', 1859071192310321154, 1856638049274470401, 1858317764171608066,
        '2024-11-20 11:08:07', 1858317764171608066, '2024-11-20 11:08:07', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859118940064227329, 'USER_TENANT', 1859118939997118466, 1854826444497596417, 1, '2024-11-20 14:17:51', 1,
        '2024-11-20 14:17:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859120664871084034, 'USER_TENANT', 1859120664858501122, 1, 1, '2024-11-20 14:24:42', 1, '2024-11-20 14:24:42',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1859132631708925953, 'USER_TENANT', 1859132631419518978, 1856638049274470401, 1, '2024-11-20 15:12:15', 1,
        '2024-11-20 15:12:15', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859133000895758338, 'USER_TENANT', 1859133000878981121, 1854826444497596417, 1859120664858501122,
        '2024-11-20 15:13:43', 1859120664858501122, '2024-11-20 15:13:43', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859133000904146946, 'USER_TENANT', 1859133000878981121, 1857372576604082178, 1859120664858501122,
        '2024-11-20 15:13:43', 1859120664858501122, '2024-11-20 15:13:43', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859145017694158849, 'USER_TENANT', 1857301047828664321, 1854826444497596417, 1859132631419518978,
        '2024-11-20 16:01:28', 1859132631419518978, '2024-11-20 16:01:28', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859398891621892098, 'USER_TENANT', 1858321662198362113, 1854826444497596417, 1, '2024-11-21 00:50:16', 1,
        '2024-11-21 00:50:16', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859398958395211777, 'USER_TENANT', 1859120954420666369, 1854826444497596417, 1, '2024-11-21 00:50:32', 1,
        '2024-11-21 00:50:32', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859411272724955137, 'USER_TENANT', 1859411272712372226, 1854826444497596417, 1, '2024-11-21 01:39:28', 1,
        '2024-11-21 01:39:28', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859411462072614913, 'USER_TENANT', 1859411462060032001, 1856638049274470401, 1, '2024-11-21 01:40:13', 1,
        '2024-11-21 01:40:13', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859415973478580226, 'USER_TENANT', 1859415973465997314, 1854826444497596417, 1858317764171608066,
        '2024-11-21 01:58:09', 1858317764171608066, '2024-11-21 01:58:09', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859416031053791233, 'USER_TENANT', 1859415656359837698, 1, 1858317764171608066, '2024-11-21 01:58:23',
        1858317764171608066, '2024-11-21 01:58:23', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859416071109394434, 'USER_TENANT', 1859416071096811521, 1856638049274470401, 1858317764171608066,
        '2024-11-21 01:58:32', 1858317764171608066, '2024-11-21 01:58:32', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859421568717864961, 'USER_TENANT', 1859421568709476353, 1854826444497596417, 1858317764171608066,
        '2024-11-21 02:20:23', 1858317764171608066, '2024-11-21 02:20:23', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859493811476942849, 'USER_TENANT', 1859070746631966722, 1, 1858317764171608066, '2024-11-21 07:07:27',
        1858317764171608066, '2024-11-21 07:07:27', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1859514148082286594, 'USER_TENANT', 1859514147931291650, 1, 1, '2024-11-21 08:28:16', 1, '2024-11-21 08:28:16',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1861245259175292929, 'USER_TENANT', 1861245259133349889, 1, 1, '2024-11-26 03:07:05', 1, '2024-11-26 03:07:05',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1861248229535248385, 'USER_TENANT', 1861248229518471170, 1856638049274470401, 1858444362388549633,
        '2024-11-26 03:18:53', 1858444362388549633, '2024-11-26 03:18:53', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1862426595393179650, 'USER_TENANT', 1862426595384791042, 1, 1858317764171608066, '2024-11-29 09:21:17',
        1858317764171608066, '2024-11-29 09:21:17', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1862435134090153985, 'USER_TENANT', 1862435134081765378, 1856638049274470401, 1, '2024-11-29 09:55:13', 1,
        '2024-11-29 09:55:13', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863413677113401345, 'USER_TENANT', 1863413676844965890, 1, 1, '2024-12-02 02:43:36', 1, '2024-12-02 02:43:36',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863413677121789954, 'USER_TENANT', 1863413676844965890, 1854826444497596417, 1, '2024-12-02 02:43:36', 1,
        '2024-12-02 02:43:36', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863413677121789955, 'USER_TENANT', 1863413676844965890, 1856638049274470401, 1, '2024-12-02 02:43:36', 1,
        '2024-12-02 02:43:36', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863413677121789956, 'USER_TENANT', 1863413676844965890, 1857372576604082178, 1, '2024-12-02 02:43:36', 1,
        '2024-12-02 02:43:36', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863427305350688770, 'USER_TENANT', 1863427305304551426, 1, 1, '2024-12-02 03:37:45', 1, '2024-12-02 03:37:45',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863427305354883073, 'USER_TENANT', 1863427305304551426, 1854826444497596417, 1, '2024-12-02 03:37:45', 1,
        '2024-12-02 03:37:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863427305354883074, 'USER_TENANT', 1863427305304551426, 1856638049274470401, 1, '2024-12-02 03:37:45', 1,
        '2024-12-02 03:37:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863472633318785026, 'USER_TENANT', 1863472633272647681, 1, 1, '2024-12-02 06:37:52', 1, '2024-12-02 06:37:52',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863490838344843265, 'USER_TENANT', 1863490838294511618, 1854826444497596417, 1, '2024-12-02 07:50:13', 1,
        '2024-12-02 07:50:13', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863490838353231874, 'USER_TENANT', 1863490838294511618, 1856638049274470401, 1, '2024-12-02 07:50:13', 1,
        '2024-12-02 07:50:13', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863500011019255809, 'USER_TENANT', 1863500011002478593, 1854826444497596417, 1858317764171608066,
        '2024-12-02 08:26:39', 1858317764171608066, '2024-12-02 08:26:39', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863768250169245698, 'USER_TENANT', 1863768250156662786, 1, 1, '2024-12-03 02:12:33', 1, '2024-12-03 02:12:33',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863837457647996929, 'USER_TENANT', 1863837457593470978, 1, 1, '2024-12-03 06:47:33', 1, '2024-12-03 06:47:33',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863846799885537282, 'USER_TENANT', 1863846799877148673, 1, 1, '2024-12-03 07:24:40', 1, '2024-12-03 07:24:40',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863847064705503234, 'USER_TENANT', 1863847064697114626, 1, 1, '2024-12-03 07:25:43', 1, '2024-12-03 07:25:43',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863847368071122946, 'USER_TENANT', 1863847368062734337, 1, 1, '2024-12-03 07:26:56', 1, '2024-12-03 07:26:56',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863847705679040513, 'USER_TENANT', 1863847705670651906, 1, 1, '2024-12-03 07:28:16', 1, '2024-12-03 07:28:16',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863848227651784706, 'USER_TENANT', 1863848227643396098, 1, 1858317764171608066, '2024-12-03 07:30:21',
        1858317764171608066, '2024-12-03 07:30:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863849047013265410, 'USER_TENANT', 1863849047004876802, 1, 1, '2024-12-03 07:33:36', 1, '2024-12-03 07:33:36',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1863881518304006146, 'USER_TENANT', 1862385918772940803, 1854826444497596417, 1, '2024-12-03 09:42:38', 1,
        '2024-12-03 09:42:38', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863881518304006147, 'USER_TENANT', 1862385918772940803, 1856638049274470401, 1, '2024-12-03 09:42:38', 1,
        '2024-12-03 09:42:38', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863884491872604162, 'USER_TENANT', 1863884491860021250, 1856638049274470401, 1, '2024-12-03 09:54:27', 1,
        '2024-12-03 09:54:27', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863884491872604163, 'USER_TENANT', 1863884491860021250, 1854826444497596417, 1, '2024-12-03 09:54:27', 1,
        '2024-12-03 09:54:27', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863890870943420418, 'USER_TENANT', 1863890870935031809, 1, 1863884491860021250, '2024-12-03 10:19:48',
        1863884491860021250, '2024-12-03 10:19:48', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1863890870947614722, 'USER_TENANT', 1863890870935031809, 1854826444497596417, 1863884491860021250,
        '2024-12-03 10:19:48', 1863884491860021250, '2024-12-03 10:19:48', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864126224178941953, 'USER_TENANT', 1864124108160962561, 1, 1858317764171608066, '2024-12-04 01:55:00',
        1858317764171608066, '2024-12-04 01:55:00', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864126886765727746, 'USER_TENANT', 1864126886753144834, 1, 1858317764171608066, '2024-12-04 01:57:38',
        1858317764171608066, '2024-12-04 01:57:38', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864206684126187521, 'USER_TENANT', 1864206683828391937, 1, 1, '2024-12-04 07:14:43', 1, '2024-12-04 07:14:43',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1864206684130381825, 'USER_TENANT', 1864206683828391937, 1854826444497596417, 1, '2024-12-04 07:14:43', 1,
        '2024-12-04 07:14:43', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864206684134576130, 'USER_TENANT', 1864206683828391937, 1856638049274470401, 1, '2024-12-04 07:14:43', 1,
        '2024-12-04 07:14:43', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486673211985922, 'USER_TENANT', 1864486673182625795, 1863490847995936769, 1, '2024-12-05 01:47:18', 1,
        '2024-12-05 01:47:18', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486673211985923, 'USER_TENANT', 1864486673182625795, 1857372576604082178, 1, '2024-12-05 01:47:18', 1,
        '2024-12-05 01:47:18', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486673216180225, 'USER_TENANT', 1864486673182625795, 1856638049274470401, 1, '2024-12-05 01:47:18', 1,
        '2024-12-05 01:47:18', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486673220374530, 'USER_TENANT', 1864486673182625795, 1854826444497596417, 1, '2024-12-05 01:47:18', 1,
        '2024-12-05 01:47:18', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486673220374531, 'USER_TENANT', 1864486673182625795, 1863496944337108993, 1, '2024-12-05 01:47:18', 1,
        '2024-12-05 01:47:18', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486747077873666, 'USER_TENANT', 1864486747069485058, 1857372648767082497, 1, '2024-12-05 01:47:36', 1,
        '2024-12-05 01:47:36', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864486747082067969, 'USER_TENANT', 1864486747069485058, 1, 1, '2024-12-05 01:47:36', 1, '2024-12-05 01:47:36',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1864497118857662465, 'USER_TENANT', 1864127767078834178, 1, 1864127767078834178, '2024-12-05 02:28:48',
        1864127767078834178, '2024-12-05 02:28:48', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864498510833913858, 'USER_TENANT', 1864498510821330945, 1, 1864127767078834178, '2024-12-05 02:34:20',
        1864127767078834178, '2024-12-05 02:34:20', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864571637302747138, 'USER_TENANT', 1857294809313804290, 1, 1, '2024-12-05 07:24:55', 1, '2024-12-05 07:24:55',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1864644955758321665, 'USER_TENANT', 1864635690737917953, 1863496944337108993, 1864499270686609410,
        '2024-12-05 12:16:16', 1864499270686609410, '2024-12-05 12:16:16', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1864644955762515970, 'USER_TENANT', 1864635690737917953, 1857372576604082178, 1864499270686609410,
        '2024-12-05 12:16:16', 1864499270686609410, '2024-12-05 12:16:16', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1865230780645154818, 'USER_TENANT', 1865230780573851650, 1, 1858317764171608066, '2024-12-07 03:04:07',
        1858317764171608066, '2024-12-07 03:04:07', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1866433355331067906, 'USER_TENANT', 1864499270686609410, 1, 1864499270686609410, '2024-12-10 10:42:43',
        1864499270686609410, '2024-12-10 10:42:43', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1866731418838818818, 'USER_TENANT', 1866731418826235906, 1863496944337108993, 1858317764171608066,
        '2024-12-11 06:27:07', 1858317764171608066, '2024-12-11 06:27:07', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1867042584689565697, 'USER_TENANT', 1867042584672788481, 1, 1858317764171608066, '2024-12-12 03:03:35',
        1858317764171608066, '2024-12-12 03:03:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1867503711988084738, 'USER_TENANT', 1864647628398190595, 1856638049274470401, 1858317764171608066,
        '2024-12-13 09:35:56', 1858317764171608066, '2024-12-13 09:35:56', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1867507085043167234, 'USER_TENANT', 1867507085026390017, 1856638049274470401, 1858317764171608066,
        '2024-12-13 09:49:20', 1858317764171608066, '2024-12-13 09:49:20', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868539000817766402, 'USER_TENANT', 1862436463827935233, 1854826444497596417, 1, '2024-12-16 06:09:48', 1,
        '2024-12-16 06:09:48', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868539000821960705, 'USER_TENANT', 1862436463827935233, 1856638049274470401, 1, '2024-12-16 06:09:48', 1,
        '2024-12-16 06:09:48', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868592007413366786, 'USER_TENANT', 1868588896309346306, 1854826444497596417, 1858773676426473474,
        '2024-12-16 09:40:26', 1858773676426473474, '2024-12-16 09:40:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868592007417561089, 'USER_TENANT', 1868588896309346306, 1, 1858773676426473474, '2024-12-16 09:40:26',
        1858773676426473474, '2024-12-16 09:40:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868592007417561090, 'USER_TENANT', 1868588896309346306, 1856638049274470401, 1858773676426473474,
        '2024-12-16 09:40:26', 1858773676426473474, '2024-12-16 09:40:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868592007421755393, 'USER_TENANT', 1868588896309346306, 1857372576604082178, 1858773676426473474,
        '2024-12-16 09:40:26', 1858773676426473474, '2024-12-16 09:40:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868592007421755394, 'USER_TENANT', 1868588896309346306, 1857372648767082497, 1858773676426473474,
        '2024-12-16 09:40:26', 1858773676426473474, '2024-12-16 09:40:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868592007421755395, 'USER_TENANT', 1868588896309346306, 1863490847995936769, 1858773676426473474,
        '2024-12-16 09:40:26', 1858773676426473474, '2024-12-16 09:40:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868599146777280514, 'USER_TENANT', 1868574425587253250, 1854826444497596417, 1868588896309346306,
        '2024-12-16 10:08:48', 1868588896309346306, '2024-12-16 10:08:48', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868909489302745089, 'USER_TENANT', 1868909489290162178, 1, 1868902227079278593, '2024-12-17 06:42:00',
        1868902227079278593, '2024-12-17 06:42:00', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868926795131072513, 'USER_TENANT', 1858773676426473474, 1854826444497596417, 1868588896309346306,
        '2024-12-17 07:50:46', 1868588896309346306, '2024-12-17 07:50:46', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829016190977, 'USER_TENANT', 1858317764171608066, 1, 1858317764171608066, '2024-12-17 07:58:51',
        1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829016190978, 'USER_TENANT', 1858317764171608066, 1854826444497596417, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829016190979, 'USER_TENANT', 1858317764171608066, 1856638049274470401, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829016190980, 'USER_TENANT', 1858317764171608066, 1863490847995936769, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829016190981, 'USER_TENANT', 1858317764171608066, 1857372648767082497, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829016190982, 'USER_TENANT', 1858317764171608066, 1857372576604082178, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829020385282, 'USER_TENANT', 1858317764171608066, 1863496944337108993, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1868928829020385283, 'USER_TENANT', 1858317764171608066, 1868639424686796802, 1858317764171608066,
        '2024-12-17 07:58:51', 1858317764171608066, '2024-12-17 07:58:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1869565658985590785, 'USER_TENANT', 1869565658977202178, 1856638049274470401, 1, '2024-12-19 02:09:23', 1,
        '2024-12-19 02:09:23', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1869566157432483842, 'USER_TENANT', 1869565802900549634, 1856638049274470401, 1858317764171608066,
        '2024-12-19 02:11:21', 1858317764171608066, '2024-12-19 02:11:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1869566157436678146, 'USER_TENANT', 1869565802900549634, 1854826444497596417, 1858317764171608066,
        '2024-12-19 02:11:21', 1858317764171608066, '2024-12-19 02:11:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1869642605686091777, 'USER_TENANT', 1859124740824408066, 1869554259983003650, 1, '2024-12-19 07:15:08', 1,
        '2024-12-19 07:15:08', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694955921410, 'USER_TENANT', 1870012694897201153, 1, 1864486747069485058, '2024-12-20 07:45:44',
        1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694960115714, 'USER_TENANT', 1870012694897201153, 1854826444497596417, 1864486747069485058,
        '2024-12-20 07:45:44', 1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694960115715, 'USER_TENANT', 1870012694897201153, 1857372576604082178, 1864486747069485058,
        '2024-12-20 07:45:44', 1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694964310018, 'USER_TENANT', 1870012694897201153, 1857372648767082497, 1864486747069485058,
        '2024-12-20 07:45:44', 1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694964310019, 'USER_TENANT', 1870012694897201153, 1863490847995936769, 1864486747069485058,
        '2024-12-20 07:45:44', 1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694964310020, 'USER_TENANT', 1870012694897201153, 1863496944337108993, 1864486747069485058,
        '2024-12-20 07:45:44', 1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870012694964310021, 'USER_TENANT', 1870012694897201153, 1868639424686796802, 1864486747069485058,
        '2024-12-20 07:45:44', 1864486747069485058, '2024-12-20 07:45:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912835002369, 'USER_TENANT', 1870013912826613761, 1, 1870012694897201153, '2024-12-20 07:50:35',
        1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912835002370, 'USER_TENANT', 1870013912826613761, 1854826444497596417, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912835002371, 'USER_TENANT', 1870013912826613761, 1856638049274470401, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912839196674, 'USER_TENANT', 1870013912826613761, 1857372576604082178, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912839196675, 'USER_TENANT', 1870013912826613761, 1857372648767082497, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912839196676, 'USER_TENANT', 1870013912826613761, 1863490847995936769, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912839196677, 'USER_TENANT', 1870013912826613761, 1868639424686796802, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870013912839196678, 'USER_TENANT', 1870013912826613761, 1863496944337108993, 1870012694897201153,
        '2024-12-20 07:50:35', 1870012694897201153, '2024-12-20 07:50:35', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1870071561987395585, 'USER_TENANT', 1858444362388549633, 1868639424686796802, 1858317764171608066,
        '2024-12-20 11:39:39', 1858317764171608066, '2024-12-20 11:39:39', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871088590232064002, 'USER_TENANT', 1871087997849538563, 1871088263877464065, 1858317764171608066,
        '2024-12-23 07:00:58', 1858317764171608066, '2024-12-23 07:00:58', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871089031342821378, 'USER_TENANT', 1871089031334432770, 1871088263877464065, 1871087997849538563,
        '2024-12-23 07:02:43', 1871087997849538563, '2024-12-23 07:02:43', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134466, 'USER_TENANT', 1871731167698907137, 1, 1864486747069485058, '2024-12-25 01:34:21',
        1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134467, 'USER_TENANT', 1871731167698907137, 1854826444497596417, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134468, 'USER_TENANT', 1871731167698907137, 1856638049274470401, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134469, 'USER_TENANT', 1871731167698907137, 1857372576604082178, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134470, 'USER_TENANT', 1871731167698907137, 1857372648767082497, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134471, 'USER_TENANT', 1871731167698907137, 1863490847995936769, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134472, 'USER_TENANT', 1871731167698907137, 1863496944337108993, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871731170035134473, 'USER_TENANT', 1871731167698907137, 1871088263877464065, 1864486747069485058,
        '2024-12-25 01:34:21', 1864486747069485058, '2024-12-25 01:34:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871737430689939458, 'USER_TENANT', 1871737430627024898, 1, 1858317764171608066, '2024-12-25 01:59:13',
        1858317764171608066, '2024-12-25 01:59:13', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1871747282912903170, 'USER_TENANT', 1871747282904514561, 1, 1858317764171608066, '2024-12-25 02:38:22',
        1858317764171608066, '2024-12-25 02:38:22', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1872561428414726145, 'USER_TENANT', 1870303799224143874, 1854826444497596417, 1870303799224143874,
        '2024-12-27 08:33:30', 1870303799224143874, '2024-12-27 08:33:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1872561428414726146, 'USER_TENANT', 1870303799224143874, 1856638049274470401, 1870303799224143874,
        '2024-12-27 08:33:30', 1870303799224143874, '2024-12-27 08:33:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1872561428414726147, 'USER_TENANT', 1870303799224143874, 1871766266521550850, 1870303799224143874,
        '2024-12-27 08:33:30', 1870303799224143874, '2024-12-27 08:33:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1872561428414726148, 'USER_TENANT', 1870303799224143874, 1, 1870303799224143874, '2024-12-27 08:33:30',
        1870303799224143874, '2024-12-27 08:33:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1876443345594335234, 'USER_TENANT', 1876443345556586497, 1856638049274470401, 1868909489290162178,
        '2025-01-07 01:38:51', 1868909489290162178, '2025-01-07 01:38:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1876443345594335235, 'USER_TENANT', 1876443345556586497, 1854826444497596417, 1868909489290162178,
        '2025-01-07 01:38:51', 1868909489290162178, '2025-01-07 01:38:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1876443345594335236, 'USER_TENANT', 1876443345556586497, 1871766266521550850, 1868909489290162178,
        '2025-01-07 01:38:51', 1868909489290162178, '2025-01-07 01:38:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1889577569127284737, 'USER_TENANT', 1889577569014038530, 1, 1870303799224143874, '2025-02-12 07:29:34',
        1870303799224143874, '2025-02-12 07:29:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1889577569135673346, 'USER_TENANT', 1889577569014038530, 1854826444497596417, 1870303799224143874,
        '2025-02-12 07:29:34', 1870303799224143874, '2025-02-12 07:29:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1889577569135673347, 'USER_TENANT', 1889577569014038530, 1856638049274470401, 1870303799224143874,
        '2025-02-12 07:29:34', 1870303799224143874, '2025-02-12 07:29:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1889577569135673348, 'USER_TENANT', 1889577569014038530, 1871766266521550850, 1870303799224143874,
        '2025-02-12 07:29:34', 1870303799224143874, '2025-02-12 07:29:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1889577569135673349, 'USER_TENANT', 1889577569014038530, 1879005620372246529, 1870303799224143874,
        '2025-02-12 07:29:34', 1870303799224143874, '2025-02-12 07:29:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1891681027062276097, 'USER_TENANT', 1891681026793840642, 1856638049274470401, 1868588896309346306,
        '2025-02-18 02:47:57', 1868588896309346306, '2025-02-18 02:47:57', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1891763439662309377, 'USER_TENANT', 1891763439645532161, 1854826444497596417, 1868588896309346306,
        '2025-02-18 08:15:26', 1868588896309346306, '2025-02-18 08:15:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1891763439662309378, 'USER_TENANT', 1891763439645532161, 1871766266521550850, 1868588896309346306,
        '2025-02-18 08:15:26', 1868588896309346306, '2025-02-18 08:15:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1891763439662309379, 'USER_TENANT', 1891763439645532161, 1879005620372246529, 1868588896309346306,
        '2025-02-18 08:15:26', 1868588896309346306, '2025-02-18 08:15:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1891763439662309380, 'USER_TENANT', 1891763439645532161, 1856638049274470401, 1868588896309346306,
        '2025-02-18 08:15:26', 1868588896309346306, '2025-02-18 08:15:26', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892131601796866050, 'USER_TENANT', 1868902227079278593, 1871766266521550850, 1868588896309346306,
        '2025-02-19 08:38:23', 1868588896309346306, '2025-02-19 08:38:23', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892132179620962306, 'USER_TENANT', 1869210851636604930, 1871766266521550850, 1868588896309346306,
        '2025-02-19 08:40:40', 1868588896309346306, '2025-02-19 08:40:40', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892132179620962307, 'USER_TENANT', 1869210851636604930, 1879005620372246529, 1868588896309346306,
        '2025-02-19 08:40:40', 1868588896309346306, '2025-02-19 08:40:40', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892160283964579842, 'USER_TENANT', 1892160283826167810, 1, 1, '2025-02-19 10:32:21', 1, '2025-02-19 10:32:21',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1892161915121016834, 'USER_TENANT', 1892161915108433921, 1, 1870303799224143874, '2025-02-19 10:38:50',
        1870303799224143874, '2025-02-19 10:38:50', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892161915121016835, 'USER_TENANT', 1892161915108433921, 1856638049274470401, 1870303799224143874,
        '2025-02-19 10:38:50', 1870303799224143874, '2025-02-19 10:38:50', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892161915121016836, 'USER_TENANT', 1892161915108433921, 1879005620372246529, 1870303799224143874,
        '2025-02-19 10:38:50', 1870303799224143874, '2025-02-19 10:38:50', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892162396522258433, 'USER_TENANT', 1892162396513869825, 1, 1870303799224143874, '2025-02-19 10:40:45',
        1870303799224143874, '2025-02-19 10:40:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892162396526452738, 'USER_TENANT', 1892162396513869825, 1856638049274470401, 1870303799224143874,
        '2025-02-19 10:40:45', 1870303799224143874, '2025-02-19 10:40:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892840868783648770, 'USER_TENANT', 1892840868737511426, 1, 1868588896309346306, '2025-02-21 07:36:45',
        1868588896309346306, '2025-02-21 07:36:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892840868787843073, 'USER_TENANT', 1892840868737511426, 1854826444497596417, 1868588896309346306,
        '2025-02-21 07:36:45', 1868588896309346306, '2025-02-21 07:36:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892840868787843074, 'USER_TENANT', 1892840868737511426, 1856638049274470401, 1868588896309346306,
        '2025-02-21 07:36:45', 1868588896309346306, '2025-02-21 07:36:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892840868787843075, 'USER_TENANT', 1892840868737511426, 1871766266521550850, 1868588896309346306,
        '2025-02-21 07:36:45', 1868588896309346306, '2025-02-21 07:36:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1892840868787843076, 'USER_TENANT', 1892840868737511426, 1879005620372246529, 1868588896309346306,
        '2025-02-21 07:36:45', 1868588896309346306, '2025-02-21 07:36:45', 0, 0);
INSERT INTO `upms_business_role`
VALUES (***********68393729, 'USER_TENANT', 1895022699634176002, 1, 1, '2025-02-27 08:06:34', 1, '2025-02-27 08:06:34',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (***********76782337, 'USER_TENANT', 1895022699634176002, 1854826444497596417, 1, '2025-02-27 08:06:34', 1,
        '2025-02-27 08:06:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (***********76782338, 'USER_TENANT', 1895022699634176002, 1856638049274470401, 1, '2025-02-27 08:06:34', 1,
        '2025-02-27 08:06:34', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895024695728615426, 'USER_TENANT', 1895024695690866689, 1, 1, '2025-02-27 08:14:30', 1, '2025-02-27 08:14:30',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1895024695728615427, 'USER_TENANT', 1895024695690866689, 1854826444497596417, 1, '2025-02-27 08:14:30', 1,
        '2025-02-27 08:14:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895024695732809730, 'USER_TENANT', 1895024695690866689, 1856638049274470401, 1, '2025-02-27 08:14:30', 1,
        '2025-02-27 08:14:30', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895030224051974145, 'USER_TENANT', 1895030224018419713, 1, 1, '2025-02-27 08:36:28', 1, '2025-02-27 08:36:28',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1895030224056168450, 'USER_TENANT', 1895030224018419713, 1854826444497596417, 1, '2025-02-27 08:36:28', 1,
        '2025-02-27 08:36:28', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895030224056168451, 'USER_TENANT', 1895030224018419713, 1856638049274470401, 1, '2025-02-27 08:36:28', 1,
        '2025-02-27 08:36:28', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895030224056168452, 'USER_TENANT', 1895030224018419713, 1871766266521550850, 1, '2025-02-27 08:36:28', 1,
        '2025-02-27 08:36:28', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895030224056168453, 'USER_TENANT', 1895030224018419713, 1879005620372246529, 1, '2025-02-27 08:36:28', 1,
        '2025-02-27 08:36:28', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895031297777020930, 'USER_TENANT', 1895031297739272193, 1, 1, '2025-02-27 08:40:44', 1, '2025-02-27 08:40:44',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1895031297781215233, 'USER_TENANT', 1895031297739272193, 1854826444497596417, 1, '2025-02-27 08:40:44', 1,
        '2025-02-27 08:40:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895031297781215234, 'USER_TENANT', 1895031297739272193, 1879005620372246529, 1, '2025-02-27 08:40:44', 1,
        '2025-02-27 08:40:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895031297781215235, 'USER_TENANT', 1895031297739272193, 1856638049274470401, 1, '2025-02-27 08:40:44', 1,
        '2025-02-27 08:40:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895031297781215236, 'USER_TENANT', 1895031297739272193, 1871766266521550850, 1, '2025-02-27 08:40:44', 1,
        '2025-02-27 08:40:44', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895288012338630658, 'USER_TENANT', 1895287375748141058, 1, 1, '2025-02-28 01:40:50', 1, '2025-02-28 01:40:50',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1895305442823053314, 'USER_TENANT', 1895285787889504257, 1871766266521550850, 1895285787889504257,
        '2025-02-28 02:50:05', 1895285787889504257, '2025-02-28 02:50:05', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895305442827247618, 'USER_TENANT', 1895285787889504257, 1879005620372246529, 1895285787889504257,
        '2025-02-28 02:50:05', 1895285787889504257, '2025-02-28 02:50:05', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895310666518306817, 'USER_TENANT', 1895310666480558082, 1, 1, '2025-02-28 03:10:51', 1, '2025-02-28 03:10:51',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1895310666522501122, 'USER_TENANT', 1895310666480558082, 1854826444497596417, 1, '2025-02-28 03:10:51', 1,
        '2025-02-28 03:10:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1895310666522501123, 'USER_TENANT', 1895310666480558082, 1856638049274470401, 1, '2025-02-28 03:10:51', 1,
        '2025-02-28 03:10:51', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1916737477568532482, 'USER_TENANT', 1916737477379788802, 1, 1, '2025-04-28 06:13:21', 1, '2025-04-28 06:13:21',
        0, 0);
INSERT INTO `upms_business_role`
VALUES (1916737477576921089, 'USER_TENANT', 1916737477379788802, 1854826444497596417, 1, '2025-04-28 06:13:21', 1,
        '2025-04-28 06:13:21', 0, 0);
INSERT INTO `upms_business_role`
VALUES (1916737477576921090, 'USER_TENANT', 1916737477379788802, 1856638049274470401, 1, '2025-04-28 06:13:21', 1,
        '2025-04-28 06:13:21', 0, 0);

-- ----------------------------
-- Table structure for upms_icon_library
-- ----------------------------
DROP TABLE IF EXISTS `upms_icon_library`;
CREATE TABLE `upms_icon_library`
(
    `id`          bigint                                                 NOT NULL,
    `name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '图标名称',
    `icon_url`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '图标url',
    `state`       tinyint                                                NULL     DEFAULT 1 COMMENT '状态.1-有效，0-无效',
    `create_by`   decimal(20, 0)                                         NULL     DEFAULT NULL COMMENT '创建人',
    `create_time` datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`   decimal(20, 0)                                         NULL     DEFAULT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint                                                NOT NULL DEFAULT 0 COMMENT '逻辑删除标记',
    `version`     bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_icon_library
-- ----------------------------
INSERT INTO `upms_icon_library`
VALUES (1820336697655377921, '第一个图标111', 'http://www.baidu.com', 0, 1, '2024-08-05 13:51:04', 1,
        '2024-08-05 13:52:28', 1, 0);
INSERT INTO `upms_icon_library`
VALUES (1820336847891152897, '第二个图标', 'http://www.baidu.com', 1, 1, '2024-08-05 13:51:39', 1,
        '2024-08-05 13:51:39', 0, 0);
INSERT INTO `upms_icon_library`
VALUES (1820336865951825922, '第三个图标', 'http://www.baidu.com', 1, 1, '2024-08-05 13:51:44', 1,
        '2024-08-05 13:51:44', 0, 0);
INSERT INTO `upms_icon_library`
VALUES (1820336897270693890, '第四个图标', 'http://www.baidu.com', 1, 1, '2024-08-05 13:51:51', 1,
        '2024-08-05 13:51:51', 0, 0);

-- ----------------------------
-- Table structure for upms_permissions
-- ----------------------------
DROP TABLE IF EXISTS `upms_permissions`;
CREATE TABLE `upms_permissions`
(
    `id`               bigint                                                 NOT NULL COMMENT '主键',
    `permissions_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限编号',
    `permissions_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限名称',
    `permissions_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '权限类型',
    `href`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '链接地址',
    `terminal`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '终端PC 、Mobile',
    `icon`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
    `icon_type`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标类型，参见字典【ICON_TYPE】',
    `state`            tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `parent_id`        bigint                                                 NOT NULL COMMENT '父级id',
    `depth`            int                                                    NOT NULL COMMENT '深度，用来记录树结构的层级关系',
    `path`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路径，用来记录树结构数据id的路径，用\'.\'分隔',
    `route_path`       json                                                   NULL COMMENT '可访问的路由路径',
    `sorting`          int                                                    NOT NULL COMMENT '排序字段',
    `create_by`        bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`      datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`        bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`      datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`      tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`          bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    `deleted_note`     varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除原因',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `C_INX_DICT_CODE` (`permissions_code` ASC) USING BTREE,
    INDEX `INX_PARENT_ID` (`parent_id` ASC) USING BTREE,
    INDEX `INX_PATH` (`path` ASC) USING BTREE,
    INDEX `PERMISSIONS_TYPE` (`permissions_type` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '系统权限信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_permissions
-- ----------------------------
INSERT INTO `upms_permissions`
VALUES (1810935050087174145, 'Home', 'HomePage', 'MENU', '/home', 'PC', 'file-home', NULL, 1, '', 0, 1,
        '1810935050087174145,', '[
        \"/api/upms/permissions/buttons\",
        \"/api/upms/permissions/routes\",
        \"/api/upms/permissions/insert\",
        \"/api/upms/duo-white-list/detail/{id}\",
        \"/api/upms/route/remove\",
        \"/api/dict/region/detail/{id}\",
        \"/api/log/request-used-time/request-used-time-clear\",
        \"/api/dict/dictionary/get-dictionary-map\",
        \"/api/dict/dictionary/subsets\",
        \"/api/dict/config/insert\",
        \"/client/dict/dictionary/subset\",
        \"/api/upms/business-role/list\",
        \"/api/dfs/oss/remove-files\",
        \"/api/log/operation-record/operation-log-clear\",
        \"/api/dfs/oss/put-file-attachList\",
        \"/api/upms/duo-white-list/remove\",
        \"/api/upms/user-ignore-tenant/reset-password\",
        \"/api/dict/lang-field/load-lang-field-result\",
        \"/api/upms/user-tenant/modify-password\",
        \"/api/upms/route/listAll\",
        \"/api/upms/user-ignore-tenant/insert\",
        \"/api/upms/role-permissions/detail-by-role/{roleId}\",
        \"/api/dfs/template-file/get-templateFile/{templateCode}\",
        \"/api/upms/route/detail/{id}\",
        \"/api/dict/config/export\",
        \"/api/upms/duo-white-list/update\",
        \"/api/dict/config/list\",
        \"/api/dfs/oss/put-file-by-name\",
        \"/api/dfs/oss/make-bucket\",
        \"/api/dict/number-generate-config/list\",
        \"/api/upms/permissions/update\",
        \"/api/dict/config/update\",
        \"/client/dfs/attach/upload-file\",
        \"/api/dfs/oss/file-link\",
        \"/client/dict/region/find-by-condition\",
        \"/api/dict/dictionary/detail/{id}\",
        \"/api/dfs/template-file/remove/{id}\",
        \"/api/dict/number-generate-config/remove/{id}\",
        \"/api/log/operation-record/export\",
        \"/api/upms/user-ignore-tenant/list\",
        \"/api/dict/number-generate-config/active/{id}\",
        \"/api/dfs/oss/put-file-attach-by-name\",
        \"/api/dfs/template-file/detail/{id}\",
        \"/api/dict/number-generate-config/update\",
        \"/api/upms/partner-ship/insert\",
        \"/api/dict/config/get-value\",
        \"/api/dict/number-generate-config/import\",
        \"/api/dict/region/update\",
        \"/api/dict/dictionary/remove/{id}\",
        \"/api/dict/dictionary/active/{id}\",
        \"/api/upms/duo-white-list/list\",
        \"/api/dfs/template-file/update\",
        \"/api/dfs/attach/list\",
        \"/api/upms/user-account/list\",
        \"/api/upms/user-account/register\",
        \"/api/log/request-used-time/list\",
        \"/api/log/alert-record/error-log-clear\",
        \"/api/dfs/attach/uploadByBase64StringList\",
        \"/api/dict/dictionary/list\",
        \"/api/upms/auth/login\",
        \"/v3/api-docs/{group}\",
        \"/api/dfs/attach/uploadByStringType\",
        \"/api/dict/config/detail/{id}\",
        \"/api/upms/route/list\",
        \"/api/upms/auth/kick-out\",
        \"/client/log/alert-record/write\",
        \"/api/dfs/oss/remove-bucket\",
        \"/api/log/alert-record/detail/{id}\",
        \"/api/log/request-path-pattern/update\",
        \"/client/upms/tenant/count-tenant\",
        \"/client/dict/region/save-batch\",
        \"/api/dict/lang-field/list-field-translation/{id}\",
        \"/api/dict/lang-field/load-cache-lang-field-result\",
        \"/api/log/third-record/list\",
        \"/api/upms/role/list-ignore-simple\",
        \"/client/upms/role/list-by-ids\",
        \"/client/dict/dictionary/save\",
        \"/client/dict/dictionary/delete-batch\",
        \"/client/dict/region/save\",
        \"/api/upms/business-role/remove/{id}\",
        \"/client/dfs/attach/uploadByBase64StringList\",
        \"/api/dfs/oss/remove-attach-files\",
        \"/api/dfs/template-file/insert\",
        \"/api/upms/tenant/init-cache\",
        \"/api/dict/lang-field/update\",
        \"/api/dict/config/remove/{id}\",
        \"/api/upms/tenant/insert\",
        \"/api/upms/user-account/remove/{id}\",
        \"/api/upms/route/update\",
        \"/api/dfs/attach/insert\",
        \"/api/dict/region/list-check-path\",
        \"/api/upms/role/init-cache\",
        \"/api/upms/business-role/update\",
        \"/client/dict/number-generate/warehouse-code\",
        \"/api/dict/config/initConfig\",
        \"/api/upms/user-account/detail/{id}\",
        \"/api/dict/dictionary/get-dictionary\",
        \"/api/log/operation-record/list\",
        \"/api/log/request-path-pattern/detail/{id}\",
        \"/api/upms/permissions/active/{id}\",
        \"/api/upms/user-tenant/update-status\",
        \"/api/upms/user-tenant/remove\",
        \"/v3/api-docs/swagger-config\",
        \"/v3/api-docs.yaml/{group}\",
        \"/api/upms/user-tenant/insert\",
        \"/api/log/request-path-pattern/active/{id}\",
        \"/api/dict/region/list-cache-all-tree/{limitDept}\",
        \"/api/log/request-path-pattern/export\",
        \"/api/dict/region/remove/{id}\",
        \"/api/dfs/oss/remove-attach-file\",
        \"/api/upms/user-ignore-tenant/detail/{id}\",
        \"/client/upms/role/get-by-roleName\",
        \"/api/upms/user-tenant/update\",
        \"/client/dict/dictionary/by-name\",
        \"/client/dict/region/delete-by-code\",
        \"/client/upms-api/permissions/list\",
        \"/client/upms/role/list-by-user-id/{userId}\",
        \"/api/log/alert-record/page\",
        \"/api/dict/number-generate-config/insert\",
        \"/api/upms/user-ignore-tenant/update-status\",
        \"/api/upms/partner-ship/list\",
        \"/api/upms/permissions/tree\",
        \"/api/dict/lang-field/list\",
        \"/client/upms/role/get-role-list/{id}\",
        \"/api/upms/user-account/update\",
        \"/api/upms/role-permissions/batch-save\",
        \"/swagger-ui.html\",
        \"/api/upms/permissions/detail/{id}\",
        \"/client/dict/number-generate/config/init\",
        \"/api/upms/role-permissions/refresh-cache/{roleId}\",
        \"/client/upms/permission/routes\",
        \"/api/log/third-record/login-log-clear\",
        \"/api/dfs/attach/upload-file\",
        \"/api/upms/permissions/remove\",
        \"/api/dict/region/list-cache-tree/{parentCode}\",
        \"/api/upms/route/insert\",
        \"/client/auth/token\",
        \"/api/dict/lang-field/insert\",
        \"/api/dict/lang-field/import-translation\",
        \"/client/dict/number-generate/zone-id\",
        \"/api/dict/dictionary/subset\",
        \"/api/dict/region/get-area/{depth}\",
        \"/api/upms/tenant/update-status\",
        \"/client/dict/number-generate/code\",
        \"/v3/api-docs\",
        \"/api/dfs/oss/fileDocAttach\",
        \"/client/upms/user/detail/{userId}\",
        \"/api/log/request-used-time/export\",
        \"/api/dfs/attach/detail/{id}\",
        \"/client/upms/user/get-by-user-name/{username}\",
        \"/api/log/request-path-pattern/remove/{id}\",
        \"/api/dict/dictionary/tree\",
        \"/api/upms/business-role/detail/{id}\",
        \"/client/dict/dictionary/save-batch\",
        \"/api/upms/auth/validate-username\",
        \"/api/dfs/attach/remove/{id}\",
        \"/api/dict/config/active/{id}\",
        \"/api/dict/dictionary/insert\",
        \"/client/log/log-record/client/log/log-record/write\",
        \"/api/dfs/oss/remove-file\",
        \"/api/upms/role/remove\",
        \"/api/upms/tenant/update\",
        \"/api/dfs/oss/get-file\",
        \"/api/upms/user-tenant/reset-password\",
        \"/api/upms/partner-ship/remove\",
        \"/api/dict/region/tree\",
        \"/api/dict/lang-field/detail/{id}\",
        \"/api/upms/user-ignore-tenant/remove\",
        \"/client/dict/config/save\",
        \"/api/upms/role/insert\",
        \"/api/upms/role/detail/{id}\",
        \"/api/upms/auth/select-tenant\",
        \"/api/dfs/template-file/active/{id}\",
        \"/api/upms/user-ignore-tenant/update\",
        \"/api/dict/lang-field/remove/{id}\",
        \"/api/upms/business-role/insert\",
        \"/api/dict/dictionary/update\",
        \"/api/dfs/template-file/list\",
        \"/api/dict/number-generate-config/detail/{id}\",
        \"/api/dict/region/list/{parentId}\",
        \"/api/upms/duo-white-list/insert\",
        \"/api/log/request-path-pattern/list\",
        \"/api/upms/partner-ship/update\",
        \"/api/upms/role/active/{id}\",
        \"/api/dict/dictionary/export\",
        \"/api/log/error-record/error-log-clear\",
        \"/api/dict/number-generate-config/export\",
        \"/client/upms/user/get-by-user-id/{userId}\",
        \"/api/dfs/oss/fileDoc\",
        \"/v3/api-docs.yaml\",
        \"/api/upms/auth/captcha\",
        \"/client/dict/dictionary/all\",
        \"/client/dict/dictionary/get-dictionary\",
        \"/api/dfs/attach/update\",
        \"/api/log/login-record/export\",
        \"/client/dfs/attach/uploadByBase64String\",
        \"/api/dict/region/insert\",
        \"/api/dfs/oss/get-file-path\",
        \"/client/dict/number-generate/warehouse-code-delimiter\",
        \"/api/upms/user-account/reSetPassword\",
        \"/api/dict/region/list-check\",
        \"/api/upms/partner-ship/detail/{id}\",
        \"/api/upms/role/update\",
        \"/api/upms/user-account/insert\",
        \"/api/dict/lang-field/add-field-translation\",
        \"/api/log/error-record/export\",
        \"/api/dfs/oss/copy-file\",
        \"/api/upms/tenant/detail/{id}\",
        \"/client/auth/validate-sign\",
        \"/api/upms/tenant/remove\",
        \"/api/dfs/oss/put-file-attach\",
        \"/client/upms/userAccount/listByUserAccount\",
        \"/api/dfs/oss/fileImage\",
        \"/api/dict/region/import-region\",
        \"/api/upms/user-account/userByEmail\",
        \"/client/dict/region/get-region\",
        \"/api/log/request-path-pattern/insert\",
        \"/api/log/login-record/login-log-clear\",
        \"/api/upms/tenant/list-my\"
    ]', 1, 1, '2024-07-10 07:12:16', 1, '2025-02-28 07:13:29', 0, 35, NULL);
INSERT INTO `upms_permissions`
VALUES (1843494292244459522, 'ProductManage', 'ProductManage', 'MENU', '/product-manage', 'PC', 'file-message', NULL, 1,
        NULL, 0, 1, '1843494292244459522,', '[
        \"/api/upms/permissions/insert\",
        \"/api/upms/permissions/routes\",
        \"/api/upms/permissions/update\",
        \"/api/upms/permissions/buttons\",
        \"/api/upms/permissions/tree\",
        \"/api/upms/permissions/remove\",
        \"/api/upms/permissions/detail/{id}\",
        \"/api/upms/permissions/active/{id}\"
    ]', 2, 1, '2024-10-08 11:31:04', 1858317764171608066, '2025-02-28 01:51:57', 0, 6, NULL);
INSERT INTO `upms_permissions`
VALUES (1843901413280772097, 'RequestManage', 'RequestManage', 'MENU', '/request-manage', 'PC', 'file-workflow', NULL,
        1, NULL, 0, 1, '1843901413280772097,', 'null', 8, 1, '2024-10-09 14:28:50', 1868588896309346306,
        '2024-12-26 02:19:27', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1843917431059312641, 'OToC', 'OToC', 'MENU', '/otoc', 'PC', 'file-message', NULL, 1, NULL, 0, 1,
        '1843917431059312641,', 'null', 5, 1, '2024-10-09 15:32:29', 1868588896309346306, '2024-12-26 02:17:57', 0, 2,
        NULL);
INSERT INTO `upms_permissions`
VALUES (1843917543319859201, 'Dashboard', 'Dashboard', 'MENU', '/dashboard/index', 'PC', 'file-message', NULL, 1, NULL,
        1843917431059312641, 2, '1843917431059312641,1843917543319859201,', 'null', 1, 1, '2024-10-09 15:32:55',
        1858317764171608066, '2024-12-24 09:08:11', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1843917867224985601, 'Normal', 'Normal', 'MENU', '/normal', 'PC', 'file-message', NULL, 1, NULL,
        1843917431059312641, 2, '1843917431059312641,1843917867224985601,', 'null', 2, 1, '2024-10-09 15:34:13',
        1858317764171608066, '2024-12-24 09:08:38', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1843917998439591938, 'OtcWorkOrder', 'OtcWorkOrder', 'MENU', '/OtcWorkOrder/index', 'PC', 'file-message', NULL,
        1, NULL, 1843917867224985601, 3, '1843917431059312641,1843917867224985601,1843917998439591938,', 'null', 1, 1,
        '2024-10-09 15:34:44', 1858317764171608066, '2024-12-24 09:10:23', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1843918321946259457, 'LogButton', 'LogButton', 'BUTTON', 'Log', 'PC', NULL, NULL, 1, NULL, 1843494292244459522,
        2, '1843494292244459522,1843918321946259457,', 'null', 1, 1, '2024-10-09 15:36:01', 1, '2024-10-09 15:43:26', 0,
        0, NULL);
INSERT INTO `upms_permissions`
VALUES (1844267412932849666, 'InBoundRequest', 'InBoundRequest', 'MENU', '/request-manage/inbound-request/index', 'PC',
        'file-message', NULL, 1, NULL, 1843901413280772097, 2, '1843901413280772097,1844267412932849666,', 'null', 1, 1,
        '2024-10-10 14:43:11', 1858317764171608066, '2024-12-24 09:13:11', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1844267586526703617, 'OTCRequest', 'OTCRequest', 'MENU', '/request-manage/outbound-to-c/index', 'PC',
        'file-message', NULL, 1, NULL, 1843901413280772097, 2, '1843901413280772097,1844267586526703617,', 'null', 2, 1,
        '2024-10-10 14:43:52', 1858317764171608066, '2025-03-13 02:24:09', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1844267695347920897, 'OTBRequest', 'OTBRequest', 'MENU', '/request-manage/outbound-to-b/index', 'PC',
        'file-message', NULL, 1, NULL, 1843901413280772097, 2, '1843901413280772097,1844267695347920897,', 'null', 3, 1,
        '2024-10-10 14:44:18', 1858317764171608066, '2025-03-13 02:24:19', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1844267829251076097, 'RI', 'RI', 'MENU', '/request-manage/routing-instruction/index', 'PC', 'file-message',
        NULL, 1, NULL, 1843901413280772097, 2, '1843901413280772097,1844267829251076097,', 'null', 4, 1,
        '2024-10-10 14:44:50', 1858317764171608066, '2025-03-13 02:05:05', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1844912863222755329, 'ControlCenter', 'ControlCenter', 'MENU', '/control-center', 'PC', 'file-system', NULL, 1,
        NULL, 0, 1, '1844912863222755329,', 'null', 7, 1, '2024-10-12 09:27:58', 1868588896309346306,
        '2024-12-26 02:18:11', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1844913538388258818, 'InboundRequests', 'InboundRequests', 'MENU', '/control-center/inbound-requests/index',
        'PC', 'file-announcement', NULL, 1, NULL, 1844912863222755329, 2, '1844912863222755329,1844913538388258818,',
        'null', 1, 1, '2024-10-12 09:30:39', 1858317764171608066, '2024-12-24 09:12:16', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1844914205790105601, 'OToCRequests', 'OTCRequest', 'MENU', '/control-center/otoc-requests/index', 'PC',
        'file-announcement', NULL, 1, NULL, 1844912863222755329, 2, '1844912863222755329,1844914205790105601,', 'null',
        2, 1, '2024-10-12 09:33:18', 1858317764171608066, '2025-03-13 02:29:57', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1844914348639711234, 'OToBRequests', 'OTBRequest', 'MENU', '/control-center/otob-requests/index', 'PC',
        'file-announcement', NULL, 1, NULL, 1844912863222755329, 2, '1844912863222755329,1844914348639711234,', 'null',
        3, 1, '2024-10-12 09:33:52', 1858317764171608066, '2025-03-13 02:31:58', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1844914555934797825, 'AuditRI', 'AuditRI', 'MENU', '/control-center/audit-ri/index', 'PC', 'file-announcement',
        NULL, 1, NULL, 1844912863222755329, 2, '1844912863222755329,1844914555934797825,', 'null', 4, 1,
        '2024-10-12 09:34:42', 1858317764171608066, '2024-12-24 09:12:53', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845032869218545666, 'BusinessConfig', 'BusinessConfig', 'MENU', '/business-config', 'PC', 'list', NULL, 1,
        NULL, 0, 1, '1845032869218545666,', 'null', 98, 1, '2024-10-12 17:24:50', 1858317764171608066,
        '2024-12-24 08:43:35', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845033077759340546, 'BusinessConfigDictionary', 'Dictionary', 'MENU', '/business-config/dictionary', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1845032869218545666, 2, '1845032869218545666,1845033077759340546,', 'null', 1,
        1, '2024-10-12 17:25:39', 1858317764171608066, '2024-12-24 08:43:29', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845033297431818241, 'SystemDictionaryIndexAddAs', 'AddSibling', 'BUTTON', 'SystemDictionaryIndexAddAs', 'PC',
        NULL, NULL, 1, NULL, 1845033077759340546, 3, '1845032869218545666,1845033077759340546,1845033297431818241,',
        'null', 1, 1, '2024-10-12 17:26:32', 1, '2024-12-09 02:46:59', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1845033445276839938, 'SystemDictionaryIndexAddLower', 'AddChild', 'BUTTON', 'SystemDictionaryIndexAddLower',
        'PC', NULL, NULL, 1, NULL, 1845033077759340546, 3,
        '1845032869218545666,1845033077759340546,1845033445276839938,', 'null', 2, 1, '2024-10-12 17:27:07', 1,
        '2024-12-09 02:47:19', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1845033530219884545, 'SystemDictionaryIndexEdit', 'Edit', 'BUTTON', 'SystemDictionaryIndexEdit', 'PC', NULL,
        NULL, 1, NULL, 1845033077759340546, 3, '1845032869218545666,1845033077759340546,1845033530219884545,', 'null',
        3, 1, '2024-10-12 17:27:27', 1, '2024-12-09 02:47:28', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1845033611069288450, 'SystemDictionaryIndexDelete', 'Delete', 'BUTTON', 'SystemDictionaryIndexDelete', 'PC',
        NULL, NULL, 1, NULL, 1845033077759340546, 3, '1845032869218545666,1845033077759340546,1845033611069288450,',
        'null', 4, 1, '2024-10-12 17:27:47', 1, '2024-12-09 02:47:37', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1845033826358718465, 'LogManage', 'LogManage', 'MENU', '/log-manage', 'PC', 'file-message', NULL, 1, NULL, 0, 1,
        '1845033826358718465,', 'null', 99, 1, '2024-10-12 17:28:38', 1858317764171608066, '2024-12-24 08:42:12', 0, 1,
        NULL);
INSERT INTO `upms_permissions`
VALUES (1845033958135361537, 'LogManageConfigureRules', 'ConfigRules', 'MENU', '/log-manage/configure-rules', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1845033826358718465, 2, '1845033826358718465,1845033958135361537,', 'null', 1,
        1, '2024-10-12 17:29:09', 1858317764171608066, '2024-12-24 08:42:06', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845034034471694338, 'LogManageUserOperation', 'UserLogs', 'MENU', '/log-manage/user-operation', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1845033826358718465, 2, '1845033826358718465,1845034034471694338,', 'null', 2,
        1, '2024-10-12 17:29:28', 1858317764171608066, '2024-12-24 08:42:27', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845034136225509378, 'LogManageSystemOperation', 'SystemLogs', 'MENU', '/log-manage/system-operation', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1845033826358718465, 2, '1845033826358718465,1845034136225509378,', 'null', 3,
        1, '2024-10-12 17:29:52', 1858317764171608066, '2024-12-24 08:42:44', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845034261463232514, 'LogManageErrorLog', 'ErrorLogs', 'MENU', '/log-manage/error-log', 'PC', 'file-sub-menu',
        NULL, 1, NULL, 1845033826358718465, 2, '1845033826358718465,1845034261463232514,', 'null', 4, 1,
        '2024-10-12 17:30:22', 1858317764171608066, '2024-12-24 08:42:59', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845034316513472514, 'LogConfigIndexAdd', 'Add', 'BUTTON', 'LogConfigIndexAdd', 'PC', 'plus', NULL, 1, NULL,
        1845033958135361537, 3, '1845033826358718465,1845033958135361537,1845034316513472514,', 'null', 1, 1,
        '2024-10-12 17:30:35', 1, '2024-12-09 02:48:04', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1845034673171918850, 'SystemConfig', 'Settings', 'MENU', '/system-config', 'PC', 'file-system', NULL, 1, NULL,
        0, 1, '1845034673171918850,', 'null', 10, 1, '2024-10-12 17:32:00', 1868588896309346306, '2024-12-26 02:21:53',
        0, 5, NULL);
INSERT INTO `upms_permissions`
VALUES (1845035283279572993, 'SystemConfigRole', 'Roles', 'MENU', '/system-config/role', 'PC', 'file-sub-menu', NULL, 1,
        NULL, 1845034673171918850, 2, '1845034673171918850,1845035283279572993,', '[
        \"/api/upms/business-role/list\",
        \"/api/upms/role/insert\",
        \"/api/upms/business-role/update\",
        \"/api/upms/role/init-cache\",
        \"/api/upms/business-role/insert\",
        \"/api/upms/role/remove\",
        \"/api/upms/role/update\",
        \"/api/upms/role/detail/{id}\",
        \"/api/upms/role/active/{id}\",
        \"/api/upms/business-role/detail/{id}\",
        \"/api/upms/business-role/remove/{id}\",
        \"/api/upms/role/list-simple\",
        \"/api/upms/role/list\",
        \"/client/upms/role/get-role-list/{id}\",
        \"/client/upms/role/list-by-user-id/{userId}\",
        \"/api/upms/role/list-ignore-simple\",
        \"/client/upms/role/list-by-ids\",
        \"/client/upms-api/role/list\",
        \"/client/upms/role/get-by-roleName\",
        \"/api/upms/role-permissions/detail-by-role/{roleId}\"
    ]', 3, 1, '2024-10-12 17:34:25', 1, '2025-02-13 12:21:42', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1845035418868838402, 'SystemConfigUserIndex', 'Users', 'MENU', '/system-config/user', 'PC', 'file-sub-menu',
        NULL, 1, NULL, 1845034673171918850, 2, '1845034673171918850,1845035418868838402,', 'null', 4, 1,
        '2024-10-12 17:34:58', 1858317764171608066, '2024-12-24 08:38:12', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845035516214439937, 'SystemConfigCodeRule', 'CodeRules', 'MENU', '/system-config/code-rules', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1845034673171918850, 2, '1845034673171918850,1845035516214439937,', 'null', 5,
        1, '2024-10-12 17:35:21', 1858317764171608066, '2024-12-24 08:37:51', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845731047488155650, 'InboundManage', 'InboundManage', 'MENU', '/inbound-manage', 'PC', 'file-site', NULL, 1,
        NULL, 0, 1, '1845731047488155650,', 'null', 4, 1, '2024-10-14 15:39:08', 1868588896309346306,
        '2024-12-26 02:17:48', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1845731302677999617, 'InboundDashboard', 'InboundDashboard', 'MENU', '/inbound-manage/inbound-dashboard/index',
        'PC', 'file-lock', NULL, 1, NULL, 1845731047488155650, 2, '1845731047488155650,1845731302677999617,', 'null', 1,
        1, '2024-10-14 15:40:09', 1858317764171608066, '2024-12-24 09:04:30', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845731764164685826, 'InboundWorkOrder', 'InboundWorkOrder', 'MENU', '/inbound-manage/inbounce-order/index',
        'PC', 'file-message', NULL, 1, NULL, 1845731047488155650, 2, '1845731047488155650,1845731764164685826,', 'null',
        2, 1, '2024-10-14 15:41:59', 1858317764171608066, '2024-12-24 09:06:24', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845732124392484866, 'PutAwaySlip', 'PutAwaySlip', 'MENU', '/inbound-manage/put-away-slip/index', 'PC',
        'file-jmreport', NULL, 1, NULL, 1845731047488155650, 2, '1845731047488155650,1845732124392484866,', 'null', 3,
        1, '2024-10-14 15:43:25', 1858317764171608066, '2024-12-24 09:06:35', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1845732399232643073, 'Pallets', 'Pallets', 'MENU', '/inbound-manage/pallets/index', 'PC', 'file-site', NULL, 1,
        NULL, 1845731047488155650, 2, '1845731047488155650,1845732399232643073,', 'null', 5, 1, '2024-10-14 15:44:31',
        1858317764171608066, '2024-12-24 09:07:36', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1846391298629627906, 'WarehouseManage', 'WarehouseManage', 'MENU', '/warehouse-manage', 'PC', 'file-version',
        NULL, 1, NULL, 0, 1, '1846391298629627906,', 'null', 3, 1, '2024-10-16 11:22:45', 1868588896309346306,
        '2024-12-26 02:16:26', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1846392351609647106, 'BinLocationList', 'BinLocationList', 'MENU', '/warehouse-manage/binLocation-list', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1846391298629627906, 2, '1846391298629627906,1846392351609647106,', 'null', 1,
        1, '2024-10-16 11:26:56', 1, '2024-12-05 14:03:49', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1846392493272264706, 'InventoryList', 'InventoryList', 'MENU', '/warehouse-manage/inventory-list', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1846391298629627906, 2, '1846391298629627906,1846392493272264706,', 'null', 2,
        1, '2024-10-16 11:27:29', 1, '2024-12-05 14:03:43', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1846502061776105473, 'PartnerSettings', 'PartnerSettings', 'MENU', '/partner-setting', 'PC', 'file-lock', NULL,
        1, NULL, 0, 1, '1846502061776105473,', 'null', 9, 1, '2024-10-16 18:42:53', 1868588896309346306,
        '2024-12-26 02:21:49', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1846502403746099201, 'Partner', 'Partner', 'MENU', '/partner-setting/partner/index', 'PC', 'file-skin', NULL, 1,
        NULL, 1846502061776105473, 2, '1846502061776105473,1846502403746099201,', '[
        \"/api/upms/user-tenant/list\"
    ]', 1, 1, '2024-10-16 18:44:14', 1, '2025-02-12 03:55:20', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1846502507030835201, 'AppAccessConfig', 'AppAccessConfig', 'MENU', '/partner-setting/app-access-config/index',
        'PC', 'file-jmreport', NULL, 1, NULL, 1846502061776105473, 2, '1846502061776105473,1846502507030835201,',
        'null', 2, 1, '2024-10-16 18:44:39', 1858317764171608066, '2024-12-24 08:41:21', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1846502845603442689, 'PartnerAppAccessConfig', 'PartnerAppAccessConfig', 'MENU',
        '/partner-setting/partner-app-access-config/index', 'PC', 'file-short-cuts', NULL, 1, NULL, 1846502061776105473,
        2, '1846502061776105473,1846502845603442689,', 'null', 3, 1, '2024-10-16 18:45:59', 1858317764171608066,
        '2024-12-24 08:41:35', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847095688986423297, 'ContainerShipment', 'ContainerShipment', 'MENU', '/container-shipment', 'PC',
        'file-suggestion', NULL, 1, NULL, 0, 1, '1847095688986423297,', 'null', 11, 1, '2024-10-18 10:01:44',
        1868588896309346306, '2024-12-26 02:21:58', 0, 4, NULL);
INSERT INTO `upms_permissions`
VALUES (1847097252333547521, 'ContainerTypeConfig', 'ContainerTypeConfig', 'MENU',
        '/container-shipment/container-type-config/index', 'PC', 'bell-filled', NULL, 1, NULL, 1847095688986423297, 2,
        '1847095688986423297,1847097252333547521,', 'null', 1, 1, '2024-10-18 10:07:57', 1858317764171608066,
        '2024-12-24 08:43:58', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847097847694028801, 'ShipmentPlan', 'ShipmentPlan', 'MENU', '/container-shipment/shipment-plan/index', 'PC',
        'file-short-cuts', NULL, 1, NULL, 1847095688986423297, 2, '1847095688986423297,1847097847694028801,', 'null', 2,
        1, '2024-10-18 10:10:19', 1858317764171608066, '2024-12-24 08:44:57', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847098209431777282, 'ContainerManage', 'ContainerManage', 'MENU', '/container-shipment/container-manage/index',
        'PC', 'file-jmreport', NULL, 1, NULL, 1847095688986423297, 2, '1847095688986423297,1847098209431777282,',
        'null', 3, 1, '2024-10-18 10:11:45', 1858317764171608066, '2024-12-24 08:45:11', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847098666392809473, 'PickingSlip', 'PickingSlip', 'MENU', '/container-shipment/picking-slip/index', 'PC',
        'list', NULL, 1, NULL, 1847095688986423297, 2, '1847095688986423297,1847098666392809473,', 'null', 4, 1,
        '2024-10-18 10:13:34', 1858317764171608066, '2024-12-24 08:46:00', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847099059373928450, 'Packing', 'Packing', 'MENU', '/container-shipment/packing/index', 'PC', 'file-lock', NULL,
        1, NULL, 1847095688986423297, 2, '1847095688986423297,1847099059373928450,', 'null', 5, 1,
        '2024-10-18 10:15:08', 1858317764171608066, '2024-12-24 08:46:13', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847154856980180993, 'OtcPickingSlip', 'OtcPickingSlip', 'MENU', '/otc-picking-slip/index', 'PC', 'list', NULL,
        1, 'OTCPickingSlip', 1843917867224985601, 3, '1843917431059312641,1843917867224985601,1847154856980180993,',
        'null', 2, 1, '2024-10-18 13:56:51', 1868588896309346306, '2025-03-27 01:31:09', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847208874985189377, 'SlapAndGo', 'SlapAndGo', 'MENU', '/SlapAndGo', 'PC', 'position', NULL, 1, 'SlapAndGo',
        1843917867224985601, 3, '1843917431059312641,1843917867224985601,1847208874985189377,', 'null', 3, 1,
        '2024-10-18 17:31:30', 1868588896309346306, '2025-03-27 01:31:22', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847209034469404673, 'SOSP', 'SOSP', 'MENU', '/SOSP', 'PC', 'position', NULL, 1, 'SOSP', 1843917867224985601, 3,
        '1843917431059312641,1843917867224985601,1847209034469404673,', 'null', 4, 1, '2024-10-18 17:32:08',
        1858317764171608066, '2024-12-24 09:11:03', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847209155244388354, 'SOMP', 'SOMP', 'MENU', '/SOMP', 'PC', 'position', NULL, 1, 'SOMP', 1843917867224985601, 3,
        '1843917431059312641,1843917867224985601,1847209155244388354,', 'null', 5, 1, '2024-10-18 17:32:37',
        1858317764171608066, '2024-12-24 09:11:12', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847209845777821698, 'OtcPackages', 'OtcPackages', 'MENU', '/OtcPackages', 'PC', 'file-lock', NULL, 1,
        'Packages', 1843917867224985601, 3, '1843917431059312641,1843917867224985601,1847209845777821698,', 'null', 6,
        1, '2024-10-18 17:35:21', 1858317764171608066, '2024-12-24 09:11:24', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1847211623097364482, 'BuildPallet', 'BuildPallet', 'MENU', '/BuildPallet', 'PC', 'file-short-cuts', NULL, 1,
        'BuildPallet', 1843917867224985601, 3, '1843917431059312641,1843917867224985601,1847211623097364482,', 'null',
        7, 1, '2024-10-18 17:42:25', 1858317764171608066, '2024-12-24 09:11:35', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848183056317345793, 'OTCShipStationConfig', 'OTCShipStationConfig', 'MENU', '/warehouse-manage/ship-station',
        'PC', 'file-sub-menu', NULL, 1, NULL, 1846391298629627906, 2, '1846391298629627906,1848183056317345793,', '[
        \"/api/dict/lang-field/import-translation\",
        \"/api/dfs/oss/put-file-attachList\",
        \"/client/upms/user/detail/{userId}\",
        \"/client/dict/number-generate/warehouse-code\"
    ]', 4, 1, '2024-10-21 10:02:33', 1858317764171608066, '2025-02-28 01:50:51', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1848183392889270273, 'WarehouseSetting', 'WarehouseSetting', 'MENU', '/warehouse-manage/warehouse-setting',
        'PC', 'file-sub-menu', NULL, 1, NULL, 1846391298629627906, 2, '1846391298629627906,1848183392889270273,',
        'null', 3, 1, '2024-10-21 10:03:53', 1858317764171608066, '2024-12-24 08:46:51', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848184522088181762, 'PalletEmptyProfile', 'PalletEmptyProfile', 'MENU',
        '/warehouse-manage/pallet-empty-profile', 'PC', 'file-sub-menu', NULL, 1, NULL, 1846391298629627906, 2,
        '1846391298629627906,1848184522088181762,', 'null', 6, 1, '2024-10-21 10:08:22', 1858317764171608066,
        '2024-12-24 08:47:49', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848185556097363969, 'ProfileWarehouse', 'ProfileWarehouse', 'MENU', '/warehouse-manage/profile-warehouse',
        'PC', 'file-sub-menu', NULL, 1, NULL, 1846391298629627906, 2, '1846391298629627906,1848185556097363969,',
        'null', 7, 1, '2024-10-21 10:12:29', 1858317764171608066, '2024-12-24 08:49:00', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848202616194002945, 'ShipPallet', 'ShipPallet', 'MENU', '/ShipPallet', 'PC', 'promotion', NULL, 1,
        'ShipPallet', 1843917867224985601, 3, '1843917431059312641,1843917867224985601,1848202616194002945,', 'null', 8,
        1, '2024-10-21 11:20:16', 1858317764171608066, '2024-12-24 09:11:46', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848643017820401665, 'Ship', 'Ship', 'MENU', '/system-config/ship', 'PC', 'help-filled', NULL, 1, NULL,
        1845034673171918850, 2, '1845034673171918850,1848643017820401665,', 'null', 1, 1, '2024-10-22 16:30:16',
        1858317764171608066, '2024-12-24 08:39:06', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848643679706738689, 'ShipCarrierAPI', 'ShipCarrierAPI', 'MENU', '/system-config/ship/ship-carrier-api', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1848643017820401665, 3,
        '1845034673171918850,1848643017820401665,1848643679706738689,', 'null', 2, 1, '2024-10-22 16:32:54',
        1858317764171608066, '2024-12-24 08:36:26', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1848645723008069634, 'ShipApiProfile', 'ShipApiProfile', 'MENU', '/system-config/ship/ship-api-profile', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1848643017820401665, 3,
        '1845034673171918850,1848643017820401665,1848645723008069634,', 'null', 3, 1, '2024-10-22 16:41:01',
        1858317764171608066, '2024-12-24 08:36:40', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1848647834441674754, 'Auth', 'Auth', 'MENU', '/system-config/auth', 'PC', 'file-announcement', NULL, 1, NULL,
        1845034673171918850, 2, '1845034673171918850,1848647834441674754,', 'null', 2, 1, '2024-10-22 16:49:25',
        1858317764171608066, '2024-12-24 08:39:21', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1848647990302011394, 'DuoWhiteList', 'DuoWhiteList', 'MENU', '/system-config/auth/duowhite-list', 'PC',
        'file-sub-menu', NULL, 1, NULL, 1848647834441674754, 3,
        '1845034673171918850,1848647834441674754,1848647990302011394,', 'null', 1, 1, '2024-10-22 16:50:02',
        1864647628398190595, '2025-01-16 07:25:58', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1850060908306427905, 'SystemMenuConfig', 'Menu', 'MENU', '/system-config/menu', 'PC', 'file-sub-menu', NULL, 1,
        NULL, 1845034673171918850, 2, '1845034673171918850,1850060908306427905,', 'null', 100, 1, '2024-10-26 14:24:28',
        1858317764171608066, '2024-12-24 08:34:18', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1851499059734441985, 'Prep', 'Prep', 'MENU', '/prep', 'PC', 'file-sub-menu', NULL, 1, 'Prep',
        1843917431059312641, 2, '1843917431059312641,1851499059734441985,', 'null', 3, 1, '2024-10-30 13:39:10',
        1858317764171608066, '2024-12-24 09:08:53', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1851499263141408770, 'OtcPrepWorkOrder', 'OtcPrepWorkOrder', 'MENU', '/OtcPrepWorkOrder', 'PC', 'list', NULL, 1,
        'PrepWorkOrder', 1851499059734441985, 3, '1843917431059312641,1851499059734441985,1851499263141408770,', 'null',
        1, 1, '2024-10-30 13:39:58', 1858317764171608066, '2024-12-24 09:09:09', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1851499474001653761, 'OtcPrepPickingSlip', 'OtcPrepPickingSlip', 'MENU', '/OtcPrepPickingSlip', 'PC',
        'file-exit', NULL, 1, 'PrepPickingSlip', 1851499059734441985, 3,
        '1843917431059312641,1851499059734441985,1851499474001653761,', 'null', 2, 1, '2024-10-30 13:40:48',
        1858317764171608066, '2024-12-24 09:09:22', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1851499845604405249, 'PrepSlapAndGo', 'PrepSlapAndGo', 'MENU', '/PrepSlapAndGo', 'PC', 'clipboard-checked',
        NULL, 1, 'PrepSlapAndGo', 1851499059734441985, 3,
        '1843917431059312641,1851499059734441985,1851499845604405249,', 'null', 3, 1, '2024-10-30 13:42:17',
        1858317764171608066, '2024-12-24 09:09:33', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1851499995835985921, 'PrepSOSP', 'PrepSOSP', 'MENU', '/PrepSOSP', 'PC', 'file-short-cuts', NULL, 1, 'PrepSOSP',
        1851499059734441985, 3, '1843917431059312641,1851499059734441985,1851499995835985921,', 'null', 4, 1,
        '2024-10-30 13:42:53', 1858317764171608066, '2024-12-24 09:09:44', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1851500091612917761, 'PrepSOMP', 'PrepSOMP', 'MENU', '/PrepSOMP', 'PC', 'file-announcement', NULL, 1, NULL,
        1851499059734441985, 3, '1843917431059312641,1851499059734441985,1851500091612917761,', 'null', 5, 1,
        '2024-10-30 13:43:16', 1858317764171608066, '2024-12-24 09:09:54', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1851500235876003842, 'PrepMultiBox', 'PrepMultiBox', 'MENU', '/PrepMultiBox', 'PC', 'daoru', NULL, 1,
        'PrepMultiBox', 1851499059734441985, 3, '1843917431059312641,1851499059734441985,1851500235876003842,', 'null',
        6, 1, '2024-10-30 13:43:50', 1858317764171608066, '2024-12-24 09:10:05', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853244611903942658, 'Otb', 'Otb', 'MENU', '/otob', 'PC', 'file-announcement', NULL, 1, 'Otb', 0, 1,
        '1853244611903942658,', 'null', 6, 1, '2024-11-04 09:15:22', 1868588896309346306, '2024-12-26 02:18:01', 0, 3,
        NULL);
INSERT INTO `upms_permissions`
VALUES (1853244755474968577, 'OtbDashboard', 'OtbDashboard', 'MENU', '/otb-dashboard/index', 'PC', 'file-jmreport',
        NULL, 1, 'OtbDashboard', 1853244611903942658, 2, '1853244611903942658,1853244755474968577,', 'null', 1, 1,
        '2024-11-04 09:15:56', 1858317764171608066, '2024-12-24 08:51:13', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853245052423303170, 'OtbNormal', 'OtbNormal', 'MENU', '/otb-normal', 'PC', 'file-announcement', NULL, 1, NULL,
        1853244611903942658, 2, '1853244611903942658,1853245052423303170,', 'null', 2, 1, '2024-11-04 09:17:07',
        1858317764171608066, '2024-12-24 09:00:44', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853245203929952258, 'OtbPrep', 'OtbPrep', 'MENU', '/otb-prep', 'PC', 'file-company', NULL, 1, NULL,
        1853244611903942658, 2, '1853244611903942658,1853245203929952258,', 'null', 3, 1, '2024-11-04 09:17:43',
        1858317764171608066, '2024-12-24 09:00:31', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853245336075694081, 'OtbWorkorder', 'OtbWorkorder', 'MENU', '/otb-workorder/index', 'PC', 'list', NULL, 1,
        'OtbWorkorder', 1853245052423303170, 3, '1853244611903942658,1853245052423303170,1853245336075694081,', 'null',
        1, 1, '2024-11-04 09:18:14', 1858317764171608066, '2024-12-24 09:01:04', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853245548101955585, 'OtbPickingSlip', 'OtbPickingSlip', 'MENU', '/otb-picking-slip/index', 'PC',
        'file-short-cuts', NULL, 1, 'OtbPickingSlip', 1853245052423303170, 3,
        '1853244611903942658,1853245052423303170,1853245548101955585,', 'null', 2, 1, '2024-11-04 09:19:05',
        1858317764171608066, '2024-12-24 09:01:17', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853245980253679617, 'OtbPrepWorkorder', 'OtbPrepWorkorder', 'MENU', '/otb-prep-workorder', 'PC',
        'clipboard-checked', NULL, 1, 'OtbPrepWorkorder', 1853245203929952258, 3,
        '1853244611903942658,1853245203929952258,1853245980253679617,', 'null', 1, 1, '2024-11-04 09:20:48',
        1858317764171608066, '2024-12-24 09:00:04', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853246102156931074, 'OtbPrepPickingSlip', 'OtbPrepPickingSlip', 'MENU', '/otb-prep-picking-slip', 'PC',
        'file-site', NULL, 1, 'OtbPrepPickingSlip', 1853245203929952258, 3,
        '1853244611903942658,1853245203929952258,1853246102156931074,', 'null', 2, 1, '2024-11-04 09:21:17',
        1858317764171608066, '2024-12-24 08:59:47', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853246362371551234, 'BuildPackages', 'BuildPackages', 'MENU', '/otb-build-packages/index', 'PC', 'file-lock',
        NULL, 1, NULL, 1853245052423303170, 3, '1853244611903942658,1853245052423303170,1853246362371551234,', 'null',
        3, 1, '2024-11-04 09:22:19', 1858317764171608066, '2024-12-24 09:01:34', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853246528830894081, 'OtbPackages', 'OtbPackages', 'MENU', '/otb-packages', 'PC', 'file-lock', NULL, 1, NULL,
        1853245052423303170, 3, '1853244611903942658,1853245052423303170,1853246528830894081,', 'null', 4, 1,
        '2024-11-04 09:22:59', 1858317764171608066, '2024-12-24 09:01:47', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853246720128905217, 'OtbBuildPallets', 'OtbBuildPallets', 'MENU', '/otb-build-pallets', 'PC', 'file-monitor',
        NULL, 1, 'OtbBuildPallets', 1853245052423303170, 3,
        '1853244611903942658,1853245052423303170,1853246720128905217,', 'null', 5, 1, '2024-11-04 09:23:44',
        1858317764171608066, '2025-01-08 02:44:26', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1853246833861652482, 'OtbPallets', 'OtbPallets', 'MENU', '/otb-pallets', 'PC', 'file-announcement', NULL, 1,
        'OtbPallets', 1853245052423303170, 3, '1853244611903942658,1853245052423303170,1853246833861652482,', 'null', 6,
        1, '2024-11-04 09:24:12', 1858317764171608066, '2024-12-24 09:02:28', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853247008336310274, 'BuildShipmentLTL', 'BuildShipmentLTL', 'MENU', '/otb-build-shipment-ltl', 'PC',
        'file-exit', NULL, 1, 'BuildShipmentLTL', 1853245052423303170, 3,
        '1853244611903942658,1853245052423303170,1853247008336310274,', 'null', 7, 1, '2024-11-04 09:24:53',
        1858317764171608066, '2024-12-24 09:03:17', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853247141664845825, 'BuildSmallParcel', 'BuildSmallParcel', 'MENU', '/otb-build-small-parcel', 'PC',
        'info-filled', NULL, 1, 'BuildSmallParcel', 1853245052423303170, 3,
        '1853244611903942658,1853245052423303170,1853247141664845825,', 'null', 8, 1, '2024-11-04 09:25:25',
        1858317764171608066, '2024-12-24 09:03:31', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853247236053463042, 'OtbShipment', 'OtbShipment', 'MENU', '/otb-shipment', 'PC', 'file-home', NULL, 1,
        'OtbShipment', 1853245052423303170, 3, '1853244611903942658,1853245052423303170,1853247236053463042,', 'null',
        9, 1, '2024-11-04 09:25:47', 1858317764171608066, '2024-12-24 09:03:41', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853247347085078529, 'OtbShipLTL', 'OtbShipLTL', 'MENU', '/otb-ship-ltl', 'PC', 'file-workflow', NULL, 1, NULL,
        1853245052423303170, 3, '1853244611903942658,1853245052423303170,1853247347085078529,', 'null', 10, 1,
        '2024-11-04 09:26:14', 1858317764171608066, '2024-12-24 09:03:52', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853247454228574209, 'OtbShipSmallParcel', 'OtbShipSmallParcel', 'MENU', '/ship-small-parcel', 'PC',
        'file-system-role-index', NULL, 1, 'OtbShipSmallParcel', 1853245052423303170, 3,
        '1853244611903942658,1853245052423303170,1853247454228574209,', 'null', 11, 1, '2024-11-04 09:26:39',
        1858317764171608066, '2024-12-24 09:04:06', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1853247613880561665, 'OtbPrePacking', 'OtbPrePacking', 'MENU', '/otb-pre-packing', 'PC', 'help-filled', NULL, 1,
        NULL, 1853245203929952258, 3, '1853244611903942658,1853245203929952258,1853247613880561665,', 'null', 3, 1,
        '2024-11-04 09:27:17', 1858317764171608066, '2024-12-24 08:59:32', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1862302952037859330, 'HazmatButton', 'button:somp操作危险品', 'BUTTON', 'LOG', 'PC', 'select', 'ALI_ICON', 1,
        NULL, 1847209034469404673, 4,
        '1843917431059312641,1843917867224985601,1847209034469404673,1862302952037859330,', 'null', 1, 1,
        '2024-11-29 01:09:58', 1, '2024-11-29 01:09:58', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1864962506657759234, 'ProfilePartner', 'ProfilePartner', 'MENU', '/system-config/profile-partner', 'PC',
        'file-jmreport', 'ALI_ICON', 1, NULL, 1845034673171918850, 2, '1845034673171918850,1864962506657759234,',
        'null', 7, 1, '2024-12-06 09:18:06', 1858317764171608066, '2024-12-24 08:35:12', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1864962933256224769, 'ProfileSystem', 'ProfileSystem', 'MENU', '/system-config/profile-system', 'PC',
        'file-site', 'ALI_ICON', 1, NULL, 1845034673171918850, 2, '1845034673171918850,1864962933256224769,', 'null', 8,
        1, '2024-12-06 09:19:47', 1858317764171608066, '2024-12-24 08:35:00', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1868900847828545537, 'PrinterSettings', 'PrinterSettings', 'MENU', '/system-config/printer-setting', 'PC',
        'file-sub-menu', 'ALI_ICON', 1, '打印配置', 1845034673171918850, 2, '1845034673171918850,1868900847828545537,',
        'null', 101, 1, '2024-12-17 06:07:39', 1858317764171608066, '2024-12-24 08:34:40', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1872237005786710018, 'PalletTemplate', 'PalletTemplate', 'MENU', '/inbound-manage/pallet-template/index', 'PC',
        'file-sub-menu', 'ALI_ICON', 1, NULL, 1846391298629627906, 2, '1846391298629627906,1872237005786710018,',
        'null', 5, 1858317764171608066, '2024-12-26 11:04:21', 1858317764171608066, '2024-12-26 11:05:14', 0, 2, NULL);
INSERT INTO `upms_permissions`
VALUES (1878635012754698242, 'PalletsRollbackButton', 'PalletsRollback回滚', 'BUTTON', 'PalletsRollback', 'PC',
        'select', 'ALI_ICON', 1, NULL, 1845732399232643073, 3,
        '1845731047488155650,1845732399232643073,1878635012754698242,', 'null', 1, 1858317764171608066,
        '2025-01-13 02:47:45', 1870303799224143874, '2025-01-23 05:54:23', 0, 12, NULL);
INSERT INTO `upms_permissions`
VALUES (1878635735592660993, 'PutAwaySlipRollbackButton', 'PutAwaySlipRollback回滚', 'BUTTON',
        'PutAwaySlipRollbackButton', 'PC', 'select', 'ALI_ICON', 1, NULL, 1845732124392484866, 3,
        '1845731047488155650,1845732124392484866,1878635735592660993,', 'null', 1, 1858317764171608066,
        '2025-01-13 02:50:37', 1870303799224143874, '2025-01-23 05:54:20', 0, 13, NULL);
INSERT INTO `upms_permissions`
VALUES (1878646646941061121, 'ConfirmWithChangeProductButton', 'ConfirmWithChangeProduct改变产品', 'BUTTON',
        'ConfirmWithChangeProductButton', 'PC', 'select', 'ALI_ICON', 1, '确认改变产品按钮', 1845731764164685826, 3,
        '1845731047488155650,1845731764164685826,1878646646941061121,', 'null', 1, 1858317764171608066,
        '2025-01-13 03:33:59', 1864647628398190595, '2025-03-11 07:31:53', 0, 5, NULL);
INSERT INTO `upms_permissions`
VALUES (1892118907621801986, 'BatchAuditInBoundButton', 'BatchAuditInBoundButton', 'BUTTON', 'BatchAuditInBoundButton',
        'PC', 'select', 'ALI_ICON', 1, 'Control Centerl  Inbound Requests  批量审核按钮', 1844913538388258818, 3,
        '1844912863222755329,1844913538388258818,1892118907621801986,', '[
        \"/api/dfs/oss/fileDoc\"
    ]', 1, 1858317764171608066, '2025-02-19 07:47:56', 1858317764171608066, '2025-02-19 07:47:56', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1892122143355371522, 'BatchAuditOTBButton', 'BatchAuditOTBButton', 'BUTTON', 'BatchAuditOTBButton', 'PC',
        'select', 'ALI_ICON', 1, 'Control Center OtoB Requests 批量审核按钮', 1844914348639711234, 3,
        '1844912863222755329,1844914348639711234,1892122143355371522,', '[
        \"/api/dfs/oss/fileDoc\"
    ]', 1, 1858317764171608066, '2025-02-19 08:00:48', 1858317764171608066, '2025-02-19 08:00:48', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1901838492307632130, 'FeeConfig', 'FeeConfig', 'MENU', '/fee-config', 'PC', 'list', 'ALI_ICON', 1, NULL, 0, 1,
        '1901838492307632130,', '[]', 7, 1858317764171608066, '2025-03-18 03:30:06', 1858317764171608066,
        '2025-03-18 03:43:10', 0, 3, NULL);
INSERT INTO `upms_permissions`
VALUES (1901839073516531713, 'FeeConfigInbound', 'FeeConfigInbound', 'MENU', '/fee-config/inbound/index', 'PC', 'daoru',
        'ALI_ICON', 1, NULL, 1901838492307632130, 2, '1901838492307632130,1901839073516531713,', '[]', 1,
        1858317764171608066, '2025-03-18 03:32:24', 1868588896309346306, '2025-04-07 06:07:01', 0, 1, NULL);
INSERT INTO `upms_permissions`
VALUES (1904114544831827969, 'FeeConfigOtc', 'FeeConfigOtc', 'MENU', 'FeeConfigOtc', 'PC', 'file-workflow', 'ALI_ICON',
        1, NULL, 1901838492307632130, 2, '1901838492307632130,1904114544831827969,', '[]', 2, 1868588896309346306,
        '2025-03-24 10:14:19', 1868588896309346306, '2025-04-07 06:06:38', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1904114911866982401, 'FeeConfigOtb', 'FeeConfigOtb', 'MENU', 'FeeConfigOtb', 'PC', 'help-filled', 'ALI_ICON', 1,
        NULL, 1901838492307632130, 2, '1901838492307632130,1904114911866982401,', '[]', 2, 1868588896309346306,
        '2025-03-24 10:15:46', 1868588896309346306, '2025-03-24 10:15:47', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1904115096554770433, 'FeeConfigStorage', 'FeeConfigStorage', 'MENU', 'FeeConfigStorage', 'PC',
        'file-announcement', 'ALI_ICON', 1, NULL, 1901838492307632130, 2, '1901838492307632130,1904115096554770433,',
        '[]', 3, 1868588896309346306, '2025-03-24 10:16:30', 1868588896309346306, '2025-03-24 10:16:31', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1904826707909218305, 'Quote', 'Quote', 'MENU', 'Quote', 'PC', 'file-jmreport', 'ALI_ICON', 1, NULL,
        1901838492307632130, 2, '1901838492307632130,1904826707909218305,', '[]', 5, 1868588896309346306,
        '2025-03-26 09:24:12', 1868588896309346306, '2025-04-07 06:07:39', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1905075950980894722, 'Supplier', 'Supplier', 'MENU', 'Supplier', 'PC', 'file-customer', 'ALI_ICON', 1, NULL,
        1901838492307632130, 2, '1901838492307632130,1905075950980894722,', '[]', 5, 1868588896309346306,
        '2025-03-27 01:54:36', 1868588896309346306, '2025-03-27 01:54:36', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1906533253800656897, 'BatchAuditOTCButton', 'BatchAuditOTCButton', 'BUTTON', 'BatchAuditOTCButton', 'PC',
        'select', 'ALI_ICON', 1, 'Control Center OtoC Requests 批量审核按钮', 1844914205790105601, 3,
        '1844912863222755329,1844914205790105601,1906533253800656897,', '[]', 1, 1905183483661500418,
        '2025-03-31 02:25:24', 1905183483661500418, '2025-03-31 02:25:47', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1909497822039871489, 'OtcPutAwaySlip', 'OtcPutAwaySlip', 'MENU', '/OtcPutAwaySlip', 'PC', 'file-sub-menu',
        'ALI_ICON', 1, NULL, 1843917867224985601, 3, '1843917431059312641,1843917867224985601,1909497822039871489,',
        '[]', 9, 1868588896309346306, '2025-04-08 06:45:32', 1868588896309346306, '2025-04-08 06:52:57', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1911623012413304834, 'Fee', 'Fee', 'MENU', 'Fee', 'PC', 'file-site', 'ALI_ICON', 1, NULL, 0, 1,
        '1911623012413304834,', '[]', 7, 1868588896309346306, '2025-04-14 03:30:17', 1868588896309346306,
        '2025-04-14 03:30:29', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1911623374062972930, 'FeeOriginalData', 'FeeOriginalData', 'MENU', '/fee/original-data/index', 'PC', 'list',
        'ALI_ICON', 1, NULL, 1911623012413304834, 2, '1911623012413304834,1911623374062972930,', '[]', 1,
        1868588896309346306, '2025-04-14 03:31:43', 1868588896309346306, '2025-04-14 03:43:11', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1911623533106786306, 'FeeOtc', 'FeeOtc', 'MENU', '/fee/otc/index', 'PC', 'file-announcement', 'ALI_ICON', 1,
        NULL, 1911623012413304834, 2, '1911623012413304834,1911623533106786306,', '[]', 2, 1868588896309346306,
        '2025-04-14 03:32:21', 1868588896309346306, '2025-04-14 03:32:21', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1911623630016180226, 'FeeOtb', 'FeeOtb', 'MENU', '/fee/otb/index', 'PC', 'promotion', 'ALI_ICON', 1, NULL,
        1911623012413304834, 2, '1911623012413304834,1911623630016180226,', '[]', 3, 1868588896309346306,
        '2025-04-14 03:32:44', 1868588896309346306, '2025-04-14 03:32:45', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1911623714812424193, 'FeeStorage', 'FeeStorage', 'MENU', '/fee/storage/index', 'PC', 'info-filled', 'ALI_ICON',
        1, NULL, 1911623012413304834, 2, '1911623012413304834,1911623714812424193,', '[]', 4, 1868588896309346306,
        '2025-04-14 03:33:05', 1868588896309346306, '2025-04-14 03:33:05', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1911624312609796097, 'FeeInbound', 'FeeInbound', 'MENU', '/fee/inbound/index', 'PC', 'daoru', 'ALI_ICON', 1,
        NULL, 1911623012413304834, 2, '1911623012413304834,1911624312609796097,', '[]', 1, 1868588896309346306,
        '2025-04-14 03:35:27', 1868588896309346306, '2025-04-14 03:35:27', 0, 0, NULL);
INSERT INTO `upms_permissions`
VALUES (1912086132989227010, 'OtcPrepPutAwaySlip', 'OtcPrepPutAwaySlip', 'MENU', '/OtcPrepPutAwaySlip', 'PC', 'menu',
        'ALI_ICON', 1, 'OtcPrepPutAwaySlip', 1851499059734441985, 3,
        '1843917431059312641,1851499059734441985,1912086132989227010,', '[]', 7, 1868588896309346306,
        '2025-04-15 10:10:34', 1868588896309346306, '2025-04-15 10:10:34', 0, 0, NULL);

-- ----------------------------
-- Table structure for upms_role
-- ----------------------------
DROP TABLE IF EXISTS `upms_role`;
CREATE TABLE `upms_role`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键',
    `role_code`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '角色编号',
    `role_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色名称',
    `role_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '角色名称英文',
    `description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `state`        tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `role_group`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '分组信息，同一分组下的配置编号唯一',
    `sorting`      int                                                    NOT NULL COMMENT '排序字段',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`  datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`      bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除原因',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `INX_REGION_CODE` (`role_code` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '系统角色信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_role
-- ----------------------------
INSERT INTO `upms_role`
VALUES (1, 'ADMIN', 'Operation Admin', 'Operation Admin', 'Operation Admin', 1, 'Operation', 1, 1,
        '2021-04-21 14:41:05', 1881535367252185089, '2025-01-21 03:26:22', 0, 15, NULL);
INSERT INTO `upms_role`
VALUES (1854826444497596417, 'Partner-Normal', 'Supplier-Admin', 'Supplier-Admin', 'Vendor systems processing staff', 1,
        'Supply', 2, 1, '2024-11-08 18:01:00', 1868909489290162178, '2024-12-25 03:33:27', 0, 3, NULL);
INSERT INTO `upms_role`
VALUES (1855901454514184194, 'warehouse-normal', '仓库普通用户', NULL, NULL, 1, 'Logistic', 3, 1, '2024-11-11 17:12:42',
        1, '2024-11-11 17:12:42', 1, 0, '1');
INSERT INTO `upms_role`
VALUES (1856638049274470401, 'Warehouse-Normal', 'Warehouse-Admin', 'Warehouse-Admin', NULL, 1, 'Logistic', 3, 1,
        '2024-11-13 17:59:40', 1864647628398190595, '2025-01-23 13:53:40', 0, 10, NULL);
INSERT INTO `upms_role`
VALUES (1871766266521550850, 'Warehouse-Processor', 'Warehouse-Processor', 'Warehouse-Processor', '打单员', 1,
        'Logistic', 4, 1868909489290162178, '2024-12-25 03:53:48', 1868588896309346306, '2025-03-07 02:16:06', 0, 1,
        NULL);
INSERT INTO `upms_role`
VALUES (1887378852790308865, 'Warehouse-Rollback', 'Warehouse-Rollback', 'Warehouse-Rollback', 'Warehouse-Rollback', 1,
        'Logistic', 6, 1868588896309346306, '2025-02-06 05:52:39', 1868588896309346306, '2025-02-06 05:52:39', 0, 0,
        NULL);
INSERT INTO `upms_role`
VALUES (1887704922253070338, 'Warehouse-Change', 'Warehouse-Change', 'Warehouse-Change', 'Warehouse-Change', 1,
        'Logistic', 7, 1868588896309346306, '2025-02-07 03:28:20', 1868588896309346306, '2025-02-07 03:28:20', 0, 0,
        NULL);
INSERT INTO `upms_role`
VALUES (1897832269712666626, 'Warehouse-BatchAudit', 'Warehouse-BatchAudit', 'Warehouse-BatchAudit',
        'Warehouse-BatchAudit', 1, 'Logistic', 8, 1868588896309346306, '2025-03-07 02:10:48', 1868588896309346306,
        '2025-03-07 02:10:48', 0, 0, NULL);
INSERT INTO `upms_role`
VALUES (1907677145764786177, 'Warehouse-Check', 'Warehouse-Check', 'Warehouse-Check', 'Warehouse-Check', 1, 'Logistic',
        9, 1868588896309346306, '2025-04-03 06:10:49', 1868588896309346306, '2025-04-03 06:10:49', 0, 0, NULL);

-- ----------------------------
-- Table structure for upms_role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `upms_role_permissions`;
CREATE TABLE `upms_role_permissions`
(
    `id`             bigint                                                NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id`        bigint                                                NOT NULL COMMENT '角色id',
    `permissions_id` bigint                                                NOT NULL COMMENT '权限id',
    `terminal`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '1' COMMENT '终端，PC 、Mobile',
    `create_by`      bigint                                                NOT NULL COMMENT '创建人',
    `create_time`    datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`      bigint                                                NOT NULL COMMENT '最后更新人',
    `update_time`    datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag`    tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`        bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `C_INX_CONF_CODE` (`role_id` ASC) USING BTREE,
    INDEX `IDX_PERMISSIONS_ID` (`permissions_id` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1912086296625803312
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '系统角色权限信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_role_permissions
-- ----------------------------
INSERT INTO `upms_role_permissions`
VALUES (1855902006249709570, 1855901454514184194, 1843901413280772097, 'PC', 1, '2024-11-11 17:14:54', 1,
        '2024-11-11 17:14:54', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1855902006258098178, 1855901454514184194, 1844267586526703617, 'PC', 1, '2024-11-11 17:14:54', 1,
        '2024-11-11 17:14:54', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1855902006258098179, 1855901454514184194, 1844267695347920897, 'PC', 1, '2024-11-11 17:14:54', 1,
        '2024-11-11 17:14:54', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591586295809, 1854826444497596417, 1810935050087174145, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591586295810, 1854826444497596417, 1843494292244459522, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591586295811, 1854826444497596417, 1843901413280772097, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591586295812, 1854826444497596417, 1843918321946259457, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591586295813, 1854826444497596417, 1844267412932849666, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591590490113, 1854826444497596417, 1844267586526703617, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591594684417, 1854826444497596417, 1844267695347920897, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591594684418, 1854826444497596417, 1844267829251076097, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591594684419, 1854826444497596417, 1845034673171918850, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1871760591594684420, 1854826444497596417, 1845035418868838402, 'PC', 1868909489290162178, '2024-12-25 03:31:15',
        1868909489290162178, '2024-12-25 03:31:15', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055292250722306, 1887704922253070338, 1810935050087174145, 'PC', 1858317764171608066, '2025-02-08 02:40:35',
        1858317764171608066, '2025-02-08 02:40:35', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055292254916609, 1887704922253070338, 1845731047488155650, 'PC', 1858317764171608066, '2025-02-08 02:40:35',
        1858317764171608066, '2025-02-08 02:40:35', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055292254916610, 1887704922253070338, 1845731764164685826, 'PC', 1858317764171608066, '2025-02-08 02:40:35',
        1858317764171608066, '2025-02-08 02:40:35', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055292254916611, 1887704922253070338, 1878646646941061121, 'PC', 1858317764171608066, '2025-02-08 02:40:35',
        1858317764171608066, '2025-02-08 02:40:35', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055311733264386, 1887378852790308865, 1810935050087174145, 'PC', 1858317764171608066, '2025-02-08 02:40:39',
        1858317764171608066, '2025-02-08 02:40:39', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055311733264387, 1887378852790308865, 1845731047488155650, 'PC', 1858317764171608066, '2025-02-08 02:40:39',
        1858317764171608066, '2025-02-08 02:40:39', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055311733264388, 1887378852790308865, 1845732124392484866, 'PC', 1858317764171608066, '2025-02-08 02:40:39',
        1858317764171608066, '2025-02-08 02:40:39', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055311733264389, 1887378852790308865, 1845732399232643073, 'PC', 1858317764171608066, '2025-02-08 02:40:39',
        1858317764171608066, '2025-02-08 02:40:39', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055311733264390, 1887378852790308865, 1878635012754698242, 'PC', 1858317764171608066, '2025-02-08 02:40:39',
        1858317764171608066, '2025-02-08 02:40:39', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1888055311733264391, 1887378852790308865, 1878635735592660993, 'PC', 1858317764171608066, '2025-02-08 02:40:39',
        1858317764171608066, '2025-02-08 02:40:39', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135041, 1871766266521550850, 1810935050087174145, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135042, 1871766266521550850, 1843917431059312641, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135043, 1871766266521550850, 1843917543319859201, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135044, 1871766266521550850, 1843917867224985601, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135045, 1871766266521550850, 1843917998439591938, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135046, 1871766266521550850, 1844912863222755329, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066674135047, 1871766266521550850, 1844913538388258818, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329345, 1871766266521550850, 1844914205790105601, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329346, 1871766266521550850, 1844914348639711234, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329347, 1871766266521550850, 1844914555934797825, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329348, 1871766266521550850, 1845034673171918850, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329349, 1871766266521550850, 1845731047488155650, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329350, 1871766266521550850, 1845731302677999617, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329351, 1871766266521550850, 1845731764164685826, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329352, 1871766266521550850, 1845732124392484866, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329353, 1871766266521550850, 1845732399232643073, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329354, 1871766266521550850, 1847154856980180993, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329355, 1871766266521550850, 1847208874985189377, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329356, 1871766266521550850, 1847209034469404673, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329357, 1871766266521550850, 1847209155244388354, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066678329358, 1871766266521550850, 1847209845777821698, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523650, 1871766266521550850, 1847211623097364482, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523651, 1871766266521550850, 1848202616194002945, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523652, 1871766266521550850, 1851499059734441985, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523653, 1871766266521550850, 1851499263141408770, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523654, 1871766266521550850, 1851499474001653761, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523655, 1871766266521550850, 1851499845604405249, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523656, 1871766266521550850, 1851499995835985921, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523657, 1871766266521550850, 1851500091612917761, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523658, 1871766266521550850, 1851500235876003842, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523659, 1871766266521550850, 1853244611903942658, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523660, 1871766266521550850, 1853244755474968577, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523661, 1871766266521550850, 1853245052423303170, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523662, 1871766266521550850, 1853245203929952258, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523663, 1871766266521550850, 1853245336075694081, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523664, 1871766266521550850, 1853245548101955585, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523665, 1871766266521550850, 1853245980253679617, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523666, 1871766266521550850, 1853246102156931074, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066682523667, 1871766266521550850, 1853246362371551234, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717953, 1871766266521550850, 1853246528830894081, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717954, 1871766266521550850, 1853246720128905217, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717955, 1871766266521550850, 1853246833861652482, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717956, 1871766266521550850, 1853247008336310274, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717957, 1871766266521550850, 1853247141664845825, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717958, 1871766266521550850, 1853247236053463042, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717959, 1871766266521550850, 1853247347085078529, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717960, 1871766266521550850, 1853247454228574209, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717961, 1871766266521550850, 1853247613880561665, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1897834066686717962, 1871766266521550850, 1868900847828545537, 'PC', 1868588896309346306, '2025-03-07 02:17:56',
        1868588896309346306, '2025-03-07 02:17:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1907677676860141569, 1907677145764786177, 1843494292244459522, 'PC', 1868588896309346306, '2025-04-03 06:12:56',
        1868588896309346306, '2025-04-03 06:12:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1907677676860141570, 1907677145764786177, 1843918321946259457, 'PC', 1868588896309346306, '2025-04-03 06:12:56',
        1868588896309346306, '2025-04-03 06:12:56', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365967163394, 1897832269712666626, 1844912863222755329, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365971357698, 1897832269712666626, 1844913538388258818, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365971357699, 1897832269712666626, 1844914205790105601, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365971357700, 1897832269712666626, 1844914348639711234, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365971357701, 1897832269712666626, 1892118907621801986, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365971357702, 1897832269712666626, 1892122143355371522, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1910512365971357703, 1897832269712666626, 1906533253800656897, 'PC', 1868588896309346306, '2025-04-11 01:56:58',
        1868588896309346306, '2025-04-11 01:56:58', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470793777153, 1856638049274470401, 1810935050087174145, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691714, 1856638049274470401, 1843494292244459522, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691715, 1856638049274470401, 1843901413280772097, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691716, 1856638049274470401, 1843917431059312641, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691717, 1856638049274470401, 1843917543319859201, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691718, 1856638049274470401, 1843917867224985601, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691719, 1856638049274470401, 1843917998439591938, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691720, 1856638049274470401, 1843918321946259457, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691721, 1856638049274470401, 1844267412932849666, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691722, 1856638049274470401, 1844267586526703617, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691723, 1856638049274470401, 1844267695347920897, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691724, 1856638049274470401, 1844267829251076097, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691725, 1856638049274470401, 1844912863222755329, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691726, 1856638049274470401, 1844913538388258818, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691727, 1856638049274470401, 1844914205790105601, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691728, 1856638049274470401, 1844914348639711234, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691729, 1856638049274470401, 1844914555934797825, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691730, 1856638049274470401, 1845032869218545666, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691731, 1856638049274470401, 1845033077759340546, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691732, 1856638049274470401, 1845033297431818241, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691733, 1856638049274470401, 1845033445276839938, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691734, 1856638049274470401, 1845033530219884545, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691735, 1856638049274470401, 1845033611069288450, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691736, 1856638049274470401, 1845033826358718465, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691737, 1856638049274470401, 1845033958135361537, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691738, 1856638049274470401, 1845034034471694338, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691739, 1856638049274470401, 1845034136225509378, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691740, 1856638049274470401, 1845034261463232514, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691741, 1856638049274470401, 1845034316513472514, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691742, 1856638049274470401, 1845034673171918850, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691743, 1856638049274470401, 1845035283279572993, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691744, 1856638049274470401, 1845035418868838402, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691745, 1856638049274470401, 1845035516214439937, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691746, 1856638049274470401, 1845731047488155650, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691747, 1856638049274470401, 1845731302677999617, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470856691748, 1856638049274470401, 1845731764164685826, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800577, 1856638049274470401, 1845732124392484866, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800578, 1856638049274470401, 1845732399232643073, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800579, 1856638049274470401, 1846391298629627906, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800580, 1856638049274470401, 1846392351609647106, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800581, 1856638049274470401, 1846392493272264706, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800582, 1856638049274470401, 1846502061776105473, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800583, 1856638049274470401, 1846502403746099201, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800584, 1856638049274470401, 1846502507030835201, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800585, 1856638049274470401, 1846502845603442689, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800586, 1856638049274470401, 1847095688986423297, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800587, 1856638049274470401, 1847097252333547521, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800588, 1856638049274470401, 1847097847694028801, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800589, 1856638049274470401, 1847098209431777282, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800590, 1856638049274470401, 1847098666392809473, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800591, 1856638049274470401, 1847099059373928450, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800592, 1856638049274470401, 1847154856980180993, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800593, 1856638049274470401, 1847208874985189377, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800594, 1856638049274470401, 1847209034469404673, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800595, 1856638049274470401, 1847209155244388354, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800596, 1856638049274470401, 1847209845777821698, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800597, 1856638049274470401, 1847211623097364482, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800598, 1856638049274470401, 1848183056317345793, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800599, 1856638049274470401, 1848183392889270273, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800600, 1856638049274470401, 1848184522088181762, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800601, 1856638049274470401, 1848185556097363969, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800602, 1856638049274470401, 1848202616194002945, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800603, 1856638049274470401, 1848643017820401665, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800604, 1856638049274470401, 1848643679706738689, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800605, 1856638049274470401, 1848645723008069634, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800606, 1856638049274470401, 1848647834441674754, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800607, 1856638049274470401, 1848647990302011394, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800608, 1856638049274470401, 1850060908306427905, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800609, 1856638049274470401, 1851499059734441985, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800610, 1856638049274470401, 1851499263141408770, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800611, 1856638049274470401, 1851499474001653761, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800612, 1856638049274470401, 1851499845604405249, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800613, 1856638049274470401, 1851499995835985921, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800614, 1856638049274470401, 1851500091612917761, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800615, 1856638049274470401, 1851500235876003842, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800616, 1856638049274470401, 1853244611903942658, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800617, 1856638049274470401, 1853244755474968577, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470923800618, 1856638049274470401, 1853245052423303170, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909442, 1856638049274470401, 1853245203929952258, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909443, 1856638049274470401, 1853245336075694081, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909444, 1856638049274470401, 1853245548101955585, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909445, 1856638049274470401, 1853245980253679617, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909446, 1856638049274470401, 1853246102156931074, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909447, 1856638049274470401, 1853246362371551234, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909448, 1856638049274470401, 1853246528830894081, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909449, 1856638049274470401, 1853246720128905217, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909450, 1856638049274470401, 1853246833861652482, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909451, 1856638049274470401, 1853247008336310274, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909452, 1856638049274470401, 1853247141664845825, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909453, 1856638049274470401, 1853247236053463042, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909454, 1856638049274470401, 1853247347085078529, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909455, 1856638049274470401, 1853247454228574209, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909456, 1856638049274470401, 1853247613880561665, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909457, 1856638049274470401, 1862302952037859330, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909458, 1856638049274470401, 1864962506657759234, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909459, 1856638049274470401, 1864962933256224769, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909460, 1856638049274470401, 1868900847828545537, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909461, 1856638049274470401, 1872237005786710018, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909462, 1856638049274470401, 1878635012754698242, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909463, 1856638049274470401, 1878635735592660993, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909464, 1856638049274470401, 1878646646941061121, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909465, 1856638049274470401, 1901838492307632130, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909466, 1856638049274470401, 1901839073516531713, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909467, 1856638049274470401, 1904114544831827969, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909468, 1856638049274470401, 1904114911866982401, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909469, 1856638049274470401, 1904115096554770433, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909470, 1856638049274470401, 1904826707909218305, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909471, 1856638049274470401, 1905075950980894722, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909472, 1856638049274470401, 1911623012413304834, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909473, 1856638049274470401, 1911623374062972930, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909474, 1856638049274470401, 1911623533106786306, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909475, 1856638049274470401, 1911623630016180226, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909476, 1856638049274470401, 1911623714812424193, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1911624470990909477, 1856638049274470401, 1911624312609796097, 'PC', 1868588896309346306, '2025-04-14 03:36:05',
        1868588896309346306, '2025-04-14 03:36:05', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296613220353, 1, 1810935050087174145, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414658, 1, 1843494292244459522, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414659, 1, 1843901413280772097, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414660, 1, 1843917431059312641, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414661, 1, 1843917543319859201, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414662, 1, 1843917867224985601, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414663, 1, 1843917998439591938, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414664, 1, 1843918321946259457, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414665, 1, 1844267412932849666, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414666, 1, 1844267586526703617, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414667, 1, 1844267695347920897, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414668, 1, 1844267829251076097, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414669, 1, 1844912863222755329, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414670, 1, 1844913538388258818, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414671, 1, 1844914205790105601, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414672, 1, 1844914348639711234, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296617414673, 1, 1844914555934797825, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608961, 1, 1845032869218545666, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608962, 1, 1845033077759340546, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608963, 1, 1845033297431818241, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608964, 1, 1845033445276839938, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608965, 1, 1845033530219884545, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608966, 1, 1845033611069288450, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608967, 1, 1845033826358718465, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608968, 1, 1845033958135361537, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608969, 1, 1845034034471694338, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608970, 1, 1845034136225509378, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608971, 1, 1845034261463232514, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608972, 1, 1845034316513472514, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608973, 1, 1845034673171918850, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608974, 1, 1845035283279572993, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608975, 1, 1845035418868838402, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608976, 1, 1845035516214439937, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608977, 1, 1845731047488155650, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608978, 1, 1845731302677999617, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608979, 1, 1845731764164685826, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608980, 1, 1845732124392484866, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608981, 1, 1845732399232643073, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608982, 1, 1846391298629627906, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608983, 1, 1846392351609647106, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608984, 1, 1846392493272264706, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608985, 1, 1846502061776105473, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608986, 1, 1846502403746099201, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608987, 1, 1846502507030835201, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608988, 1, 1846502845603442689, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608989, 1, 1847095688986423297, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608990, 1, 1847097252333547521, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608991, 1, 1847097847694028801, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608992, 1, 1847098209431777282, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608993, 1, 1847098666392809473, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608994, 1, 1847099059373928450, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608995, 1, 1847154856980180993, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608996, 1, 1847208874985189377, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608997, 1, 1847209034469404673, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608998, 1, 1847209155244388354, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621608999, 1, 1847209845777821698, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621609000, 1, 1847211623097364482, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621609001, 1, 1848183056317345793, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621609002, 1, 1848183392889270273, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296621609003, 1, 1848184522088181762, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803266, 1, 1848185556097363969, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803267, 1, 1848202616194002945, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803268, 1, 1848643017820401665, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803269, 1, 1848643679706738689, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803270, 1, 1848645723008069634, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803271, 1, 1848647834441674754, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803272, 1, 1848647990302011394, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803273, 1, 1850060908306427905, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803274, 1, 1851499059734441985, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803275, 1, 1851499263141408770, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803276, 1, 1851499474001653761, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803277, 1, 1851499845604405249, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803278, 1, 1851499995835985921, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803279, 1, 1851500091612917761, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803280, 1, 1851500235876003842, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803281, 1, 1853244611903942658, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803282, 1, 1853244755474968577, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803283, 1, 1853245052423303170, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803284, 1, 1853245203929952258, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803285, 1, 1853245336075694081, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803286, 1, 1853245548101955585, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803287, 1, 1853245980253679617, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803288, 1, 1853246102156931074, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803289, 1, 1853246362371551234, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803290, 1, 1853246528830894081, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803291, 1, 1853246720128905217, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803292, 1, 1853246833861652482, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803293, 1, 1853247008336310274, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803294, 1, 1853247141664845825, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803295, 1, 1853247236053463042, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803296, 1, 1853247347085078529, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803297, 1, 1853247454228574209, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803298, 1, 1853247613880561665, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803299, 1, 1862302952037859330, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803300, 1, 1864962506657759234, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803301, 1, 1864962933256224769, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803302, 1, 1868900847828545537, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803303, 1, 1872237005786710018, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803304, 1, 1878635012754698242, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803305, 1, 1878635735592660993, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803306, 1, 1878646646941061121, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803307, 1, 1892118907621801986, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803308, 1, 1892122143355371522, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803309, 1, 1906533253800656897, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803310, 1, 1909497822039871489, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);
INSERT INTO `upms_role_permissions`
VALUES (1912086296625803311, 1, 1912086132989227010, 'PC', 1868588896309346306, '2025-04-15 10:11:13',
        1868588896309346306, '2025-04-15 10:11:13', 0, 0);

-- ----------------------------
-- Table structure for upms_route
-- ----------------------------
DROP TABLE IF EXISTS `upms_route`;
CREATE TABLE `upms_route`
(
    `id`             bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `server_name`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '所属服务',
    `request_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求方式',
    `path`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '接口路径',
    `ignore_flag`    tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '是否忽略权限控制（0不需要，1需要）',
    `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '备注',
    `create_by`      bigint                                                 NOT NULL DEFAULT 0 COMMENT '创建人',
    `create_time`    datetime                                               NULL     DEFAULT NULL COMMENT '创建时间',
    `update_by`      bigint                                                 NOT NULL DEFAULT 0 COMMENT '最后更新人',
    `update_time`    datetime                                               NULL     DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`    tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`        bigint                                                 NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 119217
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '接口权限路由'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_route
-- ----------------------------
INSERT INTO `upms_route`
VALUES (118974, 'base', 'GET', '/api/log/error-record/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118975, 'base', 'POST', '/client/upms/userAccount/listByUserAccount', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118976, 'base', 'GET', '/api/upms/partner-ship/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118977, 'base', 'POST', '/api/upms/business-role/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118978, 'base', 'GET', '/api/upms/user-account/register', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118979, 'base', 'POST', '/api/log/request-used-time/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118980, 'base', 'POST', '/api/upms/app-access-config/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118981, 'base', 'POST', '/api/dfs/oss/put-file-attach', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118982, 'base', 'POST', '/api/upms/tenant/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118983, 'base', 'POST', '/api/log/alert-record/error-log-clear', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118984, 'base', 'GET', '/api/upms/app-access-config/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118985, 'base', 'GET', '/api/dict/dictionary/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118986, 'base', 'GET', '/api/dict/dictionary/get-dictionary-map', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118987, 'base', 'POST', '/api/upms/role/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118988, 'base', 'POST', '/api/dfs/oss/remove-file', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118989, 'base', 'GET', '/api/upms/role/list-ignore-simple', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118990, 'base', 'POST', '/api/dfs/attach/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118991, 'base', 'POST', '/api/dfs/oss/fileImage', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118992, 'base', 'POST', '/api/dict/region/list-check', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118993, 'base', 'POST', '/api/upms/partner-app-access-config/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118994, 'base', 'GET', '/api/dfs/oss/get-file-path', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118995, 'base', 'POST', '/api/upms/user-account/reSetPassword', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118996, 'base', 'POST', '/client/dict/config/save', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118997, 'base', 'GET', '/api/dict/dictionary/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118998, 'base', 'POST', '/api/dict/lang-field/add-field-translation', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (118999, 'base', 'POST', '/api/upms/user-account/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119000, 'base', 'POST', '/api/dict/region/import-region', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119001, 'base', 'POST', '/api/upms/partner-app-access-config/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119002, 'base', 'GET', '/client/dict/number-generate/warehouse-code-delimiter', 0, NULL, 0, NULL, 0, NULL, 0,
        0);
INSERT INTO `upms_route`
VALUES (119003, 'base', 'POST', '/api/upms/role/init-cache', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119004, 'base', 'POST', '/api/dfs/attach/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119005, 'base', 'GET', '/api/upms/route/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119006, 'base', 'POST', '/api/dfs/oss/copy-file', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119007, 'base', 'POST', '/api/log/alert-record/page', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119008, 'base', 'GET', '/api/upms/auth/captcha', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119009, 'base', 'GET', '/client/dict/dictionary/all', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119010, 'base', 'POST', '/api/upms/duo-white-list/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119011, 'base', 'GET', '/api/dict/config/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119012, 'base', 'POST', '/api/upms/role/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119013, 'base', 'GET', '/api/dict/dictionary/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119014, 'base', 'GET', '/api/upms/tenant/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119015, 'base', 'POST', '/api/upms/role/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119016, 'base', 'POST', '/api/dict/dictionary/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119017, 'base', 'GET', '/api/log/login-record/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119018, 'base', 'POST', '/client/upms/tenant/list-by-code', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119019, 'base', 'POST', '/client/dict/region/get-region', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119020, 'base', 'POST', '/api/upms/user-tenant/update-status', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119021, 'base', 'POST', '/api/upms/auth/select-tenant', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119022, 'base', 'POST', '/api/upms/partner-app-access-config/update-status', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119023, 'base', 'GET', '/api/upms/permissions/tree', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119024, 'base', 'POST', '/api/upms/user-ignore-tenant/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119025, 'base', 'GET', '/client/upms/user/get-by-user-id/{userId}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119026, 'base', 'GET', '/client/upms/permission/routes', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119027, 'base', 'GET', '/api/dict/number-generate-config/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119028, 'base', 'POST', '/api/upms/tenant/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119029, 'base', 'POST', '/api/upms/role/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119030, 'base', 'POST', '/api/upms/tenant/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119031, 'base', 'POST', '/client/log/log-record/client/log/log-record/write', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119032, 'base', 'POST', '/api/upms/user-tenant/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119033, 'base', 'POST', '/api/upms/user-tenant/reset-password', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119034, 'base', 'POST', '/api/upms/business-role/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119035, 'base', 'GET', '/api/dict/config/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119036, 'base', 'GET', '/v3/api-docs.yaml', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119037, 'base', 'POST', '/client/dict/dictionary/by-name', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119038, 'base', 'GET', '/api/dict/number-generate-config/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119039, 'base', 'POST', '/api/log/request-path-pattern/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119040, 'base', 'GET', '/api/dfs/template-file/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119041, 'base', 'POST', '/api/log/error-record/error-log-clear', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119042, 'base', 'GET', '/api/dict/lang-field/list-field-translation/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119043, 'base', 'POST', '/api/dict/dictionary/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119044, 'base', 'GET', '/api/dfs/template-file/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119045, 'base', 'POST', '/api/upms/route/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119046, 'base', 'POST', '/api/upms/tenant/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119047, 'base', 'POST', '/api/upms/user-tenant/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119048, 'base', 'POST', '/api/dfs/template-file/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119049, 'base', 'POST', '/api/dict/lang-field/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119050, 'base', 'GET', '/api/upms/role/list-simple', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119051, 'base', 'POST', '/api/upms/duo-white-list/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119052, 'base', 'POST', '/api/upms/app-access-config/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119053, 'base', 'POST', '/api/upms/app-access-config/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119054, 'base', 'POST', '/api/dfs/attach/uploadByBase64StringList', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119055, 'base', 'GET', '/api/upms/partner-app-access-config/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119056, 'base', 'POST', '/api/dfs/oss/remove-attach-file', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119057, 'base', 'POST', '/api/upms/auth/kick-out', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119058, 'base', 'POST', '/api/dict/region/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119059, 'base', 'POST', '/api/upms/route/listAll', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119060, 'base', 'POST', '/api/dfs/template-file/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119061, 'base', 'POST', '/client/upms-api/permissions/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119062, 'base', 'POST', '/client/upms/tenant/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119063, 'base', 'POST', '/client/dict/region/save-batch', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119064, 'base', 'POST', '/api/upms/partner-app-access-config/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119065, 'base', 'POST', '/api/upms/business-role/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119066, 'base', 'GET', '/client/upms/user/get-by-user-name/{username}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119067, 'base', 'POST', '/api/upms/app-access-config/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119068, 'base', 'POST', '/client/upms/role/list-by-ids', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119069, 'base', 'POST', '/api/upms/user-ignore-tenant/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119070, 'base', 'POST', '/api/dict/number-generate-config/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119071, 'base', 'POST', '/api/dict/lang-field/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119072, 'base', 'GET', '/api/upms/user-account/userByEmail', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119073, 'base', 'POST', '/api/dfs/oss/put-file-by-name', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119074, 'base', 'GET', '/api/dict/number-generate-config/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119075, 'base', 'POST', '/api/dict/dictionary/subset', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119076, 'base', 'POST', '/client/dict/dictionary/delete-batch', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119077, 'base', 'POST', '/api/dict/lang-field/load-cache-lang-field-result', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119078, 'base', 'GET', '/api/dict/dictionary/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119079, 'base', 'GET', '/api/dict/config/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119080, 'base', 'POST', '/client/dfs/attach/uploadByBase64StringList', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119081, 'base', 'POST', '/api/log/third-record/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119082, 'base', 'GET', '/api/dfs/oss/file-link', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119083, 'base', 'POST', '/api/dfs/oss/remove-attach-files', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119084, 'base', 'POST', '/client/dict/dictionary/save', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119085, 'base', 'POST', '/client/upms/tenant/get-by-code/{code}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119086, 'base', 'POST', '/client/dict/region/save', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119087, 'base', 'POST', '/client/dict/dictionary/get-dictionary', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119088, 'base', 'POST', '/api/dfs/oss/put-file-attachList', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119089, 'base', 'POST', '/client/log/alert-record/write', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119090, 'base', 'POST', '/api/dict/region/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119091, 'base', 'POST', '/api/dfs/oss/remove-bucket', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119092, 'base', 'GET', '/api/dict/config/get-value', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119093, 'base', 'GET', '/api/dict/config/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119094, 'base', 'POST', '/api/dfs/oss/remove-files', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119095, 'base', 'POST', '/api/log/third-record/login-log-clear', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119096, 'base', 'GET', '/api/dfs/template-file/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119097, 'base', 'GET', '/api/dfs/attach/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119098, 'base', 'POST', '/api/upms/partner-app-access-config/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119099, 'base', 'POST', '/api/dfs/attach/uploadByStringType', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119100, 'base', 'GET', '/api/dict/number-generate-config/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119101, 'base', 'POST', '/api/log/error-record/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119102, 'base', 'GET', '/api/log/operation-record/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119103, 'base', 'POST', '/client/upms/tenant/count-tenant', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119104, 'base', 'POST', '/api/upms/partner-ship/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119105, 'base', 'POST', '/client/upms-api/role/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119106, 'base', 'GET', '/api/dfs/oss/get-file', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119107, 'base', 'POST', '/client/dict/dictionary/save-batch', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119108, 'base', 'GET', '/api/dfs/attach/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119109, 'base', 'POST', '/api/dfs/template-file/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119110, 'base', 'GET', '/api/upms/permissions/buttons', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119111, 'base', 'GET', '/v3/api-docs/{group}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119112, 'base', 'GET', '/api/upms/permissions/routes', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119113, 'base', 'POST', '/api/upms/tenant/update-status', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119114, 'base', 'POST', '/client/dfs/attach/upload-file', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119115, 'base', 'POST', '/api/dfs/attach/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119116, 'base', 'POST', '/api/upms/auth/login', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119117, 'base', 'GET', '/api/dict/lang-field/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119118, 'base', 'GET', '/api/log/request-path-pattern/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119119, 'base', 'POST', '/api/dfs/oss/fileDocAttach', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119120, 'base', 'POST', '/api/upms/user-account/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119121, 'base', 'GET', '/api/upms/user-tenant/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119122, 'base', 'GET', '/api/upms/role/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119123, 'base', 'GET', '/api/dict/region/list-cache-all-tree/{limitDept}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119124, 'base', 'GET', '/api/log/request-path-pattern/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119125, 'base', 'POST', '/api/upms/user-ignore-tenant/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119126, 'base', 'POST', '/api/upms/permissions/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119127, 'base', 'POST', '/api/dict/config/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119128, 'base', 'POST', '/api/dict/config/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119129, 'base', 'POST', '/api/upms/auth/validate-username', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119130, 'base', 'GET', '/client/dict/number-generate/code', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119131, 'base', 'POST', '/api/log/login-record/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119132, 'base', 'GET', '/v3/api-docs', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119133, 'base', 'GET', '/api/dict/region/get-area/{depth}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119134, 'base', 'GET', '/api/dict/lang-field/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119135, 'base', 'GET', '/api/dict/region/list/{parentId}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119136, 'base', 'GET', '/api/dict/region/list-cache-tree/{parentCode}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119137, 'base', 'POST', '/api/dict/lang-field/load-lang-field-result', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119138, 'base', 'GET', '/api/log/alert-record/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119139, 'base', 'GET', '/client/upms/user/detail/{userId}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119140, 'base', 'POST', '/api/dfs/oss/put-file-attach-by-name', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119141, 'base', 'GET', '/client/dict/number-generate/warehouse-code', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119142, 'base', 'GET', '/api/dict/config/initConfig', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119143, 'base', 'GET', '/api/upms/business-role/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119144, 'base', 'POST', '/api/upms/user-tenant/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119145, 'base', 'GET', '/client/auth/token', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119146, 'base', 'POST', '/api/dfs/oss/make-bucket', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119147, 'base', 'POST', '/api/dict/number-generate-config/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119148, 'base', 'POST', '/api/upms/app-access-config/update-status', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119149, 'base', 'POST', '/api/upms/duo-white-list/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119150, 'base', 'POST', '/api/upms/partner-ship/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119151, 'base', 'POST', '/client/dict/region/find-by-condition', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119152, 'base', 'POST', '/api/dict/lang-field/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119153, 'base', 'POST', '/api/log/request-path-pattern/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119154, 'base', 'POST', '/api/dict/number-generate-config/import', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119155, 'base', 'GET', '/api/upms/role/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119156, 'base', 'POST', '/api/log/operation-record/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119157, 'base', 'POST', '/api/upms/duo-white-list/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119158, 'base', 'POST', '/api/dict/region/tree', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119159, 'base', 'GET', '/client/dict/number-generate/zone-id', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119160, 'base', 'GET', '/api/upms/duo-white-list/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119161, 'base', 'POST', '/api/upms/user-ignore-tenant/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119162, 'base', 'GET', '/api/upms/business-role/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119163, 'base', 'POST', '/api/upms/partner-ship/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119164, 'base', 'POST', '/api/upms/user-ignore-tenant/update-status', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119165, 'base', 'POST', '/api/log/request-path-pattern/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119166, 'base', 'POST', '/api/upms/permissions/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119167, 'base', 'GET', '/client/dict/number-generate/product-code', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119168, 'base', 'POST', '/api/upms/tenant/list-my', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119169, 'base', 'POST', '/api/dfs/oss/fileDoc', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119170, 'base', 'POST', '/api/upms/role-permissions/batch-save', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119171, 'base', 'POST', '/api/dict/number-generate-config/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119172, 'base', 'GET', '/api/dict/dictionary/subsets', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119173, 'base', 'GET', '/api/dict/region/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119174, 'base', 'GET', '/api/dfs/template-file/get-templateFile/{templateCode}', 0, NULL, 0, NULL, 0, NULL, 0,
        0);
INSERT INTO `upms_route`
VALUES (119175, 'base', 'GET', '/api/upms/role-permissions/refresh-cache/{roleId}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119176, 'base', 'GET', '/api/upms/permissions/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119177, 'base', 'POST', '/api/upms/partner-ship/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119178, 'base', 'GET', '/client/dict/number-generate/config/init', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119179, 'base', 'GET', '/swagger-ui.html', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119180, 'base', 'POST', '/api/log/login-record/login-log-clear', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119181, 'base', 'POST', '/api/upms/user-ignore-tenant/reset-password', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119182, 'base', 'GET', '/client/upms/role/get-role-list/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119183, 'base', 'GET', '/client/dict/region/delete-by-code', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119184, 'base', 'GET', '/api/upms/role-permissions/detail-by-role/{roleId}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119185, 'base', 'GET', '/api/dict/region/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119186, 'base', 'GET', '/api/upms/user-ignore-tenant/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119187, 'base', 'GET', '/client/upms/role/get-by-roleName', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119188, 'base', 'GET', '/client/upms/role/list-by-user-id/{userId}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119189, 'base', 'GET', '/v3/api-docs/swagger-config', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119190, 'base', 'POST', '/api/dict/dictionary/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119191, 'base', 'POST', '/client/dict/dictionary/subset', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119192, 'base', 'GET', '/api/log/request-used-time/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119193, 'base', 'POST', '/api/log/request-used-time/request-used-time-clear', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119194, 'base', 'POST', '/api/upms/user-tenant/modify-password', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119195, 'base', 'POST', '/api/upms/route/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119196, 'base', 'POST', '/api/upms/route/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119197, 'base', 'POST', '/api/upms/tenant/init-cache', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119198, 'base', 'GET', '/v3/api-docs.yaml/{group}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119199, 'base', 'POST', '/api/dict/lang-field/import-translation', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119200, 'base', 'POST', '/api/upms/user-tenant/list', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119201, 'base', 'POST', '/api/log/operation-record/operation-log-clear', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119202, 'base', 'POST', '/client/dfs/attach/uploadByBase64String', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119203, 'base', 'GET', '/api/dict/dictionary/tree', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119204, 'base', 'GET', '/api/upms/user-account/remove/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119205, 'base', 'GET', '/client/auth/validate-sign', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119206, 'base', 'POST', '/api/upms/route/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119207, 'base', 'POST', '/api/dict/region/list-check-path', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119208, 'base', 'POST', '/api/upms/user-account/update', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119209, 'base', 'GET', '/api/upms/permissions/active/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119210, 'base', 'GET', '/api/log/request-path-pattern/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119211, 'base', 'POST', '/api/upms/permissions/remove', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119212, 'base', 'POST', '/api/dfs/attach/upload-file', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119213, 'base', 'POST', '/api/dict/config/insert', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119214, 'base', 'GET', '/api/log/request-path-pattern/export', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119215, 'base', 'GET', '/api/dict/dictionary/get-dictionary', 0, NULL, 0, NULL, 0, NULL, 0, 0);
INSERT INTO `upms_route`
VALUES (119216, 'base', 'GET', '/api/upms/user-account/detail/{id}', 0, NULL, 0, NULL, 0, NULL, 0, 0);

-- ----------------------------
-- Table structure for upms_tenant
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant`;
CREATE TABLE `upms_tenant`
(
    `id`                     bigint                                                 NOT NULL COMMENT '主键',
    `tenant_code`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '编码',
    `state`                  tinyint                                                NULL     DEFAULT NULL COMMENT '状态 1:有效 0:无效',
    `create_by`              bigint                                                 NULL     DEFAULT NULL COMMENT '创建人',
    `create_time`            datetime                                               NULL     DEFAULT NULL COMMENT '创建时间',
    `update_by`              bigint                                                 NULL     DEFAULT NULL COMMENT '最后更新人',
    `update_time`            datetime                                               NULL     DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                             NULL     DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `tenant_logo`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '租户logo',
    `create_by_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '创建人名称',
    `update_by_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '更新人名称',
    `name`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴名称',
    `abbr_name`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '合作伙伴简称',
    `contact_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '联系人姓名',
    `contact_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人邮箱',
    `address_name`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址名称',
    `address_company`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '公司地址',
    `address_country`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '国家',
    `address_state`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '州/省',
    `address_city`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
    `address_zip_code`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '邮编',
    `address_addr1`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址1',
    `address_addr2`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址2',
    `address_addr3`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址3',
    `address_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址邮箱',
    `address_phone`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '地址电话',
    `address_note`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '地址备注',
    `partner_type`           json                                                   NOT NULL COMMENT '合作伙伴类型',
    `version`                bigint                                                 NOT NULL COMMENT '版本',
    `deleted_note`           varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '删除备注',
    `address_is_residential` tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '是否住宅地址',
    `default_flag`           tinyint(1)                                             NOT NULL DEFAULT 0 COMMENT '是否默认租户',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_CODE` (`tenant_code` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '租户'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_tenant
-- ----------------------------
INSERT INTO `upms_tenant`
VALUES (1, 'ADMIN', 1, 1, '2024-11-08 17:42:03', 1895285787889504257, '2025-02-28 02:50:32', 0, NULL, NULL, NULL,
        'admin', 'ADMIN', 'admin', '<EMAIL>', 'admin', 'admin', 'admin', 'admin', 'admin', 'admin', 'admin',
        'admin', 'admin', '<EMAIL>', 'admin', 'admin', '[
        \"Operation\"
    ]', 10, NULL, 0, 1);
INSERT INTO `upms_tenant`
VALUES (1001, 'T001', 1, 1, '2022-02-22 22:22:01', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'IPOWER', 'IPOWER',
        'IPOWER', '<EMAIL>', 'IPOWER', 'IPOWER', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10001', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1002, 'T002', 1, 1, '2022-02-22 22:22:02', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'DHS', 'DHS', 'DHS',
        '<EMAIL>', 'DHS', 'DHS', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10002', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1003, 'T003', 1, 1, '2022-02-22 22:22:03', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'BHAPH', 'BHAPH',
        'BHAPH', '<EMAIL>', 'BHAPH', 'BHAPH', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL,
        NULL, '6268637344', '10003', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1004, 'T004', 1, 1, '2022-02-22 22:22:04', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'GPM', 'GPM', 'GPM',
        '<EMAIL>', 'GPM', 'GPM', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10004', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1005, 'T005', 1, 1, '2022-02-22 22:22:05', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'BAIYI', 'BAIYI',
        'BAIYI', '<EMAIL>', 'BAIYI', 'BAIYI', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL,
        NULL, '6268637344', '10005', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1006, 'T006', 1, 1, '2022-02-22 22:22:06', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'GREEN_MASTER',
        'GREEN_MASTER', 'GREEN_MASTER', 'green_master@green_master.com', 'GREEN_MASTER', 'GREEN_MASTER', 'US', 'CA',
        'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10006', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1007, 'T007', 1, 1, '2022-02-22 22:22:07', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'FLY_ELEPHANT',
        'FLY_ELEPHANT', 'FLY_ELEPHANT', 'fly_elephant@fly_elephant.com', 'FLY_ELEPHANT', 'FLY_ELEPHANT', 'US', 'CA',
        'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10007', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1008, 'T008', 1, 1, '2022-02-22 22:22:08', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'EASY_HOME',
        'EASY_HOME', 'EASY_HOME', 'easy_home@easy_home.com', 'EASY_HOME', 'EASY_HOME', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10008', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1009, 'T009', 1, 1, '2022-02-22 22:22:09', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'ATK', 'ATK', 'ATK',
        '<EMAIL>', 'ATK', 'ATK', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10009', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1010, 'T010', 1, 1, '2022-02-22 22:22:10', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'HDS', 'HDS', 'HDS',
        '<EMAIL>', 'HDS', 'HDS', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10010', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1011, 'T011', 1, 1, '2022-02-22 22:22:11', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'ELITELUX',
        'ELITELUX', 'ELITELUX', '<EMAIL>', 'ELITELUX', 'ELITELUX', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10011', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1012, 'T012', 1, 1, '2022-02-22 22:22:12', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'BIZLIVE', 'BIZLIVE',
        'BIZLIVE', '<EMAIL>', 'BIZLIVE', 'BIZLIVE', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave',
        NULL, NULL, NULL, '6268637344', '10012', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1013, 'T013', 1, 1, '2022-02-22 22:22:13', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, '39F', '39F', '39F',
        '<EMAIL>', '39F', '39F', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10013', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1014, 'T014', 1, 1, '2022-02-22 22:22:14', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'FOA', 'FOA', 'FOA',
        '<EMAIL>', 'FOA', 'FOA', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10014', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1015, 'T015', 1, 1, '2022-02-22 22:22:15', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'LTMATE', 'LTMATE',
        'LTMATE', '<EMAIL>', 'LTMATE', 'LTMATE', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10015', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1016, 'T016', 1, 1, '2022-02-22 22:22:16', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'JULI', 'JULI',
        'JULI', '<EMAIL>', 'JULI', 'JULI', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10016', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1017, 'T017', 1, 1, '2022-02-22 22:22:17', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'OEM', 'OEM', 'OEM',
        '<EMAIL>', 'OEM', 'OEM', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10017', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1018, 'T018', 1, 1, '2022-02-22 22:22:18', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'SUMTER_EASY',
        'SUMTER_EASY', 'SUMTER_EASY', 'sumter_easy@sumter_easy.com', 'SUMTER_EASY', 'SUMTER_EASY', 'US', 'CA',
        'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10018', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1019, 'T019', 1, 1, '2022-02-22 22:22:19', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'KBJ', 'KBJ', 'KBJ',
        '<EMAIL>', 'KBJ', 'KBJ', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10019', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1020, 'T020', 1, 1, '2022-02-22 22:22:20', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'CCF', 'CCF', 'CCF',
        '<EMAIL>', 'CCF', 'CCF', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10020', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1021, 'T021', 1, 1, '2022-02-22 22:22:21', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'GOLDORO', 'GOLDORO',
        'GOLDORO', '<EMAIL>', 'GOLDORO', 'GOLDORO', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave',
        NULL, NULL, NULL, '6268637344', '10021', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1022, 'T022', 1, 1, '2022-02-22 22:22:22', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'XIONGGU', 'XIONGGU',
        'XIONGGU', '<EMAIL>', 'XIONGGU', 'XIONGGU', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave',
        NULL, NULL, NULL, '6268637344', '10022', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1023, 'T023', 1, 1, '2022-02-22 22:22:23', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'DPF', 'DPF', 'DPF',
        '<EMAIL>', 'DPF', 'DPF', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10023', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1024, 'T024', 1, 1, '2022-02-22 22:22:24', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'ENPAI', 'ENPAI',
        'ENPAI', '<EMAIL>', 'ENPAI', 'ENPAI', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL,
        NULL, '6268637344', '10024', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1025, 'T025', 1, 1, '2022-02-22 22:22:25', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'LE_CHAI', 'LE_CHAI',
        'LE_CHAI', 'le_chai@le_chai.com', 'LE_CHAI', 'LE_CHAI', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave',
        NULL, NULL, NULL, '6268637344', '10025', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1026, 'T026', 1, 1, '2022-02-22 22:22:26', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'MIER', 'MIER',
        'MIER', '<EMAIL>', 'MIER', 'MIER', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10026', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1027, 'T027', 1, 1, '2022-02-22 22:22:27', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'IGO', 'IGO', 'IGO',
        '<EMAIL>', 'IGO', 'IGO', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10027', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1028, 'T028', 1, 1, '2022-02-22 22:22:28', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'FLUFFY_PET',
        'FLUFFY_PET', 'FLUFFY_PET', 'fluffy_pet@fluffy_pet.com', 'FLUFFY_PET', 'FLUFFY_PET', 'US', 'CA', 'Irwindale',
        '91010', '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10028', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1029, 'T029', 1, 1, '2022-02-22 22:22:29', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'VITACUP', 'VITACUP',
        'VITACUP', '<EMAIL>', 'VITACUP', 'VITACUP', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave',
        NULL, NULL, NULL, '6268637344', '10029', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1030, 'T030', 1, 1, '2022-02-22 22:22:30', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'ANZHELUO',
        'ANZHELUO', 'ANZHELUO', '<EMAIL>', 'ANZHELUO', 'ANZHELUO', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10030', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1031, 'T031', 1, 1, '2022-02-22 22:22:31', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'KUKA', 'KUKA',
        'KUKA', '<EMAIL>', 'KUKA', 'KUKA', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL,
        '6268637344', '10031', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1032, 'T032', 1, 1, '2022-02-22 22:22:32', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'DEYUAN', 'DEYUAN',
        'DEYUAN', '<EMAIL>', 'DEYUAN', 'DEYUAN', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10032', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1033, 'T033', 1, 1, '2022-02-22 22:22:33', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'IPOWER33',
        'IPOWER33', 'IPOWER33', '<EMAIL>', 'IPOWER33', 'IPOWER33', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10033', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1034, 'T034', 1, 1, '2022-02-22 22:22:34', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'XUNMEI', 'XUNMEI',
        'XUNMEI', '<EMAIL>', 'XUNMEI', 'XUNMEI', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10034', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1035, 'T035', 1, 1, '2022-02-22 22:22:35', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'CHONGRAN',
        'CHONGRAN', 'CHONGRAN', '<EMAIL>', 'CHONGRAN', 'CHONGRAN', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10035', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1036, 'T036', 1, 1, '2022-02-22 22:22:36', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'PISHEN', 'PISHEN',
        'PISHEN', '<EMAIL>', 'PISHEN', 'PISHEN', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10036', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1037, 'T037', 1, 1, '2022-02-22 22:22:37', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'HENGJIAN',
        'HENGJIAN', 'HENGJIAN', '<EMAIL>', 'HENGJIAN', 'HENGJIAN', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10037', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1038, 'T038', 1, 1, '2022-02-22 22:22:38', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'THIRTY_EIGHT',
        'THIRTY_EIGHT', 'THIRTY_EIGHT', 'thirty_eight@thirty_eight.com', 'THIRTY_EIGHT', 'THIRTY_EIGHT', 'US', 'CA',
        'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10038', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1039, 'T039', 1, 1, '2022-02-22 22:22:39', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'LESHAN', 'LESHAN',
        'LESHAN', '<EMAIL>', 'LESHAN', 'LESHAN', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10039', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1040, 'T040', 1, 1, '2022-02-22 22:22:40', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'ZHITUO', 'ZHITUO',
        'ZHITUO', '<EMAIL>', 'ZHITUO', 'ZHITUO', 'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL,
        NULL, NULL, '6268637344', '10040', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1041, 'T041', 1, 1, '2022-02-22 22:22:41', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'GENGNIAN',
        'GENGNIAN', 'GENGNIAN', '<EMAIL>', 'GENGNIAN', 'GENGNIAN', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10041', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1042, 'T042', 1, 1, '2022-02-22 22:22:42', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'LEVELPLAY',
        'LEVELPLAY', 'LEVELPLAY', '<EMAIL>', 'LEVELPLAY', 'LEVELPLAY', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10042', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1043, 'T043', 1, 1, '2022-02-22 22:22:43', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'YEAHDONE',
        'YEAHDONE', 'YEAHDONE', '<EMAIL>', 'YEAHDONE', 'YEAHDONE', 'US', 'CA', 'Irwindale', '91010',
        '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10043', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);
INSERT INTO `upms_tenant`
VALUES (1044, 'T044', 1, 1, '2022-02-22 22:22:44', 1, '2022-02-22 22:22:22', 0, NULL, NULL, NULL, 'KEJIN_LIGHTING',
        'KEJIN_LIGHTING', 'KEJIN_LIGHTING', 'kejin_lighting@kejin_lighting.com', 'KEJIN_LIGHTING', 'KEJIN_LIGHTING',
        'US', 'CA', 'Irwindale', '91010', '2399 Bateman Ave', NULL, NULL, NULL, '6268637344', '10044', '[
        \"Operation\",
        \"Supply\",
        \"Logistic\"
    ]', 0, NULL, 0, 0);

-- ----------------------------
-- Table structure for upms_tenant_config
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant_config`;
CREATE TABLE `upms_tenant_config`
(
    `id`                bigint     NOT NULL COMMENT '主键',
    `credit_limit_flag` tinyint    NULL DEFAULT NULL COMMENT '白条额度控制',
    `back_invoice_flag` tinyint    NULL DEFAULT NULL COMMENT '代反向开票',
    `tenant_id`         bigint     NULL DEFAULT NULL COMMENT '租户id',
    `create_by`         bigint     NULL DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime   NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint     NULL DEFAULT NULL COMMENT '最后更新人',
    `update_time`       datetime   NULL DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1) NULL DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`           bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_TENANT_CONFIG_TENANT_ID` (`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '租户配置'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_tenant_config
-- ----------------------------

-- ----------------------------
-- Table structure for upms_tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant_info`;
CREATE TABLE `upms_tenant_info`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键',
    `tenant_code`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编码',
    `tenant_name`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '名称',
    `tenant_logo`   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户logo',
    `tenant_type`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户类型:数据字典(TENANT_TYPE)',
    `tenant_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '状态:数据字典(STATE)',
    `create_by`     bigint                                                 NULL DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime                                               NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 NULL DEFAULT NULL COMMENT '最后更新人',
    `update_time`   datetime                                               NULL DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                             NULL DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_TENANT_INFO_CODE` (`tenant_code` ASC) USING BTREE,
    FULLTEXT INDEX `IDX_TENANT_INFO_TENANT_NAME` (`tenant_name`)
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '租户'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_tenant_info
-- ----------------------------

-- ----------------------------
-- Table structure for upms_user
-- ----------------------------
DROP TABLE IF EXISTS `upms_user`;
CREATE TABLE `upms_user`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键',
    `user_type`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '用户类型',
    `tenant_id`     bigint                                                 NULL     DEFAULT NULL COMMENT '租户id',
    `terminal_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '终端类型',
    `name`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户的姓名',
    `nick_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户昵称',
    `first_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '用户的名字',
    `last_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '用户的姓氏',
    `password`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户密码',
    `salt`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '加密盐值',
    `state`         tinyint(1)                                             NOT NULL DEFAULT 1 COMMENT '数据状态',
    `email`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '邮箱',
    `phone`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL     DEFAULT NULL COMMENT '手机号',
    `avatar`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '用户头像',
    `signature`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '个性签名',
    `remark`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT NULL COMMENT '备注',
    `create_by`     bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`   datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`   datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `C_INX_CONF_CODE` (`user_type` ASC) USING BTREE,
    INDEX `IDX_UPMS_USER_TENANT_ID` (`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '系统用户信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_user
-- ----------------------------
INSERT INTO `upms_user`
VALUES (1, 'ADMIN', NULL, 'SYSTEM_TERMINAL', 'admin', 'admin', 'admin', 'admin',
        '$2a$10$ndTV5/gk9rd17U/Nspkp5OmDNA2eGExhiVmgk4PmICY6NMKWOVMVK', '2937483729847', 1, '<EMAIL>',
        '***********', '', '', '', 1, '2021-02-25 17:08:45', 1, '2025-02-28 03:13:52', 0, 3);
INSERT INTO `upms_user`
VALUES (1001, 'TENANT', NULL, NULL, 'frp', 'frp', 'frp', 'frp',
        '$2a$10$GYgUFjSu172zXs4LyO1u/Oxg2FTAO47TlAZIllklrSK5c75qUqCX.', NULL, 1, '<EMAIL>', '***********',
        '', '', '', 1, '2021-02-25 17:08:45', 1, '2025-02-28 03:13:52', 0, 0);
INSERT INTO `upms_user`
VALUES (1916737477358817282, 'TENANT', 1001, NULL, 'bop bop', 'bop bop', 'bop', 'bop',
        '$2a$10$yPg2N.QkKte9twARo6T1j.ChOkGIAOe/aK0LeNry23ekq9oRCE8XO', NULL, 1, '<EMAIL>', '**********',
        NULL, NULL, NULL, 1, '2025-04-28 06:13:21', 1, '2025-04-28 06:13:21', 0, 0);

-- ----------------------------
-- Table structure for upms_user_account
-- ----------------------------
DROP TABLE IF EXISTS `upms_user_account`;
CREATE TABLE `upms_user_account`
(
    `id`             bigint                                                 NOT NULL COMMENT '主键',
    `user_id`        bigint                                                 NOT NULL COMMENT '用户id',
    `user_account`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户账号',
    `account_type`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '帐号类型',
    `state`          tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `create_by`      bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`    datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`      bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`    datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`    tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `login_type`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '登陆类型',
    `login_ip`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '登陆ip',
    `login_terminal` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '登陆终端',
    `login_date`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '登陆时间',
    `expired_date`   datetime                                               NULL DEFAULT NULL COMMENT '账号有效期时间',
    `active_date`    datetime                                               NULL DEFAULT NULL COMMENT '活跃时间',
    `version`        bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_USER_ACCOUNT` (`user_account` ASC) USING BTREE,
    INDEX `IDX_UPMS_USER_ACCOUNT_USER_ID` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '用户账号信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_user_account
-- ----------------------------
INSERT INTO `upms_user_account`
VALUES (1, 1, 'admin', 'USER_NAME', 1, NULL, 1, '2024-01-10 00:00:00', 1, '2024-10-25 22:14:38', 0, NULL, NULL, NULL,
        NULL, NULL, NULL, 0);
INSERT INTO `upms_user_account`
VALUES (1001, 1001, '<EMAIL>', 'USER_NAME', 1, NULL, 1, '2024-01-10 00:00:00', 1, '2024-10-25 22:14:38', 0,
        NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `upms_user_account`
VALUES (1916737477375594497, 1916737477358817282, '<EMAIL>', 'USER_NAME', 1, NULL, 1, '2025-04-28 06:13:21',
        1, '2025-04-28 06:13:21', 0, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for upms_user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `upms_user_permissions`;
CREATE TABLE `upms_user_permissions`
(
    `id`                  bigint                                                  NOT NULL COMMENT '主键',
    `user_id`             bigint                                                  NOT NULL COMMENT '用户id',
    `data_scope`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'SELF' COMMENT '数据范围（可看全部 ALL ，看个人 SELF， 看组织 ORGNIZATION）',
    `data_scope_dept_ids` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL     DEFAULT '' COMMENT '数据范围(指定部门数组)',
    `create_by`           bigint                                                  NOT NULL COMMENT '创建人',
    `create_time`         datetime                                                NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                  NOT NULL COMMENT '最后更新人',
    `update_time`         datetime                                                NOT NULL COMMENT '最后更新时间',
    `remove_flag`         tinyint(1)                                              NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`             bigint                                                  NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '用户权限信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_user_permissions
-- ----------------------------
INSERT INTO `upms_user_permissions`
VALUES (100001, 1001, 'DEPT',
        '1823974819587809281,1823977980008402945,1823978236745945090,1823978408846626818,1823978820341403650,1823991097509666817,1825426726276759553,1825826727017943041,1826566976853610498,1826905612245635073,1827890925361270786,1827960505517576193,1828275016787136514,1828277025643761665,1828278058512056321,1828278592514060289,1828319853586518018,1828324934125981698,1828350764646502402,1828602748272218114,1828640089543131138,1828672390763159554,1828674058984660993,1828677418529976321,1828680739902734337,1828694803299291137,1828703151117598721,1828715614262366210,1828980453253681153,1828988496204894209,1828988844109828098,1828998491914997762,1829001118472048642,1829031319113404418,1829033938795991042,1829046140466728962,1829085901369741314,1829327967584620546,1829350890278064130,1831221622415134721,1831221770667003906,1831221924434382849,1831242840928722946,1831242878799093762,1831242896004128770,1831242910889713665,1831242927335579650,1831242946256084994,1831242962857140225,1831242979969900545,1831242999095926786,1831243015701176322,1831243031098466306,1831243044205666306,1831243056968933377,1831243073691623425,1831243085595058177,1831243098303799298,1831243111721377794,1831243151495962626,1831243167832776705,1831243187810246657,1831243200737091586,1831243216734167041,1831243229510017026,1831243246434033665,1831243263483879425,1831243278667259906,1831243294949548034,1831243309323427841,1831243321646292994,1831243345738375169,1831580264683376642,1831582926778765313,1831957499323977729,1832240195275165697,1832247435717611522,1832253244680757249,1832262890874523650,1832354136827465729,1833425270075396097,1836219988928729089,1836248544383700994,1836316974336020481,1837004058705104897,1837004848043757570,1837012624027017217,1837022321698770945,1837028043266297858,1837371120896020482,1837375029114933249,1838183185776742401,1838186473259012098,1838192441065738241,1838471936893620225,1838488828488290306,1838489735795937282,1839254842436063233,1839508843647832066,1805153612487299074,',
        1797577031307759618, '2024-06-05 18:17:15', 1, '2024-10-25 22:14:38', 0, 0);
INSERT INTO `upms_user_permissions`
VALUES (1798298027698671617, 1, 'DEPT',
        '1823974819587809281,1823977980008402945,1823978236745945090,1823978408846626818,1823978820341403650,1823991097509666817,1825426726276759553,1825826727017943041,1826566976853610498,1826905612245635073,1827890925361270786,1827960505517576193,1828275016787136514,1828277025643761665,1828278058512056321,1828278592514060289,1828319853586518018,1828324934125981698,1828350764646502402,1828602748272218114,1828640089543131138,1828672390763159554,1828674058984660993,1828677418529976321,1828680739902734337,1828694803299291137,1828703151117598721,1828715614262366210,1828980453253681153,1828988496204894209,1828988844109828098,1828998491914997762,1829001118472048642,1829031319113404418,1829033938795991042,1829046140466728962,1829085901369741314,1829327967584620546,1829350890278064130,1831221622415134721,1831221770667003906,1831221924434382849,1831242840928722946,1831242878799093762,1831242896004128770,1831242910889713665,1831242927335579650,1831242946256084994,1831242962857140225,1831242979969900545,1831242999095926786,1831243015701176322,1831243031098466306,1831243044205666306,1831243056968933377,1831243073691623425,1831243085595058177,1831243098303799298,1831243111721377794,1831243151495962626,1831243167832776705,1831243187810246657,1831243200737091586,1831243216734167041,1831243229510017026,1831243246434033665,1831243263483879425,1831243278667259906,1831243294949548034,1831243309323427841,1831243321646292994,1831243345738375169,1831580264683376642,1831582926778765313,1831957499323977729,1832240195275165697,1832247435717611522,1832253244680757249,1832262890874523650,1832354136827465729,1833425270075396097,1836219988928729089,1836248544383700994,1836316974336020481,1837004058705104897,1837004848043757570,1837012624027017217,1837022321698770945,1837028043266297858,1837371120896020482,1837375029114933249,1838183185776742401,1838186473259012098,1838192441065738241,1838471936893620225,1838488828488290306,1838489735795937282,1839254842436063233,1839508843647832066,1805153612487299074,',
        1797577031307759618, '2024-06-05 18:17:15', 1, '2024-10-25 22:14:38', 0, 0);

-- ----------------------------
-- Table structure for upms_user_tenant
-- ----------------------------
DROP TABLE IF EXISTS `upms_user_tenant`;
CREATE TABLE `upms_user_tenant`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键',
    `user_id`      bigint                                                 NULL DEFAULT NULL COMMENT '用户id',
    `tenant_id`    bigint                                                 NULL DEFAULT NULL COMMENT '租户id',
    `first_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '名字',
    `last_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '姓氏',
    `email`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电子邮件',
    `phone`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NULL DEFAULT NULL COMMENT '电话号码',
    `active_flag`  tinyint(1)                                             NOT NULL COMMENT '激活标志',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '删除备注',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`  datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`      bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `IDX_USER_TENANT_USER_ID` (`user_id` ASC) USING BTREE,
    INDEX `IDX_USER_TENANT_TENANT_ID` (`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT = '用户租户信息'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of upms_user_tenant
-- ----------------------------
INSERT INTO `upms_user_tenant`
VALUES (1, 1, 1, 'admin', 'admin', 'admin', '**********', 1, '', 1, '2024-10-30 17:45:49', 1857360949485883393,
        '2024-11-15 18:33:32', 0, 0);
INSERT INTO `upms_user_tenant`
VALUES (1001, 1001, 1001, 'frp', 'frp', 'frp', '**********', 1, '', 1, '2024-10-30 17:45:49', 1857360949485883393,
        '2024-11-15 18:33:32', 0, 0);
INSERT INTO `upms_user_tenant`
VALUES (1916737477379788802, 1916737477358817282, 1001, 'bop', 'bop', '<EMAIL>', '**********', 1, NULL, 1,
        '2025-04-28 06:13:21', 1, '2025-04-28 06:13:21', 0, 0);

SET FOREIGN_KEY_CHECKS = 1;


INSERT INTO `number_generate_record` (`id`, `code`, `prefix_code`, `max_num`, `create_by`, `create_time`, `update_by`,
                                      `update_time`, `remove_flag`, `version`, `tenant_id`)
VALUES (1924039748352339970, 'BIN_LOCATION', '011-LOC', 20000, 1, '2025-05-18 09:49:58',
        1, '2025-05-18 09:49:58', 0, 0, NULL);
INSERT INTO `number_generate_record` (`id`, `code`, `prefix_code`, `max_num`, `create_by`, `create_time`, `update_by`,
                                      `update_time`, `remove_flag`, `version`, `tenant_id`)
VALUES (1923261811205005314, 'BIN_LOCATION', '010-LOC', 20000, 1, '2025-05-16 06:18:43',
        1, '2025-05-16 06:18:43', 0, 0, NULL);


INSERT INTO `number_generate_config` (`id`, `code`, `prefix_code`, `date_format`, `stream_length`, `current_num`,
                                      `step_size`, `reset_flag`, `description`, `create_by`, `create_time`, `update_by`,
                                      `update_time`, `remove_flag`, `tenant_id`, `version`)
VALUES (57, 'TRANSFER_OWNER_SHIP_REQUEST', 'TOSR', 'yyMMdd', 4, 0, 1000, 1, '货权转移', 1, '2025-01-17 13:58:27', 1,
        '2025-01-17 13:58:31', 0, NULL, 0);

