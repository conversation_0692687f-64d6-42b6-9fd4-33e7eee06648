spring:
    # 服务名称
    application:
        name: frp-business-@NACOS_GROUP@
    # 配置spring cloud的配置中心，采用alibaba的nacos做为配置中心
    cloud:
        nacos:
            config:
                server-addr: @NACOS_HOST@:@NACOS_PORT@
                file-extension: yaml
                ## uat和prod
                namespace: @NACOS_NAMESPACE@
                username: @NACOS_USERNAME@
                password: @NACOS_PASSWORD@
                # 分组
                group: @NACOS_GROUP@
                # nacos共享配置，数据库和redis对于uat和prod环境需要配置ssl秘钥
                shared-configs:
                    -   data-id: frp-dynamic-@NACOS_GROUP@.yaml
                        group: @NACOS_GROUP@
                    -   data-id: frp-redis-@NACOS_GROUP@.yaml
                        group: @NACOS_GROUP@
                    -   data-id: frp-feign.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-security.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-xxl-job.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-tenant.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-warehouse.yaml
                        group: @SHARE_GROUP@
            discovery:
                server-addr: @NACOS_HOST@:@NACOS_PORT@
                ## uat和prod
                namespace: @NACOS_NAMESPACE@
                username: @NACOS_USERNAME@
                password: @NACOS_PASSWORD@