package cn.need.cloud.biz.service.otc.pkg;

import cn.need.cloud.biz.model.entity.otc.OtcPackageLabel;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageLabelVO;
import cn.need.cloud.biz.service.base.MarkPrintedService;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC包裹标签 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPackageLabelService extends SuperService<OtcPackageLabel>, MarkPrintedService<OtcPackageLabel, OtcPackageLabelService, PrintQuery> {

    /**
     * 根据包裹id获取标签集合
     *
     * @param id 包裹id
     * @return 包裹标签集合
     */
    List<OtcPackageLabelVO> listByPackageId(Long id);

    /**
     * 判断包裹是否全部标签打印成功
     *
     * @param otcPackageId 包裹id
     * @return 成功
     */
    boolean isAllLabelPrintedSuccess(Long otcPackageId);

    /**
     * 根据包裹id集合获取标签集合
     *
     * @param packageIdList 包裹id集合
     * @return 包裹标签集合
     */
    Map<Long, List<OtcPackageLabel>> listByPackageIdList(List<Long> packageIdList);
}