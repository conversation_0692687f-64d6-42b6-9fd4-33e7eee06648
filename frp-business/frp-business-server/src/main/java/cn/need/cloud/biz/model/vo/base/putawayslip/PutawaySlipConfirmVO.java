package cn.need.cloud.biz.model.vo.base.putawayslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * PutawaySlipConfirmVO
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Data
public class PutawaySlipConfirmVO implements Serializable {

    @Schema(description = "上架单id")
    private Long id;

    @Schema(description = "上架单状态")
    private String putawaySlipStatus;

    @Schema(description = "上架单RefNum")
    private String refNum;
}
