package cn.need.cloud.biz.controller.inventory;

import cn.need.cloud.biz.converter.inventory.InventoryAuditConverter;
import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.model.param.inventory.create.InventoryAuditCreateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryAuditQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryAuditVO;
import cn.need.cloud.biz.model.vo.page.InventoryAuditPageVO;
import cn.need.cloud.biz.service.inventory.InventoryAuditService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 库存盘点 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@RestController
@RequestMapping("/api/biz/inventory-audit")
@Tag(name = "库存盘点")
public class InventoryAuditController extends AbstractRestController<InventoryAuditService, InventoryAudit, InventoryAuditConverter, InventoryAuditVO> {

    @Operation(summary = "盘点库存", description = "接收库存盘点的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/inventory-audit")
    public Result<Boolean> inventoryAudit(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InventoryAuditCreateParam param) {
        service.lockedInventoryAudit(param);
        // 返回结果
        return success(true);
    }

    @Operation(summary = "批量盘点库存", description = "接收库存盘点的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/batch-inventory-audit")
    public Result<Boolean> batchInventoryAudit(@Valid @RequestBody @Parameter(description = "数据对象", required = true) List<InventoryAuditCreateParam> createParams) {
        service.batchInventoryAudit(createParams);
        // 返回结果
        return success(true);
    }

    @Operation(summary = "盘点库存", description = "接收库存盘点的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/inventory-audit-bop")
    public Result<Long> inventoryAuditWithBop(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InventoryAuditCreateParam param) {

        // 返回结果
        return success(service.inventoryAuditWithBop(param));
    }

    @Operation(summary = "根据id获取库存盘点详情", description = "根据数据主键id，从数据库中获取其对应的库存盘点详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InventoryAuditVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取库存盘点详情
        InventoryAuditVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取库存盘点详情", description = "根据数据RefNum，从数据库中获取其对应的库存盘点详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InventoryAuditVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取库存盘点详情
        InventoryAuditVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取库存盘点分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的库存盘点列表")
    @PostMapping(value = "/list")
    public Result<PageData<InventoryAuditPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InventoryAuditQuery> search) {

        // 获取库存盘点分页
        PageData<InventoryAuditPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
