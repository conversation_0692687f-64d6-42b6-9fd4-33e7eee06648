package cn.need.cloud.biz.model.vo.otb.ri;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * otb发货指南托盘标签 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "otb发货指南托盘标签 vo对象")
public class OtbRoutingInstructionPalletLabelVO extends BaseSuperVO {


    /**
     * 路由指令ID
     */
    @Schema(description = "路由指令ID")
    private Long otbRoutingInstructionId;

    /**
     * 托盘SSCC编号
     */
    @Schema(description = "托盘SSCC编号")
    @NotBlank(message = "palletSsccNum cannot be empty")
    private String palletSsccNum;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型")
    @NotBlank(message = "labelType cannot be empty")
    private String labelType;

    /**
     * label RefNum
     */
    @Schema(description = "labelRefNum")
    @NotBlank(message = "labelRefNum cannot be empty")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型")
    @NotBlank(message = "paperType cannot be empty")
    private String paperType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    @NotBlank(message = "rawDataType cannot be empty")
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    @NotBlank(message = "fileIdRawDataType cannot be empty")
    private String fileIdRawDataType;

    /**
     * label数据类型
     */
    @Schema(description = "label数据类型")
    @NotBlank(message = "labelRawData cannot be empty")
    private String labelRawData;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    @NotNull(message = "lineNum cannot be empty")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

}