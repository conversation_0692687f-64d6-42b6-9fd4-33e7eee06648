package cn.need.cloud.biz.model.query.inbound;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上架详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上架详情 query对象")
public class InboundPutawaySlipDetailQuery extends SuperQuery {

    /**
     * 入库工单详情id
     */
    @Schema(description = "入库工单详情id")
    private Long inboundWorkOrderDetailId;

    /**
     * 入库工单id
     */
    @Schema(description = "inboundWorkorderId")
    private Long inboundWorkOrderId;

    /**
     * inboundUnloadId
     */
    @Schema(description = "inboundUnloadId")
    private Long inboundUnloadId;

    /**
     * inboundPutawaySlipId
     */
    @Schema(description = "inboundPutawaySlipId")
    private Long inboundPutawaySlipId;

    /**
     * pickBookQty
     */
    @Schema(description = "pickBookQty")
    private Integer pickBookQty;

    /**
     * pickFinishQty
     */
    @Schema(description = "pickFinishQty")
    private Integer pickFinishQty;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;


}