package cn.need.cloud.biz.model.bo.otc;

import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.vo.base.BasePackedVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageFullOutputVO;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 构建包裹上下文
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
@Getter
@Setter
public class OtcBuildByWarehouseContextBO {

    /**
     * 构建包裹所属工单
     */
    private OtcWorkorder workOrder;

    /**
     * 包裹
     */
    private OtcPackage pkg;

    /**
     * 在该工单下的包裹集合
     */
    private List<OtcPackage> inWorkorderPackageList;

    /**
     * 包裹详情
     */
    private List<OtcPackageDetail> packageDetailList;

    /**
     * 包裹标签
     */
    private OtcPackageLabel label;

    /**
     * 工单详情
     */
    private List<OtcWorkorderDetail> workorderDetailList;

    /**
     * 相应参数
     */
    private OtcPackageFullOutputVO result;

    /**
     * 当前箱子信息
     */
    private ProductMultibox currentBox;

    /**
     * 所有箱子能够构建的情况
     */
    private Map<String, Boolean> canBuildMap;

    /**
     * 构建完包裹后置处理
     */
    private Consumer<OtcBuildByWarehouseContextBO> afterBuildConsumer = context -> {
    };


    private List<BasePackedVO> packedList = new ArrayList<>();
}
