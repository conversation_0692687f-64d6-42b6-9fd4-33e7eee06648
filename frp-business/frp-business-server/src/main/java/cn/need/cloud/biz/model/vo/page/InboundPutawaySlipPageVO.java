package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 上架 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上架 vo对象")
public class InboundPutawaySlipPageVO extends BaseSuperVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createBy;

    /**
     * 最后更新人
     */
    @Schema(description = "最后更新人")
    private Long updateBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 上架状态
     */
    @Schema(description = "上架状态")
    private String inboundPutawaySlipStatus;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseVO baseWarehouseVO;

    /**
     * 回滚或上架按钮是否置灰
     */
    @Schema(description = "回滚或上架按钮是否置灰")
    private Boolean flag = true;

}