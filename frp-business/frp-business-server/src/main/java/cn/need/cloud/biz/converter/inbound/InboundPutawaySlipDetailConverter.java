package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundPutawaySlipDetailDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlipDetail;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 上架详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundPutawaySlipDetailConverter extends AbstractModelConverter<InboundPutawaySlipDetail, InboundPutawaySlipDetailVO, InboundPutawaySlipDetailDTO> {

}
