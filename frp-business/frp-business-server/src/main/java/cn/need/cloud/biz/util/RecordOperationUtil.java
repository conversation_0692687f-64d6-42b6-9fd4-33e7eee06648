package cn.need.cloud.biz.util;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 记录操作工具类，提供基于ID的记录变更识别功能
 */
public final class RecordOperationUtil {

    private RecordOperationUtil() {
        // 工具类禁止实例化
        throw new AssertionError("No RecordOperationUtils instances for you!");
    }

    /**
     * 根据ID识别需要更新、删除和新增的记录
     *
     * @param existingRecords 数据库中已存在的记录集合
     * @param newRecords      新的记录集合
     * @param idExtractor     从实体中提取ID的函数
     * @param <T>             实体类型
     * @param <ID>            ID类型
     * @return 包含需要操作的记录分类的结果对象
     * @throws NullPointerException 如果任何参数为null
     */
    public static <T, ID> RecordChangeSet<T> identifyChanges(
            Collection<T> existingRecords,
            Collection<T> newRecords,
            Function<T, ID> idExtractor) {

        Objects.requireNonNull(existingRecords, "Existing records must not be null");
        Objects.requireNonNull(newRecords, "New records must not be null");
        Objects.requireNonNull(idExtractor, "ID extractor function must not be null");

        // 如果两个集合都为空，直接返回空结果
        if (existingRecords.isEmpty() && newRecords.isEmpty()) {
            return RecordChangeSet.empty();
        }

        // 创建已存在记录的ID到记录的映射，便于快速查找
        Map<ID, T> existingRecordsMap = existingRecords.stream()
                .filter(Objects::nonNull)
                .filter(r -> idExtractor.apply(r) != null)
                .collect(Collectors.toMap(
                        idExtractor,
                        Function.identity(),
                        // 如有重复ID，保留第一个遇到的记录
                        (existing, replacement) -> existing
                ));

        List<T> recordsToUpdate = new ArrayList<>();
        List<T> recordsToInsert = new ArrayList<>();
        Set<ID> processedIds = new HashSet<>();

        // 划分需要更新和新增的记录
        for (T newRecord : newRecords) {
            if (newRecord == null) {
                continue;
            }

            ID id = idExtractor.apply(newRecord);
            if (id != null && existingRecordsMap.containsKey(id)) {
                recordsToUpdate.add(newRecord);
                processedIds.add(id);
            } else {
                recordsToInsert.add(newRecord);
            }
        }

        // 识别需要删除的记录
        List<T> recordsToDelete = existingRecordsMap.entrySet().stream()
                .filter(entry -> !processedIds.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .toList();

        return new RecordChangeSet<>(
                Collections.unmodifiableList(recordsToUpdate),
                Collections.unmodifiableList(recordsToInsert),
                Collections.unmodifiableList(recordsToDelete)
        );
    }


}