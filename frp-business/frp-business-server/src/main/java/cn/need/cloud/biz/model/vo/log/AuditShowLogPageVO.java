package cn.need.cloud.biz.model.vo.log;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * query对象
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = " query对象")
public class AuditShowLogPageVO extends BaseSuperVO {

    /**
     * refTableId
     */
    @Schema(description = "refTableId")
    private Long refTableId;

    /**
     * refTableName
     */
    @Schema(description = "refTableName")
    private String refTableName;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum")
    private String refTableRefNum;

    /**
     * refTableShowName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;

    /**
     * event
     */
    @Schema(description = "event")
    private String event;

    /**
     * type
     */
    @Schema(description = "type")
    private String type;

    /**
     * showFlag
     */
    @Schema(description = "showFlag")
    private Boolean showFlag;

    /**
     * warehouseId
     */
    @Schema(description = "warehouseId")
    private Long warehouseId;

    /**
     * note
     */
    @Schema(description = "note")
    private String note;

    /**
     * description
     */
    @Schema(description = "description")
    private String description;

    /**
     * tenantId
     */
    @Schema(description = "tenantId")
    private Long tenantId;


}