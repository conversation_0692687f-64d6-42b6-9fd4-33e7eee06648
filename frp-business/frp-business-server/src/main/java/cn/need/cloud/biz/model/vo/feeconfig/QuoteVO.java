package cn.need.cloud.biz.model.vo.feeconfig;

import cn.need.cloud.biz.model.vo.base.FeeConfigRefNumVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * 仓库报价 VO对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价 VO对象")
public class QuoteVO extends RefNumVO {

    @Serial
    private static final long serialVersionUID = 6419271333873575687L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 费用配置Inbound
     */
    @Schema(description = "费用配置Inbound")
    private List<FeeConfigRefNumVO> feeConfigInboundList;

    /**
     * 费用配置Otc
     */
    @Schema(description = "费用配置Otc")
    private List<FeeConfigRefNumVO> feeConfigOtcList;

    /**
     * 费用配置Otc
     */
    @Schema(description = "费用配置Otb")
    private List<FeeConfigRefNumVO> feeConfigOtbList;

    /**
     * 费用配置Storage
     */
    @Schema(description = "费用配置Storage")
    private List<FeeConfigRefNumVO> feeConfigStorageList;


}