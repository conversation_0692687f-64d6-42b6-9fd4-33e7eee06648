package cn.need.cloud.biz.model.vo.inbound.workorder;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 入库工单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库工单 vo对象")
public class InboundWorkorderVO extends BaseSuperVO {


    /**
     * 入库请求运输方式类型
     */
    @Schema(description = "入库请求运输方式类型")
    private String transportMethodType;

    /**
     * request快照transactionPartnerId
     */
    @Schema(description = "request快照transactionPartnerId")
    private Long transactionPartnerId;

    /**
     * 入库请求id
     */
    @Schema(description = "入库请求id")
    private Long inboundRequestId;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum")
    private String requestSnapshotRequestRefNum;

    /**
     * 请求单预计到达时间
     */
    @Schema(description = "请求单预计到达时间")
    private LocalDateTime estimateArrivalDate;

    /**
     * 请求单实际到达时间
     */
    @Schema(description = "请求单实际到达时间")
    private LocalDateTime actualArrivalDate;

    /**
     * 请求单物流跟踪编码
     */
    @Schema(description = "请求单物流跟踪编码")
    private String trackingNum;

    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String fromAddressPhone;

    /**
     * request快照备注
     */
    @Schema(description = "request快照备注")
    private String requestSnapshotNote;

    /**
     * 入库工单 卸货状态
     */
    @Schema(description = "入库工单 卸货状态")
    private String inboundWorkorderUnloadStatus;

    /**
     * 入库工单 上架状态
     */
    @Schema(description = "入库工单 上架状态")
    private String inboundWorkorderPutawayStatus;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String fromAddressNote;

    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * 入库工单状态
     */
    @Schema(description = "入库工单状态")
    private String inboundWorkorderStatus;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String containerType;

    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean fromAddressIsResidential;

    /**
     * 入库工单详情
     */
    @Schema(description = "入库工单详情")
    private List<InboundWorkorderDetailVO> details;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseVO baseWarehouseVO;

}