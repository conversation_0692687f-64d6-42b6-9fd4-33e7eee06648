package cn.need.cloud.biz.model.vo.otc.request;

import cn.need.cloud.biz.model.vo.otc.BaseDetailFullVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTC请求详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC请求Package基础详情 vo对象")
public class OtcRequestPackageDetailFullVO extends BaseDetailFullVO {

}