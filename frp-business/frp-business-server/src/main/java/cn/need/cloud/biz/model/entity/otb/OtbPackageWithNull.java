package cn.need.cloud.biz.model.entity.otb;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * OTB包裹
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_package")
public class OtbPackageWithNull extends OtbPackage {
    /**
     * otb托盘id
     */
    @TableField(value = "otb_pallet_id", updateStrategy = FieldStrategy.ALWAYS)
    private Long otbPalletId;

}

