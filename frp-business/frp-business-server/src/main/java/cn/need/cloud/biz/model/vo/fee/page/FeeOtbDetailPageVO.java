package cn.need.cloud.biz.model.vo.fee.page;

import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * 费用详情otb 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "费用详情otb 分页列表VO对象")
public class FeeOtbDetailPageVO extends SuperVO {

    @Serial
    private static final long serialVersionUID = -2563179069861324393L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 费用
     */
    @Schema(description = "费用")
    private BigDecimal fee;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id")
    private Long feeConfigDetailId;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id")
    private Long feeConfigId;

    /**
     * header表id
     */
    @Schema(description = "header表id")
    private Long headerId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;


}