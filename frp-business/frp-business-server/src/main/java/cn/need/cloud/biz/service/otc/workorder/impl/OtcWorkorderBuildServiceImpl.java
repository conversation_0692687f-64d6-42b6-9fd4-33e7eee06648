package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.WorkorderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkOrderPrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductConfigTypeEnum;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderDetailReturnContext;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderInputContext;
import cn.need.cloud.biz.model.bo.inventory.CheckInventoryProductBO;
import cn.need.cloud.biz.model.bo.otc.OtcSplitRequestBO;
import cn.need.cloud.biz.model.bo.otc.OtcSplitRequestDetailBO;
import cn.need.cloud.biz.model.bo.otc.OtcWorkOrderReturnContext;
import cn.need.cloud.biz.model.bo.product.OtcWorkorderCreateContext;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderDetail;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcPackageBatchCreateParam;
import cn.need.cloud.biz.model.param.otc.create.workorder.OtcWorkorderCreateParam;
import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;
import cn.need.cloud.biz.model.vo.otc.BaseDetailFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestDetailFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageDetailFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.inventory.InventoryService;
import cn.need.cloud.biz.service.inventory.WorkorderBuildService;
import cn.need.cloud.biz.service.inventory.impl.BuildWorkorderContextUtil;
import cn.need.cloud.biz.service.inventory.impl.InventoryUtil;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageDetailService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.ship.OtcShipStationConfigService;
import cn.need.cloud.biz.service.otc.workorder.*;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.cloud.biz.service.warehouse.WarehouseProductService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC工单构建服务实现类
 * </p>
 * <p>
 * 该类实现了OTC工单的构建功能，负责将OTC请求拆分为多个工单。
 * 根据产品类型（多箱产品/单订单单件/单订单多件/即贴即发）、包装信息、库存信息等
 * 使用不同的拆单逻辑，生成对应的工单明细。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 根据产品类型将OTC请求拆分为不同类型的工单
 * 2. 处理多箱产品、即贴即发产品的特殊拆单逻辑
 * 3. 在拆分过程中对库存进行锁定和预留
 * 4. 处理需要组装、拆分或合并的产品，生成对应的预处理工单（PrepWorkorder）
 * 5. 根据工单类型创建对应的包装信息
 * </p>
 * <p>
 * 该类在OTC流程中起到关键作用，将客户请求转换为可执行的工单，
 * 是连接请求处理和实际仓库操作的桥梁。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@Lazy
public class OtcWorkorderBuildServiceImpl implements OtcWorkorderBuildService {

    /**
     * OTC工单详情服务，用于创建和管理工单详情
     */
    @Resource
    private OtcWorkorderDetailService otcWorkorderDetailService;

    /**
     * OTC包装详情服务，用于创建和管理包装详情
     */
    @Resource
    private OtcPackageDetailService otcPackageDetailService;

    /**
     * OTC包装服务，用于创建和管理包装信息
     */
    @Resource
    private OtcPackageService otcPackageService;

    /**
     * OTC请求服务，用于获取请求信息
     */
    @Resource
    private OtcRequestService otcRequestService;

    /**
     * OTC工单服务，用于创建和管理工单
     */
    @Resource
    private OtcWorkorderService otcWorkorderService;

    /**
     * 库存服务，用于查询和管理库存信息
     */
    @Resource
    private InventoryService inventoryService;

    /**
     * 仓库产品服务，用于获取产品在仓库中的特殊属性，如即贴即发标记
     */
    @Resource
    private WarehouseProductService warehouseProductService;

    /**
     * 库存锁定服务，用于锁定库存，防止并发操作导致的库存冲突
     */
    @Resource
    private InventoryLockedService inventoryLockedService;

    /**
     * 库存预留服务，用于预留库存，确保工单执行时有足够的库存
     */
    @Resource
    private InventoryReserveService inventoryReserveService;

    /**
     * OTC预处理工单详情服务，用于创建和管理预处理工单详情
     */
    @Resource
    private OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;

    /**
     * OTC预处理工单服务，用于创建和管理预处理工单
     */
    @Resource
    private OtcPrepWorkorderService otcPrepWorkorderService;

    /**
     * 工单构建服务，提供通用的工单构建功能
     */
    @Resource
    private WorkorderBuildService workorderBuildService;

    /**
     * 产品特殊服务，用于获取产品的特殊配置，如多箱版本
     */
    @Resource
    private ProductSpecialService productSpecialService;

    /**
     * 发货站配置服务，用于获取发货配置信息
     */
    @Resource
    private OtcShipStationConfigService shipStationConfigService;

    /**
     * 根据参数创建工单
     * <p>
     * 该方法是工单构建的核心方法，负责将OTC请求拆分为多个工单。
     * 整个流程包括以下步骤：
     * 1. 获取并验证OTC请求信息
     * 2. 构建工单创建上下文，包含产品信息、库存信息等
     * 3. 根据产品类型拆分请求，形成统一的拆分结果对象
     * 4. 将拆分结果转换为工单上下文对象
     * 5. 对需要预处理的工单进行二次拆分
     * 6. 格式化行号、填充库存锁定/预留的引用信息
     * 7. 将工单及其关联实体落库，包括包装、预处理工单、工单详情等
     * </p>
     * <p>
     * 在此过程中会进行库存锁定与预留，确保工单执行时有足够的库存。
     * 如果产品需要组装、拆分或合并，还会生成对应的预处理工单（PrepWorkorder）。
     * </p>
     * <p>
     * 该方法在事务中执行，确保所有操作要么全部成功，要么全部失败。
     * </p>
     *
     * @param createParam OTC工单创建参数，包含OTC请求ID和备注信息
     * @throws BusinessException 如果请求不存在、状态不允许构建工单、库存不足或其他错误，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertByParam(OtcWorkorderCreateParam createParam) {
        Long otcRequestId = createParam.getOtcRequestId();
        OtcRequestVO request = otcRequestService.detailById(otcRequestId);

        // 1. 基本校验
        checkRequestValid(request, otcRequestId);

        // 2. 构建工单创建上下文
        OtcWorkorderCreateContext workorderCreateContext = buildCreateContext(request);

        // 3. 拆分RequestDetail，形成统一的拆分结果对象
        OtcSplitRequestBO splitRequestBO = buildOtcSplitRequestBO(request, workorderCreateContext);

        // 4. 将拆分结果进一步生成若干个WorkOrder上下文对象
        List<OtcWorkOrderReturnContext> returnContextList = splitRequestToWorkOrderList(splitRequestBO, workorderCreateContext);

        // 5. 对SlapAndGo和MultiBox的部分根据PrepWorkorder进行二次拆分（如需要）
        List<OtcWorkOrderReturnContext> finalReturnContextList = refineWorkOrderByPrep(returnContextList);

        // 6. 格式化行号、填充InventoryLocked/Reserve的引用信息
        BuildWorkorderContextUtil.finalizeLineNumberAndInventoryRefs(finalReturnContextList);

        // 7. 将工单及其关联实体落库：Package、PrepWorkorder、WorkorderDetail等
        persistAllData(request, finalReturnContextList, createParam.getNote());
    }

    /**
     * 校验请求是否合法
     * <p>
     * 该方法用于验证OTC请求是否存在以及状态是否允许构建工单。
     * 验证包括两个方面：
     * 1. 检查请求是否存在，如果不存在则抛出异常
     * 2. 检查请求状态是否允许审核（只有特定状态的请求才能构建工单）
     * </p>
     * <p>
     * 该方法是工单构建过程的第一道防线，确保只有有效的请求才能进入工单构建流程。
     * </p>
     *
     * @param request      OTC请求VO对象
     * @param otcRequestId OTC请求的ID
     * @throws BusinessException 如果请求不存在或状态不允许构建工单，则抛出业务异常
     */
    private void checkRequestValid(OtcRequestVO request, Long otcRequestId) {
        if (ObjectUtil.isEmpty(request)) {
            throw new BusinessException(StringUtil.format("otcRequestId:{} Not Found In otcRequest", otcRequestId));
        }
        if (RequestStatusEnum.isNotCanAudit(request.getOtcRequestStatus())) {
            throw new BusinessException(StringUtil.format(
                    "{} Current Status is {}, Count Be Build",
                    request.toString(),
                    request.getOtcRequestStatus()
            ));
        }
    }

    /**
     * 构建工单创建上下文
     * <p>
     * 该方法用于构建工单创建的上下文对象，包含产品信息、库存信息和产品分类。
     * 主要流程包括：
     * 1. 从请求详情中提取所有产品ID
     * 2. 查询库存信息并校验库存是否足够
     * 3. 提取产品信息并构建产品ID到产品信息的映射
     * 4. 从仓库产品服务获取即贴即发（SlapAndGo）标识
     * 5. 分别获取多箱产品、即贴即发产品和普通产品列表
     * 6. 对多箱产品进行特殊验证，确保其库存状态符合要求
     * </p>
     * <p>
     * 该方法对产品进行分类，将产品分为三类：
     * - 多箱产品（MultiBox）：由多个箱子组成的产品
     * - 即贴即发产品（SlapAndGo）：需要特殊处理的产品
     * - 普通产品（Normal）：其他产品
     * </p>
     *
     * @param request OTC请求VO对象
     * @return 工单创建上下文对象，包含产品分类和信息
     * @throws BusinessException 如果库存不足或多箱产品验证失败，则抛出业务异常
     */
    private OtcWorkorderCreateContext buildCreateContext(OtcRequestVO request) {
        // 1. 从requestDetail中提取所有productId
        List<Long> detailProductIdList = request.getDetailList().stream()
                .map(OtcRequestDetailFullVO::getProductId)
                .distinct()
                .toList();

        // 2. 查询库存信息
        Map<Long, InventoryVO> inventoryMap = inventoryService.getInventoryReturnAll(detailProductIdList);

        // 3. 校验库存
        List<CheckInventoryProductBO> checkInventoryProductBOList =
                BeanUtil.copyNew(request.getDetailList(), CheckInventoryProductBO.class);
        InventoryUtil.checkInventory(checkInventoryProductBOList, inventoryMap);

        // 4. 提取BaseFullProductVO并构建productId->BaseFullProductVO映射
        Map<Long, BaseFullProductVO> productMap = inventoryMap.values().stream()
                .filter(vo -> vo != null && vo.getBaseFullProductVO() != null)
                .collect(Collectors.toMap(
                        InventoryVO::getProductId,
                        InventoryVO::getBaseFullProductVO,
                        (existing, replacement) -> existing
                ));

        // 5. 从仓库产品获取SlapAndGo标识
        List<Long> productIdList = new ArrayList<>(inventoryMap.keySet());
        Map<Long, WarehouseProduct> warehouseProductMap = warehouseProductService.listByProductIdList(productIdList);

        // 6. 分别获取MultiBox、SlapAndGo产品列表
        List<Long> multiBoxProductIdList = productMap.values().stream()
                .filter(BaseFullProductVO::getMultiboxFlag)
                .map(BaseFullProductVO::getId)
                .distinct()
                .toList();

        if (!ObjectUtil.isEmpty(multiBoxProductIdList)) {

            for (Long multiboxProductId : multiBoxProductIdList) {
                InventoryVO inventory = inventoryMap.get(multiboxProductId);
                BaseFullProductVO baseProduct = productMap.get(multiboxProductId);

                checkNullProductOrInventory(multiboxProductId, baseProduct, inventory);

                if (inventory.getInStockCanAllocateQty() > 0) {
                    throw new BusinessException(StringUtil.format(
                            "product:{} is MultiBox, Should have Zero InStock Qty",
                            baseProduct.toString()
                    ));
                }
                if (inventory.getGroupsInStockCanAllocateQty() > 0) {
                    throw new BusinessException(StringUtil.format(
                            "product:{} is MultiBox, Should have Zero Group Qty",
                            baseProduct.toString()
                    ));
                }
            }
        }

        List<Long> slapAndGoProductIdList = warehouseProductMap.values().stream()
                .filter(WarehouseProduct::getSlapAndGoFlag)
                .map(WarehouseProduct::getProductId)
                .distinct()
                .toList();

        // 7. 其余产品视为Normal
        Set<Long> multiAndSlapSet = new HashSet<>();
        multiAndSlapSet.addAll(multiBoxProductIdList);
        multiAndSlapSet.addAll(slapAndGoProductIdList);

        List<Long> normalProductIdList = productIdList.stream()
                .filter(id -> !multiAndSlapSet.contains(id))
                .toList();

        // 8. 构建上下文并返回
        OtcWorkorderCreateContext context = new OtcWorkorderCreateContext();
        context.setSlapAndGoProductIdList(slapAndGoProductIdList);
        context.setMultiBoxProductIdList(multiBoxProductIdList);
        context.setNormalProductIdList(normalProductIdList);
        context.setProductMap(productMap);
        context.setInventoryMap(inventoryMap);
        return context;
    }

    /**
     * 依据request是否提供运单标签，选择拆分逻辑。
     *
     * @param request                OtcRequestVO对象
     * @param workorderCreateContext 上下文
     * @return 拆分后的OtcSplitRequestBO
     */
    private OtcSplitRequestBO buildOtcSplitRequestBO(OtcRequestVO request, OtcWorkorderCreateContext workorderCreateContext) {
        if (Boolean.TRUE.equals(request.getProvideShippingLabelFlag())) {
            // 有包裹信息
            return splitRequests(request, workorderCreateContext);
        } else {
            // 无包裹信息
            return splitRequestNoPackages(request, workorderCreateContext);
        }
    }

    /**
     * 将拆分结果进一步转换为一个或多个OtcWorkOrderReturnContext（工单上下文）。
     * 根据orderType分别处理SlapAndGo、SOSP、SOMP、MultiBox四种类型。
     *
     * @param splitRequestBO         拆分后的结果
     * @param workorderCreateContext 上下文
     * @return 若干个OtcWorkOrderReturnContext
     */
    private List<OtcWorkOrderReturnContext> splitRequestToWorkOrderList(
            OtcSplitRequestBO splitRequestBO,
            OtcWorkorderCreateContext workorderCreateContext
    ) {
        List<OtcWorkOrderReturnContext> result = new ArrayList<>();

        // SlapAndGo
        result.addAll(splitRequestToWorkOrderList(
                splitRequestBO.getRequest(),
                splitRequestBO.getSlapAndGoRequestList(),
                OtcOrderTypeEnum.SLAP_AND_GO,
                workorderCreateContext
        ));

        // Single Order Single Piece (SOSP)
        result.addAll(splitRequestToWorkOrderList(
                splitRequestBO.getRequest(),
                splitRequestBO.getSospRequestList(),
                OtcOrderTypeEnum.SOSP,
                workorderCreateContext
        ));

        // Single Order Multiple Piece (SOMP)
        result.addAll(splitRequestToWorkOrderList(
                splitRequestBO.getRequest(),
                splitRequestBO.getSompRequestList(),
                OtcOrderTypeEnum.SOMP,
                workorderCreateContext
        ));

        // MultiBox
        result.addAll(splitRequestToWorkOrderList(
                splitRequestBO.getRequest(),
                splitRequestBO.getMultiBoxRequestList(),
                OtcOrderTypeEnum.MULTI_BOX,
                workorderCreateContext
        ));

        return result;
    }

    /**
     * 按指定orderType生成若干个OtcWorkOrderReturnContext
     *
     * @param request                原始请求
     * @param splitRequestDetails    对应拆分后某种类型的明细列表
     * @param orderType              工单类型
     * @param workorderCreateContext 上下文
     * @return 生成的OtcWorkOrderReturnContext列表
     */
    private List<OtcWorkOrderReturnContext> splitRequestToWorkOrderList(
            OtcRequestVO request,
            List<OtcSplitRequestDetailBO> splitRequestDetails,
            OtcOrderTypeEnum orderType,
            OtcWorkorderCreateContext workorderCreateContext
    ) {
        List<OtcWorkOrderReturnContext> result = new ArrayList<>();
        if (ObjectUtil.isEmpty(splitRequestDetails)) {
            return result;
        }

        Map<Long, InventoryVO> inventoryMap = workorderCreateContext.getInventoryMap();

        for (OtcSplitRequestDetailBO splitDetail : splitRequestDetails) {
            // 1) 生成基础OtcWorkorder
            OtcWorkorder workOrder = otcWorkorderService.buildBaseWorkOrder(request, orderType);

            // 如果有Package,那么 workorder 使用package 里面的
            if (ObjectUtil.isNotEmpty(splitDetail.getRequestPackageList())) {
                for (OtcRequestPackageFullVO packageFullVO : splitDetail.getRequestPackageList()) {
                    if (ObjectUtil.isNotEmpty(packageFullVO.getShipCarrier())) {
                        workOrder.setShipCarrier(packageFullVO.getShipCarrier());
                    }
                    if (ObjectUtil.isNotEmpty(packageFullVO.getShipMethod())) {
                        workOrder.setShipMethod(packageFullVO.getShipMethod());
                    }
                }
                //todo: 这是一个临时解决方案，这个不好的
                workOrder.setPickToStation(shipStationConfigService.getShipStation(workOrder.getShipCarrier(), workOrder.getShipMethod()));
            }

            // // 2) 分配库存
            // List<OtcWorkOrderDetailReturnContext> finalDetailContextList = allocateInventoryForWorkOrder(
            //         splitDetail.getWorkorderDetailList(),
            //         workOrder,
            //         inventoryMap,
            //         workorderCreateContext
            // );

            BuildWorkOrderInputContext inputContext = new BuildWorkOrderInputContext();

            inputContext.setMultibox(workOrder.getOrderType().equals(OtcOrderTypeEnum.MULTI_BOX.getStatus()));

            inputContext.setWorkorderType(WorkorderTypeEnum.OTC);

            inputContext.setProductMap(workorderCreateContext.getProductMap());
            inputContext.setInventoryMap(inventoryMap);


            List<BuildWorkOrderDetailReturnContext> buildWorkOrderDetailReturnContexts = workorderBuildService.allocateInventoryForWorkOrder(splitDetail.getWorkorderDetailList(),
                    workOrder, inputContext);
            // 3) 封装结果
            OtcWorkOrderReturnContext context = new OtcWorkOrderReturnContext();
            context.setWorkorder(workOrder);
            context.setWorkorderDetailContextList(buildWorkOrderDetailReturnContexts);
            context.setRequestPackageList(splitDetail.getRequestPackageList());
            result.add(context);
        }

        return result;
    }

    /**
     * 进一步细分（主要用于SlapAndGo和MultiBox的包裹处理，以及是否有PrepWorkorder），
     * 将需要Prep的部分拆分为新的OtcWorkOrder。
     *
     * @param originalList 原始工单上下文列表
     * @return 最终工单上下文列表
     */
    private List<OtcWorkOrderReturnContext> refineWorkOrderByPrep(List<OtcWorkOrderReturnContext> originalList) {
        List<OtcWorkOrderReturnContext> resultList = new ArrayList<>(originalList);
        List<OtcWorkOrderReturnContext> newList = new ArrayList<>();
        List<OtcWorkOrderReturnContext> toRemove = new ArrayList<>();

        for (OtcWorkOrderReturnContext ctx : originalList) {
            OtcWorkorder workorder = ctx.getWorkorder();

            //规范化Workorderid
            for (BuildWorkOrderDetailReturnContext detailReturnContext : ctx.getWorkorderDetailContextList()) {

                if (ObjectUtil.isNotEmpty(detailReturnContext.getWorkorderDetail())) {
                    ((OtcWorkorderDetail) detailReturnContext.getWorkorderDetail()).setOtcWorkorderId(workorder.getId());
                }
                if (ObjectUtil.isNotEmpty(detailReturnContext.getPrepWorkorder())) {
                    ((OtcPrepWorkorder) detailReturnContext.getPrepWorkorder()).setOtcWorkorderId(workorder.getId());
                }
            }

            if (OtcOrderTypeEnum.SOMP.getStatus().equals(workorder.getOrderType())) {
                // SINGLE_ORDER_MULTIPLE_PIECE不需要拆分
                continue;
            }
            if (WorkOrderPrepStatusEnum.NONE.getStatus().equals(workorder.getWorkorderPrepStatus())) {
                // 无需预处理
                continue;
            }

            // 针对SlapAndGo和MultiBox需要进一步拆分
            // 按prepWorkorderType分组
            Map<String, List<BuildWorkOrderDetailReturnContext>> prepMap =
                    ctx.getWorkorderDetailContextList().stream()
                            .filter(d -> d.getPrepWorkorder() != null)
                            .collect(Collectors.groupingBy(d -> d.getPrepWorkorder().getPrepWorkorderType()));

            for (Map.Entry<String, List<BuildWorkOrderDetailReturnContext>> entry : prepMap.entrySet()) {
                List<BuildWorkOrderDetailReturnContext> details = entry.getValue();
                long totalQty = details.stream().mapToLong(d -> d.getWorkorderDetail().getQty()).sum();

                // 复制新的工单
                OtcWorkorder newWorkorder = BeanUtil.copyNew(workorder, OtcWorkorder.class);
                newWorkorder.setId(IdWorker.getId());
                newWorkorder.setRefNum(otcWorkorderService.getOtcWorkOrderRefNum());
                newWorkorder.setWorkorderPrepStatus(OtcWorkOrderPrepWorkOrderTypeEnum.NEW.getStatus());

                // 分配包裹
                List<OtcRequestPackageFullVO> subPackageList = allocatePackageForPrep(
                        ctx.getRequestPackageList(),
                        workorder.getOrderType(),
                        totalQty
                );

                // 更新明细的otcWorkorderId
                for (BuildWorkOrderDetailReturnContext detail : details) {
                    ((OtcPrepWorkorder) detail.getPrepWorkorder()).setOtcWorkorderId(newWorkorder.getId());
                    ((OtcWorkorderDetail) detail.getWorkorderDetail()).setOtcWorkorderId(newWorkorder.getId());
                }

                // 构建新的上下文
                OtcWorkOrderReturnContext newCtx = new OtcWorkOrderReturnContext();
                newCtx.setWorkorder(newWorkorder);
                newCtx.setWorkorderDetailContextList(details);
                newCtx.setRequestPackageList(subPackageList);
                newList.add(newCtx);
            }

            // 如果原工单中全部明细都走Prep，则移除当前上下文
            List<BuildWorkOrderDetailReturnContext> noPrepDetails = ctx.getWorkorderDetailContextList().stream()
                    .filter(d -> d.getPrepWorkorder() == null)
                    .toList();
            if (ObjectUtil.isEmpty(noPrepDetails)) {
                toRemove.add(ctx);
            }
        }

        resultList.removeAll(toRemove);
        resultList.addAll(newList);
        return resultList;
    }

    /**
     * 分配包裹给新的工单（MultiBox按行拆分，其它则简单取totalQty个包裹）
     *
     * @param originalPackageList 原始包裹列表
     * @param orderType           工单类型
     * @param totalQty            需要分配的总数量
     * @return 分配后的子包裹列表
     */
    private List<OtcRequestPackageFullVO> allocatePackageForPrep(
            List<OtcRequestPackageFullVO> originalPackageList,
            String orderType,
            long totalQty
    ) {
        if (ObjectUtil.isEmpty(originalPackageList)) {
            return Collections.emptyList();
        }

        if (OtcOrderTypeEnum.MULTI_BOX.getStatus().equals(orderType)) {
            // MultiBox根据getPackageMultiboxUpc分组
            Map<String, List<OtcRequestPackageFullVO>> groupedMap = originalPackageList.stream()
                    .collect(Collectors.groupingBy(OtcRequestPackageFullVO::getPackageMultiboxUpc));
            List<OtcRequestPackageFullVO> subList = new ArrayList<>();
            //long remainQty = totalQty;

            for (Map.Entry<String, List<OtcRequestPackageFullVO>> entry : groupedMap.entrySet()) {
                List<OtcRequestPackageFullVO> grpPackages = entry.getValue();

                if (grpPackages.size() < totalQty) {
                    throw new BusinessException(StringUtil.format(
                            "originalPackageList:{} is MultiBoxPackage,But Not enough to allocate",
                            ObjectUtil.toString(originalPackageList)
                    ));
                }

                int qtyToTake = (int) Math.min(totalQty, grpPackages.size());
                List<OtcRequestPackageFullVO> taken = new ArrayList<>(grpPackages.subList(0, qtyToTake));
                subList.addAll(taken);
                originalPackageList.removeAll(taken);

                // remainQty -= qtyToTake;
                // if (remainQty <= 0) {
                //     break;
                // }
            }
            return subList;
        } else {
            // 其它类型
            int minQty = (int) Math.min(totalQty, originalPackageList.size());
            List<OtcRequestPackageFullVO> subList = new ArrayList<>(originalPackageList.subList(0, minQty));
            originalPackageList.subList(0, minQty).clear();
            return subList;
        }
    }

    /**
     * 最终持久化所有数据：包裹、PrepWorkorder、WorkorderDetail、库存锁定/预留等，并记录日志。
     *
     * @param request                OtcRequestVO对象
     * @param finalReturnContextList 最终工单上下文列表
     * @param note                   备注信息
     */
    private void persistAllData(OtcRequestVO request,
                                List<OtcWorkOrderReturnContext> finalReturnContextList,
                                String note) {

        // 1. 构建并落库Package
        persistPackages(finalReturnContextList, request);

        // 2. 构建并落库PrepWorkorder
        final List<OtcPrepWorkorder> prepWorkorderEntityList = BuildWorkorderContextUtil.collectPrepWorkorderList(finalReturnContextList, OtcPrepWorkorder.class);

        if (ObjectUtil.isNotEmpty(prepWorkorderEntityList)) {
            otcPrepWorkorderService.insertBatch(prepWorkorderEntityList);
        }
        // 3. 构建并落库PrepWorkorderDetail
        List<OtcPrepWorkorderDetail> prepWorkorderDetails = BuildWorkorderContextUtil.collectPrepWorkorderDetailList(finalReturnContextList, OtcPrepWorkorderDetail.class);
        if (ObjectUtil.isNotEmpty(prepWorkorderDetails)) {
            otcPrepWorkorderDetailService.insertBatch(prepWorkorderDetails);
        }
        // 4. 构建并落库PrepWorkorderDetail相关的锁定记录
        List<InventoryLocked> prepLockedList = BuildWorkorderContextUtil.collectPrepLockedList(finalReturnContextList);
        if (ObjectUtil.isNotEmpty(prepLockedList)) {
            inventoryLockedService.insertBatch(prepLockedList);
        }

        // 5. 构建并落库WorkOrderDetail相关的预留记录
        List<InventoryReserve> reserveList = BuildWorkorderContextUtil.collectInventoryReserveList(finalReturnContextList);
        if (ObjectUtil.isNotEmpty(reserveList)) {
            inventoryReserveService.insertBatch(reserveList);
        }

        // 6. 构建并落库Workorder
        final List<OtcWorkorder> workorderEntityList = BuildWorkorderContextUtil.collectWorkorderList(finalReturnContextList, OtcWorkorder.class);

        otcWorkorderService.insertBatch(workorderEntityList);

        // 7. 构建并落库WorkOrderDetail相关的锁定记录
        List<InventoryLocked> lockedList = BuildWorkorderContextUtil.collectWorkOrderLockedList(finalReturnContextList);
        inventoryLockedService.insertBatch(lockedList);

        // 8. 构建并落库WorkOrderDetail
        List<OtcWorkorderDetail> workorderDetails = BuildWorkorderContextUtil.collectWorkorderDetailList(finalReturnContextList, OtcWorkorderDetail.class);

        otcWorkorderDetailService.insertBatch(workorderDetails);

        // 9. 记录日志
        recordAuditLogs(workorderEntityList, prepWorkorderEntityList, request, note);
    }

    // region ============== 辅助方法：分配group组件、分配普通组件、collect数据、记录日志等 ==============

    /**
     * 检查产品和库存是否为空
     *
     * @param productId   产品ID
     * @param baseProduct 产品信息
     * @param inventory   库存信息
     */
    private void checkNullProductOrInventory(Long productId, BaseFullProductVO baseProduct, InventoryVO inventory) {
        if (baseProduct == null) {
            throw new BusinessException(StringUtil.format(
                    "product:{} Not Found In baseFullProductVO, Please contact Developer",
                    productId
            ));
        }
        if (inventory == null) {
            throw new BusinessException(StringUtil.format(
                    "product:{} Not Found In Inventory, Please contact Developer",
                    productId
            ));
        }
    }

    /**
     * 根据orderType和标记判断返回PREPPACK/PREPMULTIBOX或PREPCONVERTPACK/PREPCONVERTMULTIBOX
     *
     * @param workOrder  当前工单
     * @param directPack 是否直接打包
     * @return PrepWorkOrderTypeEnum枚举类型
     */
    private PrepWorkOrderTypeEnum getPackOrMultiBoxType(OtcWorkorder workOrder, boolean directPack) {
        boolean isMultiBox = OtcOrderTypeEnum.MULTI_BOX.getStatus().equals(workOrder.getOrderType());
        if (directPack) {
            return isMultiBox ? PrepWorkOrderTypeEnum.PREPMULTIBOX : PrepWorkOrderTypeEnum.PREPPACK;
        } else {
            return isMultiBox ? PrepWorkOrderTypeEnum.PREPCONVERTMULTIBOX : PrepWorkOrderTypeEnum.PREPCONVERTPACK;
        }
    }

    /**
     * 获取BaseFullProductVO的字符串表示
     *
     * @param baseProduct 产品信息
     * @return 字符串表示
     */
    private String baseFullProductStr(BaseFullProductVO baseProduct) {
        return baseProduct == null ? "NULL" : baseProduct.toString();
    }

    /**
     * 构建并落库Package
     *
     * @param contextList 工单上下文列表
     * @param request     原始请求
     */
    private void persistPackages(List<OtcWorkOrderReturnContext> contextList, OtcRequestVO request) {
        List<OtcPackageBatchCreateParam> packageParams = contextList.stream()
                .map(x -> {
                    OtcPackageBatchCreateParam param = new OtcPackageBatchCreateParam();
                    param.setWorkorder(x.getWorkorder());
                    param.setRequest(request);
                    param.setRequestPackageList(x.getRequestPackageList());
                    return param;
                })
                .toList();
        otcPackageService.batchInsertByParam(packageParams);
    }


    /**
     * 记录审核日志
     *
     * @param workorderEntityList     工单实体列表
     * @param prepWorkorderEntityList 预处理工单实体列表
     * @param request                 原始请求
     * @param note                    备注信息
     */
    private void recordAuditLogs(List<OtcWorkorder> workorderEntityList,
                                 List<OtcPrepWorkorder> prepWorkorderEntityList,
                                 OtcRequestVO request,
                                 String note) {

        // 记录Workorder日志
        if (ObjectUtil.isNotEmpty(workorderEntityList)) {
            OtcWorkOrderAuditLogHelper.recordLog(
                    workorderEntityList,
                    StringUtil.format("Create by {}/{}", request.getRefNum(), request.getRequestRefNum()),
                    note
            );
        }

        // 记录PrepWorkorder日志
        if (ObjectUtil.isNotEmpty(prepWorkorderEntityList)) {
            OtcPrepWorkorderAuditLogHelper.recordLog(prepWorkorderEntityList);
        }
    }

    // endregion

    /**
     * 当OtcRequest不提供运单标签（无包裹信息）时的拆分逻辑。
     *
     * @param request OtcRequestVO对象
     * @param context 上下文
     * @return 拆分后的OtcSplitRequestBO
     */
    public OtcSplitRequestBO splitRequestNoPackages(OtcRequestVO request, OtcWorkorderCreateContext context) {
        OtcSplitRequestBO result = new OtcSplitRequestBO(request);

        // 将requestDetailList按productId分组
        Map<Long, List<OtcRequestDetailFullVO>> detailMap = request.getDetailList().stream()
                .collect(Collectors.groupingBy(OtcRequestDetailFullVO::getProductId));

        // MultiBox产品处理
        List<OtcSplitRequestDetailBO> multiBoxDetails = buildRequestDetailsForProducts(context.getMultiBoxProductIdList(), detailMap);
        result.getMultiBoxRequestList().addAll(multiBoxDetails);

        // SlapAndGo产品处理
        List<OtcSplitRequestDetailBO> slapAndGoDetails = buildRequestDetailsForProductsSlapAndGo(context.getSlapAndGoProductIdList(), detailMap);

        result.getSlapAndGoRequestList().addAll(slapAndGoDetails);

        // Normal产品处理
        List<OtcSplitRequestDetailBO> normalDetails = buildRequestDetailsForProducts(context.getNormalProductIdList(), detailMap);
        handleNormalProducts(result, normalDetails);

        return result;
    }

    /**
     * 当有包裹信息时的拆分逻辑
     *
     * @param request OtcRequestVO对象
     * @param context 上下文
     * @return 拆分后的OtcSplitRequestBO
     */
    private OtcSplitRequestBO splitRequests(OtcRequestVO request, OtcWorkorderCreateContext context) {
        OtcSplitRequestBO result = new OtcSplitRequestBO(request);
        List<OtcRequestPackageFullVO> requestPackages = request.getPackageList();

        // 1. MultiBox包裹处理
        List<OtcRequestPackageFullVO> multiBoxPackages = requestPackages.stream()
                .filter(pkg -> ObjectUtil.isNotNull(pkg.getPackageMultiboxProductId()))
                .toList();

        if (ObjectUtil.isNotEmpty(multiBoxPackages)) {
            List<OtcSplitRequestDetailBO> multiBoxList = handleMultiBoxPackages(request, multiBoxPackages);
            result.setMultiBoxRequestList(multiBoxList);
        }

        // 2. 其余包裹（非MultiBox）
        List<OtcRequestPackageFullVO> nonMultiBoxPackages = requestPackages.stream()
                .filter(pkg -> ObjectUtil.isNull(pkg.getPackageMultiboxProductId()))
                .toList();

        // SlapAndGo包裹
        List<OtcRequestPackageFullVO> slapAndGoList = new ArrayList<>();
        // SOSP包裹
        List<OtcRequestPackageFullVO> sospList = new ArrayList<>();
        // SOMP包裹
        List<OtcRequestPackageFullVO> sompList = new ArrayList<>();
        splitSlapAndGoPackages(nonMultiBoxPackages, context, slapAndGoList, sospList, sompList);

        // 3. 处理SlapAndGo包裹
        if (ObjectUtil.isNotEmpty(slapAndGoList)) {
            List<OtcSplitRequestDetailBO> slapAndGoDetail = handleSlapAndGoPackages(slapAndGoList, context);
            result.getSlapAndGoRequestList().addAll(slapAndGoDetail);
        }

        // 4. 处理SOSP包裹
        if (ObjectUtil.isNotEmpty(sospList)) {
            for (OtcRequestPackageFullVO requestPackage : sospList) {
                List<OtcWorkorderDetail> detailList = new ArrayList<>(otcWorkorderDetailService.buildWorkorderDetailList(requestPackage.getDetailList()));
                OtcSplitRequestDetailBO currentSplitRequestDetail = new OtcSplitRequestDetailBO(detailList, Lists.arrayList(requestPackage));
                result.getSospRequestList().add(currentSplitRequestDetail);
            }
        }

        // 5. 处理SOMP包裹
        if (ObjectUtil.isNotEmpty(sompList)) {
            List<OtcWorkorderDetail> detailList = new ArrayList<>();

            Map<Long, Integer> productQtySumMap = sompList.stream()
                    // 1. 拉平所有明细
                    .flatMap(pkg -> pkg.getDetailList().stream())
                    // 2. 按productId分组，求和qty
                    .collect(Collectors.groupingBy(
                            OtcRequestPackageDetailFullVO::getProductId,
                            Collectors.summingInt(OtcRequestPackageDetailFullVO::getQty)
                    ));
            for (Map.Entry<Long, Integer> productQtySum : productQtySumMap.entrySet()) {
                Long productId = productQtySum.getKey();
                Integer productSumQty = productQtySum.getValue();
                OtcWorkorderDetail workorderDetail = otcWorkorderDetailService.buildWorkorderDetailByProductId(productId);
                workorderDetail.setQty(productSumQty);
                detailList.add(workorderDetail);
            }
            OtcSplitRequestDetailBO currentSplitRequestDetail = new OtcSplitRequestDetailBO(detailList, sompList);
            result.getSompRequestList().add(currentSplitRequestDetail);
        }

        return result;
    }

    /**
     * 将Normal类型的产品根据总数量判断放入SOMP或SOSP
     *
     * @param result        拆分结果对象
     * @param normalDetails Normal产品明细列表
     */
    private void handleNormalProducts(OtcSplitRequestBO result, List<OtcSplitRequestDetailBO> normalDetails) {
        if (ObjectUtil.isEmpty(normalDetails)) {
            return;
        }

        OtcSplitRequestDetailBO otcSplitRequestDetailBO = new OtcSplitRequestDetailBO();
        for (OtcSplitRequestDetailBO normalDetail : normalDetails) {
            otcSplitRequestDetailBO.getRequestPackageList().addAll(normalDetail.getRequestPackageList());
            otcSplitRequestDetailBO.getWorkorderDetailList().addAll(normalDetail.getWorkorderDetailList());
        }
        List<OtcSplitRequestDetailBO> splitRequestDetailBOList = new ArrayList<>();
        splitRequestDetailBOList.add(otcSplitRequestDetailBO);

        int totalQty = otcSplitRequestDetailBO.getWorkorderDetailList().stream()
                .mapToInt(OtcWorkorderDetail::getQty)
                .sum();

        if (totalQty > 1) {
            result.setSompRequestList(splitRequestDetailBOList);
        } else {
            result.setSospRequestList(splitRequestDetailBOList);
        }
    }

    /**
     * 根据指定的productIds从detailMap中构建OtcSplitRequestDetailBO列表
     *
     * @param productIds 产品ID列表
     * @param detailMap  明细映射
     * @return 构建后的OtcSplitRequestDetailBO列表
     */
    private List<OtcSplitRequestDetailBO> buildRequestDetailsForProducts(
            List<Long> productIds,
            Map<Long, List<OtcRequestDetailFullVO>> detailMap
    ) {
        List<OtcSplitRequestDetailBO> result = new ArrayList<>();
        for (Long pid : productIds) {
            List<OtcRequestDetailFullVO> detailList = detailMap.getOrDefault(pid, Collections.emptyList());
            if (ObjectUtil.isEmpty(detailList)) {
                continue;
            }
            int totalQty = detailList.stream().mapToInt(OtcRequestDetailFullVO::getQty).sum();
            if (totalQty <= 0) {
                continue;
            }

            // 用第一个模板构建
            OtcRequestDetailFullVO template = detailList.get(0);
            OtcWorkorderDetail wd = otcWorkorderDetailService.buildWorkorderDetail(template);
            wd.setQty(totalQty);

            result.add(new OtcSplitRequestDetailBO(Collections.singletonList(wd), Collections.emptyList()));
        }
        return result;
    }

    /**
     * 根据指定的productIds从detailMap中构建OtcSplitRequestDetailBO列表
     *
     * @param productIds 产品ID列表
     * @param detailMap  明细映射
     * @return 构建后的OtcSplitRequestDetailBO列表
     */
    private List<OtcSplitRequestDetailBO> buildRequestDetailsForProductsSlapAndGo(
            List<Long> productIds,
            Map<Long, List<OtcRequestDetailFullVO>> detailMap
    ) {
        List<OtcSplitRequestDetailBO> result = new ArrayList<>();
        for (Long pid : productIds) {
            List<OtcRequestDetailFullVO> detailList = detailMap.getOrDefault(pid, Collections.emptyList());
            if (ObjectUtil.isEmpty(detailList)) {
                continue;
            }
            int totalQty = detailList.stream().mapToInt(OtcRequestDetailFullVO::getQty).sum();
            if (totalQty <= 0) {
                continue;
            }

            // 用第一个模板构建
            OtcRequestDetailFullVO template = detailList.get(0);

            for (int i = 0; i < totalQty; i++) {
                OtcWorkorderDetail wd = otcWorkorderDetailService.buildWorkorderDetail(template);
                wd.setQty(1);
                result.add(new OtcSplitRequestDetailBO(Collections.singletonList(wd), Collections.emptyList()));
            }
        }
        return result;
    }

    /**
     * 处理MultiBox包裹场景
     *
     * @param request          原始请求
     * @param multiBoxPackages MultiBox包裹列表
     * @return 处理后的OtcSplitRequestDetailBO列表
     */
    private List<OtcSplitRequestDetailBO> handleMultiBoxPackages(
            OtcRequestVO request,
            List<OtcRequestPackageFullVO> multiBoxPackages
    ) {
        Map<Long, List<OtcRequestPackageFullVO>> groupedByProduct = multiBoxPackages.stream()
                .collect(Collectors.groupingBy(OtcRequestPackageFullVO::getPackageMultiboxProductId));

        List<OtcSplitRequestDetailBO> result = new ArrayList<>();
        for (Map.Entry<Long, List<OtcRequestPackageFullVO>> entry : groupedByProduct.entrySet()) {
            Long productId = entry.getKey();
            List<OtcRequestPackageFullVO> pkgList = entry.getValue();

            // 检查多箱版本
            final Integer currentMultiBoxVersionInt = productSpecialService.getProductVersionInt(productId, ProductConfigTypeEnum.MULTIBOX);
            if (ObjectUtil.isNull(currentMultiBoxVersionInt)) {
                throw new BusinessException(StringUtil.format(
                        "product:{} is MultiBox,But No MultiBox Version Found,May Product Not Multibox", productId
                ));
            }

            // Use Objects.equals to avoid NPE:
            boolean allMatchMultiBox = pkgList.stream()
                    .allMatch(pkg -> Objects.equals(pkg.getPackageMultiboxVersionInt(), currentMultiBoxVersionInt));

            if (!allMatchMultiBox) {
                throw new BusinessException(StringUtil.format(
                        "product:{} is MultiBox,But MultiBox Version Not Match,Please Update Request", productId
                ));
            }


            OtcRequestDetailFullVO matchedDetail = request.getDetailList().stream()
                    .filter(d -> d.getProductId().equals(productId))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("Not Found ProductId From Request Details"));

            OtcWorkorderDetail wd = otcWorkorderDetailService.buildWorkorderDetail(matchedDetail);

            Map<String, Long> countMap = pkgList.stream()
                    .collect(Collectors.groupingBy(
                            OtcRequestPackageFullVO::getPackageMultiboxUpc,
                            Collectors.counting()
                    ));

            Map.Entry<String, Long> firstEntry = countMap.entrySet().stream()
                    .findFirst()
                    .orElse(null);

            if (ObjectUtil.isNull(firstEntry)) {
                throw new BusinessException(StringUtil.format(
                        "product:{} is MultiBox,But No MultiBox UPC Found.",
                        productId
                ));
            }

            wd.setQty(firstEntry.getValue().intValue());


            OtcSplitRequestDetailBO detailBO = new OtcSplitRequestDetailBO(
                    Collections.singletonList(wd),
                    pkgList
            );
            result.add(detailBO);
        }
        return result;
    }

    /**
     * 将nonMultiBoxPackages拆分出SlapAndGoList与SOSPList
     *
     * @param nonMultiBoxPackages 非MultiBox包裹列表
     * @param context             上下文
     * @param slapAndGoList       SlapAndGo包裹列表
     * @param sospList            SOSP包裹列表
     * @param sompList            SOMP包裹列表
     */
    private void splitSlapAndGoPackages(
            List<OtcRequestPackageFullVO> nonMultiBoxPackages,
            OtcWorkorderCreateContext context,
            List<OtcRequestPackageFullVO> slapAndGoList,
            List<OtcRequestPackageFullVO> sospList,
            List<OtcRequestPackageFullVO> sompList
    ) {
        for (OtcRequestPackageFullVO pkg : nonMultiBoxPackages) {
            int productSum = pkg.getDetailList().stream().mapToInt(BaseDetailFullVO::getQty).sum();
            if (productSum == 1) {
                Long pid = pkg.getDetailList().get(0).getProductId();
                if (context.getSlapAndGoProductIdList().contains(pid)) {
                    slapAndGoList.add(pkg);
                } else {
                    sospList.add(pkg);
                }
            } else {
                sompList.add(pkg);
            }
        }
    }

    /**
     * 将所有SlapAndGo包裹合并
     *
     * @param slapAndGoList SlapAndGo包裹列表
     * @param context       上下文
     * @return 处理后的OtcSplitRequestDetailBO列表
     */
    private List<OtcSplitRequestDetailBO> handleSlapAndGoPackages(
            List<OtcRequestPackageFullVO> slapAndGoList,
            OtcWorkorderCreateContext context
    ) {

        List<OtcSplitRequestDetailBO> result = new ArrayList<>();

        for (OtcRequestPackageFullVO requestPackage : slapAndGoList) {

            OtcRequestPackageDetailFullVO requestPackageDetail = requestPackage.getDetailList().get(0);
            OtcWorkorderDetail wd = otcWorkorderDetailService.buildWorkorderDetailByProductId(requestPackageDetail.getProductId());
            wd.setQty(1);

            List<OtcRequestPackageFullVO> combinedPkg = new ArrayList<>(List.of(requestPackage));
            OtcSplitRequestDetailBO detailBO = new OtcSplitRequestDetailBO(Collections.singletonList(wd), combinedPkg);
            result.add(detailBO);
        }

        return result;

        //下面是 相同产品 的SlapAndGo 合并到 一个工单

        // // 以(productId + Package)组合来汇总
        // Map<Long, Map<OtcRequestPackageFullVO, List<OtcRequestPackageDetailFullVO>>> packageMap = new HashMap<>();
        // for (OtcRequestPackageFullVO pkg : slapAndGoList) {
        //     for (OtcRequestPackageDetailFullVO detail : pkg.getDetailList()) {
        //         packageMap
        //                 .computeIfAbsent(detail.getProductId(), k -> new HashMap<>())
        //                 .computeIfAbsent(pkg, k -> new ArrayList<>())
        //                 .add(detail);
        //     }
        // }
        //
        // List<OtcSplitRequestDetailBO> result = new ArrayList<>();
        // for (Map.Entry<Long, Map<OtcRequestPackageFullVO, List<OtcRequestPackageDetailFullVO>>> entry : packageMap.entrySet()) {
        //     Long productId = entry.getKey();
        //     Map<OtcRequestPackageFullVO, List<OtcRequestPackageDetailFullVO>> pkgMap = entry.getValue();
        //
        //     // 构建一条工单明细
        //     OtcWorkorderDetail wd = otcWorkorderDetailService.buildWorkorderDetailByProductId(productId);
        //
        //     // 计算总数量
        //     int totalQty = pkgMap.values().stream()
        //             .flatMap(Collection::stream)
        //             .mapToInt(OtcRequestPackageDetailFullVO::getQty)
        //             .sum();
        //     wd.setQty(totalQty);
        //
        //     // 汇总所有包裹
        //     List<OtcRequestPackageFullVO> combinedPkg = new ArrayList<>(pkgMap.keySet());
        //     OtcSplitRequestDetailBO detailBO = new OtcSplitRequestDetailBO(Collections.singletonList(wd), combinedPkg);
        //     result.add(detailBO);
        // }
        //
        // return result;
    }

    /**
     * 处理剩余包裹
     *
     * @param leftoverPackages 剩余包裹列表
     * @return 处理后的OtcSplitRequestDetailBO对象
     */
    private OtcSplitRequestDetailBO handleLeftoverPackages(List<OtcRequestPackageFullVO> leftoverPackages) {
        List<OtcWorkorderDetail> detailList = new ArrayList<>();
        for (OtcRequestPackageFullVO pkg : leftoverPackages) {
            detailList.addAll(otcWorkorderDetailService.buildWorkorderDetailList(pkg.getDetailList()));
        }
        return new OtcSplitRequestDetailBO(detailList, leftoverPackages);
    }
}
