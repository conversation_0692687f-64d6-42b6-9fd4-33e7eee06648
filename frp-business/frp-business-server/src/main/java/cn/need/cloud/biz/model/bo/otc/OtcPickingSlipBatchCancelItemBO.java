package cn.need.cloud.biz.model.bo.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPickingSlip;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import lombok.Data;

import java.util.List;

/**
 * OtcPickingSlipCancelBO
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
public class OtcPickingSlipBatchCancelItemBO {

    /**
     * 拣货单
     */
    private OtcPickingSlip pickingSlip;

    /**
     * 拣货单关联的工单
     */
    private List<OtcWorkorder> workorderList;
}
