package cn.need.cloud.biz.service.otb.pallet;

import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.param.otb.update.pallet.OtbPalletBatchRollbackUpdateParam;

import java.util.List;

/**
 * <p>
 * OTB装运 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPalletSpecialService {

    /**
     * 批量Rollback
     *
     * @param param 批量参数
     * @return /
     */
    boolean batchRollback(OtbPalletBatchRollbackUpdateParam param);

    /**
     * 触发流程
     *
     * @param process 流程
     */
    void processTriggering(WorkorderProcessBO process);

    /**
     * 回滚Shipment
     *
     * @param cancelList 取消列表
     */
    void rollbackByShipment(List<OtbShipment> cancelList);
}