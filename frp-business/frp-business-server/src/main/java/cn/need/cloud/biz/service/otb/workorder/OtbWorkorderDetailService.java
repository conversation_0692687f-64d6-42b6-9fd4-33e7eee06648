package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import cn.need.cloud.biz.service.inventory.WorkorderDetailService;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTB工单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbWorkorderDetailService extends
        SuperService<OtbWorkorderDetail>,
        WorkorderDetailService<OtbWorkorderDetail, OtbWorkorder> {

    /**
     * 根据OTB工单id获取OTB工单详情集合
     *
     * @param otbWorkorderId OTB工单id
     * @return OTB工单详情集合
     */
    List<OtbWorkorderDetailVO> listByOtbWorkorderId(Long otbWorkorderId);


    /**
     * 根据工单id集合获取工单详情
     *
     * @param workOrderIdList otb工单集合
     * @return /
     */
    Map<Long, List<OtbWorkorderDetail>> groupByOtbWorkOrderIdList(List<Long> workOrderIdList);

    /**
     * 根据工单id集合获取锁定库存参数
     *
     * @param workOrderIdList Otb工单集合
     * @return /
     */
    List<InventoryReleaseLockedParam> findInventoryReleaseLockedParam(List<Long> workOrderIdList);

    List<OtbWorkorderDetail> buildWorkorderDetails(
            OtbWorkorder workorder,
            List<OtbRequestDetailVO> detailList
    );

    // OtbWorkorderDetail createNewWorkOrderDetail(OtbWorkorderDetail originalDetail, OtbWorkorder workOrder);

    /**
     * 根据otbWorkorderId集合获取otbWorkorderDetail集合
     *
     * @param list otbWorkorderId集合
     * @return otbWorkorderDetail集合
     */
    List<OtbWorkorderDetail> listItemsByOtbWorkOrderId(Collection<Long> list);

    /**
     * 根据工单id集合获取工单详情
     *
     * @param workorderIds 工单id集合
     * @return /
     */
    List<OtbWorkorderDetail> listByWorkorderIds(List<Long> workorderIds);

    /**
     * 根据工单id获取工单详情
     *
     * @param workorderId 工单id
     * @return /
     */
    default List<OtbWorkorderDetail> listByWorkorderId(Long workorderId) {
        return this.listByWorkorderIds(List.of(workorderId));
    }
}