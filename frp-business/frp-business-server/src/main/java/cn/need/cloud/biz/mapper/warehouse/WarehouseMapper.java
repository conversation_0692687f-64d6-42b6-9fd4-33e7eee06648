package cn.need.cloud.biz.mapper.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.query.warehouse.WarehouseQuery;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓库基础信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface WarehouseMapper extends SuperMapper<Warehouse> {

    /**
     * 根据条件获取仓库基础信息列表
     *
     * @param query 查询条件
     * @return 仓库基础信息集合
     */
    default List<WarehousePageVO> listByQuery(WarehouseQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取仓库基础信息分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 仓库基础信息集合
     */
    List<WarehousePageVO> listByQuery(@Param("qo") WarehouseQuery query, @Param("page") Page<?> page);

}