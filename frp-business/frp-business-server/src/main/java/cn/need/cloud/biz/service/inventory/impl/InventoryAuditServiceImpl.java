package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.inventory.InventoryAuditConverter;
import cn.need.cloud.biz.mapper.inventory.InventoryAuditMapper;
import cn.need.cloud.biz.model.bo.inbound.BinLocationDetailContextBO;
import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.model.param.inventory.create.InventoryAuditCreateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryAuditQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryAuditVO;
import cn.need.cloud.biz.model.vo.page.InventoryAuditPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.inventory.InventoryAuditService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.redisson.api.RLock;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 库存盘点 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Service
public class InventoryAuditServiceImpl extends SuperServiceImpl<InventoryAuditMapper, InventoryAudit> implements InventoryAuditService {

    @Resource
    private BinLocationDetailService binLocationDetailService;
    @Resource
    @Lazy
    private InventoryAuditService self;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long inventoryAudit(InventoryAuditCreateParam createParam) {
        // 检查传入库存盘点参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new IllegalArgumentException("Parameter cannot be empty");
        }
        //校验库位
        dataValidBinLocation(createParam);
        //校验库位是否被禁用
        BinLocationCheckUtil.checkStatus(createParam);
        //获取库位信息
        BinLocationCache binLocationCache = BinLocationCacheUtil.getById(createParam.getBinLocationId());
        //校验是否为默认库位
        Validate.isTrue(!binLocationCache.getDefaultFlag(), "InventoryAudit Not Support virtual bin location. Error");
        // 获取库存盘点转换器实例，用于将库存盘点参数对象转换为实体对象
        InventoryAuditConverter converter = Converters.get(InventoryAuditConverter.class);
        // 将库存盘点参数对象转换为实体对象
        InventoryAudit entity = converter.toEntity(createParam);
        //获取库位详情
        BinLocationDetailContextBO context = BeanUtil.copyNew(createParam, BinLocationDetailContextBO.class);
        context.setInStockQty(createParam.getCountedQty());
        context.setRefNumModel(entity);
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.INVENTORY_AUDIT));
        entity.setId(IdWorker.getId());
        binLocationDetailService.updateBinLocationDetail(context);
        //构建持久化实体
        InventoryAudit inventoryAudit = buildBody(context, entity);
        // 插入库存盘点实体对象到数据库
        super.insert(inventoryAudit);
        // 返回库存盘点ID
        return entity.getId();
    }

    @Override
    public void lockedInventoryAudit(InventoryAuditCreateParam createParam) {
        //分布式锁id
        String key = StringUtil.concat(
                createParam.getBinLocationId(),
                createParam.getProductVersionId()
        );
        RedissonKit.getInstance().lock(key, 10, (RLock lock) -> self.inventoryAudit(createParam));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long inventoryAuditWithBop(InventoryAuditCreateParam auditParam) {
        //更新frp库存
        Long auditId = inventoryAudit(auditParam);
        //获取缓存
        InventoryAudit inventoryAudit = getById(auditId);
        //更新bop库存
        updateBop(auditParam, inventoryAudit.getRefNum());
        //盘点库存id
        return auditId;
    }

    @Override
    public List<InventoryAuditPageVO> listByQuery(InventoryAuditQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<InventoryAuditPageVO> pageByQuery(PageSearch<InventoryAuditQuery> search) {
        Page<InventoryAudit> page = Conditions.page(search, entityClass);
        List<InventoryAuditPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public InventoryAuditVO detailById(Long id) {
        InventoryAudit entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InventoryAudit");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InventoryAudit", id));
        }
        return buildInventoryAuditVO(entity);
    }

    @Override
    public InventoryAuditVO detailByRefNum(String refNum) {
        InventoryAudit entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in InventoryAudit");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InventoryAudit", "refNum", refNum));
        }
        return buildInventoryAuditVO(entity);
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 校验库位下是否有相同产品不同版本
     *
     * @param createParam 前端参数
     */
    private void dataValidBinLocation(InventoryAuditCreateParam createParam) {
        BinLocationCache cache = BinLocationCacheUtil.getById(createParam.getBinLocationId());
        Validate.isTrue(!cache.getDefaultFlag(), "Bin location is default");
        //获取产品版本信息
        ProductVersionCache productVersionCache = ProductVersionCacheUtil.getById(createParam.getProductVersionId());
        //获取产品信息
        ProductCache productCache = ProductCacheUtil.getById(createParam.getProductId());
        Validate.notNull(productCache, "product is not existed");
        //校验产品id
        Validate.isTrue(
                ObjectUtil.equals(productVersionCache.getProductId(), createParam.getProductId()),
                StringUtil.format(
                        "productVersion does not refer to the version of the product:{}.",
                        productVersionCache.getRefNum()
                )
        );
        boolean exist = binLocationDetailService.exist(
                createParam.getBinLocationId(),
                createParam.getProductId(),
                createParam.getProductVersionId()
        );
        //校验库位下是否存在相同产品不同版本
        Validate.isTrue(!exist, "Different versions of the same product cannot be stocked in the same storage location ");
    }

    /**
     * 更新bop库存
     *
     * @param auditParam 盘点参数
     */
    private void updateBop(InventoryAuditCreateParam auditParam, String refNum) {
        //获取仓库缓存
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        //仓库判空
        Validate.notNull(warehouseCache, "warehouse can not null");
        // //构建请求体
        // buildBopReq(warehouseCache, auditParam, refNum);
    }

    // /**
    //  * 构建bop请求体
    //  *
    //  * @param warehouseCache 仓库缓存
    //  * @param auditParam     库存盘点数据
    //  */
    // private void buildBopReq(WarehouseCache warehouseCache, InventoryAuditCreateParam auditParam, String refNum) {
    //     //bop更新库存请求体
    //     Map<String, Object> reqMap = Maps.hashMap();
    //     BinLocationCache binLocationCache = BinLocationCacheUtil.getById(auditParam.getBinLocationId());
    //     //获取产品缓存
    //     ProductVersion productVersion = productVersionService.getById(auditParam.getProductVersionId());
    //     Validate.notNull(productVersion, "prduct not exist");
    //     reqMap.put("SourceLocationID", binLocationCache.getLocationName());
    //     reqMap.put("UserName", Users.name());
    //     reqMap.put("Warehouse", warehouseCache.getName());
    //     reqMap.put("Note", Users.name() + auditParam.getNote());
    //     reqMap.put("ItemPickTypes", new ItemPickTypeVO(productVersion.getSupplierSku(), refNum, auditParam.getCountedQty(), binLocationCache.getLocationName()));
    //     //更新bop库存
    //     InventoryAduitStrategy inventoryAduitStrategy = SpringUtil.getBean(InventoryAduitStrategy.class);
    //     Validate.notNull(inventoryAduitStrategy, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "bean"));
    //     inventoryAduitStrategy.updateInventory(OpenConstant.BOP_URL, reqMap);
    // }

    /**
     * 构建库存盘点VO对象
     *
     * @param entity 库存盘点对象
     * @return 返回包含详细信息的库存盘点VO对象
     */
    private InventoryAuditVO buildInventoryAuditVO(InventoryAudit entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的库存盘点VO对象
        return Converters.get(InventoryAuditConverter.class).toVO(entity);
    }

    /**
     * 构建持久化实体
     * 改方法用于构建持久化实体，传入库存审计基本信息
     *
     * @param context 库位详情
     * @param entity  持久化实体
     * @return 持久化实体
     */
    private InventoryAudit buildBody(BinLocationDetailContextBO context, InventoryAudit entity) {
        entity.setCurrentInStockQty(context.getCurrentInStockQty());
        entity.setDiffQty(entity.getCountedQty() - entity.getCurrentInStockQty());
        entity.setBinLocationDetailId(context.getBinLocationDetailId());
        entity.setProductId(context.getProductId());
        return entity;
    }

}
