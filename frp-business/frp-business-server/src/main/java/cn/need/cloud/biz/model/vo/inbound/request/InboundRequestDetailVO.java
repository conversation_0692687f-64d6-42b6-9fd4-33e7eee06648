package cn.need.cloud.biz.model.vo.inbound.request;

import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 入库请求详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "入库请求详情 vo对象")
public class InboundRequestDetailVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 请求详情id
     */
    @Schema(description = "请求详情id")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 入库请求Id
     */
    @Schema(description = "入库请求Id")
    private Long inboundRequestId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseProductVersionVO baseProductVersionVO;

    /**
     * 详情请求参考编号
     */
    @Schema(description = "详情请求参考编号")
    private String detailRequestRefNum;

    /**
     * 详情类型
     */
    @Schema(description = "详情类型")
    private String detailType;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;
}