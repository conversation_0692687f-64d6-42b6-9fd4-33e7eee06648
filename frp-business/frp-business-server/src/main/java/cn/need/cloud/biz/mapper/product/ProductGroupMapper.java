package cn.need.cloud.biz.mapper.product;

import cn.need.cloud.biz.model.entity.product.ProductGroup;
import cn.need.cloud.biz.model.query.product.ProductGroupQuery;
import cn.need.cloud.biz.model.vo.product.ProductGroupListVO;
import cn.need.cloud.biz.model.vo.product.page.ProductGroupPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 产品同类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface ProductGroupMapper extends SuperMapper<ProductGroup> {

    /**
     * 根据条件获取产品同类列表
     *
     * @param query 查询条件
     * @return 产品同类集合
     */
    default List<ProductGroupPageVO> listByQuery(ProductGroupQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取产品同类分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 产品同类集合
     */
    List<ProductGroupPageVO> listByQuery(@Param("qo") ProductGroupQuery query, @Param("page") Page<?> page);

    /**
     * 根据产品ID获取父级产品组列表
     *
     * @param productId 产品ID，用于查询父级产品组信息
     * @return 返回一个ProductGroupListVO对象的列表，表示父级产品组信息
     */
    List<ProductGroupListVO> getParentList(@Param("productId") Long productId);

    /**
     * 获取旧版本号
     * 主要用于在产品版本更新或比较时，需要参考旧版本信息的场景
     *
     * @param productId 产品ID，用于标识特定的产品
     * @return 旧版本号，如果找不到对应的产品，则返回null
     */
    Integer getOldVersionInt(@Param("productId") Long productId);

    /**
     * 根据产品ID和版本号获取产品组信息
     *
     * @param productId       产品ID，用于标识特定的产品
     * @param groupVersionInt 版本号，用于标识特定的产品版本
     * @return 返回一个ProductGroup对象的列表，表示符合指定条件的产品组信息
     */
    List<ProductGroup> findByParentProductIdAndVersionInt(@Param("productId") Long productId, @Param("versionInt") Integer groupVersionInt);

    /**
     * 批量获取产品组信息
     *
     * @param parentIdList        父id集合
     * @param childIdList         子id集合
     * @param groupVersionIntList group版本值
     * @return /
     */
    List<ProductGroup> findByParentIdAndVersionInts(@Param("parentIdList") Collection<Long> parentIdList,
                                                    @Param("childIdList") List<Long> childIdList,
                                                    @Param("groupVersionIntList") List<Integer> groupVersionIntList);

    /**
     * 通过子产品获取父产品id
     *
     * @param childId    childId
     * @param versionInt groupVersionInt
     * @return /
     */
    Long findParentIdByChildProductIdAndVersionInt(@Param("childId") Long childId, @Param("versionInt") Integer versionInt);

    /**
     * 通过子产品获取父产品id
     *
     * @param childIdList    childId
     * @param versionIntList groupVersionInt
     * @return /
     */
    List<Long> findParentIdsByChildProductIdAndVersionInt(@Param("childIdList") List<Long> childIdList, @Param("versionIntList") List<Integer> versionIntList);
}