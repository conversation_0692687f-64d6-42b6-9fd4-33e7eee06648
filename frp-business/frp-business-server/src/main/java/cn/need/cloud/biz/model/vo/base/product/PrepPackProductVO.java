package cn.need.cloud.biz.model.vo.base.product;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
 * PrepPackProductVO.java
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@Schema(description = "Prep拣货单Description VO对象")
public class PrepPackProductVO implements Serializable {

    /**
     * 组合产品id
     */
    @Schema(description = "组合产品id")
    private Long componentProductId;

    @Schema(description = "产品")
    private BaseProductVO baseProductVO;

    /**
     * 组合产品
     */
    @Schema(description = "组合产品")
    private List<PrepComponentVO> componentList;

    /**
     * 组装说明备注
     */
    @Schema(description = "组装说明备注")
    private String assemblyInstructionNote;
}
