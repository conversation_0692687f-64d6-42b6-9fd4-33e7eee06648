package cn.need.cloud.biz.mapper.fee;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.fee.FeeOtc;
import cn.need.cloud.biz.model.query.fee.FeeOtcQuery;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtcPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 费用otc Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Mapper
public interface FeeOtcMapper extends SuperMapper<FeeOtc> {

    /**
     * 根据条件获取费用otc列表
     *
     * @param query 查询条件
     * @return 费用otc集合
     */
    default List<FeeOtcPageVO> listByQuery(@Param("qofo") FeeOtcQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取费用otc分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 费用otc集合
     */
    List<FeeOtcPageVO> listByQuery(
            @Param("qofo") FeeOtcQuery query,
            @Param("page") Page<?> page);

    /**
     * 费用otc下拉列表
     *
     * @param columnList 查询字段名
     * @param query      查询条件
     * @return 费用otc下拉列表
     */
    List<Map<String, Object>> dropProList(
            @Param("columnList") List<DropColumnInfoBO> columnList,
            @Param("qofo") FeeOtcQuery query
    );
}