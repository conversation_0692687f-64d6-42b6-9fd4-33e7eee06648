package cn.need.cloud.biz.model.query.otc.pkg;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * OTC工单包裹位置 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC工单包裹仓储位置 query对象")
public class OtcPackageBinLocationQuery extends SuperQuery {

    /**
     * 包裹id
     */
    @Schema(description = "包裹id")
    @NotNull(message = "Package id is not null")
    private Long otcPackageId;

}

