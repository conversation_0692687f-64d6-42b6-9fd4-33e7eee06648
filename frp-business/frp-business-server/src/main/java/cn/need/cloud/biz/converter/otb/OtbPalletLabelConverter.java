package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPalletLabelDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPalletLabel;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB托盘标签 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPalletLabelConverter extends AbstractModelConverter<OtbPalletLabel, OtbPalletLabelVO, OtbPalletLabelDTO> {

}
