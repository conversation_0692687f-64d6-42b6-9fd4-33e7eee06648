package cn.need.cloud.biz.model.bo.common;

import cn.need.cloud.ship.client.dto.base.BaseAddressDTO;
import cn.need.cloud.ship.client.dto.common.CommonPackageResDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema
public class CommonShipRespBO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * 用于存储运单号的字符串变量
     */
    @Schema(description = "用于存储运单号的字符串变量")
    private String refNum;

//    /**
//     * 用于存储请求输入的字符串变量
//     */
//    private String requestInput;

    /**
     * 用于存储用户档案参考号的字符串变量
     */
    @Schema(description = "用于存储用户档案参考号的字符串变量")
    private String profileRefNum;

    /**
     * 用于存储运输状态的字符串变量
     */
    @Schema(description = "用于存储运输状态的字符串变量")
    private String shipStatus;

    /**
     * 用于存储错误信息的字符串变量
     */
    @Schema(description = "用于存储错误信息的字符串变量")
    private String errorMsg;

    /**
     * 用于存储包裹信息的列表变量，类型为CommonPackageResDTO
     */
    @Schema(description = "用于存储包裹信息的列表变量.类型为CommonPackageResDTO")
    private List<CommonPackageResDTO> packages;

    /**
     * 用于存储实际运输方式的字符串变量
     */
    @Schema(description = "用于存储实际运输方式的字符串变量")
    private String actualShipMethod;

    /**
     * 用于存储实际运输承运人的字符串变量
     */
    @Schema(description = "用于存储实际运输承运人的字符串变量")
    private String actualShipCarrier;

    /**
     * 用于存储请求参考号的字符串变量
     */
    @Schema(description = "用于存储请求参考号的字符串变量")
    private String requestRefNum;

    /**
     * 用于存储运输方式的字符串变量
     */
    @Schema(description = "用于存储运输方式的字符串变量")
    private String shipMethod;

    /**
     * 用于存储运输承运人的字符串变量
     */
    @Schema(description = "用于存储运输承运人的字符串变量")
    private String shipCarrier;

    /**
     * 用于存储运输目的地地址的信息，类型为BaseAddressDTO
     */
    @Schema(description = "于存储运输目的地地址的信息，类型为BaseAddressDTO")
    private BaseAddressDTO shipToAddress;

    /**
     * 用于存储运输起始地地址的信息，类型为BaseAddressDTO
     */
    @Schema(description = "用于存储运输起始地地址的信息，类型为BaseAddressDTO")
    private BaseAddressDTO shipFromAddress;

//    /**
//     * 用于存储参考采购订单号的字符串变量
//     */
//    private String refPoNum;
//
//    /**
//     * 用于存储参考发票号的字符串变量
//     */
//    private String refInvoiceNum;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createByName;


    @Schema(description = "reference1")
    private String reference1;

    @Schema(description = "reference2")
    private String reference2;

    @Schema(description = "reference3")
    private String reference3;


}
