package cn.need.cloud.biz.model.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 库存-库位 vo对象
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Data
@Schema(description = "库存-库位详情 vo对象")
public class InventoryBinLocationVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private String locationName;

    /**
     * 库位类型（用户可修改）
     */
    @Schema(description = "库位类型（用户可修改）")
    private String binType;

    /**
     * 库位类型（用户不可修改）
     */
    @Schema(description = "库位类型（用户不可修改）")
    private String type;

    /**
     * 仓库分区
     */
    @Schema(description = "仓库分区")
    private String warehouseZoneType;
}
