package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeOtcDetailDTO;
import cn.need.cloud.biz.model.entity.fee.FeeOtcDetail;
import cn.need.cloud.biz.model.vo.fee.FeeOtcDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用详情otc 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeOtcDetailConverter extends AbstractModelConverter<FeeOtcDetail, FeeOtcDetailVO, FeeOtcDetailDTO> {

}
