package cn.need.cloud.biz.model.entity.inbound;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.framework.common.annotation.validation.Unique;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * <p>
 * 入库请求
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inbound_request")
public class InboundRequest extends RefNumModel {


    @Serial
    private static final long serialVersionUID = 7902020263874582279L;
    /**
     * 运输方式类型
     */
    @TableField("transport_method_type")
    private String transportMethodType;

    /**
     * 外部唯一标识码
     */
    @TableField("request_ref_num")
    //todo: 不能通过这个来实现
    @Unique(message = "requestRefNum is existed", combined = {"transactionPartnerId"})
    private String requestRefNum;

    /**
     * 预计到达日期
     */
    @TableField("estimate_arrival_date")
    private LocalDateTime estimateArrivalDate;

    /**
     * 实际到达日期
     */
    @TableField("actual_arrival_date")
    private LocalDateTime actualArrivalDate;

    /**
     * 快递号
     */
    @TableField("tracking_num")
    private String trackingNum;

    /**
     * 发件人姓名
     */
    @TableField("from_address_name")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @TableField("from_address_company")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @TableField("from_address_country")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @TableField("from_address_state")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @TableField("from_address_city")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @TableField("from_address_zip_code")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @TableField("from_address_addr1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @TableField("from_address_addr2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @TableField("from_address_addr3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @TableField("from_address_email")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @TableField("from_address_phone")
    private String fromAddressPhone;

    /**
     * 发件人备注
     */
    @TableField("from_address_note")
    private String fromAddressNote;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 交易伙伴ID
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 请求状态
     */
    @TableField("inbound_request_status")
    private String inboundRequestStatus;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 容器类型
     */
    @TableField("container_type")
    private String containerType;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 发件人地址是否为住宅
     */
    @TableField("from_address_is_residential")
    private Boolean fromAddressIsResidential;

    /**
     * 处理开始时间
     */
    @TableField
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    @TableField
    private LocalDateTime processEndTime;

    /**
     * 计费状态
     */
    @TableField
    private String feeStatus;

}
