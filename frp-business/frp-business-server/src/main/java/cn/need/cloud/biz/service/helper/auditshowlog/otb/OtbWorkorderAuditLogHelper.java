package cn.need.cloud.biz.service.helper.auditshowlog.otb;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 工单日志辅助类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public class OtbWorkorderAuditLogHelper {

    /**
     * 记录日志
     *
     * @param workorderList 工单
     * @param status        状态
     * @param description   描述
     * @param note          Note
     */
    public static void recordLog(List<OtbWorkorder> workorderList, String status, String description, String note, String type) {
        workorderList.forEach(workorder -> recordLog(workorder, status, description, note, type));
    }

    /**
     * 记录日志
     *
     * @param workorderList 工单
     * @param status        状态
     * @param description   描述
     * @param note          Note
     */
    public static void recordLog(List<OtbWorkorder> workorderList, String status, String description, String note) {
        workorderList.forEach(workorder -> recordLog(workorder, status, description, note));
    }

    /**
     * 记录日志
     *
     * @param workorderList 工单
     * @param description   描述
     * @param note          Note
     */
    public static void recordLog(List<OtbWorkorder> workorderList, String description, String note) {
        workorderList.forEach(workorder -> recordLog(workorder, description, note));
    }

    /**
     * 记录日志
     *
     * @param workorderList 工单
     */
    public static void recordLog(List<OtbWorkorder> workorderList) {
        workorderList.forEach(workorder -> recordLog(workorder, workorder.getOtbWorkorderStatus(), null, null));
    }

    /**
     * 记录日志
     *
     * @param workorder 工单
     */
    public static void recordLog(OtbWorkorder workorder) {
        recordLog(workorder, workorder.getOtbWorkorderStatus(), null, null);
    }

    /**
     * 记录日志
     *
     * @param workorder   工单
     * @param description 描述
     * @param note        Note
     */
    public static void recordLog(OtbWorkorder workorder, String description, String note) {
        recordLog(workorder, workorder.getOtbWorkorderStatus(), description, note);
    }

    /**
     * 记录日志
     *
     * @param workorder   工单
     * @param status      状态
     * @param description 描述
     * @param note        Note
     */
    public static void recordLog(OtbWorkorder workorder, String status, String description, String note) {
        recordLog(workorder, status, description, note, BaseTypeLogEnum.STATUS.getType());
    }

    /**
     * 记录日志
     *
     * @param workorder   工单
     * @param status      状态
     * @param description 描述
     * @param note        Note
     */
    public static void recordLog(OtbWorkorder workorder, String status, String description, String note, String type) {
        AuditShowLog showLog = AuditLogUtil.commonLog(workorder)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(showLog);
    }
}
