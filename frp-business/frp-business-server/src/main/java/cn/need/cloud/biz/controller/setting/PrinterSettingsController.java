package cn.need.cloud.biz.controller.setting;

import cn.need.cloud.biz.converter.setting.PrinterSettingsConverter;
import cn.need.cloud.biz.model.entity.setting.PrinterSettings;
import cn.need.cloud.biz.model.param.setting.create.PrinterSettingsCreateParam;
import cn.need.cloud.biz.model.param.setting.update.PrinterSettingsUpdateParam;
import cn.need.cloud.biz.model.query.setting.PrinterSettingsQuery;
import cn.need.cloud.biz.model.vo.page.PrinterSettingsPageVO;
import cn.need.cloud.biz.model.vo.setting.PrinterSettingsVO;
import cn.need.cloud.biz.service.setting.PrinterSettingsService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/biz/printer-settings")
@Tag(name = "打印机设置")
public class PrinterSettingsController extends AbstractRestController<PrinterSettingsService, PrinterSettings, PrinterSettingsConverter, PrinterSettingsVO> {

    @Operation(summary = "新增打印机设置", description = "接收打印机设置的参数对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrinterSettingsCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));

    }

    @Operation(summary = "修改打印机设置", description = "接收打印机设置的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrinterSettingsUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除打印机设置", description = "根据打印机设置主键id，从数据库中删除其对应的数据")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) IdCondition id) {

        // 校验参数
        Validate.notNull(id.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeById(id.getId()));
    }

    @Operation(summary = "根据id获取打印机设置详情", description = "根据打印机设置主键id，从数据库中获取其对应的数据详情")
    @GetMapping(value = "/detail/{id}")
    public Result<PrinterSettingsVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {


        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据用户id获取打印机设置详情", description = "根据用户id，从数据库中获取其对应的数据详情")
    @GetMapping(value = "/detail")
    public Result<PrinterSettingsVO> detail() {


        // 返回结果
        return success(service.detailByUserId());
    }

    @Operation(summary = "获取打印机设置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @PostMapping(value = "/list")
    public Result<PageData<PrinterSettingsPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<PrinterSettingsQuery> search) {


        // 获取分页
        PageData<PrinterSettingsPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "获取打印机设置列表", description = "从数据库中获取打印机列表信息，不分页")
    @GetMapping(value = "/simpleList")
    public Result<List<PrinterSettingsPageVO>> simpleList() {

        //返回结果
        return success(service.listByQuery(null));
    }
}
