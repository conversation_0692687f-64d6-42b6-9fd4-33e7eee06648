package cn.need.cloud.biz.model.param.fee.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;


/**
 * 费用inbound CreateParam对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@Schema(description = "费用inbound BuildParam对象")
public class FeeInboundBuildParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -3235526066897697022L;
    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id")
    @NotNull(message = "feeOriginalDataId cannot be null")
    private Long feeOriginalDataId;

}