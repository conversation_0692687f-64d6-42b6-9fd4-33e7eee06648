package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeOtcDTO;
import cn.need.cloud.biz.model.entity.fee.FeeOtc;
import cn.need.cloud.biz.model.vo.fee.FeeOtcVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用otc 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeOtcConverter extends AbstractModelConverter<FeeOtc, FeeOtcVO, FeeOtcDTO> {

}
