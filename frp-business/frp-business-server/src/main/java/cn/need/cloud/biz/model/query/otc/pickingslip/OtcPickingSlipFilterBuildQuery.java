package cn.need.cloud.biz.model.query.otc.pickingslip;

import cn.need.cloud.biz.client.constant.enums.base.BuildFromType;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderListQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * OTC工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC工单 FilterBuildPickingSlip 构建查询Query对象")
public class OtcPickingSlipFilterBuildQuery implements Serializable {

    /**
     * 搜索条件
     */
    @Schema(description = "工单列表搜索条件")
    @NotNull(message = "Current Filter has No WorkOrders to Build!")
    private OtcWorkOrderListQuery filter;


    /**
     * 策略
     */
    @Schema(description = "策略")
    @NotNull(message = "Current Strategy has No WorkOrders to Build!")
    @Valid
    private OtcPickingSlipBuildStrategyQuery strategy;


    /**
     * 构建拣货单来源
     */
    @Schema(description = "构建拣货单来源", hidden = true)
    private BuildFromType buildFromType = BuildFromType.OTC_WORK_ORDER;

}