package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigOtbDetailConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigOtbDetailMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtbDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigOtbDetailCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigOtbDetailUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtbDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtbDetailPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtbDetailService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

/**
 * <p>
 * 仓库报价费用配置otb详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service("feeConfigDetailOtb")
public class FeeConfigOtbDetailServiceImpl extends SuperServiceImpl<FeeConfigOtbDetailMapper, FeeConfigOtbDetail> implements FeeConfigOtbDetailService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeConfigOtbDetailCreateParam createParam) {
        // 检查传入仓库报价费用配置otb详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将仓库报价费用配置otb详情参数对象转换为实体对象并初始化
        FeeConfigOtbDetail entity = initFeeConfigOtbDetail(createParam);

        // 插入仓库报价费用配置otb详情实体对象到数据库
        super.insert(entity);

        // 返回仓库报价费用配置otb详情ID
        return entity.getId();
    }

    /**
     * 初始化仓库报价费用配置otb详情实体对象。
     *
     * @param createParam  需要转换并初始化的仓库报价费用配置otb详情参数对象，不能为空
     * @param initConsumer 用于执行初始化操作的消费者对象
     * @return 初始化后的仓库报价费用配置otb详情实体对象
     */
    @Override
    public FeeConfigOtbDetail initFeeConfigOtbDetail(FeeConfigOtbDetailCreateParam createParam, Consumer<FeeConfigOtbDetail> initConsumer) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("FeeConfigOtbDetailCreateParam cannot be empty");
        }

        // 获取仓库报价费用配置otb详情转换器实例，用于将仓库报价费用配置otb详情参数对象转换为实体对象
        FeeConfigOtbDetailConverter converter = Converters.get(FeeConfigOtbDetailConverter.class);

        // 将仓库报价费用配置otb详情参数对象转换为实体对象并初始化
        FeeConfigOtbDetail entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_OTB_DETAIL));

        // 执行初始化操作
        initConsumer.accept(entity);
        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 批量初始化仓库报价费用配置otb详情实体对象。
     *
     * @param createParams 需要批量转换并初始化的仓库报价费用配置otb详情参数对象列表，列表及元素均不能为空
     * @param initConsumer 用于执行每个实体初始化操作的消费者对象
     * @return 包含所有初始化后的仓库报价费用配置otb详情实体对象的列表
     */

    @Override
    public List<FeeConfigOtbDetail> initFeeConfigOtbDetail(
            List<FeeConfigOtbDetailCreateParam> createParams,
            Consumer<FeeConfigOtbDetail> initConsumer) {

        // 校验参数列表是否为空
        if (ObjectUtil.isEmpty(createParams)) {
            throw new BusinessException("createParams list cannot be empty");
        }

        // 创建结果集合
        List<FeeConfigOtbDetail> entities = new ArrayList<>();

        // 遍历每个参数对象进行初始化
        for (FeeConfigOtbDetailCreateParam createParam : createParams) {
            // 调用单对象初始化方法并添加到结果集合
            FeeConfigOtbDetail entity = initFeeConfigOtbDetail(createParam, initConsumer);
            entities.add(entity);
        }

        // 返回批量初始化后的实体列表
        return entities;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeConfigOtbDetailUpdateParam updateParam) {
        // 检查传入仓库报价费用配置otb详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取仓库报价费用配置otb详情转换器实例，用于将仓库报价费用配置otb详情参数对象转换为实体对象
        FeeConfigOtbDetailConverter converter = Converters.get(FeeConfigOtbDetailConverter.class);

        // 将仓库报价费用配置otb详情参数对象转换为实体对象
        FeeConfigOtbDetail entity = converter.toEntity(updateParam);

        // 执行更新仓库报价费用配置otb详情操作
        return super.update(entity);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByFeeConfigOtbId(Long feeConfigOtbId, List<FeeConfigOtbDetailUpdateParam> updateParamList) {
        //更新Details
        List<FeeConfigOtbDetail> dbFeeConfigOtbDetailList = listEntityByFeeConfigOtbId(feeConfigOtbId);

        FeeConfigOtbDetailConverter detailConverter = Converters.get(FeeConfigOtbDetailConverter.class);
        List<FeeConfigOtbDetail> updateDetailList = detailConverter.toEntity(updateParamList);

        updateDetailList.forEach(item -> item.setHeaderId(feeConfigOtbId));

        update(dbFeeConfigOtbDetailList, updateDetailList);
    }


    @Override
    public List<FeeConfigOtbDetailPageVO> listByQuery(FeeConfigOtbDetailQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<FeeConfigOtbDetailPageVO> pageByQuery(PageSearch<FeeConfigOtbDetailQuery> search) {
        Page<FeeConfigOtbDetail> page = Conditions.page(search, entityClass);
        List<FeeConfigOtbDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public FeeConfigOtbDetailVO detailById(Long id) {
        FeeConfigOtbDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeConfigOtbDetail", id));
        }
        return buildFeeConfigOtbDetailVO(entity);
    }

    @Override
    public List<FeeConfigOtbDetail> listEntityByFeeConfigOtbId(Long feeConfigOtbId) {
        return lambdaQuery().eq(FeeConfigOtbDetail::getHeaderId, feeConfigOtbId).list();
    }

    @Override
    public List<FeeConfigOtbDetailVO> listByFeeConfigOtbId(Long feeConfigOtbId) {
        List<FeeConfigOtbDetail> list = lambdaQuery().eq(FeeConfigOtbDetail::getHeaderId, feeConfigOtbId).list();
        return Converters.get(FeeConfigOtbDetailConverter.class).toVO(list);
    }

    @Override
    public List<FeeConfigOtbDetailVO> listByFeeConfigOtbIdList(Collection<Long> feeConfigIds) {
        if (ObjectUtil.isEmpty(feeConfigIds)) {
            return java.util.Collections.emptyList();
        }
        List<FeeConfigOtbDetail> list = lambdaQuery().in(FeeConfigOtbDetail::getHeaderId, new HashSet<>(feeConfigIds)).list();
        return Converters.get(FeeConfigOtbDetailConverter.class).toVO(list);
    }

    @Override
    public Map<Long, List<FeeConfigOtbDetailVO>> mapByFeeConfigOtbIdList(Collection<Long> feeConfigIds) {
        return ObjectUtil.toMapList(listByFeeConfigOtbIdList(feeConfigIds), FeeConfigOtbDetailVO::getHeaderId);
    }

    /**
     * 初始化仓库报价费用配置otb详情对象
     * 此方法用于设置仓库报价费用配置otb详情对象的必要参数，确保其处于有效状态
     *
     * @param createParam 仓库报价费用配置otb详情对象，不应为空
     * @return 返回初始化后的仓库报价费用配置otb详情
     * @throws BusinessException 如果传入的仓库报价费用配置otb详情为空，则抛出此异常
     */
    private FeeConfigOtbDetail initFeeConfigOtbDetail(FeeConfigOtbDetailCreateParam createParam) {

        return initFeeConfigOtbDetail(createParam, entity -> {
        });
    }

    /**
     * 构建仓库报价费用配置otb详情VO对象
     *
     * @param entity 仓库报价费用配置otb详情对象
     * @return 返回包含详细信息的仓库报价费用配置otb详情VO对象
     */
    private FeeConfigOtbDetailVO buildFeeConfigOtbDetailVO(FeeConfigOtbDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的仓库报价费用配置otb详情VO对象
        return Converters.get(FeeConfigOtbDetailConverter.class).toVO(entity);
    }
}
