package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageLabel;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageLabelQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackageLabelPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC请求包裹标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcRequestPackageLabelMapper extends SuperMapper<OtcRequestPackageLabel> {

    /**
     * 根据条件获取OTC请求包裹标签列表
     *
     * @param query 查询条件
     * @return OTC请求包裹标签集合
     */
    default List<OtcRequestPackageLabelPageVO> listByQuery(OtcRequestPackageLabelQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC请求包裹标签分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC请求包裹标签集合
     */
    List<OtcRequestPackageLabelPageVO> listByQuery(@Param("qo") OtcRequestPackageLabelQuery query, @Param("page") Page<?> page);
}