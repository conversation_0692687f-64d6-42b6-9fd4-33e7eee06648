package cn.need.cloud.biz.model.bo.otb.workorder;

import cn.need.cloud.biz.model.bo.otb.pickingslip.OtbPrepPickingSlipSplitBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * OtcWorkorderSplitBO
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class OtbPrepWorkorderSplitBO {

    /**
     * 原单
     */
    private OtbPrepWorkorder prepWorkorder;

    /**
     * 拆单
     */
    private OtbPrepWorkorder splitPrepWorkorder;

    /**
     * 拆单数量
     */
    private Integer splitQty;

    /**
     * 拆单详情
     */
    private List<OtbPrepWorkorderSplitDetailBO> prepDetailHolders = new ArrayList<>();

    /**
     * 拣货单拆单
     */
    private OtbPrepPickingSlipSplitBO prepPickingSlipHolder;

}
