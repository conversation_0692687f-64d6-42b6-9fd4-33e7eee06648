package cn.need.cloud.biz.model.param.otc.update.request;

import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestCreateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTC请求 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC请求 vo对象")
public class OtcRequestUpdateParam extends OtcRequestCreateParam {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;


}