package cn.need.cloud.biz.model.vo.otc.workorder;

import cn.need.cloud.biz.model.vo.base.BaseShippedVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/***
 * 工单详情发货分配实体vo
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC 工单分配发货vo 对象")
public class OtcWorkorderDetailShippedVO extends BaseShippedVO {

}
