package cn.need.cloud.biz.model.query.otc.workorder;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC工单详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC工单详情 query对象")
public class OtcWorkorderDetailQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = 9216224710011107662L;
    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到c端工单id")
    private Long otcWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * readyToShipQty
     */
    @Schema(description = "readyToShipQty")
    private Integer readyToShipQty;

    /**
     * 库存预定id
     */
    @Schema(description = "库存预定id")
    private Long inventoryReserveId;

    /**
     * 预定数量
     */
    @Schema(description = "预定数量")
    private Integer reserveQty;


}