package cn.need.cloud.biz.model.param.warehouse.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 打托模板 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "打托模板 vo对象")
public class PalletTemplateCreateParam implements Serializable {


    /**
     * 每层几个箱子
     */
    @Schema(description = "每层几个箱子")
    private Integer cartonPerLayer;

    /**
     * 一共多少层
     */
    @Schema(description = "一共多少层")
    private Integer layersCount;

    /**
     * 多了几个箱子
     */
    @Schema(description = "多了几个箱子")
    private Integer extCarton;

    /**
     * 每箱几个产品
     */
    @Schema(description = "每箱几个产品")
    private Integer pcsPerCarton;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    /**
     * 是否设置默认
     */
    @Schema(description = "是否设置默认")
    private Boolean setDefaultFlag;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 空托盘id
     */
    @Schema(description = "空托盘id")
    private Long palletEmptyProfileId;

}