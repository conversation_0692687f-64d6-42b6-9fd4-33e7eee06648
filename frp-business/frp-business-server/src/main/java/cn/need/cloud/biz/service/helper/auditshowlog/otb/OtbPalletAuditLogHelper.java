package cn.need.cloud.biz.service.helper.auditshowlog.otb;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 打托单日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class OtbPalletAuditLogHelper {
    private OtbPalletAuditLogHelper() {
    }

    public static void recordLog(List<OtbPallet> otbPalletList, String status, String type, String note, String description) {
        otbPalletList.forEach(item -> recordLog(item, status, type, note, description));
    }

    public static void recordLog(List<OtbPallet> otbPalletList, String type, String note, String description) {
        otbPalletList.forEach(item -> recordAuditShowLogWithStatus(item, type, note, description));
    }

    public static void recordLog(List<OtbPallet> otbPalletList) {
        otbPalletList.forEach(OtbPalletAuditLogHelper::recordLog);
    }

    public static void recordLog(List<OtbPallet> otbPalletList, String description) {
        otbPalletList.forEach(item -> recordLog(item, BaseTypeLogEnum.STATUS.getType(), null, description));
    }

    public static void recordLog(OtbPallet otbPallet) {
        recordLog(
                otbPallet,
                BaseTypeLogEnum.STATUS.getType(),
                null,
                null
        );
    }

    public static void recordLog(OtbPallet otbPallet, String description) {
        recordLog(
                otbPallet,
                BaseTypeLogEnum.STATUS.getType(),
                null,
                description
        );
    }

    public static void recordLog(OtbPallet otbPallet, String type, String note, String description) {
        recordLog(otbPallet, otbPallet.getOtbPalletStatus(), type, note, description);
    }

    public static void recordLog(OtbPallet otbPallet, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(otbPallet)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }

    public static void recordAuditShowLogWithStatus(OtbPallet otbPallet, String type, String description, String status) {
        recordLog(otbPallet, status, type, null, description);
    }
}