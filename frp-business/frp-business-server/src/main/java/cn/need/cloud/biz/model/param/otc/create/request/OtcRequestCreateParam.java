package cn.need.cloud.biz.model.param.otc.create.request;

import cn.need.cloud.biz.client.constant.RegexConstant;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestProductVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTC请求 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC请求 vo对象")
public class OtcRequestCreateParam implements Serializable {


    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    @Pattern(regexp = RegexConstant.CHARACTER_REGEX, message = RegexConstant.CHARACTER_REGEX_MESSAGE)
    private String requestRefNum;


    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 是否快递标志
     */
    @Schema(description = "是否快递标志")
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 最后发货日期
     */
    @Schema(description = "最后发货日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "lastShipDate cannot be empty")
    private LocalDateTime lastShipDate;


    /**
     * 签名类型
     */
    @Schema(description = "签名类型")
    private String signatureType;

    /**
     * 保险金额付款类型
     */
    @Schema(description = "保险金额付款类型")
    private String insuranceAmountCurrency;

    /**
     * 保险金额
     */
    @Schema(description = "保险金额")
    private BigDecimal insuranceAmountAmount;
    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 设置付款标志
     */
    @Schema(description = "设置付款标志")
    private Boolean setPaymentFlag;


    /**
     * 是否提供运输标签
     */
    @Schema(description = "是否提供运输标签")
    private Boolean provideShippingLabelFlag;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @NotBlank(message = "channel cannot be empty")
    private String channel;


    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;


    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;


    @Schema(description = "仓储ID")
    private Long warehouseId;


    @Schema(description = "发货产品详情")
    @Valid
    @NotEmpty(message = "detailList cannot be empty")
    private List<OtcRequestProductVO> detailList;

    @Schema(description = "包裹信息")
    @Valid
    private List<OtcRequestPackageFullVO> packageList;
}