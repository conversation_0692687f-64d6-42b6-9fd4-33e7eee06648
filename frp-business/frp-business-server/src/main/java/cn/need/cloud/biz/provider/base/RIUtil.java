package cn.need.cloud.biz.provider.base;

import cn.need.cloud.biz.client.dto.req.otb.RIReqDTO;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class RIUtil {

    private RIUtil() {
    }

    public static void fillRIId(List<RIReqDTO> list) {
        //获取ri service 的bean
        OtbRoutingInstructionService service = SpringUtil.getBean(OtbRoutingInstructionService.class);
        //获取refNum
        Set<String> refNumList = list
                .stream()
                .map(RIReqDTO::getRefNum)
                .collect(Collectors.toSet());
        //获取ri列表
        OtbRoutingInstructionQuery query = new OtbRoutingInstructionQuery();
        query.setRefNumList(refNumList);
        List<OtbRoutingInstructionPageVO> pageList = Objects.requireNonNull(service).listByQuery(query);
        //根据refNum映射id
        Map<String, Long> map = ObjectUtil.toMap(pageList, OtbRoutingInstructionPageVO::getRefNum, OtbRoutingInstructionPageVO::getId);
        //填充id
        list.forEach(item -> item.setRiId(map.get(item.getRefNum())));
    }
}

