package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.model.entity.product.ProductHazmat;
import cn.need.cloud.biz.model.param.product.create.ProductHazmatCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductHazmatUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductHazmatQuery;
import cn.need.cloud.biz.model.vo.product.ProductHazmatVO;
import cn.need.cloud.biz.model.vo.product.page.ProductHazmatPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;

import java.util.List;

/**
 * <p>
 * service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface ProductHazmatService extends SuperService<ProductHazmat> {

    /**
     * 根据参数新增
     *
     * @param createParam 请求创建参数，包含需要插入的的相关信息
     * @return ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ProductHazmatCreateParam createParam);


    /**
     * 根据参数更新
     *
     * @param updateParam 请求创建参数，包含需要更新的的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(ProductHazmatUpdateParam updateParam);

    /**
     * 根据查询条件获取列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个对象的列表(分页)
     */
    List<ProductHazmatPageVO> listByQuery(ProductHazmatQuery query);

    /**
     * 根据查询条件获取列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个对象的列表(分页)
     */
    PageData<ProductHazmatPageVO> pageByQuery(PageSearch<ProductHazmatQuery> search);

    /**
     * 根据ID获取
     *
     * @param id ID
     * @return 返回VO对象
     */
    ProductHazmatVO detailById(Long id);

    /**
     * 根据产品ID获取
     *
     * @param id ID
     * @return 返回VO对象
     */
    ProductHazmat getByProductId(Long id);

    /**
     * 根据ID删除
     *
     * @param deletedNoteParam ID
     * @return 返回VO对象
     */
    Integer removeHazmat(DeletedNoteParam deletedNoteParam);
}