package cn.need.cloud.biz.model.bo.otb;

import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.otb.create.pallet.OtbPalletCreateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * otb打托上下文信息
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "otb打托上下文信息 Bo对象")
public class OtbBuildPalletContextBo implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * otb工单
     */
    private OtbWorkorder otbWorkorder;

    /**
     * otb打托单状态
     */
    private String palletStatus;

    /**
     * otb发货单id
     */
    private Long shipmentId;

    /**
     * otb 发货地址
     */
    private ShipAddressBO shipAddressBO;

    /**
     * otb工单
     */
    private OtbRequest otbRequest;

    /**
     * otb工单
     */
    private OtbPickingSlip otbPickingSlip;

    /**
     * otb打托单
     */
    private OtbPallet otbPallet;

    /**
     * otb打托label
     */
    private OtbPalletLabel otbPalletLabel;

    /**
     * otb打托单详情
     */
    private List<OtbPalletDetail> otbPalletDetailList;


    /**
     * 前端传参
     */
    private OtbPalletCreateParam param;

    /**
     * 包裹信息
     */
    private List<OtbPackage> otbPackageList;

    /**
     * 包裹信息
     */
    private List<OtbPackage> otbPackageCreatePalletList;

    /**
     * 包裹id
     */
    private List<Long> otbPackageIdList;

    /**
     * 包裹详情集合
     */
    private List<OtbPackageDetail> otbPackageDetailList;

    /**
     * 打托单日志容器
     */
    private List<AuditShowLog> auditShowLogList;
}
