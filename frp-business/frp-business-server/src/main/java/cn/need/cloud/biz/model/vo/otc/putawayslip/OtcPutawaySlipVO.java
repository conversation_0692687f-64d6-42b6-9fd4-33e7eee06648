package cn.need.cloud.biz.model.vo.otc.putawayslip;

import cn.need.cloud.biz.model.vo.base.putawayslip.NormalPutawaySlipVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC上架单 VO对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC上架单 VO对象")
public class OtcPutawaySlipVO extends NormalPutawaySlipVO<OtcPutawaySlipDetailVO> {
}