package cn.need.cloud.biz.model.vo.log;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

// 定义日志项类
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModificationLogVO {

    @JsonProperty("MemberPath")
    @Schema(description = "属性")
    private String memberPath;

    @JsonProperty("NewValue")
    @Schema(description = "新值")
    private String newValue;

    @JsonProperty("OldValue")
    @Schema(description = "旧值")
    private String oldValue;


}