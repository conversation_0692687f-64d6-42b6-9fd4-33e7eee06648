package cn.need.cloud.biz.model.vo.otb.workorder;

import cn.need.cloud.biz.model.vo.base.BasePutAwayVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/***
 * 工单上架详情
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OtbPrepWorkorderPutAwayVO extends BasePutAwayVO {

    /**
     * 总数量
     */
    @Schema(description = "总数量")
    private Integer qty;


    /**
     * 工单详情
     */
    private Long otbWorkorderDetailId;

    /**
     * 工单
     */
    private Long otbWorkorderId;
}
