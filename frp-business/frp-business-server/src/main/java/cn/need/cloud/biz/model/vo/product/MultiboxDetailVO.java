package cn.need.cloud.biz.model.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * vo对象
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Schema(description = " vo对象")
public class MultiboxDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * MultiboxDetailId
     */
    @Schema(description = "MultiboxDetailId")
    private Long multiboxDetailId;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Long productId;

    /**
     * 参考编号
     */
    @Schema(description = "参考编号")
    private String refNum;


    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 产品标题
     */
    @Schema(description = "产品标题")
    private String title;

    /**
     * 组装产品标志
     */
    @Schema(description = "组装产品标志")
    private Boolean assemblyProductFlag;

    /**
     * 多箱标志
     */
    @Schema(description = "多箱标志")
    private Boolean multiboxFlag;

    /**
     * 组类型
     */
    @Schema(description = "组类型")
    private String groupType;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 行号
     */
    @Schema(description = "行号")
    private Integer lineNum;


    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 创建时间
     */
    @Schema(description = "createTime")
    private LocalDateTime createTime;


}