package cn.need.cloud.biz.model.param.otc.update.workorder.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * OTC预提工单详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC预提工单详情 vo对象")
public class OtcPrepWorkorderDetailUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 7952877577083189778L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * 发货到c端预工单id
     */
    @Schema(description = "发货到c端预工单id")
    private Long otcPrepWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * 父节点id
     */
    @Schema(description = "父节点id")
    private Long parentId;

    /**
     * 预工单详情类型
     */
    @Schema(description = "预工单详情类型")
    private String prepWorkorderDetailType;

    /**
     * 预工单详情产品版本
     */
    @Schema(description = "预工单详情产品版本")
    private Integer prepWorkorderDetailVersionInt;

}