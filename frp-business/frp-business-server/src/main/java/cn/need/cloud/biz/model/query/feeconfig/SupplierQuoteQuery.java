package cn.need.cloud.biz.model.query.feeconfig;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 供应商-仓库报价 Query对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商-仓库报价 Query对象")
public class SupplierQuoteQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = 6772291189791057627L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region activeFlag

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 集合")
    @Condition(value = Keyword.IN, fields = {"activeFlag"})
    private List<Boolean> activeFlagList;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"activeFlag"})
    private List<Boolean> activeFlagNiList;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)值类型集合")
    private List<String> activeFlagValueTypeList;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 大于")
    @Condition(value = Keyword.GT, fields = {"activeFlag"})
    private Boolean activeFlagGt;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 大于等于")
    @Condition(value = Keyword.GE, fields = {"activeFlag"})
    private Boolean activeFlagGe;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 小于")
    @Condition(value = Keyword.LT, fields = {"activeFlag"})
    private Boolean activeFlagLt;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 小于等于")
    @Condition(value = Keyword.LE, fields = {"activeFlag"})
    private Boolean activeFlagLe;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"activeFlag"})
    private Boolean activeFlagLike;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"activeFlag"})
    private Boolean activeFlagLikeLeft;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"activeFlag"})
    private Boolean activeFlagLikeRight;

    // endregion activeFlag

    // region deletedNote

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 集合")
    @Condition(value = Keyword.IN, fields = {"deletedNote"})
    private List<String> deletedNoteList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"deletedNote"})
    private List<String> deletedNoteNiList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因值类型集合")
    private List<String> deletedNoteValueTypeList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于")
    @Condition(value = Keyword.GT, fields = {"deletedNote"})
    private String deletedNoteGt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于等于")
    @Condition(value = Keyword.GE, fields = {"deletedNote"})
    private String deletedNoteGe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于")
    @Condition(value = Keyword.LT, fields = {"deletedNote"})
    private String deletedNoteLt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于等于")
    @Condition(value = Keyword.LE, fields = {"deletedNote"})
    private String deletedNoteLe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"deletedNote"})
    private String deletedNoteLike;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"deletedNote"})
    private String deletedNoteLikeLeft;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"deletedNote"})
    private String deletedNoteLikeRight;

    // endregion deletedNote

    // region endTime

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private LocalDateTime endTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 集合")
    @Condition(value = Keyword.IN, fields = {"endTime"})
    private List<LocalDateTime> endTimeList;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"endTime"})
    private List<LocalDateTime> endTimeNiList;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间值类型集合")
    private List<String> endTimeValueTypeList;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 大于")
    @Condition(value = Keyword.GT, fields = {"endTime"})
    private LocalDateTime endTimeGt;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 大于等于")
    @Condition(value = Keyword.GE, fields = {"endTime"})
    private LocalDateTime endTimeGe;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 小于")
    @Condition(value = Keyword.LT, fields = {"endTime"})
    private LocalDateTime endTimeLt;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 小于等于")
    @Condition(value = Keyword.LE, fields = {"endTime"})
    private LocalDateTime endTimeLe;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"endTime"})
    private LocalDateTime endTimeLike;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"endTime"})
    private LocalDateTime endTimeLikeLeft;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"endTime"})
    private LocalDateTime endTimeLikeRight;

    // endregion endTime

    // region name

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 名称
     */
    @Schema(description = "名称 集合")
    @Condition(value = Keyword.IN, fields = {"name"})
    private List<String> nameList;

    /**
     * 名称
     */
    @Schema(description = "名称 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"name"})
    private List<String> nameNiList;

    /**
     * 名称
     */
    @Schema(description = "名称值类型集合")
    private List<String> nameValueTypeList;

    /**
     * 名称
     */
    @Schema(description = "名称 大于")
    @Condition(value = Keyword.GT, fields = {"name"})
    private String nameGt;

    /**
     * 名称
     */
    @Schema(description = "名称 大于等于")
    @Condition(value = Keyword.GE, fields = {"name"})
    private String nameGe;

    /**
     * 名称
     */
    @Schema(description = "名称 小于")
    @Condition(value = Keyword.LT, fields = {"name"})
    private String nameLt;

    /**
     * 名称
     */
    @Schema(description = "名称 小于等于")
    @Condition(value = Keyword.LE, fields = {"name"})
    private String nameLe;

    /**
     * 名称
     */
    @Schema(description = "名称 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"name"})
    private String nameLike;

    /**
     * 名称
     */
    @Schema(description = "名称 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"name"})
    private String nameLikeLeft;

    /**
     * 名称
     */
    @Schema(description = "名称 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"name"})
    private String nameLikeRight;

    // endregion name

    // region note

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 备注
     */
    @Schema(description = "备注 集合")
    @Condition(value = Keyword.IN, fields = {"note"})
    private List<String> noteList;

    /**
     * 备注
     */
    @Schema(description = "备注 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"note"})
    private List<String> noteNiList;

    /**
     * 备注
     */
    @Schema(description = "备注值类型集合")
    private List<String> noteValueTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注 大于")
    @Condition(value = Keyword.GT, fields = {"note"})
    private String noteGt;

    /**
     * 备注
     */
    @Schema(description = "备注 大于等于")
    @Condition(value = Keyword.GE, fields = {"note"})
    private String noteGe;

    /**
     * 备注
     */
    @Schema(description = "备注 小于")
    @Condition(value = Keyword.LT, fields = {"note"})
    private String noteLt;

    /**
     * 备注
     */
    @Schema(description = "备注 小于等于")
    @Condition(value = Keyword.LE, fields = {"note"})
    private String noteLe;

    /**
     * 备注
     */
    @Schema(description = "备注 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"note"})
    private String noteLike;

    /**
     * 备注
     */
    @Schema(description = "备注 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"note"})
    private String noteLikeLeft;

    /**
     * 备注
     */
    @Schema(description = "备注 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"note"})
    private String noteLikeRight;

    // endregion note

    // region quoteId

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价")
    private Long quoteId;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 集合")
    @Condition(value = Keyword.IN, fields = {"quoteId"})
    private List<Long> quoteIdList;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"quoteId"})
    private List<Long> quoteIdNiList;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价值类型集合")
    private List<String> quoteIdValueTypeList;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 大于")
    @Condition(value = Keyword.GT, fields = {"quoteId"})
    private Long quoteIdGt;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 大于等于")
    @Condition(value = Keyword.GE, fields = {"quoteId"})
    private Long quoteIdGe;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 小于")
    @Condition(value = Keyword.LT, fields = {"quoteId"})
    private Long quoteIdLt;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 小于等于")
    @Condition(value = Keyword.LE, fields = {"quoteId"})
    private Long quoteIdLe;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"quoteId"})
    private Long quoteIdLike;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"quoteId"})
    private Long quoteIdLikeLeft;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"quoteId"})
    private Long quoteIdLikeRight;

    // endregion quoteId

    // region refNum

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"refNum"})
    private List<String> refNumNiList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码值类型集合")
    private List<String> refNumValueTypeList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"refNum"})
    private String refNumGt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"refNum"})
    private String refNumGe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"refNum"})
    private String refNumLt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"refNum"})
    private String refNumLe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"refNum"})
    private String refNumLike;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"refNum"})
    private String refNumLikeLeft;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"refNum"})
    private String refNumLikeRight;

    // endregion refNum

    // region startTime

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private LocalDateTime startTime;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 集合")
    @Condition(value = Keyword.IN, fields = {"startTime"})
    private List<LocalDateTime> startTimeList;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"startTime"})
    private List<LocalDateTime> startTimeNiList;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间值类型集合")
    private List<String> startTimeValueTypeList;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 大于")
    @Condition(value = Keyword.GT, fields = {"startTime"})
    private LocalDateTime startTimeGt;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 大于等于")
    @Condition(value = Keyword.GE, fields = {"startTime"})
    private LocalDateTime startTimeGe;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 小于")
    @Condition(value = Keyword.LT, fields = {"startTime"})
    private LocalDateTime startTimeLt;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 小于等于")
    @Condition(value = Keyword.LE, fields = {"startTime"})
    private LocalDateTime startTimeLe;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"startTime"})
    private LocalDateTime startTimeLike;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"startTime"})
    private LocalDateTime startTimeLikeLeft;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"startTime"})
    private LocalDateTime startTimeLikeRight;

    // endregion startTime

    // region supplierId

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 集合")
    @Condition(value = Keyword.IN, fields = {"supplierId"})
    private List<Long> supplierIdList;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"supplierId"})
    private List<Long> supplierIdNiList;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id值类型集合")
    private List<String> supplierIdValueTypeList;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 大于")
    @Condition(value = Keyword.GT, fields = {"supplierId"})
    private Long supplierIdGt;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 大于等于")
    @Condition(value = Keyword.GE, fields = {"supplierId"})
    private Long supplierIdGe;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 小于")
    @Condition(value = Keyword.LT, fields = {"supplierId"})
    private Long supplierIdLt;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 小于等于")
    @Condition(value = Keyword.LE, fields = {"supplierId"})
    private Long supplierIdLe;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"supplierId"})
    private Long supplierIdLike;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"supplierId"})
    private Long supplierIdLikeLeft;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"supplierId"})
    private Long supplierIdLikeRight;

    // endregion supplierId

    // region warehouseId

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 集合")
    @Condition(value = Keyword.IN, fields = {"warehouseId"})
    private List<Long> warehouseIdList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"warehouseId"})
    private List<Long> warehouseIdNiList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id值类型集合")
    private List<String> warehouseIdValueTypeList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于")
    @Condition(value = Keyword.GT, fields = {"warehouseId"})
    private Long warehouseIdGt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于等于")
    @Condition(value = Keyword.GE, fields = {"warehouseId"})
    private Long warehouseIdGe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于")
    @Condition(value = Keyword.LT, fields = {"warehouseId"})
    private Long warehouseIdLt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于等于")
    @Condition(value = Keyword.LE, fields = {"warehouseId"})
    private Long warehouseIdLe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"warehouseId"})
    private Long warehouseIdLike;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"warehouseId"})
    private Long warehouseIdLikeLeft;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"warehouseId"})
    private Long warehouseIdLikeRight;

    // endregion warehouseId


}