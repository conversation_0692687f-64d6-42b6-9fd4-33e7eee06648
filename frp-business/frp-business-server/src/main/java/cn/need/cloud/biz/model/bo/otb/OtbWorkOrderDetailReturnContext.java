package cn.need.cloud.biz.model.bo.otb;

import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * OtcWorkOrderReturnContext
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "OtcWorkOrderDetailReturnContext")
public class OtbWorkOrderDetailReturnContext implements Serializable {

    /**
     * workorderDetail
     */
    @Schema(description = "workorderDetail")
    private OtbWorkorderDetail workorderDetail;

    /**
     * inventoryLocked
     */
    @Schema(description = "inventoryLocked")
    private InventoryLocked inventoryLocked;

    /**
     * inventoryReserve
     */
    @Schema(description = "inventoryReserve")
    private InventoryReserve inventoryReserve;

    /**
     * prepWorkorder
     */
    @Schema(description = "prepWorkorder")
    private OtbPrepWorkorder prepWorkorder;

    /**
     * prepWorkOrderDetailReturnContextList
     */
    @Schema(description = "prepWorkOrderDetailReturnContextList")
    private List<OtbPrepWorkOrderDetailReturnContext> prepWorkOrderDetailReturnContextList;

    public OtbWorkOrderDetailReturnContext(OtbWorkorderDetail detail, InventoryLocked inventoryLocked) {
        this.workorderDetail = detail;
        this.inventoryLocked = inventoryLocked;
        this.prepWorkOrderDetailReturnContextList = new ArrayList<>();
    }

    public OtbWorkOrderDetailReturnContext(OtbWorkorderDetail detail, InventoryLocked inventoryLocked, InventoryReserve inventoryReserve) {
        this.workorderDetail = detail;
        this.inventoryLocked = inventoryLocked;
        this.inventoryReserve = inventoryReserve;
        this.prepWorkOrderDetailReturnContextList = new ArrayList<>();
    }
}
