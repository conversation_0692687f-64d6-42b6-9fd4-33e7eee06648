package cn.need.cloud.biz.model.param.otb.create.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * OTB工单详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB工单详情 vo对象")
public class OtbWorkorderDetailCreateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 5004466311002445220L;
    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    @Size(max = 64, message = "hazmatVersionRefNum cannot exceed 64 characters")
    private String hazmatVersionRefNum;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    private Integer finishQty;


    /**
     * 请求快照,渠道产品编码
     */
    @Schema(description = "请求快照,渠道产品编码")
    private String detailSnapshotProductBarcode;

    /**
     * 库存锁定id
     */
    @Schema(description = "库存锁定id")
    private Long inventoryLockedId;

    /**
     * 库存预定id
     */
    @Schema(description = "库存预定id")
    private Long inventoryReserveId;

    /**
     * 预定数量
     */
    @Schema(description = "预定数量")
    private Integer reserveQty;

    /**
     * 打包为Package 数量
     */
    @Schema(description = "打包为Package 数量")
    private Integer packedQty;

    /**
     * 做成Shipment数量
     */
    @Schema(description = "做成Shipment数量")
    private Integer shipmentQty;

    /**
     * 完成预定数量
     */
    @Schema(description = "完成预定数量")
    private Integer finishReserveQty;

}