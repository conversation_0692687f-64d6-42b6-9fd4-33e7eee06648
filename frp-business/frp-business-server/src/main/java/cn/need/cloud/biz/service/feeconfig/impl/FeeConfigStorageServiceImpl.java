package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigStorageConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigStorageMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorage;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorageDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigStorageCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigStorageUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigStorageQuery;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigStoragePageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigStorageDetailService;
import cn.need.cloud.biz.service.feeconfig.FeeConfigStorageService;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.helper.SectionHelper;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓库报价费用配置Storage服务实现类
 * </p>
 * <p>
 * 该类实现了针对仓储业务(Storage)相关费用配置的管理服务，包括费用配置的创建、
 * 更新、查询以及状态管理等功能。Storage费用配置用于定义仓储业务中不同服务项目的价格标准，
 * 是仓储费用结算的重要基础数据。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 费用配置的基础CRUD操作
 * 2. 费用配置明细的关联管理
 * 3. 费用配置的启用/禁用管理
 * 4. 与报价单的关联处理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service("feeConfigStorage")
public class FeeConfigStorageServiceImpl extends SuperServiceImpl<FeeConfigStorageMapper, FeeConfigStorage> implements FeeConfigStorageService {

    /**
     * Storage费用配置明细服务，用于管理费用配置的详细项目
     */
    @Resource
    private FeeConfigStorageDetailService feeConfigStorageDetailservice;

    /**
     * 报价服务，用于管理与费用配置关联的报价单信息
     */
    @Resource
    private QuoteService quoteService;

    /**
     * 根据参数创建Storage费用配置
     * <p>
     * 该方法在事务中执行，确保费用配置及其明细的原子性创建。
     * 主要步骤包括：
     * 1. 验证创建参数的有效性
     * 2. 检查明细列表的合法性
     * 3. 初始化并插入费用配置主记录
     * 4. 关联并插入费用配置明细记录
     * </p>
     *
     * @param createParam Storage费用配置创建参数
     * @return 创建成功的费用配置ID
     * @throws BusinessException 如果参数为空或明细列表校验失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeConfigStorageCreateParam createParam) {
        // 检查传入仓库报价费用配置storage参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(createParam.getDetailList());

        // 获取仓库报价费用配置storage转换器实例，用于将仓库报价费用配置storage参数对象转换为实体对象
        FeeConfigStorageConverter converter = Converters.get(FeeConfigStorageConverter.class);

        // 将仓库报价费用配置storage参数对象转换为实体对象并初始化
        FeeConfigStorage entity = initFeeConfigStorage(converter.toEntity(createParam));

        // 插入仓库报价费用配置storage实体对象到数据库
        super.insert(entity);

        final List<FeeConfigStorageDetail> feeConfigStorageDetails = feeConfigStorageDetailservice.initFeeConfigStorageDetail(createParam.getDetailList(), item -> item.setHeaderId(entity.getId()));

        feeConfigStorageDetailservice.insertBatch(feeConfigStorageDetails);

        // 返回仓库报价费用配置storageID
        return entity.getId();
    }

    /**
     * 根据参数更新Storage费用配置
     * <p>
     * 该方法在事务中执行，确保费用配置及其明细的原子性更新。
     * 主要步骤包括：
     * 1. 验证更新参数的有效性
     * 2. 检查明细列表的合法性
     * 3. 检查是否有关联的报价单（有关联报价单的配置不允许修改）
     * 4. 更新费用配置明细
     * 5. 更新费用配置主记录
     * </p>
     *
     * @param updateParam Storage费用配置更新参数
     * @return 更新影响的记录数
     * @throws BusinessException 如果参数为空、ID为空、明细列表校验失败，或存在关联报价单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeConfigStorageUpdateParam updateParam) {
        // 检查传入仓库报价费用配置storage参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(updateParam.getDetailList());

        checkHasQuote(updateParam.getId());

        // 获取仓库报价费用配置storage转换器实例，用于将仓库报价费用配置storage参数对象转换为实体对象
        FeeConfigStorageConverter converter = Converters.get(FeeConfigStorageConverter.class);
        // 将仓库报价费用配置storage参数对象转换为实体对象
        FeeConfigStorage entity = converter.toEntity(updateParam);

        feeConfigStorageDetailservice.updateByFeeConfigStorageId(updateParam.getId(), updateParam.getDetailList());

        // 执行更新仓库报价费用配置storage操作
        return super.update(entity);

    }

    /**
     * 根据查询条件获取Storage费用配置列表
     * <p>
     * 该方法根据指定的查询条件返回符合条件的Storage费用配置列表，不包含分页信息。
     * </p>
     *
     * @param query 包含查询条件的Storage费用配置查询对象
     * @return 符合条件的Storage费用配置分页视图对象列表
     */
    @Override
    public List<FeeConfigStoragePageVO> listByQuery(FeeConfigStorageQuery query) {

        return mapper.listByQuery(query);
    }

    /**
     * 分页查询Storage费用配置列表
     * <p>
     * 该方法根据查询条件和分页参数获取Storage费用配置列表，并填充关联的报价单信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的Storage费用配置分页视图对象
     */
    @Override
    public PageData<FeeConfigStoragePageVO> pageByQuery(PageSearch<FeeConfigStorageQuery> search) {
        Page<FeeConfigStorage> page = Conditions.page(search, entityClass);
        List<FeeConfigStoragePageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        //fillData
        fillData(dataList);

        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取Storage费用配置详情
     * <p>
     * 该方法获取指定ID的Storage费用配置详细信息，包括配置明细等关联信息。
     * 如果找不到对应ID的配置，则抛出业务异常。
     * </p>
     *
     * @param id Storage费用配置ID
     * @return 包含详细信息的Storage费用配置视图对象
     * @throws BusinessException 如果找不到指定ID的配置记录
     */
    @Override
    public FeeConfigStorageVO detailById(Long id) {
        FeeConfigStorage entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeConfigStorage", id));
        }

        return buildFeeConfigStorageVO(entity);
    }

    /**
     * 根据参考编号获取Storage费用配置详情
     * <p>
     * 该方法根据费用配置的参考编号获取配置详细信息，包括配置明细等关联信息。
     * 如果找不到对应参考编号的配置，则抛出业务异常。
     * </p>
     *
     * @param refNum Storage费用配置参考编号
     * @return 包含详细信息的Storage费用配置视图对象
     * @throws BusinessException 如果找不到指定参考编号的配置记录
     */
    @Override
    public FeeConfigStorageVO detailByRefNum(String refNum) {
        FeeConfigStorage entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeConfigStorage", "refNum", refNum));
        }
        return buildFeeConfigStorageVO(entity);
    }

    /**
     * 根据报价ID获取Storage费用配置列表
     * <p>
     * 该方法获取指定报价ID关联的所有Storage费用配置详细信息，包括配置明细等关联信息。
     * 如果报价ID为空，则返回空列表。
     * </p>
     *
     * @param quoteId 报价ID
     * @return 包含详细信息的Storage费用配置视图对象列表
     */
    @Override
    public List<FeeConfigStorageVO> listDetailByQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return java.util.Collections.emptyList();
        }
        List<FeeConfigStorage> feeConfigStorageList = lambdaQuery().eq(FeeConfigStorage::getQuoteId, quoteId).list();
        return buildFeeConfigStorageVOList(feeConfigStorageList);
    }

    /**
     * 在切换Storage费用配置状态为 {@code ACTIVE} 之前执行该方法
     * <p>
     * 该方法检查Storage费用配置是否已经关联了报价单，如果没有关联报价单，抛出业务异常。
     * </p>
     *
     * @param entity Storage费用配置实体对象
     * @throws BusinessException 如果Storage费用配置没有关联报价单
     */
    @Override
    public void beforeSwitchActive(FeeConfigStorage entity) {
        checkHasQuote(entity);
    }

    /**
     * 初始化Storage费用配置对象
     * <p>
     * 此方法用于设置Storage费用配置对象的必要参数，包括ID和参考编号，
     * 确保其处于有效状态。
     * </p>
     *
     * @param entity Storage费用配置对象，不应为空
     * @return 返回初始化后的Storage费用配置
     * @throws BusinessException 如果传入的费用配置为空，则抛出此异常
     */
    private FeeConfigStorage initFeeConfigStorage(FeeConfigStorage entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("FeeConfigStorage cannot be empty");
        }

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_STORAGE));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 填充Storage费用配置视图对象列表的关联数据
     * <p>
     * 该方法为列表中的Storage费用配置视图对象填充关联的报价单信息。
     * 主要步骤包括：
     * 1. 检查数据列表是否为空
     * 2. 获取所有关联的报价单ID
     * 3. 根据报价单ID获取报价单信息（编号和名称）
     * 4. 将报价单信息设置到对应的费用配置视图对象中
     * </p>
     *
     * @param dataList Storage费用配置视图对象列表
     */
    private void fillData(List<FeeConfigStoragePageVO> dataList) {
        if (!ObjectUtil.isNotEmpty(dataList)) {
            return;
        }

        final Map<Long, RefNumWithNameVO> quoteMap = quoteService.refNumWithNameMapByIds(FeeConfigUtil.getQuoteIds(dataList));

        if (ObjectUtil.isNotEmpty(quoteMap)) {
            for (FeeConfigStoragePageVO item : dataList) {
                if (!ObjectUtil.isNotEmpty(item.getQuoteId())) {
                    continue;
                }
                item.setQuote(quoteMap.get(item.getQuoteId()));
            }
        }
    }

    /**
     * 删除Storage费用配置并记录备注
     * <p>
     * 该方法用于删除指定ID的Storage费用配置，并记录删除原因。
     * 删除前会检查是否有关联的报价单，如有则不允许删除。
     * </p>
     *
     * @param id   要删除的Storage费用配置ID
     * @param note 删除原因备注
     * @return 删除影响的记录数
     * @throws BusinessException 如果存在关联报价单
     */
    @Override
    public int removeAndNote(Long id, String note) {

        checkHasQuote(id);

        return super.removeAndNote(id, note);
    }


    /**
     * 构建Storage费用配置视图对象
     * <p>
     * 该方法将Storage费用配置实体对象转换为包含完整信息的视图对象，
     * 包括基本信息、明细列表和关联的报价单信息。
     * 主要步骤包括：
     * 1. 将实体对象转换为视图对象
     * 2. 查询并设置费用配置明细列表
     * 3. 查询并设置关联的报价单信息
     * </p>
     *
     * @param entity Storage费用配置实体对象
     * @return 包含完整信息的Storage费用配置视图对象
     */
    private FeeConfigStorageVO buildFeeConfigStorageVO(FeeConfigStorage entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        FeeConfigStorageVO result = Converters.get(FeeConfigStorageConverter.class).toVO(entity);

        //详情
        List<FeeConfigStorageDetailVO> detailList = feeConfigStorageDetailservice.listByFeeConfigStorageId(result.getId());

        result.setDetailList(detailList);

        result.setQuote(quoteService.refNumWithNameById(entity.getQuoteId()));

        return result;
    }

    /**
     * 构建Storage费用配置视图对象列表
     *
     * @param entityList Storage费用配置实体对象列表
     * @return 返回包含详细信息的Storage费用配置视图对象列表
     */
    private List<FeeConfigStorageVO> buildFeeConfigStorageVOList(List<FeeConfigStorage> entityList) {
        if (ObjectUtil.isEmpty(entityList)) {
            return java.util.Collections.emptyList();
        }

        List<FeeConfigStorageVO> resultList = Converters.get(FeeConfigStorageConverter.class).toVO(entityList);

        // 获取所有费用配置ID
        List<Long> feeConfigIds = resultList.stream().map(FeeConfigStorageVO::getId).toList();

        // 批量获取所有费用配置的明细
        Map<Long, List<FeeConfigStorageDetailVO>> detailListMap = feeConfigStorageDetailservice.mapByFeeConfigStorageIdList(feeConfigIds);

        // 批量获取所有报价信息
        final Map<Long, RefNumWithNameVO> refNumVOMap = quoteService.refNumWithNameMapByIds(entityList.stream()
                .map(FeeConfigStorage::getQuoteId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList());

        // 组装最终结果
        for (FeeConfigStorageVO feeConfigStorageVO : resultList) {
            // 设置明细
            List<FeeConfigStorageDetailVO> detailList = detailListMap.get(feeConfigStorageVO.getId());
            feeConfigStorageVO.setDetailList(ObjectUtil.isEmpty(detailList) ? java.util.Collections.emptyList() : detailList);

            // 设置报价信息
            RefNumWithNameVO quote = refNumVOMap.get(feeConfigStorageVO.getQuoteId());
            feeConfigStorageVO.setQuote(quote);
        }

        return resultList;
    }
}
