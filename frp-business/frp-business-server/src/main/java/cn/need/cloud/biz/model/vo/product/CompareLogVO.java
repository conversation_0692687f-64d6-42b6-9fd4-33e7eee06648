package cn.need.cloud.biz.model.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 产品测量日志 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "产品版本日志 vo对象")
@AllArgsConstructor
@NoArgsConstructor
public class CompareLogVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;


    private List<String> differenceList;
}
