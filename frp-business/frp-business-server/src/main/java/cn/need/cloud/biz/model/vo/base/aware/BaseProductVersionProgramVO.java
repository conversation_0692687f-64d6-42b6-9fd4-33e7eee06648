package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

/**
 * 产品版本程序对象
 * <p>
 * 子类中不能包含 productVersionId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseProductVersionProgramVO implements BaseProductVersionAware {
    /**
     * 产品版本id
     */
    @Getter
    @Setter
    private Long productVersionId;

    /**
     * 产品版本
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    @JsonIgnore
    private BaseProductVersionVO baseProductVersionVO;

    @Override
    @JsonIgnore
    public BaseProductVersionVO getBaseProductVersionVO() {
        if (ObjectUtil.isEmpty(productVersionId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseProductVersionVO)) {
            return baseProductVersionVO;
        }
        // Retrieve from cache once and store the result
        baseProductVersionVO = BaseProductVersionAware.super.getBaseProductVersionVO();
        return baseProductVersionVO;
    }
}
