package cn.need.cloud.biz.model.entity.warehouse;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * <p>
 * 托盘信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pallet_empty_profile")
public class PalletEmptyProfile extends SuperModel {


    /**
     * 名字
     */
    @TableField("name")
    @Unique(message = "name is existed", combined = {"warehouseId"})
    private String name;

    /**
     * 托盘-长
     */
    @TableField("pallet_size_length")
    private BigDecimal palletSizeLength;

    /**
     * 托盘-宽
     */
    @TableField("pallet_size_width")
    private BigDecimal palletSizeWidth;

    /**
     * 托盘-高
     */
    @TableField("pallet_size_height")
    private BigDecimal palletSizeHeight;

    /**
     * 托盘-重量
     */
    @TableField("pallet_size_weight")
    private BigDecimal palletSizeWeight;

    /**
     * 托盘-重量单位
     */
    @TableField("pallet_size_weight_unit")
    private String palletSizeWeightUnit;

    /**
     * 托盘-长度单位
     */
    @TableField("pallet_size_dimension_unit")
    private String palletSizeDimensionUnit;

    /**
     * 是否有效
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @TableField("note")
    private String note;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 使用标识
     */
    @TableField("in_use_flag")
    private Boolean inUseFlag;

}
