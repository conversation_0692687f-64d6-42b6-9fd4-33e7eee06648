package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库档案 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库档案 vo对象")
public class ProfileWarehousePageVO extends BaseSuperVO {

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码")
    private String categoryCode;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述")
    private String categoryDesc;

    /**
     * 值类型
     */
    @Schema(description = "值类型")
    private String valueType;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 值
     */
    @Schema(description = "值")
    private String value;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 激活标志
     */
    @Schema(description = "激活标志")
    private Boolean activeFlag;

    /**
     * 代码
     */
    @Schema(description = "代码")
    private String code;

}