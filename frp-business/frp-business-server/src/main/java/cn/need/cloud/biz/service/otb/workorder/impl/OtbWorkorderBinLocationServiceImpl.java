package cn.need.cloud.biz.service.otb.workorder.impl;

import cn.need.cloud.biz.mapper.otb.OtbWorkorderBinLocationMapper;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbWorkorderBinLocationPageVO;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC工单仓储位置 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbWorkorderBinLocationServiceImpl extends SuperServiceImpl<OtbWorkorderBinLocationMapper, OtbWorkorderBinLocation> implements OtbWorkorderBinLocationService {

    @Resource
    @Lazy
    private OtbWorkorderService otbWorkorderService;
    @Resource
    @Lazy
    private OtbPickingSlipService otbPickingSlipService;

    /// ///////////////////////////////////////// 共有方法 ////////////////////////////////////////////

    @Override
    public PageData<OtbWorkorderBinLocationPageVO> pageByQuery(PageSearch<OtbWorkOrderBinLocationQuery> search) {
        Page<OtbWorkorderBinLocation> page = Conditions.page(search, entityClass);
        // 查询列表
        List<OtbWorkorderBinLocationPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        // 产品填充
        this.fillField(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public List<OtbWorkorderBinLocation> listByOtbWorkorderDetailIdList(List<Long> workOrderDetailIdList) {

        return ObjectUtil.isEmpty(workOrderDetailIdList)
                ? Collections.emptyList()
                : lambdaQuery()
                .in(OtbWorkorderBinLocation::getOtbWorkorderDetailId, workOrderDetailIdList)
                .list();
    }

    @Override
    public List<OtbWorkorderBinLocation> listByWorkorderIdList(List<Long> workorderIds) {
        if (ObjectUtil.isNotEmpty(workorderIds)) {
            return lambdaQuery().in(OtbWorkorderBinLocation::getOtbWorkorderId, workorderIds).list();
        }
        return List.of();
    }

    /**
     * 产品填充
     *
     * @param dataList 需要填充单PageVO List
     */
    private void fillField(List<OtbWorkorderBinLocationPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        List<Long> workOrderIds = StreamUtils.distinctMap(dataList, OtbWorkorderBinLocationPageVO::getOtbWorkorderId);
        Map<Long, RefNumVO> workOrderMap = otbWorkorderService.refNumMapByIds(workOrderIds);
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtbWorkorderBinLocationPageVO::getOtbPickingSlipId);
        Map<Long, RefNumVO> psMap = otbPickingSlipService.refNumMapByIds(pickingSlipIds);
        dataList.forEach(obj -> {
            // 工单填充
            obj.setOtbWorkOrder(workOrderMap.get(obj.getOtbWorkorderId()));
            // 拣货单填充
            obj.setOtbPickingSlip(psMap.get(obj.getOtbPickingSlipId()));
        });
    }

}
