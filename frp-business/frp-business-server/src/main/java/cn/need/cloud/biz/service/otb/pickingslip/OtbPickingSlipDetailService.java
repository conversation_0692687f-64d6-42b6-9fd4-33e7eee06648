package cn.need.cloud.biz.service.otb.pickingslip;

import cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipMarkReLabelPrintByBarcodeQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipSummaryVO;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.List;

/**
 * <p>
 * otb拣货单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPickingSlipDetailService extends SuperService<OtbPickingSlipDetail> {

    /**
     * 根据otb拣货单id获取otb拣货单详情集合
     *
     * @param otbPickingSlipId otb拣货单id
     * @return otb拣货单详情集合
     */
    List<OtbPickingSlipDetailVO> listByOtbPickingSlipId(Long otbPickingSlipId);

    /**
     * 根据拣货单详情获取
     *
     * @param id 拣货单id
     * @return 拣货单详情集合
     */
    List<OtbPickingSlipDetailVO> listByPickSlipId(Long id);

    /**
     * 汇总产品+库位的数量
     *
     * @param pickingSlipIdList 拣货单id集合
     * @return /
     */
    List<OtbPickingSlipSummaryVO> summary(List<Long> pickingSlipIdList);

    /**
     * 拣货操作
     *
     * @param pickList 拣货条件
     * @return /
     */
    List<OtbPickingSlipDetailPickVO> pick(List<OtbPickingSlipProductPickQuery> pickList);

    /**
     * 拣货更新
     *
     * @param pickDetails 拣货信息
     * @return /
     */
    boolean pickUpdate(List<OtbPickingSlipDetailPickVO> pickDetails);

    /**
     * 忽略详情id列表，判断是否全部已拣货
     *
     * @param id                      拣货单
     * @param pickingSlipDetailIdList 将货单详情
     * @return /
     */
    boolean allPickedIgnoreDetailIdList(Long id, List<Long> pickingSlipDetailIdList);

    /**
     * 根据MarkPrint参数获取详情信息
     *
     * @param query MarkPrint条件
     * @return /
     */
    List<OtbPickingSlipDetail> findMarkReLabelDetails(OtbPickingSlipMarkReLabelPrintByBarcodeQuery query);

    /**
     * 根据拣货单id获取详情
     *
     * @param pickingSlipId 拣货单id
     * @return /
     */
    List<OtbPickingSlipDetail> listByPickingSlipId(Long pickingSlipId);


    /**
     * @param pickingSlipIdList 拣货单id集合
     * @return /
     * @description 根据拣货单id集合获取详情
     */
    List<OtbPickingSlipDetail> listByPickingSlipIds(List<Long> pickingSlipIdList);
}