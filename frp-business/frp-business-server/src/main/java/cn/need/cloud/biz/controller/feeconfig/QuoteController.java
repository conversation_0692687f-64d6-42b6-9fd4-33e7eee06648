package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.QuoteConverter;
import cn.need.cloud.biz.model.entity.feeconfig.Quote;
import cn.need.cloud.biz.model.param.feeconfig.create.QuoteCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.QuoteUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.QuoteQuery;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.QuotePageVO;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 仓库报价 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/quote")
@Tag(name = "仓库报价")
public class QuoteController extends AbstractRestController<QuoteService, Quote, QuoteConverter, QuoteVO> {

    @Operation(summary = "新增仓库报价", description = "接收仓库报价的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) QuoteCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改仓库报价", description = "接收仓库报价的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) QuoteUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除仓库报价", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取仓库报价详情", description = "根据数据主键id，从数据库中获取其对应的仓库报价详情")
    @GetMapping(value = "/detail/{id}")
    public Result<QuoteVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取仓库报价分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库报价列表")
    @PostMapping(value = "/list")
    public Result<PageData<QuotePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<QuoteQuery> search) {

        // 获取仓库报价分页
        PageData<QuotePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "启用禁用", description = "启用禁用")
    @PostMapping(value = "/switch-active")
    public Result<Integer> switchActive(@RequestBody @Parameter(description = "有效无效 vo对象", required = true) IdCondition id) {

        Validate.notNull(id.getId(), "The id value cannot be null.");
        // 返回结果
        return success(service.switchActive(id.getId()));
    }
}
