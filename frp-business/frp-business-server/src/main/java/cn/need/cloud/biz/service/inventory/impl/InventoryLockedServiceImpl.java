package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.converter.inventory.InventoryInventoryLockedConverter;
import cn.need.cloud.biz.converter.inventory.InventoryLockedConverter;
import cn.need.cloud.biz.mapper.inventory.InventoryLockedMapper;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.param.inventory.create.InventoryLockedCreateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.inventory.InventoryLockedQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInventoryLockedVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryLockedVO;
import cn.need.cloud.biz.model.vo.page.InventoryLockedPageVO;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InventoryLockedServiceImpl extends SuperServiceImpl<InventoryLockedMapper, InventoryLocked> implements InventoryLockedService {


    /**
     * 校验释放数量
     *
     * @param param           请求参数
     * @param inventoryLocked 库存锁
     */
    private static void validateReleaseQuantity(InventoryReleaseLockedParam param, InventoryLocked inventoryLocked) {
        if (param.getQty() <= 0) {
            throw new BusinessException("The quantity released must be greater than 0");
        }
        // 校验释放数量
        int remainingQty = inventoryLocked.getQty() - inventoryLocked.getFinishQty() - param.getQty();
        if (remainingQty < 0) {
            throw new BusinessException("The released quantity cannot exceed the remaining locked quantity");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InventoryLocked insertByParam(InventoryLockedCreateParam createParam) {

        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        InventoryLockedConverter converter = Converters.get(InventoryLockedConverter.class);

        InventoryLocked entity = initInventoryLocked(converter.toEntity(createParam));

        super.insert(entity);

        return entity;
    }

    /**
     * 初始化锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值对象
     * 此方法用于设置锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值对象的必要参数，确保其处于有效状态
     *
     * @param entity 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值对象，不应为空
     * @return 返回初始化后的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值
     * @throws BusinessException 如果传入的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值为空，则抛出此异常
     */
    private InventoryLocked initInventoryLocked(InventoryLocked entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("InventoryLocked cannot be empty");
        }

        entity.setFinishQty(0);

        entity.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());

        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    public List<InventoryLockedPageVO> listByQuery(InventoryLockedQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<InventoryLockedPageVO> pageByQuery(PageSearch<InventoryLockedQuery> search) {
        Page<InventoryLocked> page = Conditions.page(search, entityClass);
        List<InventoryLockedPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public InventoryLockedVO detailById(Long id) {
        InventoryLocked entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InventoryLocked");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InventoryLocked", id));
        }
        return buildInventoryLockedVO(entity);
    }

    @Override
    public InventoryLockedVO detailByRefNum(String refNum) {
        InventoryLocked entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in InventoryLocked");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InventoryLocked", "refNum", refNum));
        }
        return buildInventoryLockedVO(entity);
    }

    @Override
    public List<InventoryLockedVO> listLockeByProductIds(List<Long> productIds) {
        //库存锁定
        List<InventoryLocked> inventoryLockeds = listEntityLockeByProductIds(productIds);

        return Converters.get(InventoryLockedConverter.class).toVO(inventoryLockeds);
    }

    @Override
    public List<InventoryInventoryLockedVO> listInventoryLockeByProductIds(List<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        //库存锁定
        List<InventoryLocked> inventoryLockeds = listEntityLockeByProductIds(productIds);

        return Converters.get(InventoryInventoryLockedConverter.class).toVO(inventoryLockeds);
    }

    @Override
    public List<InventoryLocked> listEntityLockeByProductIds(List<Long> productIds) {
        //库存锁定
        List<InventoryLocked> inventoryLockedList = lambdaQuery()
                .in(InventoryLocked::getProductId, productIds)
                .in(InventoryLocked::getLockedStatus, InventoryLockedStatusEnum.LOCKED.getStatus(), InventoryLockedStatusEnum.PART_RELEASE.getStatus())
                .list();

        return inventoryLockedList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releaseLockedInventory(List<InventoryReleaseLockedParam> lockedInventoryList) {
        Validate.notEmpty(lockedInventoryList, "InventoryLocked: {} not found", lockedInventoryList);

        /* 优化建议: 当锁定库存列表很大时，多次遍历和流式操作可能会影响性能。
         * 原问题：
         * 1. 使用stream多次遍历列表，包括获取ID列表、过滤不存在的ID和处理每个锁定库存
         * 2. 没有批量处理锁定库存的释放操作
         *
         * 优化建议：
         * 1. 将多次stream操作合并，减少中间集合的创建
         * 2. 使用集合操作代替重复遍历，提高效率
         * 3. 考虑使用批量更新操作，减少数据库交互
         * 4. 对于大量数据，可以考虑分批处理，避免单个事务过大
         *
         * 示例优化代码：
         * // 去重并直接构建 ID 到参数的映射
         * Map<Long, InventoryReleaseLockedParam> paramMap = lockedInventoryList.stream()
         *         .collect(Collectors.toMap(InventoryReleaseLockedParam::getId, Function.identity(), (a, b) -> a));
         * List<Long> idList = new ArrayList<>(paramMap.keySet());
         *
         * // 一次查询获取所有锁定库存
         * List<InventoryLocked> lockedList = this.listByLocked(idList);
         *
         * // 检查缺失的ID
         * Set<Long> existingIds = lockedList.stream().map(IdModel::getId).collect(Collectors.toSet());
         * List<Long> missingIds = idList.stream().filter(id -> !existingIds.contains(id)).collect(Collectors.toList());
         */

        // 获取锁实例
        List<Long> idList = StreamUtils.distinctMap(lockedInventoryList, InventoryReleaseLockedParam::getId);

        List<InventoryLocked> lockedList = listByLocked(idList);

        Map<Long, InventoryLocked> lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // 获取不到Locked返回异常信息
        List<Long> notExistLockIdList = idList.stream()
                .filter(obj -> !lockedMap.containsKey(obj))
                .toList();
        if (ObjectUtil.isNotEmpty(notExistLockIdList)) {
            // throw new BusinessException("id: " + notExistLockIdList + " not found in InventoryLocked");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InventoryLocked", notExistLockIdList));
        }

        lockedInventoryList.stream()
                .filter(obj -> lockedMap.containsKey(obj.getId()))
                .forEach(param -> {
                    InventoryLocked inventoryLocked = lockedMap.get(param.getId());
                    // 校验释放数量
                    validateReleaseQuantity(param, inventoryLocked);
                    // 完成释放数量
                    inventoryLocked.setFinishQty(param.getQty() + inventoryLocked.getFinishQty());
                    // 释放状态判断
                    String lockedStatus = Objects.equals(inventoryLocked.getFinishQty(), inventoryLocked.getQty())
                            ? InventoryLockedStatusEnum.RELEASE.getStatus()
                            : InventoryLockedStatusEnum.PART_RELEASE.getStatus();
                    inventoryLocked.setLockedStatus(lockedStatus);

                });
        // 更新状态
        int successCount = updateBatch(lockedList);
        if (successCount != lockedList.size()) {
            throw new BusinessException("Release locked inventory failed");
        }
    }

    /**
     * 获取锁定的锁集合
     *
     * @param idList idList
     * @return /
     */
    private List<InventoryLocked> listByLocked(List<Long> idList) {
        return lambdaQuery()
                .in(IdModel::getId, idList)
                .ne(InventoryLocked::getLockedStatus, InventoryLockedStatusEnum.RELEASE.getStatus())
                .list();
    }

    /**
     * 构建锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值VO对象
     *
     * @param entity 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值对象
     * @return 返回包含详细信息的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值VO对象
     */
    private InventoryLockedVO buildInventoryLockedVO(InventoryLocked entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值VO对象
        return Converters.get(InventoryLockedConverter.class).toVO(entity);
    }

    @Override
    public <D extends BaseWorkorderDetailModel, W extends BaseWorkorderModel> InventoryLocked buildInventoryLocked(D detail, W workOrder) {
        InventoryLocked inventoryLocked = new InventoryLocked();

        inventoryLocked.setFinishQty(0);
        inventoryLocked.setRefTableId(detail.getId());
        inventoryLocked.setRefTableName(detail.getClass().getSimpleName());
        inventoryLocked.setRefTableRefNum(detail.getLineNum().toString());
        inventoryLocked.setRefTableShowName(workOrder.getClass().getSimpleName());
        inventoryLocked.setRefTableShowRefNum(workOrder.getRefNum());
        inventoryLocked.setQty(detail.getQty());
        inventoryLocked.setProductId(detail.getProductId());
        inventoryLocked.setNote(null);
        inventoryLocked.setId(IdWorker.getId());

        inventoryLocked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());

        return inventoryLocked;
    }

}
