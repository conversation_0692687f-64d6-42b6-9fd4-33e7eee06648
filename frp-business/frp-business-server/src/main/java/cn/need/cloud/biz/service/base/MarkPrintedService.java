package cn.need.cloud.biz.service.base;

import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.model.SuperModel;

/**
 * <p>
 * 打印 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface MarkPrintedService<T extends SuperModel, S extends SuperService<T>, P extends PrintQuery> {

    /**
     * 打印流程模版
     *
     * @param query 打印参数
     */
    @SuppressWarnings("unchecked")
    default void markPrinted(P query) {
        PrintStatusEnum.checkStatus(query.getPrintStatus());

        S service = (S) this;
        T model = service.getById(query.getId());
        Validate.notNull(model, "PrintModel: {} is not exist", query.getId());

        this.printSuccess(query, model);

        // markPrinted
        this.afterPrinted(query, model);
    }

    /**
     * 打印成功
     *
     * @param query 打印条件
     * @param model 需更新状态的打印模型对象
     */
    @SuppressWarnings("unchecked")
    default void printSuccess(P query, T model) {
        S service = (S) this;
        // 仅标记成功一次
        boolean isFirstPrintedSuccess = service.update()
                .eq("id", query.getId())
                .ne("print_status", PrintStatusEnum.SUCCESS.getStatus())
                .set("print_status", query.getPrintStatus())
                .update();

        // 第一次打印成功
        if (isFirstPrintedSuccess) {
            this.firstPrintedSuccess(query, model);
        }

    }

    /**
     * 打印后置处理
     *
     * @param query 打印条件
     * @param model 更新的对应Model
     */
    default void afterPrinted(P query, T model) {

    }

    /**
     * 首次打印成功
     *
     * @param query 打印条件
     * @param model 打印模型
     */
    default void firstPrintedSuccess(P query, T model) {

    }


}
