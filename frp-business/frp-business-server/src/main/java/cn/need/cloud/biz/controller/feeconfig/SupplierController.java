package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.SupplierConverter;
import cn.need.cloud.biz.model.entity.feeconfig.Supplier;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.SupplierUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuery;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierPageVO;
import cn.need.cloud.biz.service.feeconfig.SupplierService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 供应商信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/supplier")
@Tag(name = "供应商信息")
public class SupplierController extends AbstractRestController<SupplierService, Supplier, SupplierConverter, SupplierVO> {

    @Operation(summary = "新增供应商信息", description = "接收供应商信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SupplierCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改供应商信息", description = "接收供应商信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SupplierUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除供应商信息", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取供应商信息详情", description = "根据数据主键id，从数据库中获取其对应的供应商信息详情")
    @GetMapping(value = "/detail/{id}")
    public Result<SupplierVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取供应商信息分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的供应商信息列表")
    @PostMapping(value = "/list")
    public Result<PageData<SupplierPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<SupplierQuery> search) {

        // 获取供应商信息分页
        PageData<SupplierPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
