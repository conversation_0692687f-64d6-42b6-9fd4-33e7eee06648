package cn.need.cloud.biz.model.param.product.update;

import cn.need.cloud.biz.jackson.UpperCase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 产品多箱 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品多箱 新建参数")
public class ProductMultiboxCreateOrUpdateParam implements Serializable {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    // /**
    //  * 供应商id
    //  */
    // @Schema(description = "供应商id")
    // private Long transactionPartnerId;

    /**
     * 发货长度
     */
    @Schema(description = "发货长度")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @Schema(description = "发货宽度")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @Schema(description = "发货高度")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @Schema(description = "发货重量")
    private BigDecimal shipWeight;

    /**
     * 发货重量单位
     */
    @Schema(description = "发货重量单位")
    private String shipWeightUnit;

    /**
     * 发货尺寸单位
     */
    @Schema(description = "发货尺寸单位")
    private String shipDimensionUnit;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    @NotBlank(message = "upc cannot be empty")
    @UpperCase
    private String upc;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    @NotNull(message = "lineNum must not be null")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

    /**
     * 多箱详情
     */
    @Schema(description = "多箱详情")
    @Valid
    private List<MultiboxDetailParam> detailList;


}