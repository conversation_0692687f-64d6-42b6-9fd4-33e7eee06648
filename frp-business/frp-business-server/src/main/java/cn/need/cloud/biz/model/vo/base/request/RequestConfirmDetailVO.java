package cn.need.cloud.biz.model.vo.base.request;

import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 工单确认页Detail对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "请求单确认页Detail对象")
public class RequestConfirmDetailVO implements Serializable, BaseProductAware {

    @Schema(description = "请求单")
    private RequestConfirmVO request;

    @Schema(description = "数量")
    private Integer qty;

    @Schema(description = "完成数量")
    private Integer finishQty;

    @Schema(description = "产品id")
    private Long productId;
}
