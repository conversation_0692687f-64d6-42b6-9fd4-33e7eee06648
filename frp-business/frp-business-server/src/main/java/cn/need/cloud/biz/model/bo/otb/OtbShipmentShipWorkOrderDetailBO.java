package cn.need.cloud.biz.model.bo.otb;

import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024年10月28日 15:09:00
 */
@Data
@Schema(description = "OTB装运信息 vo对象")
@AllArgsConstructor
@NoArgsConstructor
public class OtbShipmentShipWorkOrderDetailBO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    public OtbWorkorderDetailVO otbWorkorderDetailVO;

    public Integer shippedQty;
}
