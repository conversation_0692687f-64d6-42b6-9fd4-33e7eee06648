package cn.need.cloud.biz.model.query.fee;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;


/**
 * 费用详情otc Query对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "费用详情otc Query对象")
public class FeeOtcDetailQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = -1602020325311092842L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region deletedNote

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 集合")
    @Condition(value = Keyword.IN, fields = {"deletedNote"})
    private List<String> deletedNoteList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"deletedNote"})
    private List<String> deletedNoteNiList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因值类型集合")
    private List<String> deletedNoteValueTypeList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于")
    @Condition(value = Keyword.GT, fields = {"deletedNote"})
    private String deletedNoteGt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于等于")
    @Condition(value = Keyword.GE, fields = {"deletedNote"})
    private String deletedNoteGe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于")
    @Condition(value = Keyword.LT, fields = {"deletedNote"})
    private String deletedNoteLt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于等于")
    @Condition(value = Keyword.LE, fields = {"deletedNote"})
    private String deletedNoteLe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"deletedNote"})
    private String deletedNoteLike;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"deletedNote"})
    private String deletedNoteLikeLeft;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"deletedNote"})
    private String deletedNoteLikeRight;

    // endregion deletedNote

    // region fee

    /**
     * 费用
     */
    @Schema(description = "费用")
    private BigDecimal fee;

    /**
     * 费用
     */
    @Schema(description = "费用 集合")
    @Condition(value = Keyword.IN, fields = {"fee"})
    private List<BigDecimal> feeList;

    /**
     * 费用
     */
    @Schema(description = "费用 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"fee"})
    private List<BigDecimal> feeNiList;

    /**
     * 费用
     */
    @Schema(description = "费用值类型集合")
    private List<String> feeValueTypeList;

    /**
     * 费用
     */
    @Schema(description = "费用 大于")
    @Condition(value = Keyword.GT, fields = {"fee"})
    private BigDecimal feeGt;

    /**
     * 费用
     */
    @Schema(description = "费用 大于等于")
    @Condition(value = Keyword.GE, fields = {"fee"})
    private BigDecimal feeGe;

    /**
     * 费用
     */
    @Schema(description = "费用 小于")
    @Condition(value = Keyword.LT, fields = {"fee"})
    private BigDecimal feeLt;

    /**
     * 费用
     */
    @Schema(description = "费用 小于等于")
    @Condition(value = Keyword.LE, fields = {"fee"})
    private BigDecimal feeLe;

    /**
     * 费用
     */
    @Schema(description = "费用 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"fee"})
    private BigDecimal feeLike;

    /**
     * 费用
     */
    @Schema(description = "费用 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"fee"})
    private BigDecimal feeLikeLeft;

    /**
     * 费用
     */
    @Schema(description = "费用 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"fee"})
    private BigDecimal feeLikeRight;

    // endregion fee

    // region feeConfigDetailId

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id")
    private Long feeConfigDetailId;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 集合")
    @Condition(value = Keyword.IN, fields = {"feeConfigDetailId"})
    private List<Long> feeConfigDetailIdList;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"feeConfigDetailId"})
    private List<Long> feeConfigDetailIdNiList;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id值类型集合")
    private List<String> feeConfigDetailIdValueTypeList;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 大于")
    @Condition(value = Keyword.GT, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdGt;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 大于等于")
    @Condition(value = Keyword.GE, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdGe;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 小于")
    @Condition(value = Keyword.LT, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdLt;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 小于等于")
    @Condition(value = Keyword.LE, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdLe;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdLike;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdLikeLeft;

    /**
     * 费用配置明细id
     */
    @Schema(description = "费用配置明细id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"feeConfigDetailId"})
    private Long feeConfigDetailIdLikeRight;

    // endregion feeConfigDetailId

    // region feeConfigId

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id")
    private Long feeConfigId;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 集合")
    @Condition(value = Keyword.IN, fields = {"feeConfigId"})
    private List<Long> feeConfigIdList;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"feeConfigId"})
    private List<Long> feeConfigIdNiList;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id值类型集合")
    private List<String> feeConfigIdValueTypeList;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 大于")
    @Condition(value = Keyword.GT, fields = {"feeConfigId"})
    private Long feeConfigIdGt;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 大于等于")
    @Condition(value = Keyword.GE, fields = {"feeConfigId"})
    private Long feeConfigIdGe;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 小于")
    @Condition(value = Keyword.LT, fields = {"feeConfigId"})
    private Long feeConfigIdLt;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 小于等于")
    @Condition(value = Keyword.LE, fields = {"feeConfigId"})
    private Long feeConfigIdLe;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"feeConfigId"})
    private Long feeConfigIdLike;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"feeConfigId"})
    private Long feeConfigIdLikeLeft;

    /**
     * 费用配置id
     */
    @Schema(description = "费用配置id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"feeConfigId"})
    private Long feeConfigIdLikeRight;

    // endregion feeConfigId

    // region headerId

    /**
     * header表id
     */
    @Schema(description = "header表id")
    private Long headerId;

    /**
     * header表id
     */
    @Schema(description = "header表id 集合")
    @Condition(value = Keyword.IN, fields = {"headerId"})
    private List<Long> headerIdList;

    /**
     * header表id
     */
    @Schema(description = "header表id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"headerId"})
    private List<Long> headerIdNiList;

    /**
     * header表id
     */
    @Schema(description = "header表id值类型集合")
    private List<String> headerIdValueTypeList;

    /**
     * header表id
     */
    @Schema(description = "header表id 大于")
    @Condition(value = Keyword.GT, fields = {"headerId"})
    private Long headerIdGt;

    /**
     * header表id
     */
    @Schema(description = "header表id 大于等于")
    @Condition(value = Keyword.GE, fields = {"headerId"})
    private Long headerIdGe;

    /**
     * header表id
     */
    @Schema(description = "header表id 小于")
    @Condition(value = Keyword.LT, fields = {"headerId"})
    private Long headerIdLt;

    /**
     * header表id
     */
    @Schema(description = "header表id 小于等于")
    @Condition(value = Keyword.LE, fields = {"headerId"})
    private Long headerIdLe;

    /**
     * header表id
     */
    @Schema(description = "header表id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"headerId"})
    private Long headerIdLike;

    /**
     * header表id
     */
    @Schema(description = "header表id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"headerId"})
    private Long headerIdLikeLeft;

    /**
     * header表id
     */
    @Schema(description = "header表id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"headerId"})
    private Long headerIdLikeRight;

    // endregion headerId

    // region lineNum

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 行序号
     */
    @Schema(description = "行序号 集合")
    @Condition(value = Keyword.IN, fields = {"lineNum"})
    private List<Integer> lineNumList;

    /**
     * 行序号
     */
    @Schema(description = "行序号 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"lineNum"})
    private List<Integer> lineNumNiList;

    /**
     * 行序号
     */
    @Schema(description = "行序号值类型集合")
    private List<String> lineNumValueTypeList;

    /**
     * 行序号
     */
    @Schema(description = "行序号 大于")
    @Condition(value = Keyword.GT, fields = {"lineNum"})
    private Integer lineNumGt;

    /**
     * 行序号
     */
    @Schema(description = "行序号 大于等于")
    @Condition(value = Keyword.GE, fields = {"lineNum"})
    private Integer lineNumGe;

    /**
     * 行序号
     */
    @Schema(description = "行序号 小于")
    @Condition(value = Keyword.LT, fields = {"lineNum"})
    private Integer lineNumLt;

    /**
     * 行序号
     */
    @Schema(description = "行序号 小于等于")
    @Condition(value = Keyword.LE, fields = {"lineNum"})
    private Integer lineNumLe;

    /**
     * 行序号
     */
    @Schema(description = "行序号 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"lineNum"})
    private Integer lineNumLike;

    /**
     * 行序号
     */
    @Schema(description = "行序号 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"lineNum"})
    private Integer lineNumLikeLeft;

    /**
     * 行序号
     */
    @Schema(description = "行序号 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"lineNum"})
    private Integer lineNumLikeRight;

    // endregion lineNum

    // region note

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 备注
     */
    @Schema(description = "备注 集合")
    @Condition(value = Keyword.IN, fields = {"note"})
    private List<String> noteList;

    /**
     * 备注
     */
    @Schema(description = "备注 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"note"})
    private List<String> noteNiList;

    /**
     * 备注
     */
    @Schema(description = "备注值类型集合")
    private List<String> noteValueTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注 大于")
    @Condition(value = Keyword.GT, fields = {"note"})
    private String noteGt;

    /**
     * 备注
     */
    @Schema(description = "备注 大于等于")
    @Condition(value = Keyword.GE, fields = {"note"})
    private String noteGe;

    /**
     * 备注
     */
    @Schema(description = "备注 小于")
    @Condition(value = Keyword.LT, fields = {"note"})
    private String noteLt;

    /**
     * 备注
     */
    @Schema(description = "备注 小于等于")
    @Condition(value = Keyword.LE, fields = {"note"})
    private String noteLe;

    /**
     * 备注
     */
    @Schema(description = "备注 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"note"})
    private String noteLike;

    /**
     * 备注
     */
    @Schema(description = "备注 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"note"})
    private String noteLikeLeft;

    /**
     * 备注
     */
    @Schema(description = "备注 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"note"})
    private String noteLikeRight;

    // endregion note

    // region refNum

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"refNum"})
    private List<String> refNumNiList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码值类型集合")
    private List<String> refNumValueTypeList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"refNum"})
    private String refNumGt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"refNum"})
    private String refNumGe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"refNum"})
    private String refNumLt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"refNum"})
    private String refNumLe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"refNum"})
    private String refNumLike;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"refNum"})
    private String refNumLikeLeft;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"refNum"})
    private String refNumLikeRight;

    // endregion refNum

    // region warehouseId

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 集合")
    @Condition(value = Keyword.IN, fields = {"warehouseId"})
    private List<Long> warehouseIdList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"warehouseId"})
    private List<Long> warehouseIdNiList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id值类型集合")
    private List<String> warehouseIdValueTypeList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于")
    @Condition(value = Keyword.GT, fields = {"warehouseId"})
    private Long warehouseIdGt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于等于")
    @Condition(value = Keyword.GE, fields = {"warehouseId"})
    private Long warehouseIdGe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于")
    @Condition(value = Keyword.LT, fields = {"warehouseId"})
    private Long warehouseIdLt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于等于")
    @Condition(value = Keyword.LE, fields = {"warehouseId"})
    private Long warehouseIdLe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"warehouseId"})
    private Long warehouseIdLike;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"warehouseId"})
    private Long warehouseIdLikeLeft;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"warehouseId"})
    private Long warehouseIdLikeRight;

    // endregion warehouseId


}