package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderUnpickDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Unpick PickingSlip 对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@Schema(description = "Unpick PickingSlip 对象")
public class OtcPickingSlipUnpickVO implements Serializable {


    @Schema(description = "拣货单详情")
    private BasePickingSlipUnpickDetailVO pickingSlipDetail;

    @Schema(description = "工单详情")
    private OtcWorkorderUnpickDetailVO workorderDetail;
}
