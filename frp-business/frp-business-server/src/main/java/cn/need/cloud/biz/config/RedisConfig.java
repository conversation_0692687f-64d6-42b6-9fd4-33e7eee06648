package cn.need.cloud.biz.config;

import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.cache.UserRepertory;
import cn.need.framework.common.dict.impl.RedisUserRepertory;
import cn.need.framework.common.support.redis.RedisExtendProperties;
import cn.need.framework.common.support.redis.RedisMode;
import cn.need.framework.common.support.redis.RedissonKit;
import io.lettuce.core.RedisConnectionException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * <AUTHOR>
 * @since 2024/6/13
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(RedisExtendProperties.class)
public class RedisConfig {


    public static void main(String[] args) {
        // 1. 构造 Redisson 配置
        Config config = new Config();
        config.useSingleServer()
                .setDatabase(0)
                .setAddress("redis://*************:30202")
                .setUsername("code")// 本地 Redis 地址
                .setPassword("code20250520!.")            // 如果有密码，去掉注释并设置
                .setConnectTimeout(10_000)                // 连接超时时间，默认 10 秒
                .setTimeout(10_000)                       // 命令执行超时时间
                .setConnectionPoolSize(16)                // 连接池大小
                .setConnectionMinimumIdleSize(2);         // 最小空闲连接数

        RedissonClient redisson = null;
        try {
            // 2. 创建 Redisson 客户端
            redisson = Redisson.create(config);
            System.out.println("✅ Connected to Redis successfully!");

            // 3. 简单的读写测试
            RBucket<String> bucket = redisson.getBucket("testKey");
            bucket.set("Hello, Redisson!");           // 写入
            String value = bucket.get();              // 读取
            System.out.println("Value from Redis: " + value);

        } catch (RedisConnectionException ex) {
            System.err.println("❌ Failed to connect to Redis: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            // 4. 关闭客户端释放资源
            if (redisson != null) {
                redisson.shutdown();
                System.out.println("Redisson client shutdown.");
            }
        }
    }

    @Bean
    public RedissonClient redissonClient(RedisProperties redisProperties,RedisExtendProperties redisExtendProperties) {
        log.info("===========>>> redisProperties={}", JsonUtil.toJson(redisProperties));

        RedisProperties.Sentinel sentinel = redisProperties.getSentinel();
        RedisProperties.Cluster cluster = redisProperties.getCluster();

        // 按以下优先级：1.cluster；2.sentinel；3.single
        String redisMode = StringPool.EMPTY;
        if (isNotNull(cluster)) {
            redisMode = RedisMode.CLUSTER;
        } else if (isNotNull(sentinel)) {
            redisMode = RedisMode.SENTINEL;
        } else {
            redisMode = RedisMode.SINGLE;
        }

        return Redisson.create(RedissonKit.createConfig(redisProperties,redisExtendProperties,redisMode));
    }

    /**
     * 注入系统用户缓存操作工具，可以直接使用DictUtil从redis缓存中获取数据字典信息
     */
    @SuppressWarnings("rawtypes")
    @Bean
    public UserRepertory userRepertory(RedisTemplate redisTemplate) {
        return new RedisUserRepertory(redisTemplate);
    }
}
