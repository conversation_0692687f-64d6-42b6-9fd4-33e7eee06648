package cn.need.cloud.biz.model.query.binlocation;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 锁定 库位详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "锁定 库位详情 query对象")
public class BinLocationDetailLockedQuery extends SuperQuery {

    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * 锁定状态
     */
    @Schema(description = "锁定状态")
    private String lockedStatus;

    /**
     * 锁定状态
     */
    @Schema(description = "锁定状态集合")
    @Condition(value = Keyword.IN, fields = {"lockedStatus"})
    private List<String> lockedStatusList;

    /**
     * 关联表Id
     */
    @Schema(description = "关联表Id")
    private Long refTableId;

    /**
     * refName
     */
    @Schema(description = "refName")
    private String refTableName;

    /**
     * 唯一标识码
     */
    @Schema(description = "关联表唯一标识码")
    private String refTableRefNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "关联表唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refTableRefNum"})
    private List<String> refTableRefNumList;

    /**
     * showRefName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * showRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;

    /**
     * showRefNum
     */
    @Schema(description = "showRefNum集合")
    @Condition(value = Keyword.IN, fields = {"showRefNum"})
    private List<String> refTableShowRefNumList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


}