package cn.need.cloud.biz.service.helper.workorder;

import cn.need.cloud.biz.model.query.base.SplitWorkorderDetailParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderParam;
import cn.need.framework.common.core.lang.ObjectUtil;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * WorkorderHelper
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public class WorkorderHelper {


    /**
     * 聚合拆单参数
     *
     * @param splitQueryList 拆单参数
     * @return /
     */
    public static List<SplitWorkorderParam> mergeSplitQuery(List<SplitWorkorderParam> splitQueryList) {
        return Optional.ofNullable(splitQueryList)
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> ObjectUtil.isNotEmpty(obj.getDetailList()))
                .peek(obj -> {
                    var groupDetails = obj.getDetailList().stream()
                            .collect(Collectors.groupingBy(SplitWorkorderDetailParam::getId));

                    var mergeDetails = groupDetails.values()
                            .stream()
                            .map(details -> details.stream()
                                    .reduce((first, second) -> {
                                        second.setSplitQty(second.getSplitQty() + first.getSplitQty());
                                        return second;
                                    })
                                    .orElse(null))
                            .filter(Objects::nonNull)
                            .toList();
                    obj.setDetailList(mergeDetails);
                })
                .toList();

    }
}
