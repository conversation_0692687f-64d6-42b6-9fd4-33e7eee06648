package cn.need.cloud.biz.model.vo.inbound.request;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundUnloadVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 卸货信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卸货信息 vo对象")
public class InboundRegularUnloadVO extends BaseSuperVO {

    /**
     * 卸货信息
     */
    @Schema(description = "卸货信息")
    private List<InboundUnloadVO> inBoundUnloads;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 上架单状态
     */
    @Schema(description = "上架单状态")
    private String putAwaySlipStatus;

    /**
     * 参考编码
     */
    @Schema(description = "参考编码")
    private String refNum;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseVO baseWarehouseVO;

}
