package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPrepPutawaySlipDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlip;
import cn.need.cloud.biz.model.vo.otb.putawayslip.OtbPrepPutawaySlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC上架单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public class OtbPrepPutawaySlipConverter extends AbstractModelConverter<OtbPrepPutawaySlip, OtbPrepPutawaySlipVO, OtbPrepPutawaySlipDTO> {

}
