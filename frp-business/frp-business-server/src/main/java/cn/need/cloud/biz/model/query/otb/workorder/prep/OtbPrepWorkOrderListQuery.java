package cn.need.cloud.biz.model.query.otb.workorder.prep;

import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC预提工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB预提工单列表 query对象")
public class OtbPrepWorkOrderListQuery implements Serializable {

    /**
     * 工单查询条件
     */
    @Schema(description = "工单查询条件")
    private OtbPrepWorkorderQuery otbPrepWorkorderQuery;

    /**
     * 库位查询条件
     */
    @Schema(description = "库位查询条件")
    private BaseBinLocationQuery binLocationQuery;
}