package cn.need.cloud.biz.service.inbound;

import cn.need.framework.common.support.api.NoteParam;

/**
 * <p>
 * special service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface InboundRequestSpecialService {

    /**
     * 取消入库申请单
     *
     * @param cancelParam 取消id及备注
     * @return 影响行数
     */
    Integer cancelRequest(NoteParam cancelParam);

    /**
     * 删除请求单
     *
     * @param id          入库请求单id
     * @param deletedNote 删除备注
     * @return 影响行数
     */
    Integer removeRequest(Long id, String deletedNote);
}
