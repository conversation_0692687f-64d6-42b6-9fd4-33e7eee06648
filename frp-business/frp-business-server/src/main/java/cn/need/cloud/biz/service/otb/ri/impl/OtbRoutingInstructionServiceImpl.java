package cn.need.cloud.biz.service.otb.ri.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.otb.*;
import cn.need.cloud.biz.converter.otb.OtbRoutingInstructionConverter;
import cn.need.cloud.biz.mapper.otb.OtbRoutingInstructionMapper;
import cn.need.cloud.biz.model.bo.otb.OtbRILogBO;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionCreateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionUpdateParam;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.*;
import cn.need.cloud.biz.service.otb.impl.logutil.OtbWorkorderLogUtil;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletLabelService;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageLabelService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionPackageLabelService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionPalletLabelService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.DropListUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ModifyCompareUtil;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.need.cloud.biz.client.constant.enums.otb.OtbConfirmedShipmentTypeEnum.NO_CHANGE;

/**
 * <p>
 * otb发货指南 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbRoutingInstructionServiceImpl extends SuperServiceImpl<OtbRoutingInstructionMapper, OtbRoutingInstruction> implements OtbRoutingInstructionService {

    @Resource
    private OtbPackageLabelService otbPackageLabelService;

    @Resource
    private OtbShipmentService otbShipmentService;

    @Resource
    private OtbRoutingInstructionPackageLabelService otbRoutingInstructionPackageLabelService;

    @Resource
    private OtbRoutingInstructionPalletLabelService otbRoutingInstructionPalletLabelService;
    @Resource
    @Lazy
    private OtbWorkorderService otbWorkorderService;
    @Resource
    private OtbRequestService otbRequestService;
    @Resource
    private OtbPalletLabelService otbPalletLabelService;
    @Resource
    private OtbPackageService otbPackageService;
    @Resource
    private OtbPalletService otbPalletService;

    @NotNull
    private static List<OtbPackageLabel> buildPackageLabel(List<OtbRoutingInstructionPackageLabel> packageLabelList, Map<String, Long> otbPackageIdMap, Map<Long, OtbPackage> otbPackageMap) {
        //处理packageLabel
        return packageLabelList.stream().map(item -> {
            //校验packageSsccNum合法性
            Long otbPackageId = otbPackageIdMap.get(item.getPackageSsccNum());
            Validate.notNull(otbPackageId, StringUtil.format("packageSsccNum:{} is not existed", item.getPackageSsccNum()));
            //获取对应包裹
            OtbPackage otbPackage = otbPackageMap.get(otbPackageId);
            //校验label类型是否位shipmentLabel
            if (StringUtil.equals(item.getPaperType(), PaperTypeEnum.LABEL_4X6.getStatus()) &&
                    StringUtil.equals(item.getLabelType(), PackageLabelTypeEnum.SHIPPING_LABEL.getType())) {
                //填充快递单号
                otbPackage.setTrackingNum(item.getLabelRefNum());
                otbPackage.setOtbPackageStatus(OtbPackageStatusEnum.BUILD_SHIPMENT_LABEL.getCode());
                OtbPackageAuditLogHelper.recordLog(otbPackage);
            }
            //生成otbPackageLabel
            OtbPackageLabel otbPackageLabel = BeanUtil.copyNew(item, OtbPackageLabel.class);
            otbPackageLabel.setOtbPackageId(otbPackage.getId());
            otbPackageLabel.setPrintStatus(PrintStatusEnum.NONE.getStatus());
            return otbPackageLabel;
        }).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbRoutingInstruction insertByParam(OtbRoutingInstructionCreateParam param) {
        // 检查传入otb发货指南参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(param)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        String otbShipmentRefNum = param.getOtbShipmentRefNum();
        OtbShipment otbShipment = otbShipmentService.getByRefNum(otbShipmentRefNum);
        if (ObjectUtil.isEmpty(otbShipment)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbShipment", "refNum", otbShipmentRefNum));
        }
        //校验otbShipmentRefNum是否已经有发货单
        if (existRoutingInstructionByShipmentRefNum(otbShipmentRefNum)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "OtbRoutingInstruction", "shipmentRefNum", otbShipmentRefNum));
        }

        // 校验ShipmentType 大小件 运输单信息匹配
        boolean largeFlag = checkShipmentTypeIsLarge(otbShipmentRefNum, param.getConfirmedShipmentType());
        // 校验运输类型和标签类型是否匹配
        checkShipmentValidTypeAndLabel(largeFlag, param);

        // 校验SchedulePickupDate
        Validate.notNull(param.getSchedulePickupDate(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "schedulePickupDate"));

        // 获取otb发货指南转换器实例，用于将otb发货指南参数对象转换为实体对象
        OtbRoutingInstructionConverter converter = Converters.get(OtbRoutingInstructionConverter.class);

        // 将otb发货指南参数对象转换为实体对象并初始化
        OtbRoutingInstruction entity = initOtbRoutingInstruction(converter.toEntity(param));

        // 设置otb发货指南实体对象的otbShipmentId字段
        Long otbShipmentId = otbShipment.getId();
        if (ObjectUtil.isEmpty(otbShipmentId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "otbShipmentId"));
        }
        //获取新的OTB路由id
        long otbRoutingInstructionId = IdWorker.getId();

        if (ObjectUtil.isNotEmpty(param.getRoutingInstructionPackageLabelList())) {
            otbRoutingInstructionPackageLabelService.insertBatchByParam(param, otbRoutingInstructionId);
        }
        if (ObjectUtil.isNotEmpty(param.getRoutingInstructionPalletLabelList())) {
            otbRoutingInstructionPalletLabelService.insertBatchByParam(param, otbRoutingInstructionId);
        }
        entity.setId(otbRoutingInstructionId);
        entity.setOtbShipmentId(otbShipmentId);
        //填充状态
        entity.setOtbRoutingInstructionStatus(OtbRIStatusEnum.NEW.getStatus());
        // 插入otb发货指南实体对象到数据库
        super.insert(entity);
        // 记录新增日志
        OtbRIAuditLogHelper.recordLog(entity);
        // 返回otb发货指南ID
        return entity;
    }

    /**
     * 校验并确认运输件是否为大件
     *
     * @param otbShipmentRefNum     运输流水号
     * @param confirmedShipmentType 确认的运输类型
     * @return boolean 是否为大件 true为大件 false为小件
     */
    private boolean checkShipmentTypeIsLarge(String otbShipmentRefNum, String confirmedShipmentType) {
        //获取发货实体
        OtbShipment otbShipment = otbShipmentService.getByRefNum(otbShipmentRefNum);
        if (ObjectUtil.isEmpty(otbShipment)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbShipment", "refNum", otbShipmentRefNum));
        }
        //获取发货类型
        String otbShipmentType = otbShipment.getOtbShipmentType();
        // 禁止大件改大件 小件改小件
        boolean checkBigParcelValid = OtbShipmentTypeEnum.LTL.getType().equals(otbShipmentType) && confirmedShipmentType.equals(OtbConfirmedShipmentTypeEnum.CREATE_PALLET.getType());
        if (checkBigParcelValid) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Cannot create pallet for LTL shipment type"));
        }
        boolean checkSmallParcelValid = OtbShipmentTypeEnum.SMALL_PARCEL.getType().equals(otbShipmentType) && confirmedShipmentType.equals(OtbConfirmedShipmentTypeEnum.SPLIT_PALLET.getType());
        if (checkSmallParcelValid) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Cannot split pallet for SMALL_PARCEL shipment type"));
        }
        // 大件情况：大件不变，小件打包
        boolean bigParcelFlag = OtbShipmentTypeEnum.LTL.getType().equals(otbShipmentType) && NO_CHANGE.getType().equals(confirmedShipmentType)
                || (OtbShipmentTypeEnum.SMALL_PARCEL.getType().equals(otbShipmentType) && OtbConfirmedShipmentTypeEnum.CREATE_PALLET.getType().equals(confirmedShipmentType));
        if (bigParcelFlag) {
            return Boolean.TRUE;
        }
        // 小件情况：小件不变，大件拆包
        boolean smallParcelFlag = OtbShipmentTypeEnum.SMALL_PARCEL.getType().equals(otbShipmentType) && NO_CHANGE.getType().equals(confirmedShipmentType)
                || (OtbShipmentTypeEnum.LTL.getType().equals(otbShipmentType) && OtbConfirmedShipmentTypeEnum.SPLIT_PALLET.getType().equals(confirmedShipmentType));
        if (smallParcelFlag) {
            return Boolean.FALSE;
        }
        throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "confirmedShipmentType", "Must be either LTL or SMALL_PARCEL"));
    }

    /**
     * 检查发货类型和标签
     *
     * @param bigParcelFlag 标志是否为大包裹，用于区分包裹大小
     * @param param         包含订单路由指令的参数，用于校验和处理
     */
    private void checkShipmentValidTypeAndLabel(boolean bigParcelFlag, OtbRoutingInstructionCreateParam param) {
        if (bigParcelFlag) {
            checkShipmentBigParcel(param);
            return;
        }
        checkShipmentSmallParcel(param);
    }

    /**
     * 校验大件
     *
     * @param param 参数
     */
    private void checkShipmentBigParcel(OtbRoutingInstructionCreateParam param) {
        // 校验参数
        if (ObjectUtil.isNotEmpty(param.getRoutingInstructionPackageLabelList())) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "LTL shipment type cannot have package labels"));
        }
        if (ObjectUtil.isNotEmpty(param.getShipCarrier()) || ObjectUtil.isNotEmpty(param.getShipMethod()) || ObjectUtil.isNotEmpty(param.getShipApiProfileRefNum())) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "LTL shipment type cannot specify shipCarrier and shipMethod"));
        }
        if (ObjectUtil.isEmpty(param.getOtbBolFileFileData()) || ObjectUtil.isEmpty(param.getOtbBolFilePaperType())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "otbBolFileFileData and otbBolFilePaperType for LTL shipment"));
        }
    }

    /**
     * 校验小件
     *
     * @param param 参数
     */
    private void checkShipmentSmallParcel(OtbRoutingInstructionCreateParam param) {
        // 校验参数
        if (ObjectUtil.isNotEmpty(param.getOtbBolFileFileData())) {
            throw new BusinessException("SmallParcel cannot fill in BolFile");
        }
        if (ObjectUtil.isNotEmpty(param.getOtbPalletFileFileData())) {
            throw new BusinessException("SmallParcel cannot fill in PalletFile");
        }
        if (ObjectUtil.isNotEmpty(param.getRoutingInstructionPalletLabelList())) {
            throw new BusinessException("SmallParcel cannot fill in PalletLabel");
        }
        if (ObjectUtil.isEmpty(param.getShipCarrier()) || ObjectUtil.isEmpty(param.getShipMethod())) {
            throw new BusinessException("SmallParcel require shipCarrier and shipMethod");
        }
        // //小件校验shipCarrier和shipMethod
        // CheckShipUtil.checkShipCarrierAndMethod(param.getShipCarrier(), param.getShipMethod());
    }

    @Override
    public boolean existRoutingInstructionByShipmentRefNum(String shippingRefNum) {
        return lambdaQuery().eq(OtbRoutingInstruction::getOtbShipmentRefNum, shippingRefNum).count() > 0;
    }

    /**
     * 初始化otb发货指南对象
     * 此方法用于设置otb发货指南对象的必要参数，确保其处于有效状态
     *
     * @param entity otb发货指南对象，不应为空
     * @return 返回初始化后的otb发货指南
     * @throws BusinessException 如果传入的otb发货指南为空，则抛出此异常
     */
    private OtbRoutingInstruction initOtbRoutingInstruction(OtbRoutingInstruction entity) {
        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OtbRoutingInstruction cannot be empty");
        }
        // 生成RefNum
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTB_ROUTING_INSTRUCTION));
        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbRoutingInstruction updateByParam(OtbRoutingInstructionUpdateParam updateParam) {
        // 检查传入otb发货指南参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }
        OtbRoutingInstruction otbRoutingInstruction = super.getById(updateParam.getId());
        //校验参数
        if (ObjectUtil.isEmpty(otbRoutingInstruction)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbRoutingInstruction", updateParam.getId()));
        }
        if (!otbRoutingInstruction.getOtbRoutingInstructionStatus().equals(OtbRIStatusEnum.NEW.getStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "OtbRoutingInstruction", "NEW", otbRoutingInstruction.getOtbRoutingInstructionStatus()));
        }

        // 校验SchedulePickupDate
        Validate.notNull(updateParam.getSchedulePickupDate(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "schedulePickupDate"));

        // 校验ShipmentType 大小件 运输单信息匹配
        checkShipmentTypeIsLarge(updateParam.getOtbShipmentRefNum(), updateParam.getConfirmedShipmentType());

        // 校验ShipmentType 大小件 运输单信息匹配
        boolean largeFlag = checkShipmentTypeIsLarge(updateParam.getOtbShipmentRefNum(), updateParam.getConfirmedShipmentType());
        // 校验运输类型和标签类型是否匹配
        checkShipmentValidTypeAndLabel(largeFlag, updateParam);

        // 更新前旧数据
        OtbRoutingInstructionVO oldVO = detailById(updateParam.getId());

        // 批量更新包标签
        otbRoutingInstructionPackageLabelService.updateBatchByParam(updateParam);
        // 批量更新打托标签
        otbRoutingInstructionPalletLabelService.updateBatchByParam(updateParam);

        // 获取otb发货指南转换器实例，用于将otb发货指南参数对象转换为实体对象
        OtbRoutingInstructionConverter converter = Converters.get(OtbRoutingInstructionConverter.class);

        // 将otb发货指南参数对象转换为实体对象
        OtbRoutingInstruction entity = converter.toEntity(updateParam);

        // 执行更新otb发货指南操作
        super.update(entity);
        // 如果请求日期需要更新为空，则更新otb发货指南的请求取货日期
        if (ObjectUtil.isNotEmpty(oldVO.getRequestPickupDate()) && ObjectUtil.isEmpty(entity.getRequestPickupDate())) {
            mapper.updateRequestPickupDate(entity);
        }
        // 更新后新数据
        OtbRoutingInstruction newVO = super.getById(entity.getId());
        // 记录日志
        OtbRIAuditLogHelper.recordModifyLog(
                newVO,
                ShowLogEnum.MODIFIED.getStatus(),
                BaseTypeLogEnum.OPERATION.getType(),
                ModifyCompareUtil.recordModifyLog(newVO, oldVO)
        );

        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer committed(Long id) {
        //获取otb发货指南
        OtbRoutingInstruction otbRoutingInstruction = getById(id);
        //获取发货单
        OtbShipment otbShipment = otbShipmentService.getById(otbRoutingInstruction.getOtbShipmentId());

        Validate.isTrue(StringUtil.equals(OtbShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus(), otbShipment.getOtbShipmentStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "OtbShipment", "WAIT_CHANNEL_CONFIRM", otbShipment.getOtbShipmentStatus()));

        if (ObjectUtil.isEmpty(otbRoutingInstruction)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbRoutingInstruction", id));
        }
        //校验状态
        if (!otbRoutingInstruction.getOtbRoutingInstructionStatus().equals(OtbRIStatusEnum.NEW.getStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "OtbRoutingInstruction", "NEW", otbRoutingInstruction.getOtbRoutingInstructionStatus()));
        }
        //渠道确认
        this.channelConfirmed(otbRoutingInstruction, otbShipment);
        //更新RI状态
        otbRoutingInstruction.setOtbRoutingInstructionStatus(OtbRIStatusEnum.APPROVED.getStatus());
        //记录日志
        OtbRIAuditLogHelper.recordLog(otbRoutingInstruction);

        return super.update(otbRoutingInstruction);
    }

    @Override
    public List<OtbRoutingInstructionPageVO> listByQuery(OtbRoutingInstructionQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtbRoutingInstructionPageVO> pageByQuery(PageSearch<OtbRoutingInstructionQuery> search) {
        Page<OtbRoutingInstruction> page = Conditions.page(search, entityClass);
        List<OtbRoutingInstructionPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public List<DropProVO> countPreDay(OtbRoutingInstructionQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtbRoutingInstruction.class,
                columnInfos -> mapper.dropProList(columnInfos, query)
        );
    }

    @Override
    public OtbRoutingInstruction getByShipmentId(Long otbShipmentId) {
        return lambdaQuery()
                .eq(OtbRoutingInstruction::getOtbShipmentId, otbShipmentId)
                .one();
    }

    @Override
    public void channelConfirmed(OtbRoutingInstruction instruction, OtbShipment otbShipment) {
        // 渠道确认
        switch (OtbConfirmedShipmentTypeEnum.getByType(instruction.getConfirmedShipmentType())) {
            case CREATE_PALLET -> otbShipment.setOtbShipmentType(OtbShipmentTypeEnum.CREATE_PALLET.getType());
            case SPLIT_PALLET -> otbShipment.setOtbShipmentType(OtbShipmentTypeEnum.SPLIT_PALLET.getType());
            case NO_CHANGE -> this.generateLabel(instruction, otbShipment);
            default -> throw new BusinessException("Invalid confirmedShipmentType");
        }
        //托盘文件状态
        String palletFileStatus = ObjectUtil.isEmpty(instruction.getOtbPalletFileFileData()) ? OtbPalletFileStatusEnum.NONE.getStatus() : OtbPalletFileStatusEnum.NEW.getStatus();
        otbShipment.setPalletFileStatus(palletFileStatus);
        //更新otb发货单状态
        otbShipment.setOtbShipmentStatus(OtbShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
        //填充ri的id
        otbShipment.setRoutingInstructionId(instruction.getId());
        //填充bolNum
        otbShipment.setBolNum(instruction.getBolNum());
        //填充RequestPickupDate，SchedulePickupDate
        otbShipment.setRoutingInstructionSchedulePickupDate(instruction.getSchedulePickupDate());
        otbShipment.setRoutingInstructionRequestPickupDate(instruction.getRequestPickupDate());
        //持久化
        otbShipmentService.update(otbShipment);
        //更新包裹状态
        List<OtbPackage> otbPackageList = BeanUtil.copyNew(otbPackageService.listByShipmentId(otbShipment.getId()), OtbPackage.class);
        otbPackageService.updateStatusBatch(otbPackageList, OtbPackageStatusEnum.CHANNEL_CONFIRM);
        //记录包裹日志
        OtbRILogBO otbRILogBO = new OtbRILogBO(
                instruction.getRefNum(),
                instruction.getConfirmedShipmentType(),
                otbShipment.getRefNum(),
                otbShipment.getOtbShipmentType()
        );
        OtbPackageAuditLogHelper.recordLog(otbPackageList, JsonUtil.toJson(otbRILogBO));
        //更新打托单
        List<OtbPallet> otbPalletList = BeanUtil.copyNew(otbPalletService.listByShipmentId(otbShipment.getId()), OtbPallet.class);
        otbPalletService.updateStatusBatch(otbPalletList, OtbPalletStatusEnum.CHANNEL_CONFIRM);
        //记录打托单日志
        OtbPalletAuditLogHelper.recordLog(otbPalletList, JsonUtil.toJson(otbRILogBO));
        //记录发货单日志
        OtbShipmentAuditLogHelper.recordLog(
                otbShipment,
                BaseTypeLogEnum.STATUS.getType(),
                null,
                JsonUtil.toJson(otbRILogBO)
        );
        //获取工单详情
        OtbWorkorder otbWorkorder = otbWorkorderService.getById(otbShipment.getOtbWorkorderId());
        otbWorkorder.setOtbRequestShipmentStatus(OtbRequestShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
        AuditLogHolder.record(OtbWorkorderLogUtil.getAuditShowLog(otbWorkorder, OtbRequestShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus(), null, "RequestShipmentStatus"));
        //获取request单
        OtbRequest otbRequest = otbRequestService.getById(otbWorkorder.getOtbRequestId());
        otbRequest.setRequestShipmentStatus(OtbRequestShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus());
        otbRequestService.update(otbRequest);
        OtbRequestAuditLogHelper.recordWithStatus(otbRequest, OtbRequestShipmentStatusEnum.CHANNEL_CONFIRMED.getStatus(), "RequestShipmentStatus");
        List<String> otbShipmentStatusList = Lists.arrayList(OtbShipmentStatusEnum.WAIT_CHANNEL_CONFIRM.getStatus(), OtbShipmentStatusEnum.NEW.getStatus());
        Boolean isAllShipmentChannelConfirmed = otbShipmentService.isAnyNotStatus(otbRequest.getId(), otbShipment.getId(), otbShipmentStatusList);
        if (Boolean.FALSE.equals(isAllShipmentChannelConfirmed)) {
            //更新工单状态
            otbWorkorderService.updateChannelConfirmed(otbWorkorder, OtbWorkorderEnum.CHANNEL_CONFIRM);
        }
    }

    @Override
    public void generateLabel(OtbRoutingInstruction instruction, OtbShipment otbShipment) {
        //获取palletLabel
        List<OtbRoutingInstructionPalletLabel> palletLabelList = otbRoutingInstructionPalletLabelService.getListByRoutingInstructionId(instruction.getId());
        //若为大件
        if (StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.LTL.getType()) &&
                ObjectUtil.isNotEmpty(palletLabelList)) {

            //获取发货单下打托单label
            List<OtbPalletLabel> labelList = otbPalletLabelService.listByOtbShipmentId(otbShipment.getId());
            //根据打托单ssccNum映射打托单
            Map<String, Long> otbPalletMap = ObjectUtil.toMap(labelList, OtbPalletLabel::getLabelRefNum, OtbPalletLabel::getOtbPalletId);
            List<OtbPalletLabel> otbPalletLabelList = palletLabelList
                    .stream()
                    .map(item -> {
                        Validate.notNull(otbPalletMap.get(item.getPalletSsccNum()), String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPallet", "palletSsccNum", item.getPalletSsccNum()));
                        OtbPalletLabel otbPalletLabel = BeanUtil.copyNew(item, OtbPalletLabel.class);
                        otbPalletLabel.setOtbPalletId(otbPalletMap.get(item.getPalletSsccNum()));
                        otbPalletLabel.setPrintStatus(PrintStatusEnum.NONE.getStatus());
                        return otbPalletLabel;
                    }).toList();
            //持久化
            otbPalletLabelService.insertBatch(otbPalletLabelList);
        }

        //若为小件
        if (StringUtil.equals(otbShipment.getOtbShipmentType(), OtbShipmentTypeEnum.SMALL_PARCEL.getType())) {
            //获取packageLabel
            List<OtbRoutingInstructionPackageLabel> packageLabelList = otbRoutingInstructionPackageLabelService.getListByRoutingInstructionId(instruction.getId());
            //获取当前发货单下的packageLabel
            List<OtbPackage> otbPackageList = BeanUtil.copyNew(otbPackageService.listByShipmentId(otbShipment.getId()), OtbPackage.class);
            List<Long> otbPackageIdList = otbPackageList.stream().map(OtbPackage::getId).toList();
            //获取包裹下label
            List<OtbPackageLabel> labelList = otbPackageLabelService.listByPackageId(otbPackageIdList);
            //根据package的ssccNum映射OtbPackage
            Map<Long, OtbPackage> otbPackageMap = ObjectUtil.toMap(otbPackageList, OtbPackage::getId);
            //根据sscc映射otbPackageId
            Map<String, Long> otbPackageIdMap = ObjectUtil.toMap(labelList, OtbPackageLabel::getLabelRefNum, OtbPackageLabel::getOtbPackageId);
            //判空
            if (ObjectUtil.isNotEmpty(packageLabelList)) {
                //持久化
                otbPackageLabelService.insertBatch(buildPackageLabel(packageLabelList, otbPackageIdMap, otbPackageMap));
            }
            //填充发货方式
            otbPackageList.forEach(item -> {
                item.setShipCarrier(instruction.getShipCarrier());
                item.setShipMethod(instruction.getShipMethod());
                item.setShipApiProfileRefNum(instruction.getShipApiProfileRefNum());
            });
            //持久化
            otbPackageService.updateBatch(otbPackageList);
        }

    }

    @Override
    public List<OtbRoutingInstructionVO> listByShipmentIdList(Set<Long> otbShipmentIdList) {
        //判空
        if (ObjectUtil.isEmpty(otbShipmentIdList)) {
            return CollUtil.newArrayList();
        }
        //获取ri
        List<OtbRoutingInstruction> list = super.lambdaQuery()
                .in(OtbRoutingInstruction::getOtbShipmentId, otbShipmentIdList)
                .list();
        return BeanUtil.copyNew(list, OtbRoutingInstructionVO.class);
    }

    @Override
    public OtbRoutingInstructionVO detailById(Long id) {

        OtbRoutingInstructionVO vo = mapper.getDetailById(id);
        // OtbRoutingInstruction entity =  getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(vo)) {
            throw new BusinessException("id: " + id + " not found in OtbRoutingInstruction");
        }
        return vo;
    }

    @Override
    public OtbRoutingInstructionVO detailByRefNum(String refNum) {
        OtbRoutingInstruction entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtbRoutingInstruction");
        }
        return buildOtbRoutingInstructionVO(entity);
    }

    @Override
    public List<DropProVO> distinctValue(OtbRoutingInstructionQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtbRoutingInstruction.class,
                columnInfos -> mapper.dropProList(columnInfos, query)
        );

    }

    /**
     * 构建otb发货指南VO对象
     *
     * @param entity otb发货指南对象
     * @return 返回包含详细信息的otb发货指南VO对象
     */
    private OtbRoutingInstructionVO buildOtbRoutingInstructionVO(OtbRoutingInstruction entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的otb发货指南VO对象
        return Converters.get(OtbRoutingInstructionConverter.class).toVO(entity);
    }

}
