package cn.need.cloud.biz.model.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 入库工单详情Vo对象
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@Schema(description = "入库工单详情 Vo对象")
public class InventoryInboundWorkOrderVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 入库工单状态
     */
    @Schema(description = "入库工单状态")
    private String inboundWorkorderStatus;
}
