package cn.need.cloud.biz.model.param.warehouse.update;

import cn.need.cloud.biz.jackson.UpperCase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;


/**
 * 仓库基础信息 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "仓库基础信息 vo对象")
public class WarehouseUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 仓库编号
     */
    @Schema(description = "仓库编号")
    @Pattern(regexp = "^[0-9A-Z]+$", message = "It can only be numbers or capital letters")
    @UpperCase
    private String code;

    /**
     * 仓库名称
     */
    @Schema(description = "仓库名称")
    private String name;

    /**
     * 时区
     */
    @Schema(description = "时区")
    private String timeZone;

    /**
     * 名字
     */
    @Schema(description = "名字")
    private String addressName;

    /**
     * 公司
     */
    @Schema(description = "公司")
    private String addressCompany;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private String addressCountry;

    /**
     * 洲
     */
    @Schema(description = "洲")
    private String addressState;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String addressCity;

    /**
     * 邮编
     */
    @Schema(description = "邮编")
    private String addressZipCode;

    /**
     * 地址1
     */
    @Schema(description = "地址1")
    private String addressAddr1;

    /**
     * 地址2
     */
    @Schema(description = "地址2")
    private String addressAddr2;

    /**
     * 地址3
     */
    @Schema(description = "地址3")
    private String addressAddr3;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String addressEmail;

    /**
     * 电话
     */
    @Schema(description = "电话")
    private String addressPhone;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String addressNote;


    /**
     * 是否是住宅地址
     */
    @Schema(description = "是否是住宅地址")
    private Integer addressIsResidential;

    /**
     * amazonShipProfileShipApiRefNum
     */
    @Schema(description = "amazonShipProfileShipApiRefNum")
    private String amazonShipProfileShipApiRefNum;

    /**
     * fedexShipProfileShipApiRefNum
     */
    @Schema(description = "fedexShipProfileShipApiRefNum")
    private String fedexShipProfileShipApiRefNum;

    /**
     * upsshipProfileShipApiRefNum
     */
    @Schema(description = "upsshipProfileShipApiRefNum")
    private String upsshipProfileShipApiRefNum;


    /**
     * amazonShipPalletProfileShipApiRefNum
     */
    @Schema(description = "amazonShipPalletProfileShipApiRefNum")
    private String amazonShipPalletProfileShipApiRefNum;

    @Schema(description = "uspsShipPalletProfileShipApiRefNum")
    private String uspsShipProfileShipApiRefNum;

    @Schema(description = "ontracShipProfileShipApiRefNum")
    private String ontracShipProfileShipApiRefNum;

    /**
     * 亚马逊仓库编码
     */
    @Schema(description = "亚马逊仓库编码")
    private String amazonWarehouseCode;

    /**
     * ssccprefix
     */
    @Schema(description = "ssccprefix")
    private String ssccprefix;

    /**
     * OTBOutSide 默认库位名称
     */
    @Schema(description = "OTBOutSide 默认库位名称")
    private String defaultOTBOutSideBinLocationName;

    @Schema(description = "销售方信息")
    private String amazonSellingPartyCode;

    @Schema(description = "amazonVendorContainerCode")
    private String amazonVendorContainerCode;

}