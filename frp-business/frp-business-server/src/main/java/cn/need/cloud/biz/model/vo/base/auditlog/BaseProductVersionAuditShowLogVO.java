package cn.need.cloud.biz.model.vo.base.auditlog;

import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * BaseProductVersionAuditShowLogVO
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "BaseProductVersionAuditShowLogVO")
public class BaseProductVersionAuditShowLogVO implements Serializable {


    /**
     * 产品版本ID
     */
    @JsonIgnore
    private Long productVersionId;

    /**
     * 产品信息
     */
    private BaseProductLogVO productVO;

    /**
     * 产品信息
     */
    public BaseProductLogVO getProductVO() {
        if (ObjectUtil.isEmpty(productVO)) {
            ProductVersionCache productVersionCache = ProductVersionCacheUtil.getById(productVersionId);

            productVO = BeanUtil.copyNew(productVersionCache, BaseProductLogVO.class);
        }
        return productVO;
    }

}
