package cn.need.cloud.biz.model.vo.otb.shipment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/***
 * 大件发货单ReadyToShip
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
public class OtbMarkPalletReadyToShipVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发货单id
     */
    @Schema(description = "发货单id")
    private Long otbShipmentId;

    /**
     * 打托单id
     */
    @Schema(description = "打托单id")
    private Long otbPalletId;
}
