package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseBinLocationFullVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * 完整库位展示对象
 * <p>
 * 子类中不能包含 binLocationId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseFullBinLocationShowVO implements BaseFullBinLocationAware {
    /**
     * 库位id
     */
    @Getter
    @Setter
    private Long binLocationId;

    /**
     * 库位
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    private BaseBinLocationFullVO baseFullBinLocationVO;

    @Override
    public BaseBinLocationFullVO getBaseFullBinLocationVO() {
        if (ObjectUtil.isEmpty(binLocationId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseFullBinLocationVO)) {
            return baseFullBinLocationVO;
        }
        // Retrieve from cache once and store the result
        baseFullBinLocationVO = BaseFullBinLocationAware.super.getBaseFullBinLocationVO();
        return baseFullBinLocationVO;
    }
}
