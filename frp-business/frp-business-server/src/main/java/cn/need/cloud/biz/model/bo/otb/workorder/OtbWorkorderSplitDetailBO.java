package cn.need.cloud.biz.model.bo.otb.workorder;

import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * OtcWorkorderSplitBO
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class OtbWorkorderSplitDetailBO {

    /**
     * 原单
     */
    private OtbWorkorderDetail detail;

    /**
     * 拆单
     */
    private OtbWorkorderDetail splitDetail;

    /**
     * 拆单数量
     */
    private Integer splitQty;

    /**
     * Prep拆单
     */
    private List<OtbPrepWorkorderSplitBO> prepWorkorderHolders = new ArrayList<>();
}
