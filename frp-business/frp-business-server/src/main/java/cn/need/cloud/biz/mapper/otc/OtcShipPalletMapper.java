package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcShipPallet;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletQuery;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC运输托盘 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcShipPalletMapper extends SuperMapper<OtcShipPallet> {

    /**
     * 根据条件获取OTC运输托盘列表
     *
     * @param query 查询条件
     * @return OTC运输托盘集合
     */
    default List<OtcShipPalletPageVO> listByQuery(OtcShipPalletQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC运输托盘分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC运输托盘集合
     */
    List<OtcShipPalletPageVO> listByQuery(@Param("qo") OtcShipPalletQuery query, @Param("page") Page<?> page);
}