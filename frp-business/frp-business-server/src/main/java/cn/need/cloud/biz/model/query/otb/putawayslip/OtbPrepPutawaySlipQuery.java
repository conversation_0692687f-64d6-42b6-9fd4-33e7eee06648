package cn.need.cloud.biz.model.query.otb.putawayslip;

import cn.need.cloud.biz.model.query.base.putawayslip.PrepPutawaySlipQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipQuery;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkorderQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTB上架单 Query对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB Prep上架单 Query对象")
public class OtbPrepPutawaySlipQuery extends PrepPutawaySlipQuery {

    @Schema(description = "OTB Prep工单 Query对象")
    private OtbPrepWorkorderQuery prepWorkorderQuery;

    @Schema(description = "OTB Prep拣货单 Query对象")
    private OtbPrepPickingSlipQuery prepPickingSlipQuery;
}