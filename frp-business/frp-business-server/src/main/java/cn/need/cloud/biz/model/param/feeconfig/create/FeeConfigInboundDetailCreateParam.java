package cn.need.cloud.biz.model.param.feeconfig.create;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeConditionTypeAndUnitTypeInboundEnum;
import cn.need.cloud.biz.model.param.base.SectionParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.math.BigDecimal;


/**
 * 仓库报价费用配置inbound详情 CreateParam对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价费用配置inbound详情 CreateParam对象")
public class FeeConfigInboundDetailCreateParam extends SectionParam {


    @Serial
    private static final long serialVersionUID = 7688157148828250247L;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格")
    @NotEmpty(message = "baseFee cannot be empty")
    private BigDecimal baseFee;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费）")
    @NotEmpty(message = "feeStartThreshold cannot be empty")
    private BigDecimal feeStartThreshold;
    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型")
    @NotNull(message = "feeUnitType cannot be null")
    private FeeConditionTypeAndUnitTypeInboundEnum feeUnitType;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    @NotEmpty(message = "lineNum cannot be empty")
    private Integer lineNum;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 512, message = "note cannot exceed 512 characters")
    private String note;
    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断")
    @NotEmpty(message = "sectionEnd cannot be empty")
    private Long sectionEnd;
    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断")
    @NotEmpty(message = "sectionStart cannot be empty")
    private Long sectionStart;
    /**
     * 单价
     */
    @Schema(description = "单价")
    @NotEmpty(message = "unitFee cannot be empty")
    private BigDecimal unitFee;

    /**
     * header表id
     */
    @Schema(description = "header表id")
    @JsonIgnore
    private Long headerId;
}