package cn.need.cloud.biz.jackson;

import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * BigDecimalDeserializer
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
public class BigDecimalDeserializer extends JsonDeserializer<BigDecimal> implements ContextualDeserializer {

    private final BigDecimalFormat annotation;

    public BigDecimalDeserializer() {
        this.annotation = null;
    }

    public BigDecimalDeserializer(BigDecimalFormat annotation) {
        this.annotation = annotation;
    }

    @Override
    public BigDecimal deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        String decimalValue = jsonParser.getValueAsString();
        if (ObjectUtil.isEmpty(decimalValue)) {
            return null;
        }
        BigDecimal bigDecimal = new BigDecimal(decimalValue);
        return (annotation == null)
                ? bigDecimal
                : bigDecimal.setScale(annotation.scale(), annotation.roundingMode());
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) throws JsonMappingException {
        if (property != null) {
            BigDecimalFormat annotation = property.getAnnotation(BigDecimalFormat.class);
            if (annotation != null) {
                return new BigDecimalDeserializer(annotation);
            }
        }
        return this;
    }

}
