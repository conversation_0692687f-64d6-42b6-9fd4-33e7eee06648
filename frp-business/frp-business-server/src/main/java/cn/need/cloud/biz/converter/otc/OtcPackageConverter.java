package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPackageDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPackage;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC包裹 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPackageConverter extends AbstractModelConverter<OtcPackage, OtcPackageVO, OtcPackageDTO> {

}
