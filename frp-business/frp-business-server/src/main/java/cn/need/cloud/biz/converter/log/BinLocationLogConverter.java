package cn.need.cloud.biz.converter.log;

import cn.need.cloud.biz.client.dto.log.BinLocationLogDTO;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.vo.log.BinLocationLogVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public class BinLocationLogConverter extends AbstractModelConverter<BinLocationLog, BinLocationLogVO, BinLocationLogDTO> {

}
