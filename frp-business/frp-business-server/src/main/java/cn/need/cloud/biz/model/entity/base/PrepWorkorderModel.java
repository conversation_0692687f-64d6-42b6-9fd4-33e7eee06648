package cn.need.cloud.biz.model.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PrepWorkorderModel extends BaseWorkorderModel {

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 预工单类型
     */
    @TableField("prep_workorder_type")
    private String prepWorkorderType;
}
