package cn.need.cloud.biz.model.vo.base.product;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/***
 * OtcPrepConvertMultiBoxProductVO.java
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@Schema(description = "Prep Convert MultiBox Group Description VO对象")
public class PrepConvertMultiBoxProductVO {


    /**
     * 组合产品id
     */
    @Schema(description = "组合产品id")
    private Long productId;


    @Schema(description = "产品")
    private BaseProductVO baseProductVO;

    /**
     * 组合产品
     */
    @Schema(description = "组合产品")
    private List<PrepMultiBoxGroupVO> multiBoxList;
}
