package cn.need.cloud.biz.service.otb.workorder.impl;

import cn.need.cloud.biz.converter.otb.OtbWorkorderDetailConverter;
import cn.need.cloud.biz.mapper.otb.OtbWorkorderDetailMapper;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB工单详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbWorkorderDetailServiceImpl extends SuperServiceImpl<OtbWorkorderDetailMapper, OtbWorkorderDetail> implements OtbWorkorderDetailService {

    @Override
    public List<OtbWorkorderDetailVO> listByOtbWorkorderId(Long otbWorkorderId) {
        List<OtbWorkorderDetail> list = lambdaQuery().eq(OtbWorkorderDetail::getOtbWorkorderId, otbWorkorderId).list();
        return Converters.get(OtbWorkorderDetailConverter.class).toVO(list);
    }

    @Override
    public Map<Long, List<OtbWorkorderDetail>> groupByOtbWorkOrderIdList(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyMap();
        }
        return ObjectUtil.emptyToDefault(lambdaQuery()
                .in(OtbWorkorderDetail::getOtbWorkorderId, workOrderIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtbWorkorderDetail::getOtbWorkorderId)), Collections.emptyMap());
    }

    @Override
    public List<InventoryReleaseLockedParam> findInventoryReleaseLockedParam(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(OtbWorkorderDetail::getOtbWorkorderId, workOrderIdList)
                .list()
                .stream()
                // 根据锁分组
                //todo: Why 分组？
                .collect(Collectors.groupingBy(OtbWorkorderDetail::getInventoryLockedId,
                        // 相同锁id数量累加
                        Collectors.reducing(0, OtbWorkorderDetail::getQty, Integer::sum)))
                .entrySet()
                .stream()
                .map(obj -> {
                    InventoryReleaseLockedParam lockedParam = new InventoryReleaseLockedParam();
                    lockedParam.setId(obj.getKey());
                    lockedParam.setQty(obj.getValue());
                    return lockedParam;
                })
                .toList();
    }

    /**
     * 构建新的 OtcWorkorderDetail（复制自原始detail，修改行号与ID）
     */
    @Override
    public OtbWorkorderDetail createNewWorkOrderDetail(final OtbWorkorderDetail originalDetail, final OtbWorkorder workOrder) {
        OtbWorkorderDetail newDetail = BeanUtil.copyNew(originalDetail, OtbWorkorderDetail.class);
        newDetail.setOtbWorkorderId(workOrder.getId());
        // 此处行号可根据具体场景来设定
        newDetail.setLineNum(originalDetail.getLineNum());
        newDetail.setId(IdWorker.getId());
        return newDetail;
    }

    @Override
    public List<OtbWorkorderDetail> listItemsByOtbWorkOrderId(Collection<Long> list) {
        return lambdaQuery()
                .in(OtbWorkorderDetail::getOtbWorkorderId, list)
                .list();
    }

    @Override
    public List<OtbWorkorderDetail> listByWorkorderIds(List<Long> workorderIds) {
        if (ObjectUtil.isNotEmpty(workorderIds)) {
            return lambdaQuery().in(OtbWorkorderDetail::getOtbWorkorderId, workorderIds).list();
        }
        return List.of();
    }

    /**
     * 构造 OTB 工单明细列表
     */
    @Override
    public List<OtbWorkorderDetail> buildWorkorderDetails(
            final OtbWorkorder workorder,
            final List<OtbRequestDetailVO> detailList
    ) {
        final List<OtbWorkorderDetail> result = new ArrayList<>();
        for (int i = 0; i < detailList.size(); i++) {
            final OtbRequestDetailVO requestDetail = detailList.get(i);
            final OtbWorkorderDetail otbWorkorderDetail = new OtbWorkorderDetail();
            otbWorkorderDetail.setId(IdWorker.getId());
            otbWorkorderDetail.setOtbWorkorderId(workorder.getId());
            otbWorkorderDetail.setLineNum(i + 1);
            otbWorkorderDetail.setProductId(requestDetail.getProductId());
            otbWorkorderDetail.setQty(requestDetail.getQty());
            otbWorkorderDetail.setDetailSnapshotProductBarcode(requestDetail.getProductBarcode());
            otbWorkorderDetail.setDetailSnapshotProductChannelSku(requestDetail.getProductChannelSku());
            // 其余字段初始化为 0 或 null
            otbWorkorderDetail.setFinishQty(0);
            otbWorkorderDetail.setReserveQty(0);
            otbWorkorderDetail.setPackedQty(0);
            otbWorkorderDetail.setShipmentQty(0);
            otbWorkorderDetail.setFinishReserveQty(0);
            otbWorkorderDetail.setPickedQty(0);
            otbWorkorderDetail.setShippedQty(0);
            result.add(otbWorkorderDetail);
        }
        return result;
    }

}
