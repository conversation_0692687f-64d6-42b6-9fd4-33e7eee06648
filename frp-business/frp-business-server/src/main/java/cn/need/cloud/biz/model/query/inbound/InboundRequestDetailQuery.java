package cn.need.cloud.biz.model.query.inbound;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 入库请求详情 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库请求详情 query对象")
public class InboundRequestDetailQuery extends SuperQuery {

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 入库请求Id
     */
    @Schema(description = "入库请求Id")
    private Long inboundRequestId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 详情请求参考编号
     */
    @Schema(description = "详情请求参考编号")
    private String detailRequestRefNum;

    /**
     * 详情请求参考编号
     */
    @Schema(description = "详情请求参考编号集合")
    @Condition(value = Keyword.IN, fields = {"detailRequestRefNum"})
    private List<String> detailRequestRefNumList;

    /**
     * 详情类型
     */
    @Schema(description = "详情类型")
    private String detailType;

    /**
     * 详情类型
     */
    @Schema(description = "详情类型集合")
    @Condition(value = Keyword.IN, fields = {"detailType"})
    private List<String> detailTypeList;


}