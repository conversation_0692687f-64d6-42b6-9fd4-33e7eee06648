package cn.need.cloud.biz.model.vo.inbound.putaway;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 打托上架 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "打托上架 vo对象")
public class InboundPutawaySlipByPalletVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 打托id
     */
    @Schema(description = "打托id")
    private Long palletId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

}
