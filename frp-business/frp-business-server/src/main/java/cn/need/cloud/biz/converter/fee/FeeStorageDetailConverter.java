package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeStorageDetailDTO;
import cn.need.cloud.biz.model.entity.fee.FeeStorageDetail;
import cn.need.cloud.biz.model.vo.fee.FeeStorageDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用详情storage 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeStorageDetailConverter extends AbstractModelConverter<FeeStorageDetail, FeeStorageDetailVO, FeeStorageDetailDTO> {

}
