package cn.need.cloud.biz.model.query.otb.pickingslip.prep;

import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * otb预拣货单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "otb预拣货单 列表query对象")
public class OtbPrepPickingSlipListQuery {

    /**
     * otb预拣货单 query对象
     */
    @Schema(description = "otb预拣货单 query对象")
    private OtbPrepPickingSlipQuery otbPrepPickingSlipQuery;

    /**
     * otb工单 query对象
     */
    @Schema(description = "otb工单 query对象")
    private OtbWorkorderQuery otbWorkorderQuery;

}