package cn.need.cloud.biz.model.bo.otb.putawayslip;

import cn.need.cloud.biz.model.bo.base.putawayslip.PutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail;
import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlip;
import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlipDetail;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * OTC上架单取消对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC上架单上架对象")
public class OtbPutawaySlipPutAwayBO extends PutawaySlipPutAwayBO<OtbPutawaySlipDetail, OtbPutawaySlipPutAwayDetailBO, OtbPutawaySlip> {


    // ------------- 绑定参数 -------------

    @Schema(description = "Prep拣货单详情", hidden = true)
    @JsonIgnore
    private List<OtbPickingSlipDetail> pickingSlipDetails;

}
