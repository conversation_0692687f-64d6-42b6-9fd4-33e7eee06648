package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcRequestConverter;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestAuditParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-request")
@Tag(name = "OTC请求")
@Validated
public class OtcRequestController extends AbstractRestController<OtcRequestService, OtcRequest, OtcRequestConverter, OtcRequestVO> {

    @Operation(summary = "新增OTC请求", description = "接收OTC请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam).getId());
    }

    @Operation(summary = "修改OTC请求", description = "接收OTC请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestUpdateParam updateParam) {

        // 修改OTC请求
        service.updateByParam(updateParam);
        // 返回结果
        return success();
    }


    /**
     * <p>
     * 审核（批量）
     * </p>
     *
     * @param param 需要审核请求的数据对象
     * @return 受影响行数
     */
    @Operation(summary = "审核（批量）接口，返回受影响行数", description = "接收数据的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/batchAudit")
    public Result<String> batchAudit(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestAuditParam param) {

        service.batchAudit(param.getType(), param.getNote(), param.getIdList());
        return Result.ok();
    }

    @Operation(summary = "根据id获取OTC请求详情", description = "根据数据主键id，从数据库中获取其对应的OTC请求详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcRequestVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC请求详情
        OtcRequestVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC请求分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC请求列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcRequestQuery> search) {

        // 获取OTC请求分页
        PageData<OtcRequestPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
