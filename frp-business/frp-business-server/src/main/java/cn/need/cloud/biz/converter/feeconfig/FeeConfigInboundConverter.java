package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigInboundDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInbound;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置inbound 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigInboundConverter extends AbstractModelConverter<FeeConfigInbound, FeeConfigInboundVO, FeeConfigInboundDTO> {

}
