package cn.need.cloud.biz.model.param.product.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品多箱详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品多箱详情 vo对象")
public class ProductMultiboxDetailUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 产品多箱ID
     */
    @Schema(description = "产品多箱ID")
    private Long productMultiboxId;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

}