package cn.need.cloud.biz.model.query.feeconfig;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * 仓库报价费用配置otb Query对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价费用配置otb Query对象")
public class FeeConfigOtbOrQuery extends SuperQuery {

    @Serial
    private static final long serialVersionUID = -2534723760382795252L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    // region activeFlag

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 集合")
    @Condition(value = Keyword.IN, fields = {"activeFlag"})
    private List<Boolean> activeFlagList;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"activeFlag"})
    private List<Boolean> activeFlagNiList;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)值类型集合")
    private List<String> activeFlagValueTypeList;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 大于")
    @Condition(value = Keyword.GT, fields = {"activeFlag"})
    private Boolean activeFlagGt;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 大于等于")
    @Condition(value = Keyword.GE, fields = {"activeFlag"})
    private Boolean activeFlagGe;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 小于")
    @Condition(value = Keyword.LT, fields = {"activeFlag"})
    private Boolean activeFlagLt;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 小于等于")
    @Condition(value = Keyword.LE, fields = {"activeFlag"})
    private Boolean activeFlagLe;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"activeFlag"})
    private Boolean activeFlagLike;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"activeFlag"})
    private Boolean activeFlagLikeLeft;

    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效) 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"activeFlag"})
    private Boolean activeFlagLikeRight;

    // endregion activeFlag

    // region conditionType

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据）")
    private String conditionType;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 集合")
    @Condition(value = Keyword.IN, fields = {"conditionType"})
    private List<String> conditionTypeList;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"conditionType"})
    private List<String> conditionTypeNiList;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据）值类型集合")
    private List<String> conditionTypeValueTypeList;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 大于")
    @Condition(value = Keyword.GT, fields = {"conditionType"})
    private String conditionTypeGt;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 大于等于")
    @Condition(value = Keyword.GE, fields = {"conditionType"})
    private String conditionTypeGe;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 小于")
    @Condition(value = Keyword.LT, fields = {"conditionType"})
    private String conditionTypeLt;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 小于等于")
    @Condition(value = Keyword.LE, fields = {"conditionType"})
    private String conditionTypeLe;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"conditionType"})
    private String conditionTypeLike;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"conditionType"})
    private String conditionTypeLikeLeft;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据） 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"conditionType"})
    private String conditionTypeLikeRight;

    // endregion conditionType

    // region currency

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）")
    private String currency;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 集合")
    @Condition(value = Keyword.IN, fields = {"currency"})
    private List<String> currencyList;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"currency"})
    private List<String> currencyNiList;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）值类型集合")
    private List<String> currencyValueTypeList;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 大于")
    @Condition(value = Keyword.GT, fields = {"currency"})
    private String currencyGt;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 大于等于")
    @Condition(value = Keyword.GE, fields = {"currency"})
    private String currencyGe;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 小于")
    @Condition(value = Keyword.LT, fields = {"currency"})
    private String currencyLt;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 小于等于")
    @Condition(value = Keyword.LE, fields = {"currency"})
    private String currencyLe;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"currency"})
    private String currencyLike;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"currency"})
    private String currencyLikeLeft;

    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY） 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"currency"})
    private String currencyLikeRight;

    // endregion currency

    // region deletedNote

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 集合")
    @Condition(value = Keyword.IN, fields = {"deletedNote"})
    private List<String> deletedNoteList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"deletedNote"})
    private List<String> deletedNoteNiList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因值类型集合")
    private List<String> deletedNoteValueTypeList;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于")
    @Condition(value = Keyword.GT, fields = {"deletedNote"})
    private String deletedNoteGt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 大于等于")
    @Condition(value = Keyword.GE, fields = {"deletedNote"})
    private String deletedNoteGe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于")
    @Condition(value = Keyword.LT, fields = {"deletedNote"})
    private String deletedNoteLt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 小于等于")
    @Condition(value = Keyword.LE, fields = {"deletedNote"})
    private String deletedNoteLe;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"deletedNote"})
    private String deletedNoteLike;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"deletedNote"})
    private String deletedNoteLikeLeft;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"deletedNote"})
    private String deletedNoteLikeRight;

    // endregion deletedNote

    // region extraFeeJudgeFields

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段")
    private String extraFeeJudgeFields;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 集合")
    @Condition(value = Keyword.IN, fields = {"extraFeeJudgeFields"})
    private List<String> extraFeeJudgeFieldsList;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"extraFeeJudgeFields"})
    private List<String> extraFeeJudgeFieldsNiList;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段值类型集合")
    private List<String> extraFeeJudgeFieldsValueTypeList;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 大于")
    @Condition(value = Keyword.GT, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsGt;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 大于等于")
    @Condition(value = Keyword.GE, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsGe;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 小于")
    @Condition(value = Keyword.LT, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsLt;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 小于等于")
    @Condition(value = Keyword.LE, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsLe;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsLike;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsLikeLeft;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"extraFeeJudgeFields"})
    private String extraFeeJudgeFieldsLikeRight;

    // endregion extraFeeJudgeFields

    // region feeCalculationType

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值)")
    private String feeCalculationType;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 集合")
    @Condition(value = Keyword.IN, fields = {"feeCalculationType"})
    private List<String> feeCalculationTypeList;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"feeCalculationType"})
    private List<String> feeCalculationTypeNiList;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值)值类型集合")
    private List<String> feeCalculationTypeValueTypeList;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 大于")
    @Condition(value = Keyword.GT, fields = {"feeCalculationType"})
    private String feeCalculationTypeGt;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 大于等于")
    @Condition(value = Keyword.GE, fields = {"feeCalculationType"})
    private String feeCalculationTypeGe;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 小于")
    @Condition(value = Keyword.LT, fields = {"feeCalculationType"})
    private String feeCalculationTypeLt;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 小于等于")
    @Condition(value = Keyword.LE, fields = {"feeCalculationType"})
    private String feeCalculationTypeLe;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"feeCalculationType"})
    private String feeCalculationTypeLike;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"feeCalculationType"})
    private String feeCalculationTypeLikeLeft;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值) 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"feeCalculationType"})
    private String feeCalculationTypeLikeRight;

    // endregion feeCalculationType

    // region name

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 名称
     */
    @Schema(description = "名称 集合")
    @Condition(value = Keyword.IN, fields = {"name"})
    private List<String> nameList;

    /**
     * 名称
     */
    @Schema(description = "名称 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"name"})
    private List<String> nameNiList;

    /**
     * 名称
     */
    @Schema(description = "名称值类型集合")
    private List<String> nameValueTypeList;

    /**
     * 名称
     */
    @Schema(description = "名称 大于")
    @Condition(value = Keyword.GT, fields = {"name"})
    private String nameGt;

    /**
     * 名称
     */
    @Schema(description = "名称 大于等于")
    @Condition(value = Keyword.GE, fields = {"name"})
    private String nameGe;

    /**
     * 名称
     */
    @Schema(description = "名称 小于")
    @Condition(value = Keyword.LT, fields = {"name"})
    private String nameLt;

    /**
     * 名称
     */
    @Schema(description = "名称 小于等于")
    @Condition(value = Keyword.LE, fields = {"name"})
    private String nameLe;

    /**
     * 名称
     */
    @Schema(description = "名称 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"name"})
    private String nameLike;

    /**
     * 名称
     */
    @Schema(description = "名称 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"name"})
    private String nameLikeLeft;

    /**
     * 名称
     */
    @Schema(description = "名称 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"name"})
    private String nameLikeRight;

    // endregion name

    // region note

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 备注
     */
    @Schema(description = "备注 集合")
    @Condition(value = Keyword.IN, fields = {"note"})
    private List<String> noteList;

    /**
     * 备注
     */
    @Schema(description = "备注 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"note"})
    private List<String> noteNiList;

    /**
     * 备注
     */
    @Schema(description = "备注值类型集合")
    private List<String> noteValueTypeList;

    /**
     * 备注
     */
    @Schema(description = "备注 大于")
    @Condition(value = Keyword.GT, fields = {"note"})
    private String noteGt;

    /**
     * 备注
     */
    @Schema(description = "备注 大于等于")
    @Condition(value = Keyword.GE, fields = {"note"})
    private String noteGe;

    /**
     * 备注
     */
    @Schema(description = "备注 小于")
    @Condition(value = Keyword.LT, fields = {"note"})
    private String noteLt;

    /**
     * 备注
     */
    @Schema(description = "备注 小于等于")
    @Condition(value = Keyword.LE, fields = {"note"})
    private String noteLe;

    /**
     * 备注
     */
    @Schema(description = "备注 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"note"})
    private String noteLike;

    /**
     * 备注
     */
    @Schema(description = "备注 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"note"})
    private String noteLikeLeft;

    /**
     * 备注
     */
    @Schema(description = "备注 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"note"})
    private String noteLikeRight;

    // endregion note

    // region quoteId

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id")
    private Long quoteId;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 集合")
    @Condition(value = Keyword.IN, fields = {"quoteId"})
    private List<Long> quoteIdList;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"quoteId"})
    private List<Long> quoteIdNiList;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id值类型集合")
    private List<String> quoteIdValueTypeList;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 大于")
    @Condition(value = Keyword.GT, fields = {"quoteId"})
    private Long quoteIdGt;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 大于等于")
    @Condition(value = Keyword.GE, fields = {"quoteId"})
    private Long quoteIdGe;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 小于")
    @Condition(value = Keyword.LT, fields = {"quoteId"})
    private Long quoteIdLt;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 小于等于")
    @Condition(value = Keyword.LE, fields = {"quoteId"})
    private Long quoteIdLe;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"quoteId"})
    private Long quoteIdLike;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"quoteId"})
    private Long quoteIdLikeLeft;

    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"quoteId"})
    private Long quoteIdLikeRight;

    // endregion quoteId

    // region refNum

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"refNum"})
    private List<String> refNumNiList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码值类型集合")
    private List<String> refNumValueTypeList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于")
    @Condition(value = Keyword.GT, fields = {"refNum"})
    private String refNumGt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 大于等于")
    @Condition(value = Keyword.GE, fields = {"refNum"})
    private String refNumGe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于")
    @Condition(value = Keyword.LT, fields = {"refNum"})
    private String refNumLt;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 小于等于")
    @Condition(value = Keyword.LE, fields = {"refNum"})
    private String refNumLe;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"refNum"})
    private String refNumLike;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"refNum"})
    private String refNumLikeLeft;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"refNum"})
    private String refNumLikeRight;

    // endregion refNum

    // region warehouseId

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 集合")
    @Condition(value = Keyword.IN, fields = {"warehouseId"})
    private List<Long> warehouseIdList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 不在集合")
    @Condition(value = Keyword.NOT_IN, fields = {"warehouseId"})
    private List<Long> warehouseIdNiList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id值类型集合")
    private List<String> warehouseIdValueTypeList;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于")
    @Condition(value = Keyword.GT, fields = {"warehouseId"})
    private Long warehouseIdGt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 大于等于")
    @Condition(value = Keyword.GE, fields = {"warehouseId"})
    private Long warehouseIdGe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于")
    @Condition(value = Keyword.LT, fields = {"warehouseId"})
    private Long warehouseIdLt;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 小于等于")
    @Condition(value = Keyword.LE, fields = {"warehouseId"})
    private Long warehouseIdLe;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 模糊查询")
    @Condition(value = Keyword.LIKE, fields = {"warehouseId"})
    private Long warehouseIdLike;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 左模糊查询")
    @Condition(value = Keyword.LIKE_LEFT, fields = {"warehouseId"})
    private Long warehouseIdLikeLeft;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id 右模糊查询")
    @Condition(value = Keyword.LIKE_RIGHT, fields = {"warehouseId"})
    private Long warehouseIdLikeRight;

    // endregion warehouseId

}