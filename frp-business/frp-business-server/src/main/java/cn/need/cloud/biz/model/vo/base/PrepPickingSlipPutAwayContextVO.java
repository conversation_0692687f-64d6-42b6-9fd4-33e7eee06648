package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.model.entity.base.PrepWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import cn.need.cloud.biz.model.entity.base.pickingslip.PrepPickingSlipDetailModel;
import cn.need.cloud.biz.model.entity.base.pickingslip.PrepPickingSlipModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.base.PrepPickingSlipPutAwayQuery;
import cn.need.cloud.biz.model.vo.otb.workorder.OtcPrepWorkorderPutAwayVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/***
 * 上架上下文信息
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class PrepPickingSlipPutAwayContextVO<PrepPickingSlip extends PrepPickingSlipModel, PrepPickingSlipDetail extends PrepPickingSlipDetailModel,
        Workorder extends WorkorderModel, PrepWorkorder extends PrepWorkorderModel, PrepWorkorderDetail extends PrepWorkorderDetailModel> {

    /**
     * 上架参数信息
     */
    private PrepPickingSlipPutAwayQuery query;

    /**
     * 上架的Prep拣货单
     */
    private PrepPickingSlip prepPickingSlip;

    /**
     * 上架的Prep工单
     */
    private List<PrepWorkorder> putAwayPrepWorkOrderList = new ArrayList<>();

    /**
     * 该拣货单下的Prep工单
     */
    private List<PrepWorkorder> prepWorkOrderList = new ArrayList<>();

    /**
     * 库存预留释放库存参数
     */
    private List<InventoryReleaseLockedParam> reserveInventoryReleaseList = new ArrayList<>();

    /**
     * 工单信息
     */
    private List<Workorder> workorderList = new ArrayList<>();

    /**
     * 上架库位信息
     */
    private BinLocationDetail putAwayBinLocationDetail;

    /**
     * 拣货单详情
     */
    private List<PrepPickingSlipDetail> prepPickingSlipDetailList;

    /**
     * 更新的工单详情
     */
    private List<PrepWorkorderDetail> putAwayPrepWorkorderDetailList;


    /**
     * 所有Prep工单详情
     */
    private Map<Long, List<PrepWorkorderDetail>> prepDetailGroupByWkMap;

    /**
     * 上架Prep工单的信息
     */
    private List<OtcPrepWorkorderPutAwayVO> prepWorkorderPutawayList = new ArrayList<>();
}
