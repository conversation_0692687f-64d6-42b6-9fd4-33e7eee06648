package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPrepPickingSlipDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb预拣货单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPrepPickingSlipConverter extends AbstractModelConverter<OtbPrepPickingSlip, OtbPrepPickingSlipVO, OtbPrepPickingSlipDTO> {

}
