package cn.need.cloud.biz.controller.otb;


import cn.need.cloud.biz.converter.otb.OtbPrepPutawaySlipConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlip;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.putawayslip.OtbPrepPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.putawayslip.OtbPrepPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbPrepPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otb.putawayslip.OtbPrepPutawaySlipVO;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPrepPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC上架单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@RestController
@RequestMapping("/api/biz/otb-prep-putaway-slip")
@Tag(name = "OTB Prep上架单")
public class OtbPrepPutawaySlipController extends AbstractRestController<OtbPrepPutawaySlipService, OtbPrepPutawaySlip, OtbPrepPutawaySlipConverter, OtbPrepPutawaySlipVO> {

    @Operation(summary = "根据id获取OTC上架单详情", description = "根据数据主键id，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPrepPutawaySlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取OTC上架单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPrepPutawaySlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "获取OTC上架单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC上架单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPrepPutawaySlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPrepPutawaySlipQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "Cancel", description = "取消上架")
    @PostMapping(value = "/cancel")
    public Result<Boolean> cancel(@RequestBody @Valid PutawaySlipCancelUpdateParam param) {
        // 返回结果
        return success(service.cancel(param));
    }

    @Operation(summary = "PutAway", description = "上架")
    @PostMapping(value = "/put-away")
    public Result<Boolean> putAway(@RequestBody @Valid OtbPrepPutawaySlipPutAwayUpdateParam param) {
        // 返回结果
        return success(service.putAway(param));
    }

    @Operation(summary = "Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }
}
