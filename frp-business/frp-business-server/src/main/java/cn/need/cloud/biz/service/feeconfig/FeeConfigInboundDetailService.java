package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInboundDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigInboundDetailCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigInboundDetailUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigInboundDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigInboundDetailPageVO;
import cn.need.cloud.biz.service.base.UpdateService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <p>
 * 仓库报价费用配置inbound详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface FeeConfigInboundDetailService extends
        SuperService<FeeConfigInboundDetail>,
        UpdateService<FeeConfigInboundDetail, FeeConfigInboundDetailService>,
        FeeConfigDetailService<FeeConfigInboundDetail, FeeConfigInboundDetailService> {

    /**
     * 根据参数新增仓库报价费用配置inbound详情
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价费用配置inbound详情的相关信息
     * @return 仓库报价费用配置inbound详情ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeConfigInboundDetailCreateParam createParam);


    /**
     * 初始化单个仓库报价费用配置inbound详情实体对象
     *
     * @param createParam  需要转换并初始化的仓库报价费用配置inbound详情参数对象，不能为空
     * @param initConsumer 用于执行初始化操作的消费者对象
     * @return 初始化后的仓库报价费用配置inbound详情实体对象
     */
    FeeConfigInboundDetail initFeeConfigInboundDetail(FeeConfigInboundDetailCreateParam createParam, Consumer<FeeConfigInboundDetail> initConsumer);

    /**
     * 初始化单个仓库报价费用配置inbound详情实体对象
     *
     * @param createParams 需要转换并初始化的仓库报价费用配置inbound详情参数对象，不能为空
     * @param initConsumer 用于执行初始化操作的消费者对象
     * @return 初始化后的仓库报价费用配置inbound详情实体对象
     */
    List<FeeConfigInboundDetail> initFeeConfigInboundDetail(
            List<FeeConfigInboundDetailCreateParam> createParams,
            Consumer<FeeConfigInboundDetail> initConsumer);

    /**
     * 根据参数更新仓库报价费用配置inbound详情
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价费用配置inbound详情的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeConfigInboundDetailUpdateParam updateParam);


    /**
     * 根据仓库报价费用配置inbound主表ID批量更新详情
     *
     * @param feeConfigInboundId 仓库报价费用配置inbound主表ID
     * @param updateParamList    需要更新的仓库报价费用配置inbound详情参数列表，列表及元素均不能为空
     */
    void updateByFeeConfigInboundId(Long feeConfigInboundId, List<FeeConfigInboundDetailUpdateParam> updateParamList);


    /**
     * 根据查询条件获取仓库报价费用配置inbound详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置inbound详情对象的列表(分页)
     */
    List<FeeConfigInboundDetailPageVO> listByQuery(FeeConfigInboundDetailQuery query);

    /**
     * 根据查询条件获取仓库报价费用配置inbound详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置inbound详情对象的列表(分页)
     */
    PageData<FeeConfigInboundDetailPageVO> pageByQuery(PageSearch<FeeConfigInboundDetailQuery> search);

    /**
     * 根据ID获取仓库报价费用配置inbound详情
     *
     * @param id 仓库报价费用配置inbound详情ID
     * @return 返回仓库报价费用配置inbound详情VO对象
     */
    FeeConfigInboundDetailVO detailById(Long id);

    /**
     * 根据仓库报价费用配置inbound主表ID获取实体列表
     *
     * @param feeConfigInboundId 仓库报价费用配置inbound主表ID
     * @return 对应的仓库报价费用配置inbound详情实体列表
     */
    List<FeeConfigInboundDetail> listEntityByFeeConfigInboundId(Long feeConfigInboundId);

    /**
     * 根据仓库报价费用配置inboundid获取仓库报价费用配置inbound详情集合
     *
     * @param feeConfigInboundId 仓库报价费用配置inboundid
     * @return 仓库报价费用配置inbound详情集合
     */
    List<FeeConfigInboundDetailVO> listByFeeConfigInboundId(Long feeConfigInboundId);

    List<FeeConfigInboundDetailVO> listByFeeConfigInboundIdList(Collection<Long> feeConfigIds);

    Map<Long, List<FeeConfigInboundDetailVO>> mapByFeeConfigInboundIdList(Collection<Long> feeConfigIds);
}