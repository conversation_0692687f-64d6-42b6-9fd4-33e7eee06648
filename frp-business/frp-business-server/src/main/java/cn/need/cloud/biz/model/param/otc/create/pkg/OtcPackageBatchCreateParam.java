package cn.need.cloud.biz.model.param.otc.create.pkg;

import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * OTC包裹 BatchCreate
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC包裹 BatchCreate")
public class OtcPackageBatchCreateParam implements Serializable {

    /**
     * workorder
     */
    @Schema(description = "workorder")
    private OtcWorkorder workorder;

    /**
     * request
     */
    @Schema(description = "request")
    private OtcRequestVO request;

    /**
     * requestPackageList
     */
    @Schema(description = "requestPackageList")
    private List<OtcRequestPackageFullVO> requestPackageList;

}