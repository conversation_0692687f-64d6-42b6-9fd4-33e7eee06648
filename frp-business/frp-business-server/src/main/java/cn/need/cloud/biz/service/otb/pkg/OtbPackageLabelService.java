package cn.need.cloud.biz.service.otb.pkg;

import cn.need.cloud.biz.model.bo.otb.OtbBuildPackageContextBo;
import cn.need.cloud.biz.model.entity.otb.OtbPackageLabel;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageLabelVO;
import cn.need.cloud.biz.service.base.MarkPrintedService;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * OTB包裹标签 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPackageLabelService extends SuperService<OtbPackageLabel>, MarkPrintedService<OtbPackageLabel, OtbPackageLabelService, PrintQuery> {

    /**
     * 生成打包标签标签
     *
     * @param contextBo 上下文信息
     */
    void generatePackageLabel(OtbBuildPackageContextBo contextBo);

    /**
     * 根据包裹id获取label列表
     *
     * @param id 包裹id
     * @return label列表
     */
    List<OtbPackageLabelVO> listByPackageId(Long id);

    /**
     * 检查包裹是否所有标签都打印成功
     *
     * @param otbPackageLabel 包裹标签
     */
    boolean isAllLabelPrintedSuccess(OtbPackageLabel otbPackageLabel);

    Set<Long> getPackageId(List<String> printStatusList);

    /**
     * 根据包裹id获取label列表
     *
     * @param idList          包裹id列表
     * @param printStatusList 打印状态列表
     * @return label列表
     */
    List<OtbPackageLabelVO> listByPackageId(Set<Long> idList, List<String> printStatusList);

    /**
     * 根据包裹id获取label列表
     *
     * @param idList 包裹id列表
     * @return label列表
     */
    List<OtbPackageLabel> listByPackageId(Collection<Long> idList);
}