package cn.need.cloud.biz.model.bo.inbound;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.vo.base.auditlog.BaseProductLogVO;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Optional;

/**
 * 上架产品 BO对象
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "上架产品 BO对象")
public class BinLocationDetailContextBO implements Serializable {
    @Serial
    private final static long serialVersionUID = -7888689056676234846L;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 版本产品id
     */
    private Long productVersionId;

    /**
     * 库位id
     */
    private Long binLocationId;

    /**
     * 上架数量
     */
    private Integer inStockQty;

    /**
     * 当前数量
     */
    private int currentInStockQty;

    /**
     * 工单id
     */
    private Long inboundWorkOrderId;

    /**
     * 工单id
     */
    private RefNumModel refNumModel;

    /**
     * 库位详情id
     */
    private Long binLocationDetailId;

    public String toLog() {
        return StringUtil.format("RefNum: {}, BinLocation: {}, Product: {}, PutAwayQty: {}",
                refNumModel.getRefNum(),
                Optional.ofNullable(binLocationId)
                        .map(BinLocationCacheUtil::getById)
                        .map(BinLocationCache::getLocationName)
                        .orElse(StringPool.EMPTY),
                Optional.ofNullable(productId)
                        .map(ProductCacheUtil::getById)
                        .map(cache -> BeanUtil.copyNew(cache, BaseProductLogVO.class))
                        .map(BaseProductLogVO::toLog)
                        .orElse(StringPool.EMPTY),
                getInStockQty()
        );
    }
}
