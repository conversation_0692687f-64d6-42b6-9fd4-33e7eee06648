package cn.need.cloud.biz.model.entity.inbound;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * <p>
 * 入库单打托
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inbound_pallet")
public class InboundPallet extends RefNumModel {


    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 托盘-长
     */
    @TableField("pallet_size_length")
    private BigDecimal palletSizeLength;

    /**
     * 托盘-宽
     */
    @TableField("pallet_size_width")
    private BigDecimal palletSizeWidth;

    /**
     * 托盘-高
     */
    @TableField("pallet_size_height")
    private BigDecimal palletSizeHeight;

    /**
     * 托盘-重量
     */
    @TableField("pallet_size_weight")
    private BigDecimal palletSizeWeight;

    /**
     * 托盘-重量单位
     */
    @TableField("pallet_size_weight_unit")
    private String palletSizeWeightUnit;

    /**
     * 托盘-长度单位
     */
    @TableField("pallet_size_dimension_unit")
    private String palletSizeDimensionUnit;

    /**
     * 打托状态
     */
    @TableField("pallet_status")
    private String palletStatus;

    /**
     * 打印状态
     */
    @TableField("print_status")
    private String printStatus;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;

    /**
     * 每层几个箱子
     */
    @TableField("carton_per_layer")
    private Integer cartonPerLayer;

    /**
     * 一共多少层
     */
    @TableField("layers_count")
    private Integer layersCount;

    /**
     * 多了几个箱子
     */
    @TableField("ext_carton")
    private Integer extCarton;

    /**
     * 每箱几个产品
     */
    @TableField("pcs_per_carton")
    private Integer pcsPerCarton;

    /**
     * 空托盘id
     */
    @TableField("pallet_empty_profile_id")
    private Long palletEmptyProfileId;

    /**
     * 托盘模板id
     */
    @TableField("pallet_template_id")
    private Long palletTemplateId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 版本产品id
     */
    @TableField("product_version_id")
    private Long productVersionId;

    @TableField("inbound_workorder_id")
    private Long inBoundWorkOrderId;

}
