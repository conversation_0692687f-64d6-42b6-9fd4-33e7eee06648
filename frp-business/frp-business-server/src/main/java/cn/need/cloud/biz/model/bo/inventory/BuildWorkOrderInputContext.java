package cn.need.cloud.biz.model.bo.inventory;

import cn.need.cloud.biz.client.constant.enums.base.WorkorderTypeEnum;
import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * OtcWorkOrderReturnContext
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "BuildWorkOrderInputContext")
public class BuildWorkOrderInputContext implements Serializable {

    /**
     * productMap
     */
    @Schema(description = "productMap")
    private Map<Long, BaseFullProductVO> productMap;

    /**
     * inventoryMap
     */
    @Schema(description = "inventoryMap")
    private Map<Long, InventoryVO> inventoryMap;


    /**
     * isMultibox
     */
    @Schema(description = "isMultibox")
    private boolean isMultibox;

    /**
     * workorderType
     */
    @Schema(description = "workorderType")
    private WorkorderTypeEnum workorderType;
}
