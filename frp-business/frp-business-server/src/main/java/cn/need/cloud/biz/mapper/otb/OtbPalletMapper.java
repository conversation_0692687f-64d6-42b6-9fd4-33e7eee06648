package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.model.query.otb.pallet.OtbPalletQuery;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletVO;
import cn.need.cloud.biz.model.vo.page.OtbPalletPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTB托盘 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPalletMapper extends SuperMapper<OtbPallet> {

    /**
     * 根据条件获取OTB托盘列表
     *
     * @param query 查询条件
     * @return OTB托盘集合
     */
    default List<OtbPalletPageVO> listByQuery(OtbPalletQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTB托盘分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTB托盘集合
     */
    List<OtbPalletPageVO> listByQuery(@Param("qo") OtbPalletQuery query, @Param("page") Page<?> page);

    /**
     * 根据条件获取OTB托盘分页列表
     *
     * @param otbPalletList 打托单集合
     */
    void updateBatchWithNull(@Param("otbPalletList") List<OtbPalletVO> otbPalletList);
}