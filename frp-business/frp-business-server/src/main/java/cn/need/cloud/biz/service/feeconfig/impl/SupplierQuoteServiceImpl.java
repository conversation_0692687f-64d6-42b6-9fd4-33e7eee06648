package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.SupplierQuoteConverter;
import cn.need.cloud.biz.mapper.feeconfig.SupplierQuoteMapper;
import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierQuoteCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.SupplierQuoteUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuoteQuery;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierQuoteVO;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierQuotePageVO;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.feeconfig.SupplierQuoteService;
import cn.need.cloud.biz.service.feeconfig.SupplierService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商-仓库报价 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
public class SupplierQuoteServiceImpl extends SuperServiceImpl<SupplierQuoteMapper, SupplierQuote> implements SupplierQuoteService {

    @Resource
    private QuoteService quoteService;

    @Resource
    private SupplierService supplierService;

    private static void checkActiveTimes(List<SupplierQuote> dbSupplierQuotes) {
        List<SupplierQuote> allActiveSupplierQuotes = dbSupplierQuotes.stream().filter(SupplierQuote::getActiveFlag).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(allActiveSupplierQuotes)) {
            allActiveSupplierQuotes.sort(((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())));
            for (int i = 0, allActiveSupplierQuotesSize = allActiveSupplierQuotes.size(); i < allActiveSupplierQuotesSize; i++) {
                SupplierQuote item = allActiveSupplierQuotes.get(i);

                if (item.getStartTime().isAfter(item.getStartTime())) {
                    throw new BusinessException(StringUtil.format("{} Start time cannot be less than the start time of the last record", item.refNumLog()));
                }
                if (item.getEndTime().isBefore(item.getStartTime())) {
                    throw new BusinessException(StringUtil.format("{} End time cannot be less than the start time", item.refNumLog()));
                }
                if (i > 0) {
                    SupplierQuote beforeItem = allActiveSupplierQuotes.get(i - 1);
                    if (!item.getStartTime().isEqual(beforeItem.getEndTime())) {
                        throw new BusinessException(StringUtil.format("{} Start time must equal with the {} end time", item.refNumLog(), beforeItem.refNumLog()));
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(SupplierQuoteCreateParam createParam) {
        // 检查传入供应商-仓库报价参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        if (createParam.getStartTime().isAfter(createParam.getStartTime())) {
            throw new BusinessException("Start time cannot be less than the start time of the last record");
        }
        if (createParam.getEndTime().isBefore(createParam.getStartTime())) {
            throw new BusinessException("End time cannot be less than the start time");
        }

        // 将供应商-仓库报价参数对象转换为实体对象并初始化
        SupplierQuote entity = initSupplierQuote(createParam);

        List<SupplierQuote> dbSupplierQuotes = listSupplierQuotesBySupplierIdQuoteId(createParam.getSupplierId(), createParam.getQuoteId());

        dbSupplierQuotes.add(entity);

        checkActiveTimes(dbSupplierQuotes);

        // 插入供应商-仓库报价实体对象到数据库
        super.insert(entity);

        // 返回供应商-仓库报价ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(SupplierQuoteUpdateParam updateParam) {
        // 检查传入供应商-仓库报价参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将供应商-仓库报价参数对象转换为实体对象并初始化
        SupplierQuote entity = initSupplierQuote(updateParam);

        List<SupplierQuote> dbSupplierQuotes = listSupplierQuotesBySupplierIdQuoteId(updateParam.getSupplierId(), updateParam.getQuoteId());

        dbSupplierQuotes.add(entity);

        checkActiveTimes(dbSupplierQuotes);

        // 执行更新供应商-仓库报价操作
        return super.update(entity);
    }

    @Override
    public List<SupplierQuotePageVO> listByQuery(SupplierQuoteQuery query) {

        return mapper.listByQuery(query);
    }

    @Override
    public PageData<SupplierQuotePageVO> pageByQuery(PageSearch<SupplierQuoteQuery> search) {
        Page<SupplierQuote> page = Conditions.page(search, entityClass);
        List<SupplierQuotePageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        fillData(dataList);

        return new PageData<>(dataList, page);
    }

    @Override
    public SupplierQuoteVO detailById(Long id) {
        SupplierQuote entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "SupplierQuote", id));
        }
        return buildSupplierQuoteVO(entity);
    }

    @Override
    public List<SupplierQuote> listByQuoteId(Long quoteId) {

        return lambdaQuery().eq(SupplierQuote::getQuoteId, quoteId).list();
    }

    /**
     * 启用禁用
     *
     * @param id supplierQuoteId
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer switchActive(Long id) {
        //数据初始化
        SupplierQuote supplierQuote = super.getById(id);
        if (ObjectUtil.isEmpty(supplierQuote)) {
            throw new BusinessException(StringUtil.format("id: {} not found in SupplierQuote", id));
        }
        //更新状态
        if (supplierQuote.getActiveFlag()) {
            supplierQuote.setActiveFlag(Boolean.FALSE);

        } else {
            supplierQuote.setActiveFlag(Boolean.TRUE);

        }
        List<SupplierQuote> dbSupplierQuotes = listSupplierQuotesBySupplierIdQuoteId(supplierQuote.getSupplierId(), supplierQuote.getQuoteId());
        checkActiveTimes(dbSupplierQuotes);

        return super.update(supplierQuote);
    }

    @Override
    public SupplierQuote getActiveSupplierQuote(Long transactionPartnerId, LocalDateTime time) {
        SupplierVO supplier = supplierService.getByTransactionPartnerId(transactionPartnerId);
        if (ObjectUtil.isEmpty(supplier)) {
            throw new BusinessException(StringUtil.format("TransactionPartnerId: {} not found in Supplier", transactionPartnerId));
        }
        return lambdaQuery()
                .eq(SupplierQuote::getSupplierId, supplier.getId())
                .eq(SupplierQuote::getActiveFlag, Boolean.TRUE)
                .le(SupplierQuote::getStartTime, time)
                .gt(SupplierQuote::getEndTime, time)
                .one();
    }

    public List<SupplierQuote> listSupplierQuotesBySupplierIdQuoteId(Long supplierId, Long quoteId) {
        return lambdaQuery()
                .eq(SupplierQuote::getSupplierId, supplierId)
                .eq(SupplierQuote::getQuoteId, quoteId)
                .list();
    }

    private void fillData(List<SupplierQuotePageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }

        final Map<Long, RefNumWithNameVO> quoteMap = quoteService.refNumWithNameMapByIds(FeeConfigUtil.getQuoteIds(dataList));

        if (ObjectUtil.isNotEmpty(quoteMap)) {
            for (SupplierQuotePageVO item : dataList) {
                if (ObjectUtil.isEmpty(item.getQuoteId())) {
                    continue;
                }
                item.setQuote(quoteMap.get(item.getQuoteId()));
            }
        }
    }

    /**
     * 初始化供应商-仓库报价对象
     * 此方法用于设置供应商-仓库报价对象的必要参数，确保其处于有效状态
     *
     * @param createParam 供应商-仓库报价 新增对象，不应为空
     * @return 返回初始化后的供应商-仓库报价
     * @throws BusinessException 如果传入的供应商-仓库报价为空，则抛出此异常
     */
    private SupplierQuote initSupplierQuote(SupplierQuoteCreateParam createParam) {
        // 检查传入的配置对象是否为空
        checkParam(createParam);

        // 获取供应商-仓库报价转换器实例，用于将供应商-仓库报价参数对象转换为实体对象
        SupplierQuoteConverter converter = Converters.get(SupplierQuoteConverter.class);

        // 将供应商-仓库报价参数对象转换为实体对象并初始化
        SupplierQuote entity = converter.toEntity(createParam);

        initSupplierQuote(entity);

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_SUPPLIER_QUOTE));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 初始化供应商-仓库报价对象
     * 此方法用于设置供应商-仓库报价对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 供应商-仓库报价 修改对象，不应为空
     * @return 返回初始化后的供应商-仓库报价
     * @throws BusinessException 如果传入的供应商-仓库报价为空，则抛出此异常
     */
    private SupplierQuote initSupplierQuote(SupplierQuoteUpdateParam updateParam) {
        // 检查传入的配置对象是否为空
        checkParam(updateParam);

        // 获取供应商-仓库报价转换器实例，用于将供应商-仓库报价参数对象转换为实体对象
        SupplierQuoteConverter converter = Converters.get(SupplierQuoteConverter.class);

        // 将供应商-仓库报价参数对象转换为实体对象并初始化
        SupplierQuote entity = converter.toEntity(updateParam);

        initSupplierQuote(entity);

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 检查传入的参数是否为空
     *
     * @param param 传入的参数对象
     * @throws BusinessException 如果传入的参数为空，则抛出此异常
     */
    private void checkParam(Object param) {
        if (ObjectUtil.isEmpty(param)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
    }

    /**
     * 初始化供应商-仓库报价对象
     * 此方法用于设置供应商-仓库报价对象的必要参数，确保其处于有效状态
     *
     * @param entity 供应商-仓库报价对象，不应为空
     * @throws BusinessException 如果传入的供应商-仓库报价为空，则抛出此异常
     */
    private void initSupplierQuote(SupplierQuote entity) {
        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("SupplierQuote cannot be empty");
        }

        // 添加必要的初始化逻辑
        // 例如：设置默认值、校验字段等
    }

    /**
     * 构建供应商-仓库报价VO对象
     *
     * @param entity 供应商-仓库报价对象
     * @return 返回包含详细信息的供应商-仓库报价VO对象
     */
    private SupplierQuoteVO buildSupplierQuoteVO(SupplierQuote entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的供应商-仓库报价VO对象
        final SupplierQuoteVO vo = Converters.get(SupplierQuoteConverter.class).toVO(entity);

        vo.setQuote(quoteService.refNumWithNameById(vo.getQuoteId()));

        vo.setSupplier(supplierService.detailById(vo.getSupplierId()));

        return vo;
    }

}
