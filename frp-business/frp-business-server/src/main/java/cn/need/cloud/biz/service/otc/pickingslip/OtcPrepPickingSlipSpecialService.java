package cn.need.cloud.biz.service.otc.pickingslip;

import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.pickingslip.prep.OtcPrepPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.vo.base.PrepUnpickVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderRollbackPutawayUnitsVO;

import java.util.List;

/**
 * <p>
 * OTC预提货单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPrepPickingSlipSpecialService {

    /**
     * OnHold
     *
     * @param prepWorkorderList Prep工单
     */
    void onHold(List<OtcPrepWorkorder> prepWorkorderList);

    /**
     * 触发流程
     *
     * @param query start条件
     */
    void processTriggering(WorkorderProcessBO query);

    /**
     * unpick
     *
     * @param query unpick条件
     */
    void unpick(OtcPrepPickingSlipUnpickCreateParam query);

    /**
     * unpick 拣货单信息
     *
     * @param workorderId 工单信息
     * @return /
     */
    PrepUnpickVO unpickByWorkorderId(Long workorderId);

    /**
     * Rollback
     *
     * @param param 上架参数
     */
    void rollback(OtcPrepPutawaySlipPutAwayBO param);

    /**
     * 拣货单Rollback
     *
     * @param param Rollback参数
     * @return /
     */
    boolean batchCancel(PickingSlipCancelUpdateParam param);

    /**
     * Prep拣货单混滚上架数量
     *
     * @param param 回滚参数
     * @return /
     */
    boolean rollbackPutAwayUnits(RollbackPutawayUnitsUpdateParam param);

    /**
     * Prep拣货单混滚上架数量列表
     *
     * @param prepWorkorderId Prep工单id
     * @return /
     */
    PrepWorkorderRollbackPutawayUnitsVO rollbackPutAwayUnitsList(Long prepWorkorderId);

    /**
     * 拆单
     *
     * @param splitHolders 拆单参数
     * @return /
     */
    void split(List<OtcWorkorderSplitBO> splitHolders);
}