package cn.need.cloud.biz.converter.setting;

import cn.need.cloud.biz.client.dto.PrinterConfigDTO;
import cn.need.cloud.biz.model.entity.setting.PrinterConfig;
import cn.need.cloud.biz.model.vo.setting.PrinterConfigVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 打印配置 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class PrinterConfigConverter extends AbstractModelConverter<PrinterConfig, PrinterConfigVO, PrinterConfigDTO> {

}
