package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPrepPickingSlipDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC预提货单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPrepPickingSlipConverter extends AbstractModelConverter<OtcPrepPickingSlip, OtcPrepPickingSlipVO, OtcPrepPickingSlipDTO> {

}
