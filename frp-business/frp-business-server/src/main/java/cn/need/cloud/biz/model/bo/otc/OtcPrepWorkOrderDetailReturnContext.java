package cn.need.cloud.biz.model.bo.otc;

import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * OtcPrepWorkOrderDetailReturnContext
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "OtcPrepWorkOrderDetailReturnContext")
public class OtcPrepWorkOrderDetailReturnContext implements Serializable {

    /**
     * prepWorkorderDetail
     */
    @Schema(description = "prepWorkorderDetail")
    private OtcPrepWorkorderDetail prepWorkorderDetail;

    /**
     * inventoryLocked
     */
    @Schema(description = "inventoryLocked")
    private InventoryLocked inventoryLocked;


    public OtcPrepWorkOrderDetailReturnContext(OtcPrepWorkorderDetail detail, InventoryLocked inventoryLocked) {
        this.prepWorkorderDetail = detail;
        this.inventoryLocked = inventoryLocked;
    }
}
