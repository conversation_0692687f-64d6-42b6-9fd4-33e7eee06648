package cn.need.cloud.biz.model.query.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "产品根据ScanNumAndPartner 查询对象")
public class ScanNumAndPartnerQuery implements Serializable {
    /**
     * ScanNum 扫描码
     */
    @Schema(description = "扫描码")
    private String scanNum;


    /**
     * transactionPartnerId 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;
}
