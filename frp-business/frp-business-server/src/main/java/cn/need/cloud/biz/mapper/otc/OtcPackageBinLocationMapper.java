package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPackageBinLocation;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageBinLocationQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcPackageBinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC工单仓储位置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcPackageBinLocationMapper extends SuperMapper<OtcPackageBinLocation> {

    /**
     * 根据条件获取OTC工单仓储位置列表
     *
     * @param query 查询条件
     * @return OTC工单仓储位置集合
     */
    default List<OtcPackageBinLocationPageVO> listByQuery(OtcPackageBinLocationQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC工单仓储位置分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC工单仓储位置集合
     */
    List<OtcPackageBinLocationPageVO> listByQuery(@Param("qo") OtcPackageBinLocationQuery query, @Param("page") Page<?> page);
}