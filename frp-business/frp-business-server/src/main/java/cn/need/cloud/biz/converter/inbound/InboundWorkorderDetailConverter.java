package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorderDetail;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundWorkorderDetailConverter extends AbstractModelConverter<InboundWorkorderDetail, InboundWorkorderDetailVO, InboundWorkorderDetailDTO> {

}
