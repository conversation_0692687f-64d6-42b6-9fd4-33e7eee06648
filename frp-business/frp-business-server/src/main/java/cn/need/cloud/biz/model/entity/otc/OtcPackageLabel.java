package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.service.base.UploadStringAble;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * OTC包裹标签
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_package_label")
public class OtcPackageLabel extends SuperModel implements UploadStringAble {


    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    /**
     * 面单类型
     */
    @TableField("label_type")
    private String labelType;

    /**
     * label RefNum
     */
    @TableField("label_ref_num")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @TableField("paper_type")
    private String paperType;

    /**
     * label数据类型
     */
    @TableField("label_raw_data")
    private String labelRawData;

    /**
     * c端出货打包id
     */
    @TableField("otc_package_id")
    private Long otcPackageId;

    /**
     * 打印状态
     */
    @TableField("print_status")
    private String printStatus;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 数据类型
     */
    @TableField("raw_data_type")
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    @TableField("file_id_raw_data_type")
    private String fileIdRawDataType;

}
