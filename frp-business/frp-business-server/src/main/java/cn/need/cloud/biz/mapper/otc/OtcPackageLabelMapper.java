package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPackageLabel;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageLabelQuery;
import cn.need.cloud.biz.model.vo.page.OtcPackageLabelPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC包裹标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcPackageLabelMapper extends SuperMapper<OtcPackageLabel> {

    /**
     * 根据条件获取OTC包裹标签列表
     *
     * @param query 查询条件
     * @return OTC包裹标签集合
     */
    default List<OtcPackageLabelPageVO> listByQuery(OtcPackageLabelQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC包裹标签分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC包裹标签集合
     */
    List<OtcPackageLabelPageVO> listByQuery(@Param("qo") OtcPackageLabelQuery query, @Param("page") Page<?> page);
}