package cn.need.cloud.biz.service.feeconfig.impl;

import cn.need.cloud.biz.model.entity.base.FeeConfigAble;
import cn.need.framework.common.core.lang.ObjectUtil;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
public class FeeConfigUtil {

    public static List<Long> getQuoteIds(List<? extends FeeConfigAble> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream()
                .filter(ObjectUtil::isNotEmpty)
                .map(FeeConfigAble::getQuoteId)
                .collect(Collectors.toList());
    }
}
