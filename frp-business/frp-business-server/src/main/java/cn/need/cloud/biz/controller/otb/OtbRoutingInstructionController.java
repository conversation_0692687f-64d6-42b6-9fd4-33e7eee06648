package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbRoutingInstructionConverter;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionCreateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionUpdateParam;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * otb发货指南 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-routing-instruction")
@Validated
@Tag(name = "otb发货指南")
public class OtbRoutingInstructionController extends AbstractRestController<OtbRoutingInstructionService, OtbRoutingInstruction, OtbRoutingInstructionConverter, OtbRoutingInstructionVO> {

    @Operation(summary = "新增otb发货指南", description = "接收otb发货指南的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRoutingInstructionCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam).getId());
    }

    @Operation(summary = "修改otb发货指南", description = "接收otb发货指南的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRoutingInstructionUpdateParam updateParam) {
        service.updateByParam(updateParam);
        // 返回结果
        return success(1);
    }


    @Operation(summary = "已提交过审接口，返回受影响行数", description = "根据数据主键id执行状态变更操作")
    @PostMapping(value = "/committed")
    public Result<Integer> committed(@RequestBody @Parameter(description = "数据主键id", required = true) IdCondition id) {

        Validate.notNull(id.getId(), "The id value cannot be null.");
        return success(service.committed(id.getId()));
    }


    @Operation(summary = "根据id获取otb发货指南详情", description = "根据数据主键id，从数据库中获取其对应的otb发货指南详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbRoutingInstructionVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取otb发货指南详情
        OtbRoutingInstructionVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取otb发货指南详情", description = "根据数据RefNum，从数据库中获取其对应的otb发货指南详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbRoutingInstructionVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取otb发货指南详情
        OtbRoutingInstructionVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取otb发货指南分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的otb发货指南列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbRoutingInstructionPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbRoutingInstructionQuery> search) {

        // 获取otb发货指南分页
        PageData<OtbRoutingInstructionPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "Dashboard CountPreDay", description = "下拉列表")
    @PostMapping(value = "/count-pre-day")
    public Result<List<DropProVO>> countPreDay(@RequestBody @Parameter(description = "查询条件", required = true) OtbRoutingInstructionQuery query) {

        return success(service.countPreDay(query));
    }

    @Operation(summary = "下拉列表", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "数据主键id", required = true) OtbRoutingInstructionQuery query) {

        return success(service.distinctValue(query));
    }
}
