package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.bo.inbound.InboundPutAwayContextBO;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlipDetail;
import cn.need.cloud.biz.model.query.inbound.InboundPutawaySlipDetailQuery;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipDetailVO;
import cn.need.cloud.biz.model.vo.page.InboundPutawaySlipDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 上架详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundPutawaySlipDetailService extends SuperService<InboundPutawaySlipDetail> {

    /**
     * 根据查询条件获取上架详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个上架详情对象的列表(分页)
     */
    List<InboundPutawaySlipDetailPageVO> listByQuery(InboundPutawaySlipDetailQuery query);

    /**
     * 根据查询条件获取上架详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个上架详情对象的列表(分页)
     */
    PageData<InboundPutawaySlipDetailPageVO> pageByQuery(PageSearch<InboundPutawaySlipDetailQuery> search);

    /**
     * 根据ID获取上架详情
     *
     * @param id 上架详情ID
     * @return 返回上架详情VO对象
     */
    InboundPutawaySlipDetailVO detailById(Long id);


    /**
     * 生成上架单详情
     *
     * @param context 上下文信息
     */
    void generatePutAwaySlipDetail(InboundPutAwayContextBO context);

    /**
     * 根据上架单ID获取上架单详情
     * @param unloadSlipIdList 上架单ID列表
     * @return 上架单详情列表
     */
    List<InboundPutawaySlipDetail> listByUnloadId(Set<Long> unloadSlipIdList);

    /**
     *
     * 根据工单id获取上架单详情
     * @param id 上架单id
     * @return 上架单详情
     */
    List<InboundPutawaySlipDetail> listByWorkorderId(Long id);
}