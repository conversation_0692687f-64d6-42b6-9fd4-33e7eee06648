package cn.need.cloud.biz.converter.inventory;

import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.vo.inventory.InventoryInventoryReserveVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 预留库存 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InventoryInventoryReserveConverter extends AbstractModelConverter<InventoryReserve, InventoryInventoryReserveVO, InventoryInventoryReserveVO> {

}
