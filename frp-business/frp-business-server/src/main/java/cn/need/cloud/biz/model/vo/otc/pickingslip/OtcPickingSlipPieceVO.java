package cn.need.cloud.biz.model.vo.otc.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC拣货单 发货包裹情况 vo对象")
public class OtcPickingSlipPieceVO {

    /**
     * 打包信息
     */
    @Schema(description = "打包信息")
    private List<OtcPickingSlipPiecePackageVO> otcPackageList;

    /**
     * 工单信息
     */
    @Schema(description = "工单信息")
    private OtcPickingSlipPieceWorkOrderVO otcWorkOrder;

}