package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlip;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutAwayInfoVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipByPalletVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipVO;
import cn.need.cloud.biz.model.vo.page.InboundPutawaySlipPageVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 上架 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundPutawaySlipService extends SuperService<InboundPutawaySlip>,
        HeaderPrintedService<InboundPutawaySlip, InboundPutawaySlipService, PrintQuery> {

    /**
     * 根据查询条件获取上架列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个上架对象的列表(分页)
     */
    List<InboundPutawaySlipPageVO> listByQuery(InboundPutawaySlipQuery query);

    /**
     * 根据查询条件获取上架列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个上架对象的列表(分页)
     */
    PageData<InboundPutawaySlipPageVO> pageByQuery(PageSearch<InboundPutawaySlipQuery> search);

    /**
     * 根据ID获取上架
     *
     * @param id 上架ID
     * @return 返回上架VO对象
     */
    InboundPutawaySlipVO detailById(Long id);

    /**
     * 根据上架唯一编码获取上架
     *
     * @param refNum 上架唯一编码
     * @return 返回上架VO对象
     */
    InboundPutawaySlipVO detailByRefNum(String refNum);

    /**
     * 上架
     *
     * @param putAwayInfoVO 上架数量等上架信息
     */
    void putAway(InboundPutAwayInfoVO putAwayInfoVO);

    /**
     * 打托上架
     *
     * @param inboundPutawaySlipByPalletVO 上架信息
     */
    void putAwayByPallet(InboundPutawaySlipByPalletVO inboundPutawaySlipByPalletVO);

    /**
     * 生成上架单
     *
     * @param status 上架状态
     * @return 上架单id
     */
    InboundPutawaySlip generatePutAwaySlip(String status, String note, String workOrderRefNum);

    /**
     * 批量更新上架单的状态
     *
     * @param putAwaySlipIdList 上架单id
     * @param status            上架单状态
     */
    void updateStatus(Set<Long> putAwaySlipIdList, String status);

    /**
     * 检查卸货单数量，并更新上架单状态
     *
     * @param inboundUnloadList 卸货单集合
     */
    void checkUnload(List<InboundUnload> inboundUnloadList);
}