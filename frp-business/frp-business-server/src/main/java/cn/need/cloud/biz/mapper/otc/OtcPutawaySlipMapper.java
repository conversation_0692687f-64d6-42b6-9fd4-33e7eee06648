package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlip;
import cn.need.cloud.biz.model.query.otc.putawayslip.OtcPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcPutawaySlipPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC上架单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Mapper
public interface OtcPutawaySlipMapper extends SuperMapper<OtcPutawaySlip> {

    /**
     * 根据条件获取OTC上架单分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC上架单集合
     */
    List<OtcPutawaySlipPageVO> listByQuery(@Param("qoops") OtcPutawaySlipQuery query, @Param("page") Page<?> page);
}