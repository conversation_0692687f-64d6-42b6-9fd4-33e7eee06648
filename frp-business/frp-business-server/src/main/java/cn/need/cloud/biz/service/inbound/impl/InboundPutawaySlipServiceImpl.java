package cn.need.cloud.biz.service.inbound.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundPalletEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundPutAwayStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundUnloadStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundUnloadTypeEnum;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.inbound.InboundPutawaySlipConverter;
import cn.need.cloud.biz.mapper.inbound.InboundPutawaySlipMapper;
import cn.need.cloud.biz.model.bo.inbound.BinLocationDetailContextBO;
import cn.need.cloud.biz.model.bo.inbound.InboundPutAwayContextBO;
import cn.need.cloud.biz.model.bo.inbound.InboundRegularPutAwayLogBO;
import cn.need.cloud.biz.model.entity.inbound.*;
import cn.need.cloud.biz.model.query.inbound.InboundPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutAwayInfoVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutAwayNumVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipByPalletVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipVO;
import cn.need.cloud.biz.model.vo.page.InboundPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundPalletAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundPutAwayAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.inbound.*;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.service.warehouse.PalletTemplateService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 上架 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InboundPutawaySlipServiceImpl extends SuperServiceImpl<InboundPutawaySlipMapper, InboundPutawaySlip> implements InboundPutawaySlipService {

    @Resource
    private InboundPalletService inboundPalletService;
    @Resource
    private InboundPalletDetailService inboundPalletDetailService;
    @Resource
    @Lazy
    private InboundUnloadService inboundUnloadService;
    @Resource
    private BinLocationDetailService binLocationDetailService;
    @Resource
    private InboundPutawaySlipDetailService inboundPutawaySlipDetailService;
    @Resource
    private InboundWorkorderService inboundWorkorderService;
    @Resource
    private AuditShowLogService auditShowLogService;
    @Resource
    private PalletTemplateService palletTemplateService;
    @Resource
    private InboundWorkorderDetailService inboundWorkorderDetailService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    public List<InboundPutawaySlipPageVO> listByQuery(InboundPutawaySlipQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<InboundPutawaySlipPageVO> pageByQuery(PageSearch<InboundPutawaySlipQuery> search) {
        //分页条件
        Page<InboundPutawaySlip> page = Conditions.page(search, entityClass);
        //获取查询条件
        InboundPutawaySlipQuery condition = search.getCondition();
        //填充上架单id
        Set<Long> putAwaySlipIdList = inboundUnloadService.getPutAwaySlipId(condition.getInboundWorkOrderId());
        condition.setIdList(putAwaySlipIdList.stream().toList());
        //获取分页列表
        List<InboundPutawaySlipPageVO> dataList = mapper.listByQuery(condition, page);
        if (ObjectUtil.isEmpty(dataList)) {
            return new PageData<>(Lists.arrayList(), page);
        }
        //获取上架单id
        Set<Long> putAwaySlipIdSet = dataList
                .stream()
                .map(InboundPutawaySlipPageVO::getId)
                .collect(Collectors.toSet());
        //获取卸货单
        Map<Long, List<InboundUnload>> inboundUnloadMap = inboundUnloadService.getMap(putAwaySlipIdSet);
        //遍历上架单分页列表
        dataList.forEach(item -> {
            List<InboundUnload> inboundUnloadList = inboundUnloadMap.get(item.getId());
            //判空
            if (ObjectUtil.isNotEmpty(inboundUnloadList)) {
                List<InboundUnload> list = inboundUnloadList
                        .stream()
                        .filter(obj -> !ObjectUtil.equal(obj.getQty(), obj.getPalletQty() + obj.getRegularPutawayQty()))
                        .toList();
                //还存在可上架回滚数量则不禁用按钮
                item.setFlag(ObjectUtil.isNotEmpty(list));
            }
        });
        //填充仓库基本信息
        WarehouseCacheUtil.filledWarehouse(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public InboundPutawaySlipVO detailById(Long id) {
        InboundPutawaySlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InboundPutawaySlip");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InboundPutawaySlip", id));
        }
        return buildInboundPutawaySlipVO(entity);
    }

    @Override
    public InboundPutawaySlipVO detailByRefNum(String refNum) {
        InboundPutawaySlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in InboundPutawaySlip");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InboundPutawaySlip", "refNum", refNum));
        }
        return buildInboundPutawaySlipVO(entity);
    }

    /**
     * 上架
     * 该方法用于上架，传入库位id，仓库id，上架数量，入库卸货单id，上架单id
     *
     * @param param 上架信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void putAway(InboundPutAwayInfoVO param) {
        //校验上架库位
        dataValidBinLocation(param.getBinLocationId(), param.getProductVersionId(), param.getProductId());
        //构建上下文对象
        InboundPutAwayContextBO context = new InboundPutAwayContextBO();
        BinLocationDetailContextBO contextBO = new BinLocationDetailContextBO();
        context.setPutAwayParam(param);
        //获取未上架前卸货单
        context.setBeforeInboundUnloadList(inboundUnloadService.listByPutAwaySlipId(param.getPutAwaySlipId()));
        //常规上架单校验
        putAwayValid(context);
        //生成库存详情
        List<InboundPutawaySlipDetail> putAwaySlipDetailList = BeanUtil.copyNew(param.getInboundPutAwayNumList(), InboundPutawaySlipDetail.class);
        RedissonKit.getInstance().lock(param.getBinLocationId() + "" + param.getProductVersionId(), lock -> {
            InboundUnload inboundUnload = context.getInboundUnloadList().stream().findFirst().orElse(new InboundUnload());
            BeanUtil.copy(param, contextBO);
            contextBO.setInStockQty(getPutAwayQty(param));
            contextBO.setInboundWorkOrderId(inboundUnload.getInboundWorkorderId());
            binLocationDetailService.generateBinLocationDetail(contextBO);
        });
        //填充工单信息
        fillPutAwaySlipDetailList(param, putAwaySlipDetailList, context.getInboundUnloadMap(), contextBO.getBinLocationDetailId());
        //持久化上架单详情
        inboundPutawaySlipDetailService.insertBatch(putAwaySlipDetailList);
        //持久化卸货单
        inboundUnloadService.updateBatch(context.getInboundUnloadList());

        saveLog(param, context);
        //更新上架单状态
        updatePutAwaySlipStatus(context.getBeforeInboundUnloadList(), context.getInboundPutawaySlip());
        //回填工单详情已上架数量
        inboundWorkorderDetailService.fillFinishQty(context.getInboundUnloadList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void putAwayByPallet(InboundPutawaySlipByPalletVO param) {

        BinLocationDetailContextBO contextBO = new BinLocationDetailContextBO();
        InboundPutAwayContextBO context = new InboundPutAwayContextBO();
        context.setParam(param);
        //数据校验
        putAwayByPalletValid(context);
        //获取打托单
        InboundPallet inboundPallet = context.getInboundPallet();
        //获取打托单详情
        InboundPalletDetail inboundPalletDetail = context.getInboundPalletDetailList()
                .stream()
                .findFirst()
                .orElse(new InboundPalletDetail());
        //库位校验
        dataValidBinLocation(param.getBinLocationId(), inboundPalletDetail.getProductVersionId(), inboundPalletDetail.getProductId());
        //校验打托单状态
        Validate.isTrue(
                StringUtil.equals(inboundPallet.getPalletStatus(), InboundPalletEnum.NEW.getStatus()),
                StringUtil.format(ErrorMessages.STATUS_INVALID_OPERATION, "PutAwaySlip", "InboundPallet", inboundPallet.getPalletStatus())
        );
        //获取打托详情
        List<InboundPalletDetail> inboundPalletDetailList = context.getInboundPalletDetailList();
        Validate.notEmpty(inboundPalletDetailList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "pallet details"));
        //更新库位详情
        RedissonKit.getInstance().lock(param.getBinLocationId() + "" + inboundPallet.getProductVersionId(), lock -> {
            //填充产品上架上下文信息
            filContextBO(param, inboundPalletDetailList, contextBO);
            binLocationDetailService.generateBinLocationDetail(contextBO);
        });
        //填充打托单相关信息
        fillPalletInfo(param, inboundPallet, inboundPalletDetailList, contextBO);
        //持久化打托单
        inboundPalletService.update(inboundPallet);
        //生成上架单详情
        inboundPutawaySlipDetailService.generatePutAwaySlipDetail(context);
        //更新打托单详情
        inboundPalletDetailService.updateBatch(inboundPalletDetailList);
        //获取上架单
        InboundPutawaySlip inboundPutawaySlip = getById(context.getInboundPutawaySlipId());
        //记录日志
        InboundPutAwayAuditLogHelper.recordLog(param, inboundPallet, inboundPutawaySlip, context);
        //更新上架单状态
        updatePutAwaySlipStatus(context.getBeforeInboundUnloadList(), inboundPutawaySlip);
        //回填工单详情已上架数量
        inboundWorkorderDetailService.fillFinishQty(context.getInboundUnloadList());
    }

    @Override
    public InboundPutawaySlip generatePutAwaySlip(String status, String note, String workOrderRefNum) {
        //构建上架单持久化实体
        InboundPutawaySlip inboundPutawaySlip = buildPutAwaySlip(status, note);
        //持久化上架单
        super.insert(inboundPutawaySlip);
        // 记录上架单日志
        InboundPutAwayAuditLogHelper.recordLog(
                inboundPutawaySlip,
                BaseTypeLogEnum.OPERATION.getType(),
                null,
                StringUtil.format("Create by {}", workOrderRefNum)
        );
        //返回上架单id
        return inboundPutawaySlip;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkUnload(List<InboundUnload> inboundUnloadList) {
        //获取上架单id
        Long putAwaySlipId = inboundUnloadList.stream().findFirst().orElse(new InboundUnload()).getPutawaySlipId();
        //获取该上架单下所有卸货单
        List<InboundUnload> list = inboundUnloadService.listByPutAwaySlipId(putAwaySlipId);
        //校验是否为打托卸货
        boolean palletFlag = inboundUnloadList
                .stream()
                .allMatch(item -> StringUtil.equals(item.getInboundUnloadType(), InboundUnloadTypeEnum.PALLET_UNLOAD.getType()));

        //判断是否为打托卸货
        if (palletFlag) {
            //获取上架单信息
            this.updatePutAwaySlip(putAwaySlipId, InboundPutAwayStatusEnum.CANCELLED.getStatus());
            return;
        }

        //常规卸货后在全部打托的情况
        if (ObjectUtil.isEmpty(list)) {
            this.updatePutAwaySlip(putAwaySlipId, InboundPutAwayStatusEnum.CANCELLED.getStatus());
            return;
        }

        //常规卸货回滚部分打托，可上架部分全部回滚完的情况
        long sum = list.stream()
                .mapToLong(item -> item.getQty() - item.getPalletPutawayQty() - item.getRegularPutawayQty())
                .sum();
        if (ObjectUtil.equal(sum, 0L)) {
            this.updatePutAwaySlip(putAwaySlipId, InboundPutAwayStatusEnum.PROCESSED.getStatus());
        }

        //常规卸货回滚部分打托，可上架部分全部回滚完的情况
        long total = list.stream()
                .mapToLong(item -> item.getPalletQty() + item.getRegularPutawayQty())
                .sum();
        if (ObjectUtil.equal(total, 0L)) {
            //常规卸货回滚部分打托，回滚之后没有上架部分
            this.updatePutAwaySlip(putAwaySlipId, InboundPutAwayStatusEnum.NEW.getStatus());
        }


    }

    @Override
    public void updateStatus(Set<Long> putAwaySlipIdList, String status) {
        //判空
        if (ObjectUtil.isEmpty(putAwaySlipIdList)) {
            return;
        }
        //获取上架单集合
        List<InboundPutawaySlip> inboundPutawaySlipList = super.listByIds(putAwaySlipIdList);
        //更新上架单状态
        inboundPutawaySlipList.forEach(item -> item.setInboundPutawaySlipStatus(status));
        //持久化数据库
        super.updateBatch(inboundPutawaySlipList);
        //记录日志
        inboundPutawaySlipList.forEach(InboundPutAwayAuditLogHelper::recordLog);
    }

    //////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 打托上架优化
     *
     * @param context 打托上架上下文信息
     */
    private void putAwayByPalletValid(InboundPutAwayContextBO context) {
        //获取上架参数
        InboundPutawaySlipByPalletVO param = context.getParam();
        //判空
        if (ObjectUtil.isEmpty(param)) {
            return;
        }
        //获取打托单详情
        List<InboundPalletDetail> inboundPalletDetailList = inboundPalletDetailService.listByPalletId(param.getPalletId());
        List<Long> inboundPalletIdList = inboundPalletDetailList
                .stream()
                .map(InboundPalletDetail::getInboundUnloadId)
                .distinct()
                .toList();
        //获取打托单详情
        List<InboundUnload> inboundUnloadList = inboundUnloadService.listByIds(inboundPalletIdList);
        //获取上架单id
        InboundUnload inboundUnload = inboundUnloadList.stream().findFirst().orElse(new InboundUnload());
        //获取打托单
        InboundPallet inboundPallet = inboundPalletService.getById(param.getPalletId());
        //加载到上下文信息
        context.setInboundPalletDetailList(inboundPalletDetailList);
        context.setInboundPallet(inboundPallet);
        context.setInboundUnloadList(inboundUnloadList);
        context.setBeforeInboundUnloadList(inboundUnloadService.listByPutAwaySlipId(inboundUnload.getPutawaySlipId()));
        context.setInboundPutawaySlipId(inboundUnload.getPutawaySlipId());
        context.setInboundWorkorder(inboundWorkorderService.getById(inboundUnload.getInboundWorkorderId()));
    }

    /**
     * 更新上架单状态
     *
     * @param beforeInboundUnloadList 未上架前卸货单集合
     * @param inboundPutawaySlip      上架单持久化对象
     */
    private void updatePutAwaySlipStatus(List<InboundUnload> beforeInboundUnloadList, InboundPutawaySlip inboundPutawaySlip) {
        List<InboundUnload> unloadAllList = inboundUnloadService.listByPutAwaySlipId(inboundPutawaySlip.getId());
        //判断之前是否未上架或者打托
        boolean flag = beforeInboundUnloadList
                .stream()
                .anyMatch(item -> ObjectUtil.notEqual(item.getPalletQty() + item.getRegularPutawayQty(), 0));
        if (!flag) {
            //更新上架单状态
            inboundPutawaySlip.setInboundPutawaySlipStatus(InboundPutAwayStatusEnum.PROCESSING.getStatus());
            // 记录上架单日志
            InboundPutAwayAuditLogHelper.recordLog(inboundPutawaySlip);
        }
        //校验数量
        List<InboundUnload> list = unloadAllList
                .stream()
                .filter(item -> ObjectUtil.notEqual(item.getQty(), item.getRegularPutawayQty() + item.getPalletPutawayQty()))
                .toList();
        //判空
        if (ObjectUtil.isEmpty(list)) {
            inboundPutawaySlip.setInboundPutawaySlipStatus(InboundPutAwayStatusEnum.PROCESSED.getStatus());
            // 记录上架单日志
            InboundPutAwayAuditLogHelper.recordLog(inboundPutawaySlip);
        }
        //持久化实体
        mapper.updateById(inboundPutawaySlip);

    }

    /**
     * 校验库位
     *
     * @param binLocationId    库位id
     * @param productVersionId 产品版本id
     */
    private void dataValidBinLocation(Long binLocationId, Long productVersionId, Long productId) {
        BinLocationCache cache = BinLocationCacheUtil.getById(binLocationId);
        Validate.isTrue(!cache.getDefaultFlag(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Cannot use default bin location for put away"));
        //获取产品版本信息
        ProductVersionCache productVersionCache = ProductVersionCacheUtil.getById(productVersionId);
        //获取产品信息
        ProductCache productCache = ProductCacheUtil.getById(productId);
        Validate.notNull(productCache, String.format(ErrorMessages.ENTITY_NOT_FOUND, "Product", productId));
        //校验产品id
        Validate.isTrue(
                ObjectUtil.equals(productVersionCache.getProductId(), productId),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Product version " + productVersionCache.getRefNum() + " does not match the specified product")
        );
        boolean exist = binLocationDetailService.exist(binLocationId, productId, productVersionId);
        //校验库位下是否存在相同产品不同版本
        Validate.isTrue(!exist,
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Different versions of the same product cannot be stocked in the same storage location"));
        //校验库位状态
        BinLocationCheckUtil.checkStatusByBin(binLocationId);

    }

    /**
     * 生成上架单
     * 该方法用于生成上架单
     *
     * @return 上架单集合
     */
    private InboundPutawaySlip buildPutAwaySlip(String status, String note) {
        //生成上架单
        InboundPutawaySlip inboundPutawaySlip = new InboundPutawaySlip();
        //上架单状态
        inboundPutawaySlip.setInboundPutawaySlipStatus(status);
        //生成上架单id
        inboundPutawaySlip.setId(IdWorker.getId());
        //获取当前创建人
        inboundPutawaySlip.setCreateBy(Objects.requireNonNull(Users.getUser()).getId());
        //备注
        inboundPutawaySlip.setNote(note);
        //获取仓库id
        inboundPutawaySlip.setWarehouseId(WarehouseContextHolder.getWarehouseId());
        //上架单打印状态
        inboundPutawaySlip.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        //填充refNum
        inboundPutawaySlip.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.INBOUND_PUT_AWAY_SLIP.getCode(), WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        //返回上架单
        return inboundPutawaySlip;
    }

    /**
     * 更新上架单状态
     * 该方法用于更新上架单信息，传入上架单id
     *
     * @param putAwaySlipId 上架单id
     */
    private void updatePutAwaySlip(Long putAwaySlipId, String status) {
        //获取上架单
        InboundPutawaySlip inboundPutawaySlip = super.getById(putAwaySlipId);
        //填充上架状态
        inboundPutawaySlip.setInboundPutawaySlipStatus(status);
        //持久化数据库
        mapper.updateById(inboundPutawaySlip);
        // 记录上架单日志
        InboundPutAwayAuditLogHelper.recordLog(inboundPutawaySlip);
    }

    /**
     * 获取上架数量
     *
     * @param inboundPutAwayInfoVO 上架信息
     * @return 上架数量
     */
    private int getPutAwayQty(InboundPutAwayInfoVO inboundPutAwayInfoVO) {
        return inboundPutAwayInfoVO.getInboundPutAwayNumList()
                .stream()
                .mapToInt(InboundPutAwayNumVO::getQty)
                .sum();
    }

    /**
     * 填充上架详情
     * 该方法用于填充上架详情，传入 上架信息、上架详情、根据卸货单id映射卸货单对象的map集合、库位详情
     *
     * @param inboundPutAwayInfoVO 上架信息
     * @param putawaySlipDetails   上架详情
     * @param inboundUnloadMap     根据卸货单id映射卸货单对象
     * @param binLocationDetailId  库位详情id
     */
    private void fillPutAwaySlipDetailList(InboundPutAwayInfoVO inboundPutAwayInfoVO, List<InboundPutawaySlipDetail> putawaySlipDetails, Map<Long, InboundUnload> inboundUnloadMap, Long binLocationDetailId) {
        putawaySlipDetails.forEach(item -> {
            //获取卸货单
            InboundUnload inboundUnload = inboundUnloadMap.get(item.getInboundUnloadId());
            //填充上架单id
            item.setInboundPutawaySlipId(inboundPutAwayInfoVO.getPutAwaySlipId());
            //填充入库工单id
            item.setInboundWorkorderId(inboundUnload.getInboundWorkorderId());
            //填充产品头id
            item.setProductId(inboundPutAwayInfoVO.getProductId());
            //填充版本产品id
            item.setProductVersionId(inboundPutAwayInfoVO.getProductVersionId());
            //填充入库工单详情id
            item.setInboundWorkorderDetailId(inboundUnload.getInboundWorkorderDetailId());
            //填充库位详情id
            item.setBinLocationDetailId(binLocationDetailId);
            //填充上架库位id
            item.setBinLocationId(inboundPutAwayInfoVO.getBinLocationId());
            //填充单独上架数量
            inboundUnload.setRegularPutawayQty(inboundUnload.getRegularPutawayQty() + item.getQty());
            //校验卸货单数量 palletQty+regularPutAwaySlipQty=qty
            if (ObjectUtil.equal(inboundUnload.getPalletQty() + inboundUnload.getRegularPutawayQty(), inboundUnload.getQty())) {
                inboundUnload.setInboundUnloadStatus(InboundUnloadStatusEnum.ALL_ASSIGNED.getStatus());
            }
            //校验卸货单数量 regularPutAwaySlipQty+palletPurAwaySlipQty=qty
            if (ObjectUtil.equal(inboundUnload.getRegularPutawayQty() + inboundUnload.getPalletPutawayQty(), inboundUnload.getQty())) {
                inboundUnload.setInboundUnloadStatus(InboundUnloadStatusEnum.PROCESSED.getStatus());
            }
        });
    }

    /**
     * 构建上架VO对象
     *
     * @param entity 上架对象
     * @return 返回包含详细信息的上架VO对象
     */
    private InboundPutawaySlipVO buildInboundPutawaySlipVO(InboundPutawaySlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的上架VO对象
        InboundPutawaySlipVO inboundPutawaySlipVO = Converters.get(InboundPutawaySlipConverter.class).toVO(entity);
        //获取仓库基本信息
        WarehouseCacheUtil.filledWarehouse(inboundPutawaySlipVO);
        //获取卸货单详情
        List<InboundUnload> inboundUnloadList = inboundUnloadService.listByPutAwaySlipId(entity.getId());
        //填充卸货单单详情
        inboundUnloadService.fillUnload(inboundPutawaySlipVO, inboundUnloadList);
        //填充打托模板
        fillPalletTemplate(inboundUnloadList, inboundPutawaySlipVO);
        return inboundPutawaySlipVO;
    }

    /**
     * 填充打托模板
     *
     * @param inboundUnloadList    卸货单信息
     * @param inboundPutawaySlipVO 上架单返回vo
     */
    private void fillPalletTemplate(List<InboundUnload> inboundUnloadList, InboundPutawaySlipVO inboundPutawaySlipVO) {
        //获取版本产品id
        List<Long> productVersionIdList = inboundUnloadList
                .stream()
                .map(InboundUnload::getProductVersionId)
                .distinct()
                .toList();
        //判空
        if (ObjectUtil.isEmpty(productVersionIdList)) {
            return;
        }
        //根据id映射产品默认打托模板信息
        Map<Long, PalletTemplateVO> palletTemplateMap = ObjectUtil.toMap(palletTemplateService.listByProductId(productVersionIdList), PalletTemplateVO::getId);
        //填充模板
        inboundPutawaySlipVO.setPalletTemplateVO(palletTemplateMap.get(productVersionIdList.get(0)));
    }

    /**
     * 上架校验
     *
     * @param context 上下文
     */
    private void putAwayValid(InboundPutAwayContextBO context) {
        //获取前端参数
        InboundPutAwayInfoVO param = context.getPutAwayParam();
        //获取上架单
        InboundPutawaySlip inboundPutawaySlip = getById(param.getPutAwaySlipId());
        boolean flag = StringUtil.equals(inboundPutawaySlip.getInboundPutawaySlipStatus(), InboundPutAwayStatusEnum.CANCELLED.getStatus());
        Validate.isTrue(!flag,
                String.format(ErrorMessages.STATUS_INVALID_OPERATION, "process", "InboundPutawaySlip", "CANCELED"));
        context.setInboundPutawaySlip(inboundPutawaySlip);
        //校验状态
        List<String> list = Lists.arrayList(
                InboundPutAwayStatusEnum.NEW.getStatus(),
                InboundPutAwayStatusEnum.PROCESSING.getStatus()
        );
        Validate.isTrue(list.contains(inboundPutawaySlip.getInboundPutawaySlipStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "InboundPutawaySlip", "NEW or PROCESSING", inboundPutawaySlip.getInboundPutawaySlipStatus()));
        //初始化数据
        List<InboundPutAwayNumVO> inboundPutAwayNumList = param.getInboundPutAwayNumList();
        //卸货单id
        List<Long> inboundUnloadIds = inboundPutAwayNumList.stream().map(InboundPutAwayNumVO::getInboundUnloadId).toList();
        //获取卸货单
        List<InboundUnload> inboundUnloadList = inboundUnloadService.listByIds(inboundUnloadIds);
        //根据卸货单id映射卸货单
        Map<Long, InboundUnload> inboundUnloadMap = ObjectUtil.toMap(inboundUnloadList, InboundUnload::getId);
        //校验上架数量
        inboundPutAwayNumList.forEach(item -> {
            InboundUnload inboundUnload = inboundUnloadMap.get(item.getInboundUnloadId());
            //单独上架数量
            Integer regularPutawayQty = inboundUnload.getRegularPutawayQty();
            //当前单独上架数量
            Integer currentRegularPutAwayQty = item.getQty();
            //需要单独上架数量
            int needRegularPutAwayQty = inboundUnload.getQty() - inboundUnload.getPalletQty() - regularPutawayQty;
            //单独上架数量校验
            Validate.isTrue(currentRegularPutAwayQty <= needRegularPutAwayQty,
                    String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Current put away quantity (" + currentRegularPutAwayQty + ") cannot exceed available quantity (" + needRegularPutAwayQty + ")"));
        });
        //获取一个卸货单
        InboundUnload inboundUnload = inboundUnloadList.stream().findFirst().orElse(new InboundUnload());
        //获取对应工单信息
        context.setInboundWorkorder(inboundWorkorderService.getById(inboundUnload.getInboundWorkorderId()));
        //加载上下文
        context.setInboundUnloadList(inboundUnloadList);
        context.setInboundUnloadMap(inboundUnloadMap);
    }

    /**
     * 上下文对象
     *
     * @param param                   上下文参数
     * @param inboundPalletDetailList 打托详情
     * @param contextBO               上下文对象
     */
    private void filContextBO(InboundPutawaySlipByPalletVO param, List<InboundPalletDetail> inboundPalletDetailList, BinLocationDetailContextBO contextBO) {
        InboundPalletDetail inboundPalletDetail = inboundPalletDetailList.stream().findFirst().orElse(new InboundPalletDetail());
        BeanUtil.copy(inboundPalletDetail, contextBO);
        //填充上架数量
        contextBO.setInStockQty(inboundPalletDetailList
                .stream()
                .mapToInt(InboundPalletDetail::getQty)
                .sum());
        //填充上架库位id
        contextBO.setBinLocationId(param.getBinLocationId());
        //填充工单id
        contextBO.setInboundWorkOrderId(inboundPalletDetail.getInboundWorkorderId());
    }

    /**
     * 获取打托单详情
     *
     * @param param 请求参数
     */
    private void fillPalletInfo(InboundPutawaySlipByPalletVO param, InboundPallet inboundPallet, List<InboundPalletDetail> inboundPalletDetailList, BinLocationDetailContextBO contextBO) {
        //更新打托单状态及库位信息
        inboundPallet.setPalletStatus(InboundPalletEnum.PUT_AWAY.getStatus());
        inboundPallet.setBinLocationId(param.getBinLocationId());
        // 打托单日志
        InboundPalletAuditLogHelper.recordLog(inboundPallet);
        //填充相关信息
        inboundPalletDetailList.forEach(item -> {
            item.setBinLocationId(inboundPallet.getBinLocationId());
            item.setProductId(inboundPallet.getProductId());
            item.setProductVersionId(inboundPallet.getProductVersionId());
            item.setBinLocationDetailId(contextBO.getBinLocationDetailId());
        });
    }

    /**
     * 保存日志
     *
     * @param param   请求参数
     * @param context 上下文
     */
    private void saveLog(InboundPutAwayInfoVO param, InboundPutAwayContextBO context) {
        // 获取产品信息
        ProductVersionCache productVersionCache = ProductVersionCacheUtil.getById(param.getProductVersionId());
        //获取库位信息
        BinLocationCache binLocationCache = BinLocationCacheUtil.getById(param.getBinLocationId());
        param.getInboundPutAwayNumList().forEach(item -> {

            //todo: 日志
            //拼接产品信息
            InboundRegularPutAwayLogBO regularPutAwayLogBO = new InboundRegularPutAwayLogBO(
                    productVersionCache.toLog(),
                    item.getQty(),
                    binLocationCache.getLocationName()
            );

            //记录上架日志
            InboundPutAwayAuditLogHelper.recordLog(
                    context.getInboundPutawaySlip(),
                    ShowLogEnum.REGULAR_PUTAWAY.getStatus(),
                    BaseTypeLogEnum.OPERATION.getType(),
                    item.getNote(),
                    JsonUtil.toJson(regularPutAwayLogBO)
            );
            InboundWorkOrderAuditLogHelper.recordLog(
                    context.getInboundWorkorder(),
                    ShowLogEnum.REGULAR_PUTAWAY.getStatus(),
                    BaseTypeLogEnum.OPERATION.getType(),
                    item.getNote(),
                    JsonUtil.toJson(regularPutAwayLogBO)
            );
        });
    }

}
