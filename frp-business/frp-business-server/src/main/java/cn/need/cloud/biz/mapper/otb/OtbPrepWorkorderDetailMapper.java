package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderDetail;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkorderDetailQuery;
import cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTB预提工单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPrepWorkorderDetailMapper extends SuperMapper<OtbPrepWorkorderDetail> {

    /**
     * 根据条件获取OTB预提工单详情列表
     *
     * @param query 查询条件
     * @return OTB预提工单详情集合
     */
    default List<OtbPrepWorkorderDetailPageVO> listByQuery(OtbPrepWorkorderDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTB预提工单详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTB预提工单详情集合
     */
    List<OtbPrepWorkorderDetailPageVO> listByQuery(@Param("qo") OtbPrepWorkorderDetailQuery query, @Param("page") Page<?> page);
}