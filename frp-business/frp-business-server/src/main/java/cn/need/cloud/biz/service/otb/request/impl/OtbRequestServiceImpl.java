package cn.need.cloud.biz.service.otb.request.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseShowLogStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbAuditTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbRequestShipmentStatusEnum;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.otb.OtbRequestConverter;
import cn.need.cloud.biz.mapper.otb.OtbRequestMapper;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.param.otb.create.request.OtbRequestCreateParam;
import cn.need.cloud.biz.model.param.otb.create.workorder.OtbWorkorderCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestAuditParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestUpdateParam;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbRequestAuditLogHelper;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.service.log.impl.AuditShowLogServiceImpl;
import cn.need.cloud.biz.service.otb.request.OtbRequestDetailService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderBuildService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import cn.need.framework.starter.warehouse.util.WarehouseUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB请求 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbRequestServiceImpl extends SuperServiceImpl<OtbRequestMapper, OtbRequest> implements OtbRequestService {


    @Resource
    private TenantCacheService tenantCacheService;

    @Resource
    private OtbRequestDetailService otbRequestDetailService;

    @Resource
    @Lazy
    private OtbRequestService otbRequestService;

    @Resource
    @Lazy
    private OtbWorkorderBuildService workorderBuildService;

    @Resource
    private ProductService productService;

    @Resource
    private AuditShowLogService auditShowLogService;

    @Resource
    private FileStringUploadService fileStringUploadService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbRequest insertByParam(OtbRequestCreateParam param) {
        // 检查传入OTB请求参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(param)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "OtbRequest"));
        }
        Long transactionPartnerId = param.getTransactionPartnerId();
        if (ObjectUtil.isEmpty(transactionPartnerId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "transactionPartnerId"));
        }
        //校验产品是否匹配partner
        boolean partnerFlag = productService.isAllProductInPartnerId(transactionPartnerId, param.getDetailList()
                .stream().map(OtbRequestDetailVO::getProductId).toList());
        if (!partnerFlag) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "All products must belong to the same partner"));
        }
        // 检查请求参考编号是否有效或唯一
        checkRequestRefNum(param.getRequestRefNum(), transactionPartnerId);
        // 校验日期
        checkShipWindow(param.getShipWindowStart(), param.getShipWindowEnd());
        
        //Check ShipType
        switch (param.getShipType()) {
            case BY_WAREHOUSE -> {
                if (ObjectUtil.isNotEmpty(param.getRoutingInstructionFile())) {
                    throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "shipType", "BY_WAREHOUSE cannot fill in routingInstructionFile"));
                }
            }
            case BY_REQUEST_ROUTING_INSTRUCTION_FILE -> {
                if (ObjectUtil.isEmpty(param.getRoutingInstructionFile()) ||
                        ObjectUtil.isEmpty(param.getRoutingInstructionFile().getFileType()) ||
                        ObjectUtil.isEmpty(param.getRoutingInstructionFile().getFileData()) ||
                        ObjectUtil.isEmpty(param.getRoutingInstructionFile().getFileExtension()) ||
                        ObjectUtil.isEmpty(param.getRoutingInstructionFile().getPaperType())) {
                    throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "routingInstructionFile"));
                }
                fileStringUploadService.uploadFrpFile(param.getRoutingInstructionFile());
            }

            default -> throw new IllegalStateException("Unexpected value: " + param.getShipType());
        }

        // 获取OTB请求转换器实例，用于将OTB请求参数对象转换为实体对象
        OtbRequestConverter converter = Converters.get(OtbRequestConverter.class);

        // 将OTB请求参数对象转换为实体对象并初始化
        OtbRequest entity = initOtbRequest(converter.toEntity(param));

        //获取新的OTB请求ID
        long otbRequestId = IdWorker.getId();

        // 在事务中执行操作
        WarehouseUtils.execute(entity.getWarehouseId(), () -> {
            // 新增请求产品详情
            otbRequestDetailService.insertBatchByParam(param, otbRequestId);
            //填充实体
            entity.setId(otbRequestId);
            entity.setOtbRequestStatus(RequestStatusEnum.NEW.getStatus());
            entity.setRequestShipmentStatus(OtbRequestShipmentStatusEnum.NONE.getStatus());

            // 插入OTB请求实体对象到数据库
            super.insert(entity);

            OtbRequestAuditLogHelper.recordLog(entity);
        });


        // 返回OTB请求ID
        return entity;
    }

    /**
     * 检查日期
     *
     * @param shipWindowStart 发货窗口开始时间
     * @param shipWindowEnd   发货窗口结束时间
     */
    private void checkShipWindow(LocalDateTime shipWindowStart, LocalDateTime shipWindowEnd) {
        if (ObjectUtil.isEmpty(shipWindowStart)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "shipWindowStart"));
        }
        if (ObjectUtil.isEmpty(shipWindowEnd)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "shipWindowEnd"));
        }
        if (shipWindowStart.isAfter(shipWindowEnd)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Ship window start date must be before end date"));
        }
    }

    /**
     * 检查请求参考编号是否有效或唯一
     *
     * @param requestRefNum        请求的参考编号，用于标识一个特定的请求
     * @param transactionPartnerId 交易伙伴的ID，表示与请求相关联的特定交易伙伴
     */
    private void checkRequestRefNum(String requestRefNum, Long transactionPartnerId) {
        //忽略仓库
        boolean ignore = WarehouseContextHolder.isIgnore();
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        boolean flag = true;
        try {
            flag = lambdaQuery().eq(OtbRequest::getTransactionPartnerId, transactionPartnerId).eq(OtbRequest::getRequestRefNum, requestRefNum).count() > 0;
        } catch (Exception e) {
            log.error("checkRequestRefNum error", e);
        } finally {
            WarehouseContextHolder.setIgnore(ignore);
        }
        if (flag) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "OtbRequest", "requestRefNum", requestRefNum));
        }
    }


    /**
     * 初始化OTB请求对象
     * 此方法用于设置OTB请求对象的必要参数，确保其处于有效状态
     *
     * @param entity OTB请求对象，不应为空
     * @return 返回初始化后的OTB请求
     * @throws BusinessException 如果传入的OTB请求为空，则抛出此异常
     */
    private OtbRequest initOtbRequest(OtbRequest entity) {

        // 检查传入的OTB请求对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "OtbRequest"));
        }

        // 生成RefNum
        entity.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.OTB_REQUEST.code, WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbRequest updateByParam(OtbRequestUpdateParam updateParam) {
        // 检查传入OTB请求参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "updateParam or id"));
        }

        OtbRequest otbRequest = super.getById(updateParam.getId());

        //校验参数
        if (ObjectUtil.isEmpty(otbRequest)) {
            // throw new BusinessException("id: " + updateParam.getId() + " not found in OtbRequest");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbRequest", updateParam.getId()));
        }
        //校验输入状态
        if (!otbRequest.getOtbRequestStatus().equals(RequestStatusEnum.NEW.getStatus()) && !otbRequest.getOtbRequestStatus().equals(RequestStatusEnum.REJECTED.getStatus())) {
            // throw new BusinessException("Only support New/Rejected status");
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "OtbRequest", "NEW or REJECTED", otbRequest.getOtbRequestStatus()));
        }

        //校验产品是否匹配partner
        boolean partnerFlag = productService.isAllProductInPartnerId(updateParam.getTransactionPartnerId(), updateParam.getDetailList()
                .stream().map(OtbRequestDetailVO::getProductId).toList());
        if (!partnerFlag) {
            // throw new BusinessException("Product must in this Partner");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "All products must belong to the same partner"));
        }
        if (!otbRequest.getRequestRefNum().equals(updateParam.getRequestRefNum()) || !otbRequest.getTransactionPartnerId().equals(updateParam.getTransactionPartnerId())) {
            checkRequestRefNum(updateParam.getRequestRefNum(), updateParam.getTransactionPartnerId());
        }


        //校验日期
        checkShipWindow(updateParam.getShipWindowStart(), updateParam.getShipWindowEnd());

        //获取更新前的Vo
        OtbRequestVO oldVo = detailById(updateParam.getId());

        WarehouseUtils.executeIgnore(() -> {
            //更新详情
            otbRequestDetailService.updateBatchByParam(updateParam);

            // 获取OTB请求转换器实例，用于将OTB请求参数对象转换为实体对象
            OtbRequestConverter converter = Converters.get(OtbRequestConverter.class);

            // 将OTB请求参数对象转换为实体对象
            OtbRequest entity = converter.toEntity(updateParam);
            //
            entity.setWarehouseId(updateParam.getWarehouseId());
            // 执行更新OTB请求操作
            super.update(entity);

            //获取更新后的Vo
            OtbRequestVO newVo = detailById(entity.getId());
            //记录日志
            OtbRequestAuditLogHelper.recordLog(
                    BeanUtil.copyNew(newVo, OtbRequest.class),
                    BaseShowLogStatusEnum.MODIFIED.getStatus(),
                    BaseTypeLogEnum.OPERATION.getType(),
                    null,
                    ModifyCompareUtil.recordModifyLog(newVo, oldVo)
            );

            return entity;
        });

        return null; // 修复缺少返回语句的问题
    }

    @Override
    public List<OtbRequestPageVO> listByQuery(OtbRequestQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtbRequestPageVO> pageByQuery(PageSearch<OtbRequestQuery> search) {
        Page<OtbRequest> page = Conditions.page(search, entityClass);
        List<OtbRequestPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        dataList.forEach(o -> {
                    //填充Partner
                    if (ObjectUtil.isNotEmpty(tenantCacheService.getById(o.getTransactionPartnerId()))) {
                        o.setTransactionPartnerVO(BeanUtil.copyNew(tenantCacheService.getById(o.getTransactionPartnerId())
                                , BasePartnerVO.class));
                    }
                }
        );
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbRequestVO detailById(Long id) {

        OtbRequestVO otbRequestVO = mapper.detailById(id);
        if (ObjectUtil.isEmpty(otbRequestVO)) {
            // throw new BusinessException("id: " + otbRequestVO.getId() + " not found in OtbRequest");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbRequest", id));
        }
        //填充产品信息
        ProductCacheUtil.filledProduct(otbRequestVO.getDetailList());

        //填充仓库信息
        WarehouseCacheUtil.filledWarehouse(otbRequestVO);

        //填充Partner
        otbRequestVO.setTransactionPartnerVO(BeanUtil.copyNew(tenantCacheService.getById(otbRequestVO.getTransactionPartnerId())
                , BasePartnerVO.class));

        return otbRequestVO;
    }

    @Override
    public OtbRequestVO detailByRefNum(String refNum) {
        OtbRequest entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtbRequest");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbRequest", "refNum", refNum));
        }
        return buildOtbRequestVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(OtbRequestAuditParam param) {
        RedissonKit.getInstance().lock(RedisConstant.OTB_REQUEST_AUDIT_LOCK_PREFIX + param.getId(), lock -> {
            // 根据参数中的ID获取OtbRequest对象
            OtbRequest otbRequest = super.getById(param.getId());
            // 检查获取的结果是否为空，如果为空则抛出业务异常
            if (ObjectUtil.isEmpty(otbRequest)) {
                // throw new BusinessException("otbRequest cannot be empty");
                throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbRequest", param.getId()));
            }
            // 校验输入状态
            // 只有当otbRequest的状态为NEW或REJECTED时，才允许进行后续操作
            if (!otbRequest.getOtbRequestStatus().equals(RequestStatusEnum.NEW.getStatus()) && !otbRequest.getOtbRequestStatus().equals(RequestStatusEnum.REJECTED.getStatus())) {
                // throw new BusinessException("Only support New/Rejected status");
                throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "OtbRequest", "NEW or REJECTED", otbRequest.getOtbRequestStatus()));
            }
            // 如果param的type表示审核未通过，则调用处理审核未通过的方法
            if (OtbAuditTypeEnum.REJECTED.getType().equals(param.getType())) {
                handleRejected(param, otbRequest);
            }
            // 如果param的type表示审核通过，则调用处理审核通过的方法
            if (OtbAuditTypeEnum.APPROVED.getType().equals(param.getType())) {
                handleApproved(param, otbRequest);
            }
        });

    }

    /**
     * 处理审批通过
     *
     * @param param      审批参数
     * @param otbRequest otb请求
     */
    private void handleApproved(OtbRequestAuditParam param, OtbRequest otbRequest) {
        // 创建工单创建参数对象
        OtbWorkorderCreateParam workorderCreateParam = new OtbWorkorderCreateParam();
        // 设置工单创建参数中的OTB请求ID
        workorderCreateParam.setOtbRequestId(param.getId());
        // 尝试使用工单创建参数插入新工单
        try {
            workorderBuildService.insertByParam(workorderCreateParam);
        } catch (Exception e) {
            // 如果插入工单失败，更新OTB请求状态为拒绝
            otbRequest.setOtbRequestStatus(RequestStatusEnum.REJECTED.getStatus());
            // 更新OTB请求信息，记录审批失败原因
            otbRequestService.updateByApproveFail(otbRequest, e.getMessage(), param.getNote());
            // 抛出业务异常，包含失败的请求ID和失败原因
            // throw new BusinessException("Failure to pass the review: requestRefNum=" + otbRequest.getRequestRefNum() + ", Reason for failure: " + e.getMessage());
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Request approval failed for requestRefNum=" + otbRequest.getRequestRefNum() + ". Reason: " + e.getMessage()));
        }
        // 如果插入工单成功，更新OTB请求状态为批准
        otbRequest.setOtbRequestStatus(RequestStatusEnum.APPROVED.getStatus());
        //记录日志
        OtbRequestAuditLogHelper.recordLog(
                otbRequest,
                BaseTypeLogEnum.STATUS.getType(),
                param.getNote(),
                null);
        // 更新OTB请求信息
        super.update(otbRequest);
    }

    /**
     * 处理审批拒绝
     *
     * @param param      审批参数
     * @param otbRequest otb请求
     */
    private void handleRejected(OtbRequestAuditParam param, OtbRequest otbRequest) {
        // 设置状态为拒绝
        otbRequest.setOtbRequestStatus(RequestStatusEnum.REJECTED.getStatus());
        //记录日志
        OtbRequestAuditLogHelper.recordLog(
                otbRequest,
                BaseTypeLogEnum.STATUS.getType(),
                param.getNote(),
                null
        );
        // 更新 OtbRequest 对象
        super.update(otbRequest);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateByApproveFail(OtbRequest otbRequest, String message, String note) {
        super.update(otbRequest);
        OtbRequestAuditLogHelper.recordLog(
                otbRequest,
                RequestStatusEnum.APPROVED.getStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                note,
                null
        );
        OtbRequestAuditLogHelper.recordLog(
                otbRequest,
                RequestStatusEnum.REJECTED.getStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                null,
                message
        );

        //获取并清空线程日志
        List<AuditShowLog> andClearShowLog = AuditLogHolder.getAndClearShowLog();

        //持久化日志
        auditShowLogService.insertBatch(andClearShowLog);
    }

    @Override
    public Boolean existUnfinishedOrder(Long warehouseId) {
        return WarehouseUtils.execute(warehouseId, () -> {
            List<String> list = Lists.arrayList(RequestStatusEnum.APPROVED.getStatus(),
                    RequestStatusEnum.PROCESSING.getStatus(),
                    RequestStatusEnum.NEW.getStatus(),
                    RequestStatusEnum.LOCKED.getStatus(),
                    RequestStatusEnum.REJECTED.getStatus());
            Long count = lambdaQuery().in(OtbRequest::getOtbRequestStatus, list).count();
            return count > 0;
        });
    }

    @Override
    public List<DropProVO> countPreDay(OtbRequestQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtbRequest.class,
                columnInfos -> mapper.dropProList(columnInfos, query)
        );
    }

    @Override
    public Map<Long, OtbRequest> getRefNum(Collection<Long> requestIdList) {
        if (ObjectUtil.isEmpty(requestIdList)) {
            return Collections.emptyMap();
        }
        //获取请求单集合
        List<OtbRequest> list = lambdaQuery()
                .in(OtbRequest::getId, requestIdList)
                .select(OtbRequest::getRequestRefNum, OtbRequest::getRefNum, OtbRequest::getId)
                .list();
        //返回请求单id映射请求对象关系
        return ObjectUtil.toMap(list, OtbRequest::getId);
    }

    @Override
    public Set<Long> getRequestId(Set<String> otbRequestRefNumList) {
        //判空
        if (ObjectUtil.isEmpty(otbRequestRefNumList)) {
            return CollUtil.newHashSet();
        }
        //获取请求单id
        Set<Long> requestIdList = lambdaQuery()
                .in(OtbRequest::getRequestRefNum, otbRequestRefNumList)
                .list()
                .stream()
                .map(OtbRequest::getId)
                .collect(Collectors.toSet());
        //返回请求单id
        return ObjectUtil.emptyToDefault(requestIdList, CollUtil.newHashSet(-1L));
    }

    @Override
    public Set<Long> listByRequestRefNum(Set<String> requestOfRequestRefNumList) {
        //判空
        if (ObjectUtil.isEmpty(requestOfRequestRefNumList)) {
            return CollUtil.newHashSet();
        }
        //请求单id
        Set<Long> requestIdList = lambdaQuery()
                .in(OtbRequest::getRequestRefNum, requestOfRequestRefNumList)
                .select(OtbRequest::getId)
                .list()
                .stream()
                .map(OtbRequest::getId)
                .collect(Collectors.toSet());
        return ObjectUtil.emptyToDefault(requestIdList, CollUtil.newHashSet(-1L));
    }

    @Override
    public OtbRequest getRefNum(Long otbRequestId) {
        return lambdaQuery()
                .eq(OtbRequest::getId, otbRequestId)
                .one();
    }

    /**
     * 构建OTB请求VO对象
     *
     * @param entity OTB请求对象
     * @return 返回包含详细信息的OTB请求VO对象
     */
    private OtbRequestVO buildOtbRequestVO(OtbRequest entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB请求VO对象
        return Converters.get(OtbRequestConverter.class).toVO(entity);
    }


}
