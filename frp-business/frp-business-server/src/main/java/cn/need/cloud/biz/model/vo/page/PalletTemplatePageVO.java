package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 打托模板 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "打托模板 vo对象")
public class PalletTemplatePageVO extends BaseSuperVO {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 每层几个箱子
     */
    @Schema(description = "每层几个箱子")
    private Integer cartonPerLayer;

    /**
     * 一共多少层
     */
    @Schema(description = "一共多少层")
    private Integer layersCount;

    /**
     * 多了几个箱子
     */
    @Schema(description = "多了几个箱子")
    private Integer extCarton;

    /**
     * 每箱几个产品
     */
    @Schema(description = "每箱几个产品")
    private Integer pcsPerCarton;

    /**
     * 一共几个箱子 CartonPerLayer * LayersCount + ExtCarton
     */
    @Schema(description = "一共多少箱子")
    private Integer cartonCount;

    /**
     * 一共多少产品   PcsPerCarton * CartonCount
     */
    @Schema(description = "一共多少产品")
    private Integer pcsCount;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 产品版本基本信息
     */
    @Schema(description = "产品版本基本信息")
    private BaseProductVersionVO baseProductVersionVO;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 是否设置默认
     */
    @Schema(description = "是否设置默认")
    private Boolean setDefaultFlag;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

}