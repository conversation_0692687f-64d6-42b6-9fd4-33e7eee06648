package cn.need.cloud.biz.model.vo.product;

import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductAware;
import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * VO对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = " VO对象")
public class ProductHazmatVO extends SuperVO implements BaseFullProductAware {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction")
    private String packageInstruction;

    /**
     * productId
     */
    @Schema(description = "productId")
    private Long productId;

    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass")
    private String transportationRegulatoryClass;

    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId")
    private String unRegulatoryId;

    /**
     * versionRefNum
     */
    @Schema(description = "versionRefNum")
    private String versionRefNum;


}