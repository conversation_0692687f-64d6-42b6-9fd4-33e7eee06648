package cn.need.cloud.biz.model.bo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "产品日志 bo对象")
public class ProductLogBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一标识码")
    private String refNum;

    @Schema(description = "供应商sku")
    private String supplierSku;

    @Schema(description = "产品upc")
    private String upc;

    @Schema(description = "产品版本")
    private Integer versionRefNum;

}
