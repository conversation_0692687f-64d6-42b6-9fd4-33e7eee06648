package cn.need.cloud.biz.service.helper.auditshowlog.otc;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 请求单日志记录
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public class OtcRequestAuditLogHelper {
    private OtcRequestAuditLogHelper() {
    }

    /**
     * 记录日志
     *
     * @param requestList 请求
     */
    public static void recordLog(List<OtcRequest> requestList) {
        requestList.forEach(request -> recordLog(request, request.getOtcRequestStatus(), BaseTypeLogEnum.STATUS.getType(), null, null));
    }

    /**
     * 记录日志
     *
     * @param request 请求
     */
    public static void recordLog(OtcRequest request) {
        recordLog(
                request,
                request.getOtcRequestStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                null,
                null
        );
    }

    /**
     * 记录日志
     *
     * @param request     请求
     * @param note        Note
     * @param description 描述
     */
    public static void recordLog(OtcRequest request, String description, String note) {
        recordLog(
                request,
                request.getOtcRequestStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                description,
                note
        );
    }

    /**
     * 记录日志
     *
     * @param request     请求
     * @param status      状态
     * @param note        Note
     * @param description 描述
     */
    public static void recordLog(OtcRequest request, String status, String type, String description, String note) {
        AuditShowLog showLog = AuditLogUtil.commonLog(request)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(showLog);
    }


}
