package cn.need.cloud.biz.model.param.otc.update.pickingslip;

import cn.need.cloud.biz.model.param.base.create.PickingSlipUnpickCreateParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "UnPick 对象")
public class OtcPickingSlipUnpickCreateParam extends PickingSlipUnpickCreateParam {

}