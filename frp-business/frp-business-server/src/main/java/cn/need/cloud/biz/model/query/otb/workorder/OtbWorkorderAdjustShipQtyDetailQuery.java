package cn.need.cloud.biz.model.query.otb.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


/**
 * OTB工单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB工单 调整发货数量详情 query对象")
public class OtbWorkorderAdjustShipQtyDetailQuery {

    /**
     * otb工单详情id
     */
    @Schema(description = "otb工单详情id")
    @NotNull(message = "OtbWorkorderDetailId is must not null")
    private Long otbWorkorderDetailId;

    /**
     * 实际发货数量
     */
    @Schema(description = "实际发货数量")
    @NotNull(message = "CanProcessQty is must not null")
    @Min(value = 0, message = "CanProcessQty must be greater than or equal to 0")
    private Integer canProcessQty;

}