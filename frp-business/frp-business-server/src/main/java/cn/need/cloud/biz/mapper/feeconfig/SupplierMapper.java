package cn.need.cloud.biz.mapper.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.Supplier;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuery;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Mapper
public interface SupplierMapper extends SuperMapper<Supplier> {

    /**
     * 根据条件获取供应商信息列表
     *
     * @param query 查询条件
     * @return 供应商信息集合
     */
    default List<SupplierPageVO> listByQuery(@Param("qos") SupplierQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取供应商信息分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 供应商信息集合
     */
    List<SupplierPageVO> listByQuery(@Param("qos") SupplierQuery query, @Param("page") Page<?> page);
}