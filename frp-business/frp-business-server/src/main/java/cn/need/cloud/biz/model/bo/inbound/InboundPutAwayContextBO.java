package cn.need.cloud.biz.model.bo.inbound;

import cn.need.cloud.biz.model.entity.inbound.*;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutAwayInfoVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipByPalletVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 入库上架上下文信息
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "入库上架上下文信息")
public class InboundPutAwayContextBO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 上架参数
     */
    private InboundPutawaySlipByPalletVO param;

    /**
     * 打托单详情信息
     */
    private List<InboundPalletDetail> inboundPalletDetailList;

    /**
     * 打托单信息
     */
    private List<InboundUnload> inboundUnloadList;

    /**
     * 未上架前卸货单信息
     */
    private List<InboundUnload> beforeInboundUnloadList;

    /**
     * 上架单id
     */
    private Long inboundPutawaySlipId;

    /**
     * 工单信息
     */
    private InboundWorkorder inboundWorkorder;

    /**
     * 打托单信息
     */
    private InboundPallet inboundPallet;

    ///////////////////////////////////////常规上架信息///////////////////////////////////////

    /**
     * 上架参数
     */
    private InboundPutAwayInfoVO putAwayParam;

    /**
     * 上架单信息
     */
    private InboundPutawaySlip inboundPutawaySlip;

    /**
     * 上架单详情信息
     */
    private Map<Long, InboundUnload> inboundUnloadMap;

}
