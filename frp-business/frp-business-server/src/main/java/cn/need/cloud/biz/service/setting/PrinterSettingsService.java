package cn.need.cloud.biz.service.setting;

import cn.need.cloud.biz.model.entity.setting.PrinterSettings;
import cn.need.cloud.biz.model.param.setting.create.PrinterSettingsCreateParam;
import cn.need.cloud.biz.model.param.setting.update.PrinterSettingsUpdateParam;
import cn.need.cloud.biz.model.query.setting.PrinterSettingsQuery;
import cn.need.cloud.biz.model.vo.page.PrinterSettingsPageVO;
import cn.need.cloud.biz.model.vo.setting.PrinterSettingsVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 打印设置 service 实现接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */

public interface PrinterSettingsService extends SuperService<PrinterSettings> {
    /**
     * 插入打印机设置信息
     * 该方法使用了参数校验，确保输入数据的合法性
     *
     * @param createParam 打印机设置创建参数对象，包含需要插入的打印机设置信息
     * @return 返回插入操作后生成的打印机设置记录的ID
     */
    Long insertByParam(@Valid PrinterSettingsCreateParam createParam);

    /**
     * 更新打印机设置信息
     *
     * @param updateParam 打印机设置更新参数对象，包含需要更新的打印机设置信息
     * @return 返回更新操作影响的记录数
     */
    Integer updateByParam(PrinterSettingsUpdateParam updateParam);

    /**
     * 根据ID获取打印机设置详情
     *
     * @param id 打印机设置记录的ID
     * @return 返回打印机设置的详细信息对象
     */
    PrinterSettingsVO detailById(Long id);

    /**
     * 分页查询打印机设置信息
     *
     * @param search 包含查询参数和分页信息的搜索对象
     * @return 返回根据查询条件分页后的打印机设置信息列表
     */
    PageData<PrinterSettingsPageVO> pageByQuery(PageSearch<PrinterSettingsQuery> search);

    /**
     * 根据查询条件获取打印机设置列表
     * 与分页查询不同，此方法返回满足条件的所有记录
     *
     * @param query 包含查询参数的查询对象
     * @return 返回根据查询条件筛选后的打印机设置信息列表
     */
    List<PrinterSettingsPageVO> listByQuery(PrinterSettingsQuery query);

    /**
     * 无参数，根据当前登录用户ID返回打印机设置
     *
     * @return 打印机设置 VO对象
     */
    PrinterSettingsVO detailByUserId();
}

