package cn.need.cloud.biz.service.ship;

/**
 * <p>
 * 预请求详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
public interface SsccClientService {
    /**
     * 根据 refNum 获取 ssccNum
     *
     * @param refNum refNum
     * @return ssccNum
     */
    String getSsccNum(String refNum);

    /**
     * 根据 refNum 获取 ssccNum
     *
     * @param refNum refNum
     * @return ssccNum
     */
    String getShortSsccNum(String refNum);
}
