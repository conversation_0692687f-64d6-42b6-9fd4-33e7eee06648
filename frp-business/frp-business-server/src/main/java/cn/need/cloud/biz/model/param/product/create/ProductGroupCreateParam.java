package cn.need.cloud.biz.model.param.product.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品同类 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品同类 vo对象")
public class ProductGroupCreateParam implements Serializable {


    /**
     * 父产品ID
     */
    @Schema(description = "父产品ID")
    private Long parentProductId;

    /**
     * 子产品ID
     */
    @Schema(description = "子产品ID")
    private Long childProductId;

    /**
     * 指令备注
     */
    @Schema(description = "指令备注")
    private String instructionNote;

    /**
     * 回滚指令备注
     */
    @Schema(description = "回滚指令备注")
    private String revertInstructionNote;

    /**
     * 转换组类型
     */
    @Schema(description = "转换组类型")
    private String convertGroupType;
    /**
     * 同类产品版本号
     */
    @Schema(description = "同类产品版本号")
    private Integer groupVersionInt;
}