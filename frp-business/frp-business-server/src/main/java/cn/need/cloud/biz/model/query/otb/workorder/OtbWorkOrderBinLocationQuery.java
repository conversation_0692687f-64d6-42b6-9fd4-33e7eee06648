package cn.need.cloud.biz.model.query.otb.workorder;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * OTC工单仓储位置 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB工单仓储位置 query对象")
public class OtbWorkOrderBinLocationQuery extends SuperQuery {

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到c端工单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "WorkOrder id is not null")
    private Long otbWorkorderId;

}

