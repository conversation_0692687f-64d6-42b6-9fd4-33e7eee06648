package cn.need.cloud.biz.service.helper.auditshowlog.otb;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 请求单日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class OtbRequestAuditLogHelper {

    private OtbRequestAuditLogHelper() {
    }

    public static void recordWithStatus(OtbRequest otbRequest, String status, String description) {
        recordLog(
                otbRequest,
                status,
                BaseTypeLogEnum.STATUS.getType(),
                null,
                description
        );
    }

    /**
     * 构建otb请求单日志对象
     *
     * @param requestList 打托单对象
     */
    public static void recordLog(List<OtbRequest> requestList) {
        requestList.forEach(OtbRequestAuditLogHelper::recordLog);
    }

    public static void recordLog(OtbRequest otbRequest) {
        recordWithStatus(otbRequest, otbRequest.getOtbRequestStatus(), null);
    }

    public static void recordLog(OtbRequest otbRequest, String type, String note, String description) {
        recordLog(otbRequest, otbRequest.getOtbRequestStatus(), type, note, description);
    }

    public static void recordLog(OtbRequest otbRequest, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(otbRequest)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }
}
