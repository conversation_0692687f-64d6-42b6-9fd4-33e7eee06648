package cn.need.cloud.biz.mapper.otc;

import cn.need.cloud.biz.model.entity.otc.OtcShipPalletDetail;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletDetailQuery;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC运输托盘详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtcShipPalletDetailMapper extends SuperMapper<OtcShipPalletDetail> {

    /**
     * 根据条件获取OTC运输托盘详情列表
     *
     * @param query 查询条件
     * @return OTC运输托盘详情集合
     */
    default List<OtcShipPalletDetailPageVO> listByQuery(OtcShipPalletDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取OTC运输托盘详情分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC运输托盘详情集合
     */
    List<OtcShipPalletDetailPageVO> listByQuery(@Param("qo") OtcShipPalletDetailQuery query, @Param("page") Page<?> page);
}