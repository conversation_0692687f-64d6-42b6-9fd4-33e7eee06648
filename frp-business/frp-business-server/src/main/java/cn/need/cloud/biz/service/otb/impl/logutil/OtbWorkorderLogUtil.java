package cn.need.cloud.biz.service.otb.impl.logutil;

import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
public class OtbWorkorderLogUtil {

    public static List<AuditShowLog> getAuditShowLogList(List<OtbWorkorder> workorderList, String note, String description) {

        List<AuditShowLog> auditShowLogList = new ArrayList<>();
        workorderList.forEach(workorder -> {
            auditShowLogList.add(getAuditShowLog(workorder, note, description));
        });

        return auditShowLogList;
    }

    public static List<AuditShowLog> getAuditShowLogList(List<OtbWorkorder> workorderList) {

        List<AuditShowLog> auditShowLogList = new ArrayList<>();
        workorderList.forEach(workorder -> {
            auditShowLogList.add(getAuditShowLog(workorder));
        });

        return auditShowLogList;
    }

    public static AuditShowLog getAuditShowLog(OtbWorkorder workorder) {

        return getAuditShowLog(workorder, null, null);
    }


    public static AuditShowLog getAuditShowLog(OtbWorkorder workorder, String note, String description) {
        return getAuditShowLog(workorder, workorder.getOtbWorkorderStatus(), note, description);
    }

    public static AuditShowLog getAuditShowLog(OtbWorkorder workorder, String status, String note, String description) {
        return AuditLogUtil.commonLog(workorder)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setWarehouseId, workorder.getWarehouseId())
                .build();
    }

}
