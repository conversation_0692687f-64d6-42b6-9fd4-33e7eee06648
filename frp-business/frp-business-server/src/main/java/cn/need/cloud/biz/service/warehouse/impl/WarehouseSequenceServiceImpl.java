package cn.need.cloud.biz.service.warehouse.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.warehouse.WarehouseSequenceConverter;
import cn.need.cloud.biz.mapper.warehouse.WarehouseSequenceMapper;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseSequence;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseSequenceCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseSequenceUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseSequenceQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseSequencePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseSequenceVO;
import cn.need.cloud.biz.service.warehouse.WarehouseSequenceService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 生成仓库唯一refNum service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class WarehouseSequenceServiceImpl extends SuperServiceImpl<WarehouseSequenceMapper, WarehouseSequence> implements WarehouseSequenceService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(WarehouseSequenceCreateParam createParam) {
        // 检查传入生成仓库唯一refNum参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取生成仓库唯一refNum转换器实例，用于将生成仓库唯一refNum参数对象转换为实体对象
        WarehouseSequenceConverter converter = Converters.get(WarehouseSequenceConverter.class);

        // 将生成仓库唯一refNum参数对象转换为实体对象并初始化
        WarehouseSequence entity = initWarehouseSequence(converter.toEntity(createParam));

        // 插入生成仓库唯一refNum实体对象到数据库
        super.insert(entity);

        // 返回生成仓库唯一refNumID
        return entity.getId();
    }


    /**
     * 初始化生成仓库唯一refNum对象
     * 此方法用于设置生成仓库唯一refNum对象的必要参数，确保其处于有效状态
     *
     * @param entity 生成仓库唯一refNum对象，不应为空
     * @return 返回初始化后的生成仓库唯一refNum
     * @throws BusinessException 如果传入的生成仓库唯一refNum为空，则抛出此异常
     */
    private WarehouseSequence initWarehouseSequence(WarehouseSequence entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "WarehouseSequence"));
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(WarehouseSequenceUpdateParam updateParam) {
        // 检查传入生成仓库唯一refNum参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取生成仓库唯一refNum转换器实例，用于将生成仓库唯一refNum参数对象转换为实体对象
        WarehouseSequenceConverter converter = Converters.get(WarehouseSequenceConverter.class);

        // 将生成仓库唯一refNum参数对象转换为实体对象
        WarehouseSequence entity = converter.toEntity(updateParam);

        // 执行更新生成仓库唯一refNum操作
        return super.update(entity);

    }

    @Override
    public List<WarehouseSequencePageVO> listByQuery(WarehouseSequenceQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<WarehouseSequencePageVO> pageByQuery(PageSearch<WarehouseSequenceQuery> search) {
        Page<WarehouseSequence> page = Conditions.page(search, entityClass);
        List<WarehouseSequencePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public WarehouseSequenceVO detailById(Long id) {
        WarehouseSequence entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in WarehouseSequence");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "WarehouseSequence", id));
        }
        return buildWarehouseSequenceVO(entity);
    }


    /**
     * 构建生成仓库唯一refNumVO对象
     *
     * @param entity 生成仓库唯一refNum对象
     * @return 返回包含详细信息的生成仓库唯一refNumVO对象
     */
    private WarehouseSequenceVO buildWarehouseSequenceVO(WarehouseSequence entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的生成仓库唯一refNumVO对象
        return Converters.get(WarehouseSequenceConverter.class).toVO(entity);
    }

}
