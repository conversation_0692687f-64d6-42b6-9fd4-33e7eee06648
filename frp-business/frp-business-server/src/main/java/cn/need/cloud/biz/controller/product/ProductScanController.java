package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.converter.product.ProductScanConverter;
import cn.need.cloud.biz.model.entity.product.ProductScan;
import cn.need.cloud.biz.model.param.product.create.ProductScanCreateParam;
import cn.need.cloud.biz.model.query.product.ProductScanQuery;
import cn.need.cloud.biz.model.query.product.ScanNumAndPartnerQuery;
import cn.need.cloud.biz.model.vo.product.BaseProductVersionInfoVO;
import cn.need.cloud.biz.model.vo.product.ProductScanVO;
import cn.need.cloud.biz.model.vo.product.page.ProductScanPageVO;
import cn.need.cloud.biz.service.product.ProductScanService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 产品扫描 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/product-scan")
@Tag(name = "产品扫描")
public class ProductScanController extends AbstractRestController<ProductScanService, ProductScan, ProductScanConverter, ProductScanVO> {


    @Operation(summary = "新增产品扫描", description = "接收产品扫描的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProductScanCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }


    @Operation(summary = "根据ScanNum和供应商id获取产品扫描对象", description = "根据数据主键id，从数据库中获取其对应的产品扫描对象")
    @PostMapping(value = "/productByScanNumAndTransactionPartnerId")
    public Result<ProductScanVO> productByScanNumAndTransactionPartnerId(@RequestBody @Parameter(description = "数据对象", required = true) ScanNumAndPartnerQuery query) {

        // 获取产品详情
        ProductScanVO detailVo = service.productByScanNumAndTransactionPartnerId(query);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据ScanNum获取产品扫描对象", description = "根据数据主键id，从数据库中获取其对应的产品扫描对象")
    @PostMapping(value = "/productByScanNum")
    public Result<List<ProductScanVO>> productByScanNum(@RequestBody @Parameter(description = "数据对象", required = true) ScanNumAndPartnerQuery query) {
        // 返回结果
        return success(service.productByScanNum(query));
    }

    @Operation(summary = "根据ScanNum获取产品扫描对象", description = "根据数据主键id，从数据库中获取其对应的产品扫描对象")
    @PostMapping(value = "/productVersionByScanNum")
    public Result<List<BaseProductVersionInfoVO>> productVersionByScanNum(@RequestBody @Parameter(description = "数据对象", required = true) ScanNumAndPartnerQuery query) {
        // 返回结果
        return success(service.productVersionByScanNum(query));
    }


    @Operation(summary = "获取产品扫描分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的产品扫描列表")
    @PostMapping(value = "/list")
    public Result<PageData<ProductScanPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<ProductScanQuery> search) {

        // 获取产品扫描分页
        PageData<ProductScanPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
