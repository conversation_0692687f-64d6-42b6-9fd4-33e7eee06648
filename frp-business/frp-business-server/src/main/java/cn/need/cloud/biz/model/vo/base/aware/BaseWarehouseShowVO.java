package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * 仓库展示对象
 * <p>
 * 子类中不能包含 warehouseId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseWarehouseShowVO implements BaseWarehouseAware {
    /**
     * 仓库id
     */
    @Getter
    @Setter
    private Long warehouseId;

    /**
     * 仓库
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    private BaseWarehouseVO baseWarehouseVO;

    @Override
    public BaseWarehouseVO getBaseWarehouseVO() {
        if (ObjectUtil.isEmpty(warehouseId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseWarehouseVO)) {
            return baseWarehouseVO;
        }
        // Retrieve from cache once and store the result
        baseWarehouseVO = BaseWarehouseAware.super.getBaseWarehouseVO();
        return baseWarehouseVO;
    }
}
