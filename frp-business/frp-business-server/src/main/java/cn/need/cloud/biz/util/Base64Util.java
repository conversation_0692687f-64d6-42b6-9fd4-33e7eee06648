package cn.need.cloud.biz.util;

import cn.need.framework.common.core.exception.unchecked.BusinessException;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

public class Base64Util {
    public static String convertBase64StringPNG(String base64StringZPL) {

        String zplString = new String(Base64.getDecoder().decode(base64StringZPL));

        try {
            // Convert ZPL to BufferedImage
            BufferedImage bufferedImage = convertZPLToBufferedImage(zplString);

            // Convert BufferedImage to Base64 PNG
            return convertBufferedImageToBase64PNG(bufferedImage);

        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private static BufferedImage convertZPLToBufferedImage(String zplString) throws Exception {
        // Assuming you have a method to convert ZPL to BufferedImage
        // This is a placeholder for the actual conversion logic
        // You might need to use a specific library or tool for this step
        // For example, Zebra SDK or another ZPL to image converter
        // Here we just return a dummy image for demonstration purposes
        return new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);
    }

    private static String convertBufferedImageToBase64PNG(BufferedImage bufferedImage) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
}
