package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC工单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcWorkorderDetailConverter extends AbstractModelConverter<OtcWorkorderDetail, OtcWorkorderDetailVO, OtcWorkorderDetailDTO> {

}
