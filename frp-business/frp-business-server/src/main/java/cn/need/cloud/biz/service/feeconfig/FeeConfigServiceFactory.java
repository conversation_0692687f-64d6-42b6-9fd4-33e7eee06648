package cn.need.cloud.biz.service.feeconfig;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeModelTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@AllArgsConstructor
public class FeeConfigServiceFactory {

    private final Map<String, FeeConfigService> serviceMap;

    public FeeConfigService getBuilder(FeeModelTypeEnum type) {
        FeeConfigService service = serviceMap.get(StringUtil.format("feeConfig{}", type.getCode()));
        if (service == null) {
            throw new BusinessException("Unknown fee original data build type: " + type);
        }
        return service;
    }
}