package cn.need.cloud.biz.model.bo.base;

import cn.need.cloud.biz.util.Allocation;
import lombok.Data;


/**
 * 基础上架对象，拥有相同 上架数量、数量、乐观锁版本号 字段
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
public class BaseBinLocationAllocationBO implements Allocation {

    /**
     * 拣货对象id
     */
    private Long id;

    private Long binLocationDetailLockedId;

    /**
     * 上架数量
     */
    private Integer allocationQty;

    /**
     * 上架前的数量
     */
    private Integer allocationBeforeQty;

    /**
     * 拣货数量
     */
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    private Long version;


    public int getChangeQty() {
        return allocationQty - allocationBeforeQty;
    }

    @Override
    public int total() {
        return this.qty;
    }

    @Override
    public void allocation(int allocationQty) {
        this.allocationQty = allocationQty;
    }

    @Override
    public int allocated() {
        return this.allocationQty;
    }
}