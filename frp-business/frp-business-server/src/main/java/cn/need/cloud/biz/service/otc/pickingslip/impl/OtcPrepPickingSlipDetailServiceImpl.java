package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.need.cloud.biz.mapper.otc.OtcPrepPickingSlipDetailMapper;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlipDetail;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipDetailPickVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipDetailService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC预提货单详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPrepPickingSlipDetailServiceImpl extends SuperServiceImpl<OtcPrepPickingSlipDetailMapper, OtcPrepPickingSlipDetail> implements OtcPrepPickingSlipDetailService {


    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////


    @Override
    public List<OtcPrepPickingSlipDetail> listByOtcPrepPickingSlipId(Long otcPrepPickingSlipId) {
        return lambdaQuery()
                .eq(OtcPrepPickingSlipDetail::getOtcPrepPickingSlipId, otcPrepPickingSlipId)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OtcPrepPickingSlipDetailPickVO> pick(List<OtcPrepPickingSlipProductPickQuery> pickList, Long prepPickingSlipId) {
        if (ObjectUtil.isEmpty(pickList)) {
            return Collections.emptyList();
        }
        // 每个详情拣货数量
        Map<Long, Integer> detailPickQtyMap = pickList.stream()
                .collect(Collectors.groupingBy(OtcPrepPickingSlipProductPickQuery::getOtcPrepPickingSlipDetailId,
                        Collectors.summingInt(OtcPrepPickingSlipProductPickQuery::getQty))
                );
        // 拣货单详情
        List<OtcPrepPickingSlipDetail> detailList = this.groupByPrepPickingSlipId(prepPickingSlipId)
                .values()
                .stream()
                .flatMap(Collection::stream).toList();

        Validate.notEmpty(detailList, "Pick details cannot found");

        List<OtcPrepPickingSlipDetailPickVO> pickDetailList = detailList.stream()
                .peek(obj -> {
                    // 校验拣货是否全部完成
                    Validate.isTrue(obj.getPickedQty() <= obj.getQty(),
                            "{} All items have been picked", obj.getId()
                    );
                    int pickQty = detailPickQtyMap.getOrDefault(obj.getId(), 0);
                    int canPickQty = obj.getQty() - obj.getPickedQty();
                    // 拣货数量不能大于拣货数量
                    Validate.isTrue(canPickQty >= pickQty,
                            "{} The number of items to be picked cannot be greater than the number of items that can be picked",
                            obj.getId()
                    );
                })
                .map(obj -> {
                    OtcPrepPickingSlipDetailPickVO pick = BeanUtil.copyNew(obj, OtcPrepPickingSlipDetailPickVO.class);
                    // 设置拣货前的数量
                    pick.setPickedBeforeQty(pick.getPickedQty());
                    // 拣货后
                    pick.setPickedQty(pick.getPickedQty() + detailPickQtyMap.getOrDefault(obj.getId(), 0));

                    // 绑定锁信息
                    pick.setRefTableId(obj.getId());
                    pick.setRefTableRefNum(String.valueOf(obj.getLineNum()));
                    pick.setRefTableName(OtcPrepPickingSlipDetail.class.getSimpleName());
                    return pick;
                })
                .toList();

        // 更新拣货数量
        this.pickUpdate(pickDetailList);

        return pickDetailList;
    }

    @Override
    public boolean allPickedIgnoreDetailIdList(Long pickingSlipId, List<Long> pickingDetailIdList) {
        List<OtcPrepPickingSlipDetail> detailList = lambdaQuery()
                .eq(OtcPrepPickingSlipDetail::getOtcPrepPickingSlipId, pickingSlipId)
                .notIn(ObjectUtil.isNotEmpty(pickingDetailIdList), IdModel::getId, pickingDetailIdList)
                .list();
        return ObjectUtil.isEmpty(detailList)
                || detailList
                .stream()
                .allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pickUpdate(List<OtcPrepPickingSlipDetailPickVO> pickDetails) {
        if (ObjectUtil.isEmpty(pickDetails)) {
            return false;
        }
        List<OtcPrepPickingSlipDetail> pickedList = pickDetails.stream()
                .filter(obj -> obj.getChangePickQty() > 0)
                .map(obj -> BeanUtil.copyNew(obj, OtcPrepPickingSlipDetail.class))
                .toList();

        Validate.isTrue(this.updateBatch(pickedList) == pickedList.size(), "pick update fail");
        return true;
    }

    @Override
    public Map<Long, List<OtcPrepPickingSlipDetail>> groupByPrepPickingSlipId(Long otcPrepPickingSlipId) {
        return lambdaQuery()
                .eq(OtcPrepPickingSlipDetail::getOtcPrepPickingSlipId, otcPrepPickingSlipId)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtcPrepPickingSlipDetail::getOtcPrepPickingSlipId));
    }

    @Override
    public List<OtcPrepPickingSlipDetail> listByOtcPrepPickingSlipIds(List<Long> pickingSlipIdList) {
        if (ObjectUtil.isEmpty(pickingSlipIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtcPrepPickingSlipDetail::getOtcPrepPickingSlipId, pickingSlipIdList).list();
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

}
