package cn.need.cloud.biz.model.query.inbound;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 query对象")
public class InboundWorkorderDetailQuery extends SuperQuery {

    /**
     * 入库请求详情
     */
    @Schema(description = "入库请求详情")
    private Long inboundRequestDetailId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 入库工单id
     */
    @Schema(description = "入库工单id")
    private Long inboundWorkorderId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * requestDetailSnapshotQty
     */
    @Schema(description = "requestDetailSnapshotQty")
    private Integer requestDetailSnapshotQty;

    /**
     * 请求详情序号
     */
    @Schema(description = "请求详情序号")
    private Integer requestDetailSnapshotLineNum;

    /**
     * 请求详情备注
     */
    @Schema(description = "请求详情备注")
    private String requestDetailSnapshotNote;

    /**
     * 请求详情产品id
     */
    @Schema(description = "请求详情产品id")
    private Long requestDetailSnapshotProductId;

    /**
     * needReceiveQty
     */
    @Schema(description = "needReceiveQty")
    private Integer needReceiveQty;

    /**
     * 是否重新测量过 默认为false,没有重新测量过 如果发现Product和实际不符，需要走特殊流程更改为对的
     */
    @Schema(description = "是否重新测量过 默认为false,没有重新测量过 如果发现Product和实际不符，需要走特殊流程更改为对的")
    private Boolean remeasureFlag;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 请求详情请求参考编码
     */
    @Schema(description = "请求详情请求参考编码")
    private String requestDetailSnapshotDetailRequestRefNum;

    /**
     * 请求详情请求参考编码
     */
    @Schema(description = "请求详情请求参考编码集合")
    @Condition(value = Keyword.IN, fields = {"requestDetailSnapshotDetailRequestRefNum"})
    private List<String> requestDetailSnapshotDetailRequestRefNumList;

    /**
     * 请求详情类型
     */
    @Schema(description = "请求详情类型")
    private String requestDetailSnapshotDetailType;

    /**
     * 请求详情类型
     */
    @Schema(description = "请求详情类型集合")
    @Condition(value = Keyword.IN, fields = {"requestDetailSnapshotDetailType"})
    private List<String> requestDetailSnapshotDetailTypeList;


}