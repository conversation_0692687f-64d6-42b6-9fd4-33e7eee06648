package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeOtbDetailConverter;
import cn.need.cloud.biz.model.entity.fee.FeeOtbDetail;
import cn.need.cloud.biz.model.param.fee.create.FeeOtbDetailCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtbDetailUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtbDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtbDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtbDetailPageVO;
import cn.need.cloud.biz.service.fee.FeeOtbDetailService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 费用详情otb 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-otb-detail")
@Tag(name = "费用详情otb")
public class FeeOtbDetailController extends AbstractRestController<FeeOtbDetailService, FeeOtbDetail, FeeOtbDetailConverter, FeeOtbDetailVO> {

    @Operation(summary = "新增费用详情otb", description = "接收费用详情otb的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) FeeOtbDetailCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改费用详情otb", description = "接收费用详情otb的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) FeeOtbDetailUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除费用详情otb", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取费用详情otb详情", description = "根据数据主键id，从数据库中获取其对应的费用详情otb详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeOtbDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用详情otb详情", description = "根据数据RefNum，从数据库中获取其对应的费用详情otb详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeOtbDetailVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用详情otb分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用详情otb列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeOtbDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeOtbDetailQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}
