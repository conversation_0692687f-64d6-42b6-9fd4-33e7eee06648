package cn.need.cloud.biz.service.otc.ship.impl;

import cn.need.cloud.biz.converter.otc.OtcShipPalletDetailConverter;
import cn.need.cloud.biz.mapper.otc.OtcShipPalletDetailMapper;
import cn.need.cloud.biz.model.entity.otc.OtcShipPalletDetail;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletDetailQuery;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletDetailVO;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletDetailPageVO;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.ship.OtcShipPalletDetailService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC运输托盘详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcShipPalletDetailServiceImpl extends SuperServiceImpl<OtcShipPalletDetailMapper, OtcShipPalletDetail> implements OtcShipPalletDetailService {

    @Resource
    private OtcPackageService otcPackageService;

    /// ///////////////////////////////////////// 共有方法 ////////////////////////////////////////////

    @Override
    public PageData<OtcShipPalletDetailPageVO> pageByQuery(PageSearch<OtcShipPalletDetailQuery> search) {
        Page<OtcShipPalletDetail> page = Conditions.page(search, entityClass);
        List<OtcShipPalletDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcShipPalletDetailVO detailById(Long id) {
        OtcShipPalletDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcShipPalletDetail");
        }
        return buildOtcShipPalletDetailVO(entity);
    }


    @Override
    public List<OtcShipPalletDetailVO> listDetailByShipPalletId(Long shipPalletId) {
        List<OtcShipPalletDetail> details = lambdaQuery()
                .eq(OtcShipPalletDetail::getOtcShipPalletId, shipPalletId)
                .list();
        if (ObjectUtil.isEmpty(details)) {
            return Collections.emptyList();
        }
        // 包裹关系映射
        List<Long> packageIds = details.stream()
                .map(OtcShipPalletDetail::getOtcPackageId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, RefNumVO> packageRelationMap = otcPackageService.refNumMapByIds(packageIds);

        return Converters.get(OtcShipPalletDetailConverter.class).toVO(details)
                .stream()
                .peek(obj -> obj.setPackageVO(packageRelationMap.get(obj.getOtcPackageId())))
                .collect(Collectors.toList());
    }

    @Override
    public List<OtcShipPalletDetail> listByShipPalletId(Long shipPalletId) {
        return lambdaQuery()
                .eq(OtcShipPalletDetail::getOtcShipPalletId, shipPalletId)
                .list();
    }

    @Override
    public List<OtcShipPalletDetail> listByPackageIdList(List<Long> packageList) {
        if (ObjectUtil.isEmpty(packageList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(OtcShipPalletDetail::getOtcPackageId, packageList)
                .list();
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 构建OTC运输托盘详情VO对象
     *
     * @param entity OTC运输托盘详情对象
     * @return 返回包含详细信息的OTC运输托盘详情VO对象
     */
    private OtcShipPalletDetailVO buildOtcShipPalletDetailVO(OtcShipPalletDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC运输托盘详情VO对象
        return Converters.get(OtcShipPalletDetailConverter.class).toVO(entity);
    }

}
