package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailBatchCreateBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailPutawayBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationStocktakingBO;
import cn.need.cloud.biz.model.bo.inbound.BinLocationDetailContextBO;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailVO;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 库位详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface BinLocationDetailService extends SuperService<BinLocationDetail> {


    /**
     * 根据ID获取库位详情
     *
     * @param id 库位详情ID
     * @return 返回库位详情VO对象
     */
    BinLocationDetailVO detailById(Long id);


    /**
     * 根据库位id获取库位详情集合
     *
     * @param binLocationId 库位id
     * @return 库位详情集合
     */
    List<BinLocationDetailVO> listByBinLocationId(Long binLocationId);

    /**
     * 库位是否存在产品
     *
     * @param id 库位id
     * @return 库位是否存在产品
     */
    boolean exist(Long id);

    /**
     * 根据产品id及库位id获取库位详情
     *
     * @param productId     产品id
     * @param binLocationId 库位id
     * @return 库位详情
     */
    BinLocationDetail getByBinLocationIdAndProductVersionId(Long productId, Long binLocationId);


    BinLocationDetail getOrGenerateBinLocationDetailByBinLocationIdAndProductVersionId(Long binLocationId, Long productVersionId);

    BinLocationDetail getOrGenerateBinLocationDetailByBinLocationIdAndProductId(Long binLocationId, Long productId);

    /**
     * 根据产品id查询库存大于0的库位详情
     *
     * @param productIds 产品id集合
     * @return 库位详情集合
     */
    List<InventoryBinLocationDetailVO> listInventoryPositiveInStockQtyByProductIds(List<Long> productIds);

    /**
     * 根据产品id查询库存大于0的库位详情
     *
     * @param productIds 产品id集合
     * @return 库位详情集合
     */
    List<BinLocationDetail> listEntitiesPositiveInStockQtyByProductIds(List<Long> productIds);


    /**
     * 根据产品版本id查询是否有库存记录的库位详情
     *
     * @param productVersionId 产品版本id
     * @return boolean true（存在）
     */
    boolean existInStockQtyByProductVersionId(Long productVersionId);

    /**
     * 获取仓库下所有库位详情
     *
     * @param id 仓库id
     * @return 库位详情列表
     */
    List<BinLocationDetail> listByWarehouseId(Long id);

    /**
     * 根据仓库id删除库位详情
     * 该方法用于删除库位详情，传入仓库id
     *
     * @param id 仓库id
     */
    void removeByWarehouseId(Long id);

    /**
     * 根据库位id删除库位详情
     *
     * @param id 库位id
     */
    void removeByBinLocationId(Long id);

    /**
     * 上架产品
     *
     * @param context 上架产品上下文信息
     */
    void generateBinLocationDetail(BinLocationDetailContextBO context);

    /**
     * 获取ReadyToGo库位详情
     *
     * @param binLocationId readyToGo库位
     * @param pickList      pickList
     * @return /
     */
    List<BinLocationDetail> batchCreateIfAbsent(Long binLocationId, List<BinLocationDetailBatchCreateBO> pickList);

    /**
     * 变更库存, 内部带有库位日志记录
     *
     * @param changeList 变更列表
     */
    void updateInStockByChange(Collection<? extends BinLocationDetailChangeBO> changeList);

    /**
     * 根据产品id集合获取实际可用库存，产品对应库位库存集合映射
     *
     * @param productIdList 产品id集合
     * @return 产品对应实际可用库存
     */
    Map<Long, Integer> realAvailableInStockGroupByProductId(BaseBinLocationQuery binLocationQuery, List<Long> productIdList);

    /**
     * 根据产品id集合获取实际可用库存，产品对应库位库存集合映射(指定库位下)
     *
     * @param productIdList 产品id集合
     * @return 产品对应实际可用库存
     */
    Map<Long, List<BinLocationDetail>> realAvailableGroupByProductId(BaseBinLocationQuery binLocationQuery, List<Long> productIdList);

    /**
     * 根据库位名生序 库存降序
     *
     * @param realAvailableList 可用库位详情集合
     * @return /
     */
    Map<Long, List<BinLocationDetail>> groupByProductAndSortByInStockAndLocationName(List<BinLocationDetail> realAvailableList);

    /**
     * 上架虚拟库位
     *
     * @param context 上下文
     * @return /
     */
    BinLocationDetail putAwayVirtual(BinLocationDetailContextBO context);

    /**
     * 上架库位
     *
     * @param context 上下文
     * @return /
     */
    BinLocationDetail putIfAbsent(BinLocationDetailPutawayBO context);

    /**
     * 更新库位详情
     *
     * @param context 上下文
     */
    void updateBinLocationDetail(BinLocationDetailContextBO context);

    /**
     * 获取库存不为0的库位详情
     *
     * @param binLocationId 库位id
     * @param productId     产品id
     * @return 库位详情
     */
    BinLocationDetail getNoZero(Long binLocationId, Long productId);

    /**
     * 获取库存不为0的库位详情
     *
     * @param binLocationId 库位id
     * @param productId     产品id
     * @return 库位详情
     */
    boolean exist(Long binLocationId, Long productId, Long productVersionId);

    /**
     * 根据产品id获取库位详情
     *
     * @param productId 产品id
     * @return 库位详情
     */
    List<BinLocationDetail> listByProductId(Long productId);

    /**
     * 根据产品版本id获取库位详情
     *
     * @param productVersionId 产品版本id
     * @return 库位详情
     */
    List<BinLocationDetail> listByProductVersionId(Long productVersionId);

    /**
     * 根据产品版本id集合获取库位详情
     *
     * @param productVersionIdList 产品版本id集合
     * @param binLocationId        目标库位id
     * @return 库位详情
     */
    List<BinLocationDetail> list(List<Long> productVersionIdList, Long binLocationId);

    /**
     * 盘点
     *
     * @param stocktaking 盘点对象
     */
    <T extends RefNumModel> BinLocationDetail stocktaking(BinLocationStocktakingBO<T> stocktaking);

    /**
     * 根据目标库位id、产品id集合、产品版本id集合获取库位详情
     *
     * @param destBinLocationId 目标库位id
     * @param values            产品id
     */
    List<BinLocationDetail> list(Long destBinLocationId, Collection<Long> values);

    /**
     * 重置仓库下所有库存
     *
     * @param warehouseId 仓库id
     */
    Boolean resetBinLocationEmpty(Long warehouseId);
}