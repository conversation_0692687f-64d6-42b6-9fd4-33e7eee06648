package cn.need.cloud.biz.model.vo.base.workorder;

import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC预拣货单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "Prep拣货单 RollbackPutawayUnits 详情 vo对象")
public class PrepWorkorderRollbackPutawayUnitsVO implements Serializable, BaseProductAware, BaseBinLocationAware {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;


    @Schema(description = "Prep工单id")
    private Long id;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 拣货 数量
     */
    @Schema(description = "拣货 数量")
    private Integer pickedQty;

    /**
     * 发货 数量
     */
    @Schema(description = "上架 数量")
    private Integer putawayQty;

}