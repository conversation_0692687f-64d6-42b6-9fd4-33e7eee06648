package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.model.bo.inventory.CheckInventoryProductBO;
import cn.need.cloud.biz.model.bo.inventory.CheckInventoryWorkorderBO;
import cn.need.cloud.biz.model.bo.inventory.CheckInventoryWorkorderDetailBO;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.vo.base.output.OutputBaseFullProductVO;
import cn.need.cloud.biz.model.vo.base.output.OutputBaseWarehouseVO;
import cn.need.cloud.biz.model.vo.inventory.*;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
public class InventoryUtil {

    /**
     * 检查工单库存（无子工单）
     *
     * @param workOrders  工单列表
     * @param inventories 库存列表
     * @return 库存不足的工单列表
     */
    public static List<NoEnoughAvailQtyWorkOrderVO> checkWorkOrderInStock(
            List<CheckInventoryWorkorderBO> workOrders,
            List<InventoryCopyVO> inventories) {
        return checkWorkOrderInStock(workOrders, Collections.emptyList(), inventories);
    }

    /**
     * 检查工单库存
     *
     * @param workOrders     工单列表
     * @param prepWorkOrders 子工单列表
     * @param inventories    库存列表
     * @return 库存不足的工单列表
     */
    public static List<NoEnoughAvailQtyWorkOrderVO> checkWorkOrderInStock(
            List<CheckInventoryWorkorderBO> workOrders,
            List<BuildPrepPickingSlipWorkorderModel> prepWorkOrders,
            List<InventoryCopyVO> inventories) {

        // 将库存列表转换为 Map，以 productId 为键，方便快速查找
        Map<Long, InventoryCopyVO> inventoryMap = ObjectUtil.toMap(inventories, InventoryCopyVO::getProductId);

        List<NoEnoughAvailQtyWorkOrderVO> noEnoughAvailQtyWorkOrders = new ArrayList<>();

        // 按照 RefNum 对工单进行排序，确保结果固定
        workOrders.sort(Comparator.comparing(CheckInventoryWorkorderBO::getRefNum));

        // 将子工单已入库数量按照工单明细ID进行汇总
        Map<Long, Integer> preProductQtyMap = prepWorkOrders.stream()
                .collect(Collectors.groupingBy(BuildPrepPickingSlipWorkorderModel::getWorkOrderDetailId,
                        Collectors.summingInt(BuildPrepPickingSlipWorkorderModel::getPutAwayQty)));

        for (CheckInventoryWorkorderBO workOrder : workOrders) {
            boolean inventoryInsufficient = false;
            for (CheckInventoryWorkorderDetailBO workOrderDetail : workOrder.getDetails()) {
                int detailQty = workOrderDetail.getQty();
                if (detailQty == 0) {
                    continue;
                }

                InventoryCopyVO inventory = inventoryMap.get(workOrderDetail.getProductId());
                if (inventory == null) {
                    // 获取产品信息并抛出异常
                    throw new BusinessException("ProductId " + workOrderDetail.getProductId() + " No Inventory");
                }

                int preProductQty = preProductQtyMap.getOrDefault(workOrderDetail.getId(), 0);

                // 直接拣货数量，可能为部分数量
                int directPickQty = Math.max(detailQty - preProductQty, 0);

                // 检查库存是否足够
                boolean hasEnoughDirect = inventory.getDirectInStockAvailQty() >= directPickQty;
                boolean hasEnoughPrep = inventory.getVirtualInStockAvailQty() >= preProductQty;

                if (hasEnoughDirect && hasEnoughPrep) {
                    inventory.setDirectInStockAvailQty(inventory.getDirectInStockAvailQty() - directPickQty);
                    inventory.setVirtualInStockAvailQty(inventory.getVirtualInStockAvailQty() - preProductQty);
                } else {
                    int totalAvailableQty = inventory.getDirectInStockAvailQty() + inventory.getVirtualInStockAvailQty();
                    if (totalAvailableQty == 0) {
                        continue;
                    }

                    NoEnoughAvailQtyWorkOrderVO noEnough = new NoEnoughAvailQtyWorkOrderVO();
                    noEnough.setProductId(workOrderDetail.getProductId());
                    noEnough.setWorkOrderId(workOrder.getId());
                    noEnough.setWorkOrderRefNum(workOrder.getRefNum());
                    noEnough.setNeedQty(detailQty);
                    noEnough.setAvailQty(totalAvailableQty);

                    noEnoughAvailQtyWorkOrders.add(noEnough);

                    // 如果当前工单存在库存不足的情况，跳出内层循环
                    inventoryInsufficient = true;
                    break;
                }
            }
            if (inventoryInsufficient) {
                continue;
            }
        }

        return noEnoughAvailQtyWorkOrders;
    }

    /**
     * 检查库存是否足够
     *
     * @param details      库存检查明细列表
     * @param inventoryMap 库存列表
     */
    public static void checkInventory(List<CheckInventoryProductBO> details,
                                      Map<Long, InventoryVO> inventoryMap) {
        // 汇总相同产品的总数量
        Map<Long, Integer> requiredQtyMap = details.stream()
                .collect(Collectors.groupingBy(CheckInventoryProductBO::getProductId,
                        Collectors.summingInt(CheckInventoryProductBO::getQty)));

        //todo: 这里有问题，没考虑 ComboAB 和 A 共同 Check 的场景

        /* 优化建议: 当前实现中存在以下问题：
         * 1. 不能正确处理ComboAB（组合产品）和A在同一工单中的情况
         * 2. 没有跟踪已分配的库存，可能导致重复分配
         * 3. 错误消息不够清晰，难以快速定位问题
         *
         * 优化建议：
         * 1. 增加对工单内库存分配的跟踪机制，可以增加一个新的方法：
         *    public static void checkInventoryForWorkorder(List<CheckInventoryProductBO> products,
         *                                               Map<Long, InventoryVO> inventoryMap,
         *                                               Map<Long, Integer> allocatedInventory)
         *
         * 2. 在检查组合产品时，需要考虑其组件产品的库存情况，并确保不会重复分配。
         *    可以在检查前先将产品按照依赖关系排序，确保组件产品先于组合产品检查。
         *
         * 3. 增强错误消息，包含更多上下文信息，如工单号、明细行号等，便于快速定位问题。
         *
         * 4. 使用深复制的库存映射进行检查，避免修改原始数据：
         *    Map<Long, InventoryVO> currentInventoryMap = new HashMap<>();
         *    inventoryMap.forEach((productId, inventory) ->
         *        currentInventoryMap.put(productId, BeanUtil.copyNew(inventory, InventoryVO.class)));
         */

        // Map<Long, InventoryVO> currentInventoryMap = new HashMap<>();
        // inventoryMap.forEach((productId, inventory) -> currentInventoryMap.put(productId, BeanUtil.copyNew(inventory, InventoryVO.class)));

        List<String> insufficientMessages = requiredQtyMap.entrySet().stream()
                .map(entry -> {
                    Long productId = entry.getKey();
                    int requiredQty = entry.getValue();
                    InventoryVO inventory = inventoryMap.get(productId);
                    int availQty = inventory != null ? inventory.getAvailQty() : 0;

                    if (availQty >= requiredQty) {
                        return null;
                    }

                    OutputBaseFullProductVO productVO = ProductCacheUtil.filledProductWithReturn(new OutputBaseFullProductVO(productId));
                    return String.format(StringUtil.format("{} requiredQty:{},But availQty:{}", productVO.toString(), requiredQty, availQty));

                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!insufficientMessages.isEmpty()) {
            OutputBaseWarehouseVO warehouseVO = WarehouseCacheUtil.filledWarehouseWithReturn(new OutputBaseWarehouseVO(WarehouseContextHolder.getWarehouseId()));
            String errorMessage = warehouseVO + "\n" + String.join("\r\n", insufficientMessages);
            throw new BusinessException(errorMessage);
        }
    }

    /**
     * 添加锁定库存信息
     *
     * @param needLockedProductId 需要锁定的产品ID
     * @param inventoryLocked     锁定库存信息
     * @param inventoryMap        库存列表
     */
    public static void addLockedInventories(Long needLockedProductId,
                                            InventoryLocked inventoryLocked,
                                            Map<Long, InventoryVO> inventoryMap) {
        InventoryInventoryLockedVO inventoryInventoryReserveVO = BeanUtil.copyNew(inventoryLocked, InventoryInventoryLockedVO.class);
        List<InventoryVO> inventoryList = inventoryMap.values().stream().toList();
        InventoryUtil.addLockedInventories(needLockedProductId, inventoryInventoryReserveVO, inventoryList);
        //inventoryMap = ObjectUtil.toMap(inventoryList, InventoryVO::getProductId);
    }

    /**
     * 添加锁定库存信息
     *
     * @param needLockedProductId 需要锁定的产品ID
     * @param lockedInventory     锁定库存信息
     * @param inventories         库存列表
     */
    public static void addLockedInventories(Long needLockedProductId,
                                            InventoryInventoryLockedVO lockedInventory,
                                            List<InventoryVO> inventories) {
        addLockedInventories(needLockedProductId, lockedInventory, inventories, new HashSet<>());
    }

    /**
     * 添加锁定库存信息
     *
     * @param needLockedProductId 需要锁定的产品ID
     * @param lockedInventory     锁定库存信息
     * @param inventories         库存列表
     * @param visitedInventories  已分配的产品ID集合
     */
    private static void addLockedInventories(Long needLockedProductId,
                                             InventoryInventoryLockedVO lockedInventory,
                                             List<InventoryVO> inventories,
                                             Set<String> visitedInventories) {

        traverseInventoriesAndApply(inventories, visitedInventories, inventory -> {
            if (inventory.getProductId().equals(needLockedProductId)) {
                if (inventory.getInventoryLockedList().stream()
                        .noneMatch(x -> x.getId().equals(lockedInventory.getId()))) {
                    inventory.getInventoryLockedList().add(lockedInventory);
                }
                return true; // 找到并处理完毕，停止遍历
            }
            return false; // 继续遍历
        });
    }

    /**
     * 添加预留库存信息
     *
     * @param needLockedProductId 需要预留的产品ID
     * @param inventoryReserve    预留库存信息
     * @param inventoryMap        库存列表
     */
    public static void addReserveInventories(Long needLockedProductId,
                                             InventoryReserve inventoryReserve,
                                             Map<Long, InventoryVO> inventoryMap) {

        InventoryInventoryReserveVO inventoryInventoryReserveVO = BeanUtil.copyNew(inventoryReserve, InventoryInventoryReserveVO.class);
        List<InventoryVO> inventoryList = inventoryMap.values().stream().toList();
        InventoryUtil.addReserveInventories(needLockedProductId, inventoryInventoryReserveVO, inventoryList);
        //inventoryMap = ObjectUtil.toMap(inventoryList, InventoryVO::getProductId);
    }

    /**
     * 添加预留库存信息
     *
     * @param needLockedProductId 需要预留的产品ID
     * @param reserveInventory    预留库存信息
     * @param inventories         库存列表
     */
    public static void addReserveInventories(Long needLockedProductId,
                                             InventoryInventoryReserveVO reserveInventory,
                                             List<InventoryVO> inventories) {
        addReserveInventories(needLockedProductId, reserveInventory, inventories, new HashSet<>());
    }

    /**
     * 添加预留库存信息
     *
     * @param needLockedProductId 需要预留的产品ID
     * @param reserveInventory    预留库存信息
     * @param inventories         库存列表
     * @param visitedInventories  已分配的产品ID集合
     */
    private static void addReserveInventories(Long needLockedProductId,
                                              InventoryInventoryReserveVO reserveInventory,
                                              List<InventoryVO> inventories,
                                              Set<String> visitedInventories) {
        traverseInventoriesAndApply(inventories, visitedInventories, inventory -> {
            if (inventory.getProductId().equals(needLockedProductId)) {
                if (inventory.getInventoryReserveList().stream()
                        .noneMatch(x -> x.getId().equals(reserveInventory.getId()))) {
                    inventory.getInventoryReserveList().add(reserveInventory);
                }
                return true; // 找到并处理完毕，停止遍历
            }
            return false; // 继续遍历
        });
    }

    /**
     * 遍历库存并应用指定操作
     *
     * @param inventories        库存列表
     * @param visitedInventories 已处理的产品ID集合
     * @param action             对库存执行的操作
     */
    private static void traverseInventoriesAndApply(List<InventoryVO> inventories,
                                                    Set<String> visitedInventories,
                                                    Predicate<InventoryVO> action) {
        /* 优化建议: 当前实现中存在以下问题：
         * 1. 即使操作已完成（action.test返回true），仍然会继续遍历子库存，造成不必要的计算
         * 2. 当库存结构很复杂时，可能会导致大量的对象被加入到队列中，占用内存
         * 3. 没有对空列表或空对象进行特殊处理
         *
         * 优化建议：
         * 1. 如果操作已完成（action.test返回true），应该立即返回，不再遍历子库存
         * 2. 在添加子库存到队列前，先检查子库存列表是否为空
         * 3. 对于大型库存结构，可以考虑使用深度优先搜索而不是广度优先搜索，以减少内存使用
         *
         * 优化后的代码应该类似于：
         * while (!queue.isEmpty()) {
         *     InventoryVO inventory = queue.poll();
         *
         *     if (!visitedInventories.add(inventory.getInventoryId())) {
         *         continue; // 已处理过，跳过
         *     }
         *
         *     if (action.test(inventory)) {
         *         return; // 操作已完成，直接返回
         *     }
         *
         *     if (ObjectUtil.isNotEmpty(inventory.getComponentList())) {
         *         queue.addAll(inventory.getComponentList());
         *     }
         *
         *     if (ObjectUtil.isNotEmpty(inventory.getGroupList())) {
         *         queue.addAll(inventory.getGroupList());
         *     }
         * }
         */
        Queue<InventoryVO> queue = new LinkedList<>(inventories);

        while (!queue.isEmpty()) {
            InventoryVO inventory = queue.poll();

            if (!visitedInventories.add(inventory.getInventoryId())) {
                continue; // 已处理过，跳过
            }

            action.test(inventory);
            queue.addAll(inventory.getComponentList());
            queue.addAll(inventory.getGroupList());

            // if (action.test(inventory)) {
            //     break; // 操作已完成，退出
            // } else {
            //     queue.addAll(inventory.getComponentList());
            //     queue.addAll(inventory.getGroupList());
            // }
        }
    }
}
