package cn.need.cloud.biz.model.bo.otc.pickingslip;

import cn.need.cloud.biz.model.bo.base.pickingslip.PickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlip;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlipDetail;
import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "UnPick 对象")
public class OtcPickingSlipUnpickBO extends PickingSlipUnpickBO<OtcPutawaySlip, OtcPutawaySlipDetail, PickingSlipUnpickDetailVO> {

}