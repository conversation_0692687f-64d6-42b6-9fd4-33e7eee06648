package cn.need.cloud.biz.model.vo.base.auditlog;

import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Optional;

/**
 * 产品日志感知接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@FunctionalInterface
public interface BaseProductVersionLogAware {

    /**
     * 产品id
     *
     * @return 库位id
     */
    @JsonIgnore
    Long getProductVersionId();

    /**
     * 产品日志返回实体实现
     *
     * @return 产品日志
     */
    default BaseProductVersionLogVO getProductVersion() {
        return Optional.ofNullable(this.getProductVersionId())
                .map(ProductVersionCacheUtil::getById)
                .map(productCache -> BeanUtil.copyNew(productCache, BaseProductVersionLogVO.class))
                .orElse(null);
    }
}
