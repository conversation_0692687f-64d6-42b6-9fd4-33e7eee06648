package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.model.param.inventory.create.InventoryAuditCreateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryAuditQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryAuditVO;
import cn.need.cloud.biz.model.vo.page.InventoryAuditPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 库存盘点 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
public interface InventoryAuditService extends SuperService<InventoryAudit> {

    /**
     * 根据参数新增库存盘点
     *
     * @param createParam 请求创建参数，包含需要插入的库存盘点的相关信息
     * @return 库存盘点ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long inventoryAudit(InventoryAuditCreateParam createParam);

    /**
     * 根据参数新增库存盘点
     *
     * @param createParam 请求创建参数，包含需要插入的库存盘点的相关信息
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    void lockedInventoryAudit(InventoryAuditCreateParam createParam);

    /**
     * 根据参数新增库存盘点
     *
     * @param createParams 请求创建参数，包含需要插入的库存盘点的相关信息
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    default void batchInventoryAudit(List<InventoryAuditCreateParam> createParams) {
        createParams.forEach(this::lockedInventoryAudit);
    }

    /**
     * 盘点库存并更新到bop
     *
     * @param auditParam 盘点信息
     * @return 盘点库存id
     */
    Long inventoryAuditWithBop(InventoryAuditCreateParam auditParam);

    /**
     * 根据查询条件获取库存盘点列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个库存盘点对象的列表(分页)
     */
    List<InventoryAuditPageVO> listByQuery(InventoryAuditQuery query);

    /**
     * 根据查询条件获取库存盘点列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个库存盘点对象的列表(分页)
     */
    PageData<InventoryAuditPageVO> pageByQuery(PageSearch<InventoryAuditQuery> search);

    /**
     * 根据ID获取库存盘点
     *
     * @param id 库存盘点ID
     * @return 返回库存盘点VO对象
     */
    InventoryAuditVO detailById(Long id);

    /**
     * 根据库存盘点唯一编码获取库存盘点
     *
     * @param refNum 库存盘点唯一编码
     * @return 返回库存盘点VO对象
     */
    InventoryAuditVO detailByRefNum(String refNum);
}