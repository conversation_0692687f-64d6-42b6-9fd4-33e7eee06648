package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.param.product.create.ProductMultiboxCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductMultiboxCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductMultiboxQuery;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxListVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxWithDetailVO;
import cn.need.cloud.biz.model.vo.product.page.ProductMultiboxPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 产品多箱 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface ProductMultiboxService extends SuperService<ProductMultibox> {

    /**
     * 根据参数新增产品多箱
     *
     * @param createParam 请求创建参数，包含需要插入的产品多箱的相关信息
     * @return 产品多箱ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ProductMultiboxCreateParam createParam);


    /**
     * 根据参数新增或更新产品多箱
     *
     * @param params 请求创建参数，包含需要更新的产品多箱的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int createOrUpdate(List<ProductMultiboxCreateOrUpdateParam> params);

    /**
     * 根据查询条件获取产品多箱列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品多箱对象的列表(分页)
     */
    List<ProductMultiboxPageVO> listByQuery(ProductMultiboxQuery query);

    /**
     * 根据查询条件获取产品多箱列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品多箱对象的列表(分页)
     */
    PageData<ProductMultiboxPageVO> pageByQuery(PageSearch<ProductMultiboxQuery> search);

    /**
     * 根据ID获取产品多箱
     *
     * @param id 产品多箱ID
     * @return 返回产品多箱VO对象
     */
    ProductMultiboxVO detailById(Long id);


    /**
     * 根据产品ID获取多包装列表
     *
     * @param productId 产品ID，用于查询多包装列表
     * @return 返回一个ProductMultiboxListVO对象列表，表示查询到的多包装列表
     */
    List<ProductMultiboxListVO> listByProductId(Long productId);

    /**
     * 根据产品ID获取多包装记录列表
     *
     * @param productId 产品ID，用于查询多包装记录
     * @return 返回一个ProductMultibox对象列表，包含指定产品的所有多包装记录
     */
    List<ProductMultibox> getProductMultiboxListByProductId(Long productId);

    /**
     * 根据产品ID获取多包装记录
     *
     * @param productId 产品ID，用于查询多包装记录
     * @return 返回一个ProductMultibox对象
     */
    ProductMultibox getFirstProductMultiboxByProductId(Long productId);

    /**
     * 根据产品ID删除多包装记录
     *
     * @param productId   产品ID，用于定位要删除的多包装记录
     * @param deletedNote 删除备注，记录删除操作的原因或说明
     * @return 返回一个整数，表示受影响的记录数
     */
    Integer deleteByProductId(Long productId, String deletedNote);

    /**
     * 根据合作伙伴ID获取多包装记录列表
     *
     * @param partnerId 合作伙伴ID，用于获取特定合作伙伴的多包装记录
     * @return 返回一个ProductMultibox对象列表，包含指定合作伙伴的所有多包装记录
     */
    List<ProductMultibox> getListByPartnerId(Long partnerId);

    /**
     * 根据工单详情ID获取多包装记录
     *
     * @param prepWorkOrderDetailId Prep工单详情id
     * @return 返回一个ProductMultiboxVO对象列表，包含指定工单详情的所有多包装记录
     */
    List<ProductMultiboxWithDetailVO> multiBoxByWorkOrderDetailId(Long prepWorkOrderDetailId);

    /**
     * 获取multi box
     *
     * @param productId  产品id
     * @param versionInt 版本
     * @return /
     */
    List<ProductMultibox> getListByProductAndVersionInt(Long productId, int versionInt);

    /**
     * 根据产品id和版本号和upc和行号获取多包装记录
     *
     * @param productId          产品id
     * @param multiboxVersionInt 多盒版本
     * @param upc                upc
     * @return /
     */
    ProductMultibox findOneByProductIdAndVersionIntAndUpc(Long productId, Integer multiboxVersionInt, String upc);

    /**
     * 根据产品id和版本号获取多包装记录
     *
     * @param multiBoxProductIdList  产品id
     * @param multiBoxVersionIntList 版本
     * @return /
     */
    List<ProductMultibox> listByProductIdAndVersionIntList(List<Long> multiBoxProductIdList, List<Integer> multiBoxVersionIntList);
}