package cn.need.cloud.biz.model.vo.inbound.putaway;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 上架数量信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "上架数量信息 vo对象")
public class InboundPutAwayNumVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 入库卸货id
     */
    @Schema(description = "入库卸货id")
    private Long inboundUnloadId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;
}
