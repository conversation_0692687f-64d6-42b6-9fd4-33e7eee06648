package cn.need.cloud.biz.model.bo.otc;

import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * OtcWorkOrderReturnContext
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "OtcWorkOrderDetailReturnContext")
public class OtcWorkOrderDetailReturnContext implements Serializable {

    /**
     * workorderDetail
     */
    @Schema(description = "workorderDetail")
    private OtcWorkorderDetail workorderDetail;

    /**
     * inventoryLocked
     */
    @Schema(description = "inventoryLocked")
    private InventoryLocked inventoryLocked;

    /**
     * inventoryReserve
     */
    @Schema(description = "inventoryReserve")
    private InventoryReserve inventoryReserve;

    /**
     * prepWorkorder
     */
    @Schema(description = "prepWorkorder")
    private OtcPrepWorkorder prepWorkorder;

    /**
     * prepWorkOrderDetailReturnContextList
     */
    @Schema(description = "prepWorkOrderDetailReturnContextList")
    private List<OtcPrepWorkOrderDetailReturnContext> prepWorkOrderDetailReturnContextList;

    public OtcWorkOrderDetailReturnContext(OtcWorkorderDetail detail, InventoryLocked inventoryLocked) {
        this.workorderDetail = detail;
        this.inventoryLocked = inventoryLocked;
        this.prepWorkOrderDetailReturnContextList = new ArrayList<>();
    }

    public OtcWorkOrderDetailReturnContext(OtcWorkorderDetail detail, InventoryLocked inventoryLocked, InventoryReserve inventoryReserve) {
        this.workorderDetail = detail;
        this.inventoryLocked = inventoryLocked;
        this.inventoryReserve = inventoryReserve;
        this.prepWorkOrderDetailReturnContextList = new ArrayList<>();
    }
}
