package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbPrepWorkorderBinLocationPageVO;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC预提工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPrepWorkorderBinLocationService extends SuperService<OtbPrepWorkorderBinLocation> {

    /**
     * 根据查询条件获取OTC预提工单仓储位置列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC预提工单仓储位置对象的列表(分页)
     */
    PageData<OtbPrepWorkorderBinLocationPageVO> pageByQuery(PageSearch<OtbPrepWorkOrderBinLocationQuery> search);

    /**
     * 根据OTC预提工单详情ID列表获取OTC预提工单仓储位置列表
     *
     * @param wkDetailIdList 工单详情id集合
     * @return /
     */
    List<OtbPrepWorkorderBinLocation> listByOtbWorkorderDetailIdList(List<Long> wkDetailIdList);

    /**
     * 根据OTC预提工单ID列表获取OTC预提工单仓储位置列表
     *
     * @param workorderIds 工单id集合
     * @return /
     */
    List<OtbPrepWorkorderBinLocation> listByPrepWorkorderIds(List<Long> workorderIds);

    /**
     * 根据OTC预提工单ID获取OTC预提工单仓储位置列表
     *
     * @param id 工单id
     * @return /
     */
    default List<OtbPrepWorkorderBinLocation> listByPrepWorkorderId(Long id) {
        return this.listByPrepWorkorderIds(List.of(id));
    }
    
    /**
     * 根据OTB预提工单详情ID列表获取OTC预提工单仓储位置列表
     *
     * @param workorderDetailIdList 工单详情id集合
     * @return /
     */
    default Map<Long, List<OtbPrepWorkorderBinLocation>> groupByPrepWorkorderDetailId(List<Long> workorderDetailIdList) {
        return StreamUtils.groupBy(this.listByOtbWorkorderDetailIdList(workorderDetailIdList), OtbPrepWorkorderBinLocation::getOtbPrepPickingSlipDetailId);
    }
}