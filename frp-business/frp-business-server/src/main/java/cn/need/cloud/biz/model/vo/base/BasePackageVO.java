package cn.need.cloud.biz.model.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = " 包裹详情对象")
public class BasePackageVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 产品渠道sku
     */
    @Schema(description = " 产品渠道sku")
    private String productChannelSku;

    /**
     * 产品Barcode
     */
    @Schema(description = " 产品Barcode")
    private String productBarcode;

    /**
     * 产品id
     */
    @Schema(description = " 产品id")
    private Long productId;

    /**
     * 一箱数量
     */
    @Schema(description = " 一箱数量")
    private Integer qty;
}
