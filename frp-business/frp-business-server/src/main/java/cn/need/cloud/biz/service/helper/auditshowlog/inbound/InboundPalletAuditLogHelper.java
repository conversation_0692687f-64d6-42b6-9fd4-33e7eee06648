package cn.need.cloud.biz.service.helper.auditshowlog.inbound;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 入库打托单日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class InboundPalletAuditLogHelper {

    private InboundPalletAuditLogHelper() {
    }

    public static void recordLog(List<InboundPallet> inboundPalletList, String type, String note, String description) {
        inboundPalletList.forEach(item -> recordLog(item, type, note, description));
    }

    public static void recordLog(List<InboundPallet> inboundPalletList) {
        inboundPalletList.forEach(InboundPalletAuditLogHelper::recordLog);
    }

    public static void recordLog(InboundPallet inboundPallet) {
        recordLog(inboundPallet, BaseTypeLogEnum.STATUS.getType(), null, null);
    }

    public static void recordLog(InboundPallet inboundPallet, String description, String note) {
        recordLog(inboundPallet, BaseTypeLogEnum.STATUS.getType(), note, description);
    }


    public static void recordLog(InboundPallet inboundPallet, String type, String note, String description) {
        recordLog(inboundPallet, inboundPallet.getPalletStatus(), type, note, description);
    }

    public static void recordLog(InboundPallet inboundPallet, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(inboundPallet)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }

}
