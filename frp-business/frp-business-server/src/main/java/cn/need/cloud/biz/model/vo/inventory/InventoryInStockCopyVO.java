package cn.need.cloud.biz.model.vo.inventory;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 库存-库位 vo对象
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Data
@Schema(description = "库存-库位 vo对象")
public class InventoryInStockCopyVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * warehouseId
     */
    @Schema(description = "warehouseId")
    private Long warehouseId;

    /**
     * productId
     */
    @Schema(description = "productId")
    private Long productId;

    /**
     * 产品基本信息
     */
    @Schema(description = "产品基本信息")
    private BaseProductVO baseProductVO;

    /**
     * binLocationDetailList
     */
    @Schema(description = "binLocationDetailList")
    private List<InventoryBinLocationDetailVO> binLocationDetailList;

    /**
     * inventoryLockedList
     */
    @Schema(description = "inventoryLockedList")
    private List<InventoryInventoryLockedVO> inventoryLockedList;

    /**
     * inventoryReserveList
     */
    @Schema(description = "inventoryReserveList")
    private List<InventoryInventoryReserveVO> inventoryReserveList;

    /**
     * inboundWorkorderDetailList
     */
    @Schema(description = "inboundWorkorderDetailList")
    @JsonIgnore
    private List<InventoryInboundWorkorderDetailVO> inboundWorkorderDetailList;

    /**
     * 在货架上实际数量
     */
    @Schema(description = "inStockQty")
    private Integer inStockQty;

    /**
     * 可直接拣货库位上能够被分配的数量
     */
    @Schema(description = "directInStockCanAllocateQty")
    private Integer directInStockCanAllocateQty;

    /**
     * 当前产品 DirectInStock 仓库实际上锁定的库存
     */
    @Schema(description = "directInStockLockedQty")
    private Integer directInStockLockedQty;

    /**
     * 当前产品仓库实际上锁定的库存
     */
    @Schema(description = "actualInventoryLockedQty")
    private Integer actualInventoryLockedQty;

    /**
     * 库位上能够被分配的数量
     */
    @Schema(description = "inStockCanAllocateQty")
    private Integer inStockCanAllocateQty;

    /**
     * 可直接拣货库位上可用库存
     */
    @Schema(description = "directInStockAvailQty")
    private Integer directInStockAvailQty;

    /**
     * inVirtualStockAvailQty
     */
    @Schema(description = "virtualInStockAvailQty")
    private Integer virtualInStockAvailQty;

    /**
     * inStockAvailQtyOTB
     */
    @Schema(description = "inStockAvailQtyOTB")
    private Integer inStockAvailQtyOTB;

    /**
     * inStockAvailQty_OTC
     */
    @Schema(description = "inStockAvailQtyOTC")
    private Integer inStockAvailQtyOTC;

    /**
     * 在货架上实际可用数量
     */
    @Schema(description = "inStockAvailQty")
    private Integer inStockAvailQty;

    /**
     * 在仓库层面锁定数量
     */
    @Schema(description = "inventoryLockedQty")
    private Integer inventoryLockedQty;

    /**
     * 在仓库层面预留数量
     */
    @Schema(description = "inventoryReserveQty")
    private Integer inventoryReserveQty;

    /**
     * inReceiveQty 数量
     */
    @Schema(description = "inReceiveQty")
    private Integer inReceiveQty;

    /**
     * inReceiveInboundWorkorderDetailList
     */
    @Schema(description = "inReceiveInboundWorkorderDetailList")
    private List<InventoryInboundWorkorderDetailVO> inReceiveInboundWorkorderDetailList;

    /**
     * inTransitQty 数量
     */
    @Schema(description = "inTransitQty")
    private Integer inTransitQty;

    /**
     * inTransitInboundWorkorderDetailList
     */
    @Schema(description = "inTransitInboundWorkorderDetailList")
    private List<InventoryInboundWorkorderDetailVO> inTransitInboundWorkorderDetailList;

}


