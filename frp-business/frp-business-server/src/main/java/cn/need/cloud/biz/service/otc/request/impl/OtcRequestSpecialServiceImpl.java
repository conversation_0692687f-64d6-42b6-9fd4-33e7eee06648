package cn.need.cloud.biz.service.otc.request.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelParam;
import cn.need.cloud.biz.model.query.RequestListQuery;
import cn.need.cloud.biz.model.vo.base.request.RequestConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.request.RequestConfirmVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcRequestAuditLogHelper;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageSpecialService;
import cn.need.cloud.biz.service.otc.request.OtcRequestDetailService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.request.OtcRequestSpecialService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderSpecialService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * OTC请求 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor_ = @Lazy)
public class OtcRequestSpecialServiceImpl implements OtcRequestSpecialService {

    private final OtcRequestService otcRequestService;
    private final OtcRequestDetailService otcRequestDetailService;
    private final OtcWorkorderSpecialService otcWorkorderSpecialService;
    private final OtcWorkorderService otcWorkorderService;
    private final OtcPackageSpecialService otcPackageSpecialService;


    /// //////////////////////////////// 公共方法 //////////////////////////////////////
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancel(OtcRequestCancelParam param) {
        OtcRequest entity = otcRequestService.getById(param.getId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("Parameter cannot be empty");
        }
        // 校验取消状态
        String otcRequestStatus = entity.getOtcRequestStatus();
        final List<String> canCancel = RequestStatusEnum.getCanCancel();

        Validate.isTrue(canCancel.contains(otcRequestStatus),
                ErrorConstant.STATUS_ERROR_FORMAT, entity.refNumLog(), "cancel", canCancel, otcRequestStatus
                );

        // 需要调用otc接口取消 workorder 包裹 状态 根据回执 判断请求状态  取消/挂起/部分取消
        final List<OtcWorkorder> workorderList = otcWorkorderSpecialService.allCancel(param);

        if (ObjectUtil.isEmpty(workorderList)) {
            entity.setOtcRequestStatus(RequestStatusEnum.CANCELLED.getStatus());
            int update = otcRequestService.update(entity);
            Validate.isTrue(update == 1, "Request status update failed");
        }
        else{
            // 暂直接改为OnHold
            boolean allCancelled = workorderList.stream()
                    .allMatch(workorder -> OtcWorkorderStatusEnum.CANCELLED.getStatus().equals(workorder.getOtcWorkorderStatus()));

            entity.setOtcRequestStatus(allCancelled
                    ? RequestStatusEnum.CANCELLED.getStatus()
                    : RequestStatusEnum.ON_HOLD.getStatus());

            int update = otcRequestService.update(entity);
            Validate.isTrue(update == 1, "Request status update failed");
        }
        // 日志
        OtcRequestAuditLogHelper.recordLog(entity, param.getNote(), null);
        return 1;
    }

    @Override
    public void finishCancel(List<Long> requestIdList) {
        List<OtcRequest> requests = otcRequestService.listByIds(requestIdList);
        var requestMap = StreamUtils.toMap(requests, IdModel::getId);
        Map<Long, List<OtcWorkorder>> workorderGroupRequestMap = otcWorkorderService.groupByRequestIdList(requestIdList);

        // 设置取消状态
        workorderGroupRequestMap.forEach((key, value) -> {
            var allCancel = value.stream()
                    .allMatch(obj -> OtcWorkorderStatusEnum.CANCELLED.getStatus().equals(obj.getOtcWorkorderStatus()));
            OtcRequest request = requestMap.get(key);
            var oldStatus = request.getOtcRequestStatus();
            request.setOtcRequestStatus(allCancel
                    ? RequestStatusEnum.CANCELLED.getStatus()
                    : oldStatus
            );
            // 记录日志
            if (!Objects.equals(oldStatus, request.getOtcRequestStatus())) {
                OtcRequestAuditLogHelper.recordLog(request);
            }
        });

        // 更新
        Validate.isTrue(otcRequestService.updateBatch(requests) == requests.size(),
                "Update Request status failed"
        );
    }

    @Override
    public List<RequestConfirmDetailVO> cancelConfirm(RequestListQuery query) {
        var requests = otcRequestService.listByIds(query.getIdList());
        Validate.notEmpty(requests, "Request Ids {} not exist", query.getIdList());

        var requestMap = StreamUtils.toMap(requests, IdModel::getId);
        return otcRequestDetailService.listByRequestIds(query.getIdList()).stream()
                .map(obj -> {
                    var confirmDetail = BeanUtil.copyNew(obj, RequestConfirmDetailVO.class);
                    var request = requestMap.get(obj.getOtcRequestId());
                    var confirm = BeanUtil.copyNew(request, RequestConfirmVO.class);
                    confirm.setRequestStatus(request.getOtcRequestStatus());
                    confirmDetail.setRequest(confirm);
                    return confirmDetail;
                })
                .toList();

    }
}
