package cn.need.cloud.biz.model.param.otb.update.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * OtcRequestCancelParam
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
public class OtbRequestCancelParam {

    @Schema(description = "请求id")
    @NotNull(message = "id cannot be null")
    @NotBlank(message = "id cannot be blank")
    private Long id;

    /**
     * 删除原因
     */
    @Schema(description = "原因")
    @NotNull(message = "note cannot be null")
    @NotBlank(message = "note cannot be blank")
    private String note;

}
