package cn.need.cloud.biz.service.otb.putawayslip;

import cn.need.cloud.biz.model.bo.otb.pickingslip.OtbPrepPickingSlipUnpickBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlip;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.putawayslip.OtbPrepPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.putawayslip.OtbPrepPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbPrepPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otb.putawayslip.OtbPrepPutawaySlipVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.cloud.biz.service.base.putawayslip.PrepPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC上架单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface OtbPrepPutawaySlipService extends PrepPutawaySlipService<OtbPrepPutawaySlip>, HeaderPrintedService<OtbPrepPutawaySlip, OtbPrepPutawaySlipService, PrintQuery> {

    /**
     * 根据查询条件获取OTC上架单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC上架单对象的列表(分页)
     */
    PageData<OtbPrepPutawaySlipPageVO> pageByQuery(PageSearch<OtbPrepPutawaySlipQuery> search);

    /**
     * 根据ID获取OTC上架单
     *
     * @param id OTC上架单ID
     * @return 返回OTC上架单VO对象
     */
    OtbPrepPutawaySlipVO detailById(Long id);

    /**
     * 根据OTC上架单唯一编码获取OTC上架单
     *
     * @param refNum OTC上架单唯一编码
     * @return 返回OTC上架单VO对象
     */
    OtbPrepPutawaySlipVO detailByRefNum(String refNum);

    /**
     * 取消上架
     *
     * @param param 取消参数
     * @return /
     */
    boolean cancel(PutawaySlipCancelUpdateParam param);

    /**
     * 上架
     *
     * @param param 上架参数
     * @return /
     */
    boolean putAway(OtbPrepPutawaySlipPutAwayUpdateParam param);

    /**
     * 创建上架单
     *
     * @param param unpick
     */
    void unpick(OtbPrepPickingSlipUnpickBO param);

    /**
     * 确认页
     *
     * @param prepWorkorderIds Prep工单
     * @return /
     */
    List<PrepPutawaySlipConfirmDetailVO> confirmDetailList(List<Long> prepWorkorderIds);

    /**
     * 根据工单查Prep上架单
     *
     * @param prepWorkorderIds Prep工单
     * @return /
     */
    List<OtbPrepPutawaySlip> listByPrepWorkorderIds(List<Long> prepWorkorderIds);
}