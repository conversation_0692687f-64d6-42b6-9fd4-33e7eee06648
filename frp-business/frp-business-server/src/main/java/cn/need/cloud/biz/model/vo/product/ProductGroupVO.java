package cn.need.cloud.biz.model.vo.product;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 产品同类 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品同类 vo对象")
public class ProductGroupVO extends BaseSuperVO {


    /**
     * 父产品ID
     */
    @Schema(description = "父产品ID")
    private Long parentProductId;

    /**
     * 子产品ID
     */
    @Schema(description = "子产品ID")
    private Long childProductId;

    /**
     * 指令备注
     */
    @Schema(description = "指令备注")
    private String instructionNote;

    /**
     * 回滚指令备注
     */
    @Schema(description = "回滚指令备注")
    private String revertInstructionNote;

    /**
     * 转换组类型
     */
    @Schema(description = "转换组类型")
    private String convertGroupType;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 同类产品版本号
     */
    @Schema(description = "同类产品版本号")
    private Integer groupVersionInt;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

}