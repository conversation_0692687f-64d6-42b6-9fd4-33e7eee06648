package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 关联  Base产品 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "关联  Base产品 vo对象")
public class RelatedProductVO extends BaseSuperVO implements BaseProductAware {

    /**
     * 关联对象，主键id
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 产品主键id
     */
    @Schema(description = "产品id")
    private Long productId;

}