package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPutawaySlipConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlip;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.putawayslip.OtbPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.putawayslip.OtbPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otb.putawayslip.OtbPutawaySlipVO;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC上架单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@RestController
@RequestMapping("/api/biz/otb-putaway-slip")
@Tag(name = "OTB上架单")
public class OtbPutawaySlipController extends AbstractRestController<OtbPutawaySlipService, OtbPutawaySlip, OtbPutawaySlipConverter, OtbPutawaySlipVO> {

    @Operation(summary = "根据id获取OTC上架单详情", description = "根据数据主键id，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPutawaySlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取OTC上架单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPutawaySlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "获取OTC上架单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC上架单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPutawaySlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPutawaySlipQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "Cancel", description = "取消上架")
    @PostMapping(value = "/cancel")
    public Result<Boolean> cancel(@RequestBody @Valid PutawaySlipCancelUpdateParam param) {
        // 返回结果
        return success(service.cancel(param));
    }

    @Operation(summary = "PutAway", description = "上架")
    @PostMapping(value = "/put-away")
    public Result<Boolean> putAway(@RequestBody @Valid OtbPutawaySlipPutAwayUpdateParam param) {
        // 返回结果
        return success(service.putAway(param));
    }

    @Operation(summary = "Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }
}
