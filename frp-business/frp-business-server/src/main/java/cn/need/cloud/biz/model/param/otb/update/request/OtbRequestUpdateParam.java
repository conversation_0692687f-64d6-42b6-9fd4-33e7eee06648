package cn.need.cloud.biz.model.param.otb.update.request;

import cn.need.cloud.biz.client.constant.RegexConstant;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = " Otb请求修改参数")
public class OtbRequestUpdateParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "仓储ID")
    private Long warehouseId;

    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "渠道")
    @NotBlank(message = "channel cannot be empty")
    private String channel;

    @Schema(description = "发货窗口开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipWindowStart;

    @Schema(description = "发货窗口结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipWindowEnd;

    @Schema(description = "订单号")
    private String orderNum;

    @Schema(description = "订单详情")
    @Valid
    @NotEmpty(message = "detailList cannot be empty")
    private List<OtbRequestDetailVO> detailList;

    @Schema(description = "请求参考号")
    @Pattern(regexp = RegexConstant.CHARACTER_REGEX, message = RegexConstant.CHARACTER_REGEX_MESSAGE)
    private String requestRefNum;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;


    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;


    /**
     * 发货人姓名
     */
    @Schema(description = "发货人姓名")
    private String shipFromAddressName;

    /**
     * 发货备注
     */
    @Schema(description = "发货备注")
    private String shipFromAddressNote;

    /**
     * 发货电话
     */
    @Schema(description = "发货电话")
    private String shipFromAddressPhone;

    /**
     * 发货州
     */
    @Schema(description = "发货州")
    private String shipFromAddressState;

    /**
     * 发货邮编
     */
    @Schema(description = "发货邮编")
    private String shipFromAddressZipCode;

}
