package cn.need.cloud.biz.model.vo.base.product;


import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/***
 * 组件
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@Schema(description = "产品组件 VO对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PrepComponentVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7784905981772351386L;
    /**
     * 组件产品ID
     */
    @Schema(description = "组件产品ID")
    private Long componentProductId;

    /**
     * 组件产品
     */
    @Schema(description = "组件产品")
    private BaseProductVO baseProductVO;

    /**
     * 组件数量
     */
    @Schema(description = "组件数量")
    private Integer componentQty;

    /**
     * 组装说明备注
     */
    @Schema(description = "组装说明备注")
    private String assemblyInstructionNote;

}
