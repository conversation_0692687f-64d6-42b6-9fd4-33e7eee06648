package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.entity.otc.OtcWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.vo.page.OtcWorkorderBinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcWorkorderBinLocationService extends SuperService<OtcWorkorderBinLocation> {

    /**
     * 根据查询条件获取OTC工单仓储位置列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC工单仓储位置对象的列表(分页)
     */
    PageData<OtcWorkorderBinLocationPageVO> pageByQuery(PageSearch<OtcWorkOrderBinLocationQuery> search);

    /**
     * 通过工单详情获取仓储位信息
     *
     * @param detailIdList detailIdList
     * @return /
     */
    List<OtcWorkorderBinLocation> listByOtcWorkorderDetailIdList(List<Long> detailIdList);

    /**
     * 通过工单详情获取仓储位信息
     *
     * @param workorderIdList detailIdList
     * @return /
     */
    List<OtcWorkorderBinLocation> listByOtcWorkorderIdList(List<Long> workorderIdList);

    /**
     * 通过工单id获取仓储位信息
     *
     * @param workorderId 工单id
     * @return /
     */
    List<OtcWorkorderBinLocation> listByWorkorderId(Long workorderId);
}