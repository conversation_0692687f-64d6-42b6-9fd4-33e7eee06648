package cn.need.cloud.biz.model.vo.otb.pkg;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTB包裹标签 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB包裹标签 vo对象")
public class OtbPackageLabelVO extends BaseSuperVO {


    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型")
    private String labelType;

    /**
     * label RefNum
     */
    @Schema(description = "label RefNum")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型")
    private String paperType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String rawDataType;

    /**
     * label数据类型
     */
    @Schema(description = "label数据类型")
    private String labelRawData;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * otb包裹id
     */
    @Schema(description = "otb包裹id")
    private Long otbPackageId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    private String fileIdRawDataType;

}