package cn.need.cloud.biz.model.query.warehouse;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;


/**
 * 打托模板 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "打托模板 query对象")
public class PalletTemplateQuery extends SuperQuery {

    /**
     * 每层几个箱子
     */
    @Schema(description = "每层几个箱子")
    private Integer cartonPerLayer;

    /**
     * 一共多少层
     */
    @Schema(description = "一共多少层")
    private Integer layersCount;

    /**
     * 多了几个箱子
     */
    @Schema(description = "多了几个箱子")
    private Integer extCarton;

    /**
     * 每箱几个产品
     */
    @Schema(description = "每箱几个产品")
    private Integer pcsPerCarton;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Set<Long> productVersionIdList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private List<String> refNumList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 是否设置默认
     */
    @Schema(description = "是否设置默认")
    private Boolean setDefaultFlag;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品唯一标识码
     */
    @Schema(description = "产品唯一标识码")
    private String productRefNum;

    /**
     * 产品唯一标识码
     */
    @Schema(description = "产品唯一标识码")
    private Set<String> productRefNumList;

    /**
     * 供应商sku
     */
    @Schema(description = "供应商sku")
    private String supplierSku;

    /**
     * 供应商sku
     */
    @Schema(description = "供应商sku")
    private Set<String> supplierSkuList;

    /**
     * upc
     */
    @Schema(description = "upc")
    private String upc;

    /**
     * upc
     */
    @Schema(description = "upc")
    private Set<String> upcList;

}