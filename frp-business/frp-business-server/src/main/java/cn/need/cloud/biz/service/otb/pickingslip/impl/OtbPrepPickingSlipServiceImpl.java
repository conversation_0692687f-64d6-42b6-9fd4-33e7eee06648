package cn.need.cloud.biz.service.otb.pickingslip.impl;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.converter.otb.OtbPrepPickingSlipConverter;
import cn.need.cloud.biz.mapper.otb.OtbPrepPickingSlipMapper;
import cn.need.cloud.biz.model.bo.common.WorkOrderMembershipBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipListQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipPickQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.query.product.ProductTreeQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.product.PrepFullProductVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipDetailVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtbPrepPickingSlipPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipDetailService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * otb预拣货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbPrepPickingSlipServiceImpl extends SuperServiceImpl<OtbPrepPickingSlipMapper, OtbPrepPickingSlip> implements OtbPrepPickingSlipService {

    @Resource
    private OtbPrepPickingSlipDetailService otbPrepPickingSlipDetailService;
    @Resource
    private OtbPrepWorkorderService otbPrepWorkorderService;
    @Resource
    private OtbPrepWorkorderDetailService otbPrepWorkorderDetailService;
    @Resource
    private PickingSlipService pickingSlipService;
    @Resource
    private TenantCacheService tenantCacheService;


    @Override
    public PageData<OtbPrepPickingSlipPageVO> pageByQuery(PageSearch<OtbPrepPickingSlipListQuery> search) {
        Page<OtbPrepPickingSlip> page = Conditions.page(search, entityClass);
        OtbPrepPickingSlipListQuery condition = search.getCondition();
        List<OtbPrepPickingSlipPageVO> dataList = mapper.listByQuery(condition.getOtbPrepPickingSlipQuery(), condition.getOtbWorkorderQuery(), page);

        return new PageData<>(dataList, page);
    }

    @Override
    public OtbPrepPickingSlipVO detailById(Long id) {
        OtbPrepPickingSlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtbPrepPickingSlip");
        }
        return buildOtbPrepPickingSlipVO(entity);
    }

    @Override
    public OtbPrepPickingSlipVO detailByRefNum(String refNum) {
        OtbPrepPickingSlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtbPrepPickingSlip");
        }
        return buildOtbPrepPickingSlipVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pick(OtbPrepPickingSlipPickQuery query) {
        OtbPrepPickingSlip slip = this.getById(query.getOtbPrepPickingSlipId());

        Validate.notNull(slip, "id {} not found in Prep Picking Slip", query.getOtbPrepPickingSlipId());
        // 仅支持New、InPicking状态
        Validate.isTrue(OtbPrepPickingSlipStatusEnum.canPickProcessStatus(slip.getOtbPrepPickingSlipStatus()),
                "{} Only support New,InPicking Status", slip.refNumLog()
        );

        OtbPrepPickingSlipPickContextVO context = new OtbPrepPickingSlipPickContextVO();
        context.setPickList(query.getPickDetailList());
        context.setPrepPickingSlip(slip);

        // 拣货单拣货
        this.pickPickingSlip(context);

        // 更新拣货单
        this.update(slip);

        // Prep工单拣货
        otbPrepWorkorderService.pick(context);

        // 记录日志
        this.recordPickLog(context);
        return true;
    }

    @Override
    public OtbPrepPickingSlipVO buildOtbPrepPickingSlipVO(OtbPrepPickingSlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的otb预拣货单VO对象
        OtbPrepPickingSlipVO vo = Converters.get(OtbPrepPickingSlipConverter.class).toVO(entity);

        // 详情
        List<OtbPrepPickingSlipDetailVO> detailList
                = BeanUtil.copyNew(otbPrepPickingSlipDetailService.listByOtbPrepPickingSlipId(entity.getId()), OtbPrepPickingSlipDetailVO.class);
        vo.setDetailList(detailList);

        vo.setDescription(pickingSlipService.getHowToDoDescription(this.getPrepProductTree(entity)));
        // 设置可上架数量
        vo.setCanPutawayQty(entity.getAllocatePutawayQty() - entity.getPutawayQty());

        // 供应商
        TenantCache tenantCache = tenantCacheService.getById(entity.getTransactionPartnerId());
        if (ObjectUtil.isNotNull(tenantCache)) {
            vo.setBasePartnerVO(BeanUtil.copyNew(tenantCache, BasePartnerVO.class));
        }

        return vo;
    }

    /**
     * 拣货单拣货
     *
     * @param context 上下文
     */
    private void pickPickingSlip(OtbPrepPickingSlipPickContextVO context) {
        List<OtbPrepPickingSlipProductPickQuery> pickList = context.getPickList();
        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 拣货单拣货信息
        List<OtbPrepPickingSlipDetailPickVO> pickingSlipDetailPickList = otbPrepPickingSlipDetailService.pick(pickList, prepPickingSlip);

        // 拣货的详情id
        List<Long> prepPickingSlipDetailIdList = StreamUtils.distinctMap(pickingSlipDetailPickList, BasePickVO::getId);
        // 全部分配完就拣货完成
        boolean picked = pickingSlipDetailPickList.stream().allMatch(detail -> Objects.equals(detail.getQty(), detail.getPickedQty()))
                // 数据里的也全部拣货完
                && otbPrepPickingSlipDetailService.allPickedIgnoreDetailIdList(prepPickingSlip.getId(), prepPickingSlipDetailIdList);

        // 更新计算AllocatePutAwayQty
        prepPickingSlip.setAllocatePutawayQty(this.getCanPutAwayQty(pickingSlipDetailPickList, prepPickingSlip));

        if (Objects.equals(prepPickingSlip.getOtbPrepPickingSlipStatus(), OtbPrepPickingSlipStatusEnum.NEW.getStatus())) {
            // 拣货单 InPicking 日志
            OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, OtbPrepPickingSlipStatusEnum.IN_PICKING.getStatus(), null, null);
        }

        // 设置状态
        prepPickingSlip.setOtbPrepPickingSlipStatus(picked
                ? OtbPrepPickingSlipStatusEnum.PICKED.getStatus()
                : OtbPrepPickingSlipStatusEnum.IN_PICKING.getStatus()
        );
        // 绑定拣货单锁信息
        pickingSlipDetailPickList.forEach(obj -> {
            obj.setRefTableShowRefNum(prepPickingSlip.getRefNum());
            obj.setRefTableShowName(OtbPrepPickingSlip.class.getSimpleName());
        });

        // 绑定拣货后的信息到上下文中
        context.setPickAfterPrepDetailList(pickingSlipDetailPickList);
    }

    /**
     * 获取可以PutAway的数量
     *
     * @param prepPickingSlip Prep拣货单
     * @return /
     */
    private int getCanPutAwayQty(List<OtbPrepPickingSlipDetailPickVO> pickList, OtbPrepPickingSlip prepPickingSlip) {
        boolean allPicked = pickList.stream().allMatch(obj -> obj.getQty() - obj.getPickedQty() == 0);
        // 全部拣货完成
        return allPicked
                ? prepPickingSlip.getQty()
                : pickingSlipService.getCanPutAwayQty(pickList, this.getPrepProductTree(prepPickingSlip));
    }

    /**
     * 获取Prep产品树结构
     *
     * @param prepPickingSlip prepPickingSlip
     * @return /
     */
    private PrepFullProductVO getPrepProductTree(OtbPrepPickingSlip prepPickingSlip) {
        // 获取所有工单
        Map<Long, List<OtbPrepWorkorder>> allWorkOrdertGroupByPsMap
                = otbPrepWorkorderService.groupByOtbPrepPickingSlipIdList(Collections.singletonList(prepPickingSlip.getId()));
        if (ObjectUtil.isEmpty(allWorkOrdertGroupByPsMap)) {
            return null;
        }
        // Prep工单id
        List<Long> prepWorkOrderIdList = StreamUtils.distinctMap(allWorkOrdertGroupByPsMap.get(prepPickingSlip.getId()), IdModel::getId);

        // Prep工单详情
        List<WorkOrderMembershipBO> detailList = otbPrepWorkorderDetailService.groupByOtbPrepWorkOrderIdList(prepWorkOrderIdList)
                .values()
                .stream()
                .flatMap(Collection::stream)
                .map(obj -> BeanUtil.copyNew(obj, WorkOrderMembershipBO.class))
                .toList();

        // 获取Group\填充Group
        ProductTreeQuery productTreeQuery = new ProductTreeQuery()
                .setProductId(prepPickingSlip.getProductId())
                .setProductVersionInt(prepPickingSlip.getPrepPickingSlipVersionInt())
                .setPrepWorkOrderType(prepPickingSlip.getOtbPrepPickingSlipType());
        return pickingSlipService.findAndFillWithPrepConvert(productTreeQuery, detailList);
    }

    /**
     * 记录拣货日志
     *
     * @param context 上下文
     */
    private void recordPickLog(OtbPrepPickingSlipPickContextVO context) {
        // Prep拣货单: New -> InPicking 日志
        String desc = AuditLogUtil.pickLogDesc(context.getPickAfterPrepDetailList());
        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();

        // 拣货单 Pick 日志
        OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, PickingSlipLogConstant.PICK_STATUS, desc, null, BaseTypeLogEnum.OPERATION.getType());

        // 拣货单 InPicking 日志
        OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, OtcPrepPickingSlipStatusEnum.IN_PICKING.getStatus(), null, null);

        // Picked 记录日志
        if (Objects.equals(prepPickingSlip.getOtbPrepPickingSlipStatus(), OtcPrepPickingSlipStatusEnum.PICKED.getStatus())) {
            OtbPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, PickingSlipLogConstant.PICKED_DESCRIPTION, null);
        }
    }

}
