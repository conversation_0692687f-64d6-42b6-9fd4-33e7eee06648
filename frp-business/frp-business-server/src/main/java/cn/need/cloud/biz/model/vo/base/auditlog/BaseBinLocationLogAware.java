package cn.need.cloud.biz.model.vo.base.auditlog;

import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Optional;

/**
 * 库位日志感知接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@FunctionalInterface
public interface BaseBinLocationLogAware {

    /**
     * 库位id
     *
     * @return 库位id
     */
    @JsonIgnore
    Long getBinLocationId();

    //todo: 不需要每次都去查询

    /**
     * 库位日志返回实体实现
     *
     * @return 库位日志
     */
    default BaseBinLocationLogVO getBinLocation() {
        return Optional.ofNullable(this.getBinLocationId())
                .map(BinLocationCacheUtil::getById)
                .map(binLocationCache -> BeanUtil.copyNew(binLocationCache, BaseBinLocationLogVO.class))
                .orElse(null);
    }
}
