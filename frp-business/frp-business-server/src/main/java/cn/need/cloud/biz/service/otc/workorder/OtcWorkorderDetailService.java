package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.vo.base.RelatedProductVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestDetailFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageDetailFullVO;
import cn.need.cloud.biz.service.inventory.WorkorderDetailService;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC工单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcWorkorderDetailService extends
        SuperService<OtcWorkorderDetail>,
        WorkorderDetailService<OtcWorkorderDetail, OtcWorkorder> {

    /**
     * 根据工单id集合获取工单与工单详情的映射关系
     *
     * @param workOrderIds 工单id集合
     * @return 工单与工单详情的映射关系
     */
    Map<Long, List<OtcWorkorderDetail>> groupByOtcWorkOrderIdList(List<Long> workOrderIds);

    /**
     * 根据工单详情明细id获取关联产品信息
     *
     * @param ids 详情id集合
     * @return 关联产品信息
     */
    Map<Long, RelatedProductVO> relatedProductByIds(List<Long> ids);

    /**
     * 根据工单id集合获取锁定库存参数
     *
     * @param workOrderList 工单集合
     * @return /
     */
    List<InventoryReleaseLockedParam> findInventoryLockedReleaseLockedParam(List<OtcWorkorder> workOrderList);

    /**
     * 根据OTC工单id获取OTC工单详情集合
     *
     * @param otcWorkOrderId OTC工单id
     * @return OTC工单详情集合
     */
    List<OtcWorkorderDetail> listByWorkOrderId(Long otcWorkOrderId);

    /**
     * 根据OTC工单id获取OTC工单详情集合
     *
     * @param otcWorkOrderId OTC工单id
     * @return OTC工单详情集合
     */
    List<OtcWorkorderDetail> listByWorkOrderIds(Collection<Long> otcWorkOrderId);

    OtcWorkorderDetail buildWorkorderDetailByProductId(Long productId);

    OtcWorkorderDetail buildWorkorderDetail(OtcRequestDetailFullVO detail);

    OtcWorkorderDetail buildWorkorderDetail(OtcRequestPackageDetailFullVO detail);

    List<OtcWorkorderDetail> buildWorkorderDetailList(List<OtcRequestPackageDetailFullVO> details);
}