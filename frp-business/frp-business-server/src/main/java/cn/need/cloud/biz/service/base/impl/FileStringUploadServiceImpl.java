package cn.need.cloud.biz.service.base.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RegexConstant;
import cn.need.cloud.biz.model.bo.base.FrpFileModel;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.base.UploadStringAble;
import cn.need.cloud.dfs.client.api.AttachClient;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.util.ApiUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 文件字符串上传服务实现类
 * </p>
 * <p>
 * 该类实现了文件字符串上传的功能，主要用于处理Base64编码的文件内容的上传操作。
 * 主要功能包括：
 * 1. 单个标签文件的上传，将Base64编码的文件内容上传到文件服务器
 * 2. 批量标签文件的上传，支持同时上传多个Base64编码的文件
 * 3. Base64字符串的校验，确保上传的内容是有效的Base64编码
 * </p>
 * <p>
 * 该服务主要用于处理标签文件，如托盘标签、包装标签等的上传操作，
 * 将文件内容上传到文件服务器并返回文件链接，便于后续的文件访问和下载。
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class FileStringUploadServiceImpl implements FileStringUploadService {

    /**
     * 附件客户端，用于与文件服务器进行交互，实现文件的上传和下载功能
     */
    @Resource
    private AttachClient attachClient;


    /**
     * 上传单个标签文件
     * <p>
     * 该方法用于将单个对象中的Base64编码的标签数据上传到文件服务器。
     * 主要流程包括：
     * 1. 校验上传对象及其标签数据和文件类型是否为空
     * 2. 如果文件类型不是文件链接，则校验Base64字符串并上传到文件服务器
     * 3. 将上传后的文件链接设置回对象的标签数据字段
     * 4. 从文件链接中提取文件扩展名，并设置到对象的文件类型字段
     * </p>
     * <p>
     * 该方法支持泛型，可以处理任何实现了UploadStringAble接口的对象。
     * </p>
     *
     * @param uploadObject 要上传标签的对象，必须实现UploadStringAble接口
     * @param <T>          实现UploadStringAble接口的类型
     * @throws BusinessException 如果标签数据为空或Base64字符串无效，则抛出异常
     *                           <p>
     */
    @Override
    public <T extends UploadStringAble> void uploadLabel(T uploadObject) {
        // 校验参数
        // 校验参数
        if (ObjectUtil.isEmpty(uploadObject)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        if (ObjectUtil.isEmpty(uploadObject.getLabelRawData())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "labelRawData"));
        }
        if (ObjectUtil.isEmpty(uploadObject.getFileIdRawDataType())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "fileIdRawDataType"));
        }
        String fileLink = uploadObject.getLabelRawData();
        if (ObjectUtil.notEqual(uploadObject.getFileIdRawDataType(), FileDataTypeEnum.FILE_LINK)) {
            // 校验base64String
            checkValidBase64String(uploadObject.getLabelRawData());
            // 上传文件
            fileLink = ApiUtil.getResultData(attachClient.uploadByBase64String(uploadObject.getFileIdRawDataType(), uploadObject.getLabelRawData()));
        }
        // 设置对象的标签数据为文件链接
        uploadObject.setLabelRawData(fileLink);

        // 获取文件ID的扩展名
        String fileIdRawDataType = getFileIdRawDataType(fileLink);

        // 设置对象的文件转换后的扩展名
        uploadObject.setFileIdRawDataType(fileIdRawDataType);

    }

    @Override
    public <T extends FrpFileModel> void uploadFrpFile(T uploadObject) {
        // 校验参数
        if (ObjectUtil.isEmpty(uploadObject)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        if (ObjectUtil.isEmpty(uploadObject.getFileData())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "fileData"));
        }
        if (ObjectUtil.isEmpty(uploadObject.getFileType())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "fileType"));
        }
        String fileLink = uploadObject.getFileData();
        if (ObjectUtil.notEqual(uploadObject.getFileType(), FileDataTypeEnum.FILE_LINK)) {
            // 校验base64String
            checkValidBase64String(uploadObject.getFileData());
            // 上传文件
            fileLink = ApiUtil.getResultData(attachClient.uploadByBase64String(uploadObject.getFileType().toString(), uploadObject.getFileData()));
        }
        // 设置对象的标签数据为文件链接
        uploadObject.setFileData(fileLink);

        // // 获取文件ID的扩展名
        // String fileIdRawDataType = getFileIdRawDataType(fileLink);
        //
        // // 设置对象的文件转换后的扩展名
        // uploadObject.setFileExtension(fileIdRawDataType);

    }

    /**
     * 批量上传标签文件
     * <p>
     * 该方法用于批量将多个对象中的Base64编码的标签数据上传到文件服务器。
     * 主要流程包括：
     * 1. 校验上传列表是否为空及列表中的对象ID是否为空
     * 2. 过滤出有效的需要上传的对象（标签数据和文件类型非空，且不是文件链接）
     * 3. 校验所有需要上传的Base64字符串是否有效
     * 4. 根据ID和文件类型、Base64字符串构建映射关系
     * 5. 批量上传文件到文件服务器
     * 6. 将上传后的文件链接和文件扩展名设置回对应的对象
     * </p>
     * <p>
     * 该方法支持泛型，可以处理任何实现了UploadStringAble接口的对象列表。
     * 批量上传相比单个上传可以减少网络请求次数，提高性能。
     * </p>
     *
     * @param uploadList 要上传标签的对象列表，必须实现UploadStringAble接口
     * @param <T>        实现UploadStringAble接口的类型
     * @throws BusinessException 如果对象ID为空或Base64字符串无效，则抛出异常
     *                           <p>
     */
    @Override
    public <T extends UploadStringAble> void uploadLabelBatch(List<T> uploadList) {
        //校验参数
        // 校验参数
        if (ObjectUtil.isEmpty(uploadList)) {
            return;
        }
        if (uploadList.stream().anyMatch(o -> ObjectUtil.isEmpty(o.getId()))) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "label id"));
        }
        //过滤掉没有传参的label
        List<T> list = uploadList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getLabelRawData())
                && ObjectUtil.isNotEmpty(o.getFileIdRawDataType())
                //且原类型不是文件链接
                && ObjectUtil.notEqual(o.getFileIdRawDataType(), FileDataTypeEnum.FILE_LINK.getType())).toList();

        // 获取base64StringList
        List<String> base64StringList = list.stream().map(UploadStringAble::getLabelRawData).toList();

        // 校验base64StringList
        checkValidBase64StringList(base64StringList);

        // 根据id 和文本内容构建map
        Map<Long, String> collect = getLongStringMap(list);

        // 批量上传文件
        Map<Long, String> resultData = ApiUtil.getResultData(attachClient.uploadByBase64Strings(collect));

        // 根据map 更新实体对应的文件上传路径
        uploadList.forEach(entity -> {
            String fileLink = resultData.get(entity.getId());
            if (ObjectUtil.isNotEmpty(fileLink)) {
                entity.setLabelRawData(fileLink);
            } else {
                fileLink = entity.getLabelRawData();
            }
            String fileIdRawDataType = getFileIdRawDataType(fileLink);
            entity.setFileIdRawDataType(fileIdRawDataType);
        });
    }

    /**
     * 校验Base64字符串是否有效
     * <p>
     * 该方法用于验证单个Base64字符串是否符合Base64编码格式。
     * 使用正则表达式对Base64字符串进行验证，如果不符合格式则抛出异常。
     * </p>
     *
     * @param base64String 要验证的Base64字符串
     * @throws BusinessException 如果Base64字符串无效，则抛出异常
     */
    private void checkValidBase64String(String base64String) {
        if (!base64String.matches(RegexConstant.BASE64_STRING_REGEX)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "base64String", "不符合Base64编码格式要求"));
        }
    }

    /**
     * 校验多个Base64字符串是否有效
     * <p>
     * 该方法用于验证多个Base64字符串是否符合Base64编码格式。
     * 使用流处理对列表中的每个Base64字符串进行验证，如果有任何一个不符合格式则抛出异常。
     * </p>
     *
     * @param base64StringList 要验证的Base64字符串列表
     * @throws BusinessException 如果列表中有任何一个Base64字符串无效，则抛出异常
     */
    private void checkValidBase64StringList(List<String> base64StringList) {
        if (base64StringList.stream().anyMatch(base64String -> !base64String.matches(RegexConstant.BASE64_STRING_REGEX))) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "base64String", "列表中存在不符合Base64编码格式要求的字符串"));
        }
    }

    /**
     * 根据上传列表获取ID到文件类型和Base64字符串的映射关系
     * <p>
     * 该方法用于根据上传列表创建一个映射关系，将对象的ID映射到其文件类型和Base64字符串的组合。
     * 这个映射关系用于批量上传文件时传递给文件服务器。
     * </p>
     *
     * @param list 上传对象列表，必须实现UploadStringAble接口
     * @return 对象ID到文件类型和Base64字符串的映射关系
     */
    private <T extends UploadStringAble> Map<Long, String> getLongStringMap(List<T> list) {
        // 创建map 根据id映射 base64Type,base64String拼接字符串
        return list.stream()
                .collect(Collectors.toMap(UploadStringAble::getId, o -> o.getFileIdRawDataType() + StringPool.COMMA + o.getLabelRawData()));
    }

    /**
     * 获取文件链接中的文件扩展名
     * <p>
     * 该方法用于从文件链接中提取文件的扩展名，即最后一个点号后面的内容。
     * 例如，从“http://example.com/file.pdf”中提取“pdf”。
     * </p>
     * <p>
     * 如果文件链接中没有点号，或者点号在最后一个位置，则返回空字符串。
     * </p>
     *
     * @param input 输入的文件链接字符串
     * @return 文件扩展名，如果找不到点号或点号在最后一个位置，则返回空字符串
     */
    public String getFileIdRawDataType(String input) {
        int lastDotIndex = input.lastIndexOf(StringPool.DOT);
        if (lastDotIndex != -1 && lastDotIndex < input.length() - 1) {
            // 确保.不是字符串的最后一个字符，以避免返回空字符串作为扩展名（尽管这通常不是文件名的有效情况）
            return input.substring(lastDotIndex + 1);
        } else {
            // 如果没有找到.或者.在字符串末尾，返回空字符串
            return "";
        }
    }
}
