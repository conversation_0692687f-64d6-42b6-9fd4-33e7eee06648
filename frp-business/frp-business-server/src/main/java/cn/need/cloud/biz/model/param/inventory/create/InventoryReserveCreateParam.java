package cn.need.cloud.biz.model.param.inventory.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 预留库存 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "预留库存 vo对象")
public class InventoryReserveCreateParam implements Serializable {

    /**
     * refTableId
     */
    @Schema(description = "refTableId")
    private Long refTableId;

    /**
     * refTableName
     */
    @Schema(description = "refTableName")
    private String refTableName;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum")
    private String refTableRefNum;

    /**
     * refTableShowName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * prep做完的数量
     */
    @Schema(description = "prep做完的数量")
    private Integer reserveQty;
    /**
     * prep做完的数量
     */
    @Schema(description = "reserveStatus")
    private String reserveStatus;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * reserveType
     */
    @Schema(description = "reserveType")
    private String reserveType;

}