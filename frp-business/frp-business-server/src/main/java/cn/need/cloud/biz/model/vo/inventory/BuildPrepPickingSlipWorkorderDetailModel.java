package cn.need.cloud.biz.model.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * BuildPrepPickingSlipWorkorderDetailModel 类
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "BuildPrepPickingSlipWorkorderDetailModel")
public class BuildPrepPickingSlipWorkorderDetailModel implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;
    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    private Long id;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 渠道要求的需要贴的产品标识码
     * <p>
     * 目前是和产品 UPC 做对比
     * 如果不同每个数量都要贴
     * </p>
     */
    @Schema(description = "渠道要求的需要贴的产品标识码，目前是和产品 UPC 做对比，如果不同每个数量都要贴")
    private String productBarcode;

    /**
     * 渠道要求的需要贴的产品标识 SKU
     */
    @Schema(description = "渠道要求的需要贴的产品标识 SKU")
    private String productChannelSku;

    /**
     * 产品 ID
     */
    @Schema(description = "产品 ID")
    private Long productId;
}
