package cn.need.cloud.biz.model.vo.inventory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 库存-库位 vo对象
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库存-库位 vo对象")
public class InventoryCopyVO extends InventoryInStockCopyVO {

    /**
     * 可用库存
     */
    @Schema(description = "availQty")
    private Integer availQty;

    /**
     * components
     */
    @Schema(description = "components")
    private List<InventoryVO> components;

    /**
     * componentQty
     */
    @Schema(description = "componentQty")
    private Integer componentQty;

    /**
     * 自己的可用库存
     */
    @Schema(description = "自己的可用库存")
    private Integer aloneAvailQty;

    /**
     * 这个只有 Group Child 才有值
     */
    @Schema(description = "childGroupConvertAssemblyQty")
    private Integer childGroupConvertAssemblyQty;

    /**
     * 转换库存货架上可用Qty
     */
    @Schema(description = "convertAssemblyInStockAvailQty")
    private Integer convertAssemblyInStockAvailQty;

    /**
     * Combo 转换锁定库存
     */
    @Schema(description = "convertAssemblyLockedQty")
    private Integer convertAssemblyLockedQty;

    /**
     * ConvertAssemblyQty
     */
    @Schema(description = "convertAssemblyQty")
    private Integer convertAssemblyQty;

    /**
     * groups
     */
    @Schema(description = "groups")
    private List<InventoryVO> groups;

    /**
     * 产品组可用数量
     */
    @Schema(description = "groupsQty")
    private Integer groupsQty;

    /**
     * 产品组货架上可用Qty
     */
    @Schema(description = "groupsInStockAvailQty")
    private Integer groupsInStockAvailQty;

    /**
     * 产品组转换锁定库存
     */
    @Schema(description = "groupsLockedQty")
    private Integer groupsLockedQty;
}
