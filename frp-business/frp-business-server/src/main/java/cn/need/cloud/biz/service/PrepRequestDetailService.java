package cn.need.cloud.biz.service;

import cn.need.cloud.biz.model.entity.PrepRequestDetail;
import cn.need.cloud.biz.model.param.otb.create.PrepRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.prep.PrepRequestDetailUpdateParam;
import cn.need.cloud.biz.model.query.PrepRequestDetailQuery;
import cn.need.cloud.biz.model.vo.PrepRequestDetailVO;
import cn.need.cloud.biz.model.vo.page.PrepRequestDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 预请求详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface PrepRequestDetailService extends SuperService<PrepRequestDetail> {

    /**
     * 根据参数新增预请求详情
     *
     * @param createParam 请求创建参数，包含需要插入的预请求详情的相关信息
     * @return 预请求详情ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(PrepRequestDetailCreateParam createParam);


    /**
     * 根据参数更新预请求详情
     *
     * @param updateParam 请求创建参数，包含需要更新的预请求详情的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(PrepRequestDetailUpdateParam updateParam);

    /**
     * 根据查询条件获取预请求详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个预请求详情对象的列表(分页)
     */
    List<PrepRequestDetailPageVO> listByQuery(PrepRequestDetailQuery query);

    /**
     * 根据查询条件获取预请求详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个预请求详情对象的列表(分页)
     */
    PageData<PrepRequestDetailPageVO> pageByQuery(PageSearch<PrepRequestDetailQuery> search);

    /**
     * 根据ID获取预请求详情
     *
     * @param id 预请求详情ID
     * @return 返回预请求详情VO对象
     */
    PrepRequestDetailVO detailById(Long id);


    /**
     * 根据预请求id获取预请求详情集合
     *
     * @param prepRequestId 预请求id
     * @return 预请求详情集合
     */
    List<PrepRequestDetailVO> listByPrepRequestId(Long prepRequestId);


}