package cn.need.cloud.biz.model.param.base.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * OTC上架单取消对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@Schema(description = "上架单取消对象")
public class PutawaySlipCancelUpdateParam implements Serializable {

    @Schema(description = "上架单主键")
    @NotNull(message = "id is not null")
    @NotEmpty(message = "id is not empty")
    private List<Long> idList;

    @Schema(description = "备注")
    @NotNull(message = "note is not null")
    @NotBlank(message = "note is not blank")
    private String note;
}
