package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * 库位展示对象
 * <p>
 * 子类中不能包含 binLocationId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseBinLocationShowVO implements BaseBinLocationAware {
    /**
     * 库位id
     */
    @Getter
    @Setter
    private Long binLocationId;

    /**
     * 库位
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    private BaseBinLocationVO baseBinLocationVO;

    @Override
    public BaseBinLocationVO getBaseBinLocationVO() {
        if (ObjectUtil.isEmpty(binLocationId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseBinLocationVO)) {
            return baseBinLocationVO;
        }
        // Retrieve from cache once and store the result
        baseBinLocationVO = BaseBinLocationAware.super.getBaseBinLocationVO();
        return baseBinLocationVO;
    }
}
