package cn.need.cloud.biz.service.otb.pkg.impl;

import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPackagePalletTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPackageStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbWorkorderEnum;
import cn.need.cloud.biz.converter.otb.OtbPackageConverter;
import cn.need.cloud.biz.mapper.otb.OtbPackageMapper;
import cn.need.cloud.biz.model.bo.otb.OtbBuildPalletContextBo;
import cn.need.cloud.biz.model.bo.otb.OtbBuildShipmentContextBO;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.otb.create.pallet.OtbPalletCreateParam;
import cn.need.cloud.biz.model.param.otb.create.shipment.OtbShipmentCreateParam;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageDetailVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageLabelVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import cn.need.cloud.biz.model.vo.page.OtbPackagePageVO;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPackageAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.service.otb.impl.logutil.OtbWorkorderLogUtil;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipDetailService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageDetailService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageLabelService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.bean.BeanUtil.copyNew;

/**
 * <p>
 * OTB包裹服务实现类
 * </p>
 * <p>
 * 该类负责处理OTB（Outbound Transport）出库包裹相关的业务逻辑，包括包裹的查询、状态更新、
 * 打托操作、发货单创建等功能。OTB包裹是出库业务流程中重要的实体，记录了待发货商品的包装信息。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 包裹信息的基础CRUD操作
 * 2. 包裹与托盘、发货单的关联管理
 * 3. 包裹状态的流转控制
 * 4. 包裹标签的打印和管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbPackageServiceImpl extends SuperServiceImpl<OtbPackageMapper, OtbPackage> implements OtbPackageService {

    /**
     * 出库工单服务，用于获取和管理工单信息
     */
    @Resource
    private OtbWorkorderService otbWorkorderService;

    /**
     * 出库请求服务，用于获取请求单相关信息
     */
    @Resource
    private OtbRequestService otbRequestService;

    /**
     * 拣货单服务，用于获取和管理拣货单信息
     */
    @Resource
    private OtbPickingSlipService otbPickingSlipService;

    /**
     * 拣货单详情服务，用于获取拣货单明细信息
     */
    @Resource
    private OtbPickingSlipDetailService otbPickingSlipDetailService;

    /**
     * 包裹详情服务，用于管理包裹内的具体商品信息
     */
    @Resource
    private OtbPackageDetailService otbPackageDetailService;

    /**
     * 包裹标签服务，用于管理包裹的物流标签
     */
    @Resource
    private OtbPackageLabelService otbPackageLabelService;

    /**
     * 审计日志服务，用于记录操作日志
     */
    @Resource
    private AuditShowLogService auditShowLogService;

    /**
     * 发货单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtbShipmentService otbShipmentService;

    /**
     * 托盘服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtbPalletService otbPalletService;

    /**
     * 根据查询条件获取OTB包裹列表
     * <p>
     * 该方法根据指定的查询条件返回符合条件的OTB包裹列表，不包含分页信息。
     * </p>
     *
     * @param query 包含查询条件的OTB包裹查询对象
     * @return 符合条件的OTB包裹分页视图对象列表
     * <p>
     * TODO: 缺少对查询结果的数量限制，可能在数据量大时影响性能
     * 优化建议：添加最大返回条数限制，或强制使用分页方式获取数据
     */
    @Override
    public List<OtbPackagePageVO> listByQuery(OtbPackageQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 分页查询OTB包裹列表
     * <p>
     * 该方法根据查询条件和分页参数获取OTB包裹列表，支持多种复杂条件过滤，
     * 并将查询结果组装成包含完整信息的VO对象。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的OTB包裹分页视图对象
     * <p>
     * TODO: 方法中同时进行了多次数据库查询操作，可能影响性能
     * 优化建议：将关联查询改为一次批量查询，减少数据库交互次数
     */
    @Override
    public PageData<OtbPackagePageVO> pageByQuery(PageSearch<OtbPackageQuery> search) {
        //获取分页参数
        Page<OtbPackage> page = Conditions.page(search, entityClass);
        //获取查询条件
        OtbPackageQuery condition = search.getCondition();
        //填充工单id，拣货单id，请求单id
        fillIdList(condition);
        //获取包裹分页列表
        List<OtbPackagePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        //组装数据
        assembleData(dataList, condition);
        //返回分页列表
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取OTB包裹详情
     * <p>
     * 该方法获取指定ID的OTB包裹详细信息，包括包裹标签、详情等关联信息。
     * 如果找不到对应ID的包裹，则抛出业务异常。
     * </p>
     *
     * @param id OTB包裹ID
     * @return 包含详细信息的OTB包裹视图对象
     * @throws BusinessException 如果找不到指定ID的包裹记录
     */
    @Override
    public OtbPackageVO detailById(Long id) {
        OtbPackage entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtbPackage");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbPackage", id));
        }
        return buildOtbPackageVO(entity);
    }

    /**
     * 根据参考编号获取OTB包裹详情
     * <p>
     * 该方法根据包裹的参考编号获取包裹详细信息，包括包裹标签、详情等关联信息。
     * 如果找不到对应参考编号的包裹，则抛出业务异常。
     * </p>
     *
     * @param refNum OTB包裹参考编号
     * @return 包含详细信息的OTB包裹视图对象
     * @throws BusinessException 如果找不到指定参考编号的包裹记录
     *                           <p>
     *                                                                                                                                                                                       TODO: 方法中调用了未定义的getByRefNum方法，可能需要补充实现
     *                                                                                                                                                                                       优化建议：确保getByRefNum方法已正确实现，或使用lambdaQuery替代
     */
    @Override
    public OtbPackageVO detailByRefNum(String refNum) {
        OtbPackage entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtbPackage");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPackage", "RefNum", refNum));
        }
        return buildOtbPackageVO(entity);
    }

    /**
     * 根据SSCC编号获取OTB包裹详情
     * <p>
     * 该方法根据包裹的SSCC编号(物流标准编码)获取包裹详细信息，包括包裹标签、详情等关联信息。
     * 如果找不到对应SSCC编号的包裹，则抛出业务异常。
     * </p>
     *
     * @param packageSsccNum OTB包裹SSCC编号
     * @return 包含详细信息的OTB包裹视图对象
     * @throws BusinessException 如果找不到指定SSCC编号的包裹记录
     */
    @Override
    public OtbPackageVO detailBySsccNum(String packageSsccNum) {
        OtbPackage entity = getBySsccNum(packageSsccNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("packageSsccNum: " + packageSsccNum + " not found in OtbPackage");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPackage", "SSCC Number", packageSsccNum));
        }
        return buildOtbPackageVO(entity);
    }

    /**
     * 根据SSCC编号列表批量获取OTB包裹
     * <p>
     * 该方法根据提供的SSCC编号集合批量查询对应的包裹信息。
     * 如果集合为空，则返回空列表。
     * </p>
     *
     * @param ssccnumList SSCC编号集合
     * @return 符合条件的OTB包裹列表
     */
    @Override
    public List<OtbPackage> listBySsccNum(Collection<String> ssccnumList) {
        if (ObjectUtil.isEmpty(ssccnumList)) {
            return Lists.arrayList();
        }
        return lambdaQuery()
                .in(OtbPackage::getSsccNum, ssccnumList)
                .list();
    }

    /**
     * 更新包裹的打托信息
     * <p>
     * 该方法用于将包裹关联到托盘，更新包裹的打托状态和相关信息，
     * 并记录操作日志。这是包裹从单独包装到组合成托盘的重要环节。
     * </p>
     *
     * @param contextBo 包含托盘和包裹信息的上下文对象
     *                  <p>
     *                                                                                                                        TODO: 未对包裹的原始状态进行验证，可能导致状态流转错误
     *                                                                                                                        优化建议：添加对包裹当前状态的校验，确保状态流转合理
     */
    @Override
    public void updatePackageInfo(OtbBuildPalletContextBo contextBo) {
        //打托单信息
        OtbPallet otbPallet = contextBo.getOtbPallet();
        //获取包裹
        List<OtbPackage> otbPackageList = contextBo.getOtbPackageList();
        //遍历包裹集合
        otbPackageList.forEach(item -> {
            //打托单id
            item.setOtbPalletId(otbPallet.getId());
            //打托类型
            item.setOtbPalletType(OtbPackagePalletTypeEnum.PALLET.getType());
            //包裹状态
            item.setOtbPackageStatus(OtbPackageStatusEnum.BUILD_PALLET.getCode());
            //记录日志
            OtbPackageAuditLogHelper.recordLog(item, otbPallet.toLog());
        });
        super.updateBatch(otbPackageList);
    }

    /**
     * 根据托盘ID获取关联的OTB包裹VO列表
     * <p>
     * 该方法查询指定托盘ID下的所有包裹，并转换为视图对象返回。
     * </p>
     *
     * @param id 托盘ID
     * @return 托盘关联的包裹视图对象列表
     */
    @Override
    public List<OtbPackageVO> listByOtbPalletId(Long id) {
        return lambdaQuery().eq(OtbPackage::getOtbPalletId, id)
                .list()
                .stream()
                .map(item -> BeanUtil.copyNew(item, OtbPackageVO.class))
                .toList();
    }

    /**
     * 根据托盘ID列表批量获取关联的OTB包裹
     * <p>
     * 该方法根据提供的托盘ID列表查询所有关联的包裹信息。
     * 如果列表为空，则返回空列表。
     * </p>
     *
     * @param otbPalletIdList 托盘ID列表
     * @return 托盘关联的包裹列表
     */
    @Override
    public List<OtbPackage> listByPalletIdList(List<Long> otbPalletIdList) {
        if (ObjectUtil.isEmpty(otbPalletIdList)) {
            return Lists.arrayList();
        }
        return lambdaQuery()
                .in(OtbPackage::getOtbPalletId, otbPalletIdList)
                .list();
    }

    /**
     * 根据SSCC编号获取OTB包裹
     * <p>
     * 该方法根据包裹的SSCC编号查询单个包裹信息。
     * </p>
     *
     * @param packageSsccNum 包裹SSCC编号
     * @return 对应的OTB包裹对象，如果未找到则返回null
     */
    @Override
    public OtbPackage getBySsccNum(String packageSsccNum) {
        return lambdaQuery()
                .eq(OtbPackage::getSsccNum, packageSsccNum)
                .one();
    }

    @Override
    public void updateBatchWithNull(List<OtbPackage> otbPackageList) {
        mapper.updateBatchWithNull(otbPackageList);
    }

    @Override
    public void updatePackageStatus(OtbBuildShipmentContextBO context) {
        // 更新包裹状态
        OtbShipment otbShipment = context.getOtbShipment();
        context.getOtbPackageList().forEach(item -> {
            item.setOtbShipmentId(otbShipment.getId());
            item.setOtbPackageStatus(OtbPackageStatusEnum.BUILD_SHIPMENT.getCode());
            OtbPackageAuditLogHelper.recordLog(item, otbShipment.toLogBuild());
        });
        //持久化
        super.updateBatch(context.getOtbPackageList());
    }

    @Override
    public void updateStatusBatch(List<OtbPackage> otbPackageList, OtbPackageStatusEnum statusEnum) {
        otbPackageList.forEach(item -> item.setOtbPackageStatus(statusEnum.getCode()));
        super.updateBatch(otbPackageList);
    }

    @Override
    public List<OtbPackage> listByShipmentId(List<Long> shipmentIdList) {
        if (ObjectUtil.isEmpty(shipmentIdList)) {
            return Lists.arrayList();
        }
        return lambdaQuery().in(OtbPackage::getOtbShipmentId, shipmentIdList)
                .list();
    }

    @Override
    public List<OtbPackage> listByWorkOrderIdList(List<Long> workorderIds) {
        if (ObjectUtil.isNotEmpty(workorderIds)) {
            return lambdaQuery().in(OtbPackage::getOtbWorkorderId, workorderIds).list();
        }
        return List.of();
    }

    @Override
    public List<OtbPackage> listByWorkorderIdAndIds(List<Long> idList, Long workorderId) {
        if (ObjectUtil.isNotEmpty(idList) && ObjectUtil.isNotNull(workorderId)) {
            return lambdaQuery().in(OtbPackage::getId, idList)
                    .eq(OtbPackage::getOtbWorkorderId, workorderId)
                    .list();
        }
        return List.of();
    }

    @Override
    public List<OtbPackageVO> listByShipmentId(Long id) {
        return lambdaQuery().eq(OtbPackage::getOtbShipmentId, id)
                .list()
                .stream()
                .map(item -> BeanUtil.copyNew(item, OtbPackageVO.class))
                .toList();
    }

    @Override
    public List<OtbPackageVO> listWithLabelByShipmentId(Long id) {
        //获取包裹信息
        List<OtbPackageVO> otbPackageList = lambdaQuery().eq(OtbPackage::getOtbShipmentId, id)
                .list()
                .stream()
                .map(item -> copyNew(item, OtbPackageVO.class))
                .toList();
        //获取包裹label
        List<OtbPackageLabel> otbPackageLabelList = otbPackageLabelService.listByPackageId(otbPackageList
                .stream()
                .map(OtbPackageVO::getId)
                .toList());
        //根据包裹id映射包裹label
        Map<Long, List<OtbPackageLabel>> otbPackageLabelMap = ObjectUtil.toMapList(otbPackageLabelList, OtbPackageLabel::getOtbPackageId);
        //遍历包裹信息包裹集合
        otbPackageList.forEach(item -> item.setOtbPackageLabelList(BeanUtil.copyNew(otbPackageLabelMap.get(item.getId()), OtbPackageLabelVO.class)));
        return otbPackageList;
    }


    @Override
    public void markPrintedAfter(String currentPrintStatus, String note, OtbPackageLabel otbPackageLabel) {
        //获取包裹信息
        OtbPackage otbPackage = super.getById(otbPackageLabel.getOtbPackageId());
        //记录日志
        OtbPackageAuditLogHelper.recordLog(
                otbPackage,
                StringUtil.format("{} {}", ShowLogEnum.PRINT.getStatus(), currentPrintStatus),
                BaseTypeLogEnum.OPERATION.getType(),
                note,
                otbPackageLabel.toLog()
        );
    }

    @Override
    public void firstMarkSuccessPrintedAfter(OtbPackageLabel otbPackageLabel) {
        //判断是否所有label打印完了
        if (otbPackageLabelService.isAllLabelPrintedSuccess(otbPackageLabel)) {
            return;
        }
        //获取包裹信息
        OtbPackage otbPackage = super.getById(otbPackageLabel.getOtbPackageId());
        //说明是 打包阶级
        if (StringUtil.equals(otbPackage.getOtbPackageStatus(), OtbPackageStatusEnum.NEW.getCode())) {
            otbPackage.setOtbPackageStatus(OtbPackageStatusEnum.PACKED.getCode());
            OtbPackageAuditLogHelper.recordLog(otbPackage);
            //Check相同的PickingSlip的 Package 都是Packaged
            if (!exist(otbPackage)) {
                OtbPickingSlip otbPickingSlip = otbPickingSlipService.getById(otbPackage.getOtbPickingSlipId());
                List<OtbPickingSlipDetailVO> list = otbPickingSlipDetailService.listByOtbPickingSlipId(otbPickingSlip.getId());
                int sum = list.stream().mapToInt(OtbPickingSlipDetailVO::getNeedPackedQty).sum();
                if (StringUtil.equals(otbPickingSlip.getOtbPickingSlipStatus(), OtbPickingSlipStatusEnum.PICKED.getStatus()) &&
                        ObjectUtil.equal(sum, 0)) {
                    otbPickingSlip.setOtbPickingSlipStatus(OtbPickingSlipStatusEnum.PACKED.getStatus());
                    //持久化拣货单
                    otbPickingSlipService.update(otbPickingSlip);
                    //记录日志
                    OtbPickingSlipAuditLogHelper.recordLog(otbPickingSlip);
                    //更新工单状态
                    OtbWorkorder otbWorkorder = otbWorkorderService.getById(otbPickingSlip.getOtbWorkorderId());
                    otbWorkorder.setOtbWorkorderStatus(OtbWorkorderEnum.PACKED.getStatus());
                    otbWorkorderService.update(otbWorkorder);
                    //记录日志
                    AuditLogHolder.record(OtbWorkorderLogUtil.getAuditShowLog(otbWorkorder));
                }
            }
        }
        //说明是 所有的label都打完了阶级
        if (StringUtil.equals(otbPackage.getOtbPackageStatus(), OtbPackageStatusEnum.BUILD_SHIPMENT_LABEL.getCode())) {
            //更新状态为ReadyToShip
            otbPackage.setOtbPackageStatus(OtbPackageStatusEnum.READY_TO_SHIP.getCode());
            //记录日志
            OtbPackageAuditLogHelper.recordLog(otbPackage);
            otbShipmentService.markProcessingOrReadyToShip(otbPackage.getOtbShipmentId(), otbPackage.getId());
        }
        //持久化包裹
        mapper.updateById(otbPackage);
    }

    @Override
    public boolean exist(OtbPackage otbPackage) {
        return lambdaQuery()
                .eq(OtbPackage::getOtbPickingSlipId, otbPackage.getOtbPickingSlipId())
                .ne(OtbPackage::getId, otbPackage.getId())
                .eq(OtbPackage::getOtbPackageStatus, OtbPackageStatusEnum.NEW.getCode())
                .exists();
    }

    @Override
    public boolean existNotReadyToShip(Long id, Long otbPackageId) {
        return lambdaQuery()
                .eq(OtbPackage::getOtbShipmentId, id)
                .ne(OtbPackage::getId, otbPackageId)
                .ne(OtbPackage::getOtbPackageStatus, OtbPackageStatusEnum.READY_TO_SHIP.getCode())
                .exists();
    }

    @Override
    public void checkPackageList(OtbBuildShipmentContextBO context) {
        //获取前端参数
        OtbShipmentCreateParam param = context.getParam();
        //获取包裹数据
        List<OtbPackage> otbPackageList = context.getOtbPackageList();
        //获取ssccNum
        Set<String> ssccNumList = param.getSsccNumList();
        //校验包裹列表
        // Validate.notEmpty(otbPackageList, JsonUtil.toJson(param.getSsccNumList()) + "No Found In Db");
        Validate.notEmpty(otbPackageList, String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPackage", "SSCC Numbers", JsonUtil.toJson(param.getSsccNumList())));
        //校验查出来的包裹数据
        List<String> list = otbPackageList
                .stream()
                .map(OtbPackage::getSsccNum)
                .filter(item -> !ssccNumList.contains(item))
                .toList();
        //获取当前仓库信息
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        // Validate.isTrue(ObjectUtil.isEmpty(list), JsonUtil.toJson(list) + "No Found In" + Objects.requireNonNull(warehouseCache).getRefNum());
        Validate.isTrue(ObjectUtil.isEmpty(list), String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPackage", "SSCC Numbers", JsonUtil.toJson(list) + " in warehouse " + Objects.requireNonNull(warehouseCache).getRefNum()));
        //校验是否有错误状态包裹
        List<String> errorPackageList = otbPackageList
                .stream()
                .filter(item -> !StringUtil.equals(item.getOtbPackageStatus(), OtbPackageStatusEnum.PACKED.getCode()))
                .map(OtbPackage::getSsccNum)
                .toList();
        // Validate.isTrue(ObjectUtil.isEmpty(errorPackageList), "Error Package Status" + JsonUtil.toJson(errorPackageList));
        Validate.isTrue(ObjectUtil.isEmpty(errorPackageList), String.format(ErrorMessages.STATUS_INVALID_OPERATION, "ship", "packages", JsonUtil.toJson(errorPackageList)));
        //校验包裹打托状态
        List<String> palletPackageList = otbPackageList
                .stream()
                .filter(item -> StringUtil.equals(item.getOtbPalletType(), OtbPackagePalletTypeEnum.PALLET.getType()))
                .map(OtbPackage::getSsccNum)
                .toList();
        // Validate.isTrue(ObjectUtil.isEmpty(palletPackageList), JsonUtil.toJson(palletPackageList) + "is already a pallet");
        Validate.isTrue(ObjectUtil.isEmpty(palletPackageList), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Packages " + JsonUtil.toJson(palletPackageList) + " are already part of a pallet"));
        //校验包裹工单
        List<Long> workOrderIdList = otbPackageList
                .stream()
                .map(OtbPackage::getOtbWorkorderId)
                .distinct()
                .toList();
        // Validate.isTrue(ObjectUtil.equal(workOrderIdList.size(), DataState.ENABLED), "packages has different WorkOrderId" + JsonUtil.toJson(workOrderIdList));
        Validate.isTrue(ObjectUtil.equal(workOrderIdList.size(), DataState.ENABLED), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Packages must belong to the same work order, found multiple work orders: " + JsonUtil.toJson(workOrderIdList)));
        //校验包裹拣货单
        List<Long> pickingSlipIdList = otbPackageList
                .stream()
                .map(OtbPackage::getOtbWorkorderId)
                .distinct()
                .toList();
        // Validate.isTrue(ObjectUtil.equal(pickingSlipIdList.size(), DataState.ENABLED), "packages has different PickingSlipId" + JsonUtil.toJson(pickingSlipIdList));
        Validate.isTrue(ObjectUtil.equal(pickingSlipIdList.size(), DataState.ENABLED), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Packages must belong to the same picking slip, found multiple picking slips: " + JsonUtil.toJson(pickingSlipIdList)));
    }

    @Override
    public void checkPackageStatus(OtbBuildPalletContextBo contextBo) {
        //获取前端参数
        OtbPalletCreateParam param = contextBo.getParam();
        List<OtbPackage> otbPackageList = contextBo.getOtbPackageList();
        List<String> ssccNumList = param.getSsccNumList();
        // Validate.notEmpty(ssccNumList, "otbPackageSsccNum is empty");
        Validate.notEmpty(ssccNumList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "otbPackageSsccNum"));
        // Validate.notEmpty(otbPackageList,
        //         "OtbPackageSsccNum" + JsonUtil.toJson(ssccNumList) + "No Found In Db");
        Validate.notEmpty(otbPackageList, String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPackage", "SSCC Numbers", JsonUtil.toJson(ssccNumList)));
        otbPackageList.forEach(item -> ssccNumList.remove(item.getSsccNum()));
        WarehouseCache cache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        // Validate.isTrue(ObjectUtil.isEmpty(ssccNumList), "otbPackageSsccNum: " + JsonUtil.toJson(ssccNumList) + "No Found In " + Objects.requireNonNull(cache).getName());
        Validate.isTrue(ObjectUtil.isEmpty(ssccNumList), String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbPackage", "SSCC Numbers", JsonUtil.toJson(ssccNumList) + " in warehouse " + Objects.requireNonNull(cache).getName()));
        //校验包裹状态
        List<OtbPackage> list = otbPackageList.stream()
                .filter(item -> StringUtil.equals(OtbPackageStatusEnum.NEW.getCode(), item.getOtbPackageStatus()))
                .toList();
        // Validate.isTrue(ObjectUtil.isEmpty(list), "otbPackageSsccNum: " + JsonUtil.toJson(list) + "is not Packed");
        Validate.isTrue(ObjectUtil.isEmpty(list), String.format(ErrorMessages.STATUS_REQUIRED, "Packages", "PACKED", "NEW"));
        //校验包裹是否已经打过托
        List<String> packageSsccNumList = otbPackageList.stream()
                .filter(item -> StringUtil.equals(OtbPackagePalletTypeEnum.PALLET.getType(), item.getOtbPalletType()))
                .map(OtbPackage::getSsccNum)
                .toList();
        // Validate.isTrue(ObjectUtil.isEmpty(packageSsccNumList), "otbPackageSsccNum: " + JsonUtil.toJson(packageSsccNumList) + "is already a pallet");
        Validate.isTrue(ObjectUtil.isEmpty(packageSsccNumList), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Packages " + JsonUtil.toJson(packageSsccNumList) + " are already part of a pallet"));
        //校验这些包裹是否在一个工单
        Map<Long, List<OtbPackage>> workOrderIdMap = ObjectUtil.toMapList(otbPackageList, OtbPackage::getOtbWorkorderId);
        // Validate.isTrue(ObjectUtil.equal(workOrderIdMap.size(), 1), "packages has different WorkOrderId");
        Validate.isTrue(ObjectUtil.equal(workOrderIdMap.size(), 1), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Packages must belong to the same work order"));
        //校验这些包裹是否在一个拣货单
        Map<Long, List<OtbPackage>> pickingSlipIdMap = ObjectUtil.toMapList(otbPackageList, OtbPackage::getOtbPickingSlipId);
        // Validate.isTrue(ObjectUtil.equal(pickingSlipIdMap.size(), 1), "packages has different PickingSlipId");
        Validate.isTrue(ObjectUtil.equal(pickingSlipIdMap.size(), 1), String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Packages must belong to the same picking slip"));
    }

    /**
     * 构建OTB包裹VO对象
     *
     * @param entity OTB包裹对象
     * @return 返回包含详细信息的OTB包裹VO对象
     */
    private OtbPackageVO buildOtbPackageVO(OtbPackage entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB包裹VO对象
        OtbPackageVO otbPackageVO = Converters.get(OtbPackageConverter.class).toVO(entity);
        //填充仓库信息
        WarehouseCacheUtil.filledWarehouse(otbPackageVO);
        //填充工单ref num
        Map<Long, String> workorderRefNumMap = otbWorkorderService.getRefNum(Lists.arrayList(otbPackageVO.getOtbWorkorderId()));
        otbPackageVO.setOtbWorkorderRefNum(workorderRefNumMap.get(otbPackageVO.getOtbWorkorderId()));
        //获取请求单 refNum requestRefNum
        OtbRequest otbRequest = otbRequestService.getById(otbPackageVO.getOtbRequestId());
        if (ObjectUtil.isNotEmpty(otbRequest)) {
            otbPackageVO.setOtbRequestOfRefNum(otbRequest.getRefNum());
            otbPackageVO.setOtbRequestOfRequestRefNum(otbRequest.getRequestRefNum());
        }
        //填充拣货refNum
        Map<Long, String> pickingSlipRefNumMap = otbPickingSlipService.getRefNumMap(Lists.arrayList(otbPackageVO.getOtbPickingSlipId()));
        otbPackageVO.setOtbPickingSlipRefNum(pickingSlipRefNumMap.get(otbPackageVO.getOtbPickingSlipId()));
        //填充包裹label
        otbPackageVO.setOtbPackageLabelList(otbPackageLabelService.listByPackageId(otbPackageVO.getId()));
        //填充包裹详情
        List<OtbPackageDetailVO> otbPackageDetailList = otbPackageDetailService.listByOtbPackageId(otbPackageVO.getId());
        ProductCacheUtil.filledProduct(otbPackageDetailList);
        otbPackageVO.setOtbPackageDetailList(otbPackageDetailList);
        return otbPackageVO;
    }

    /**
     * 组装数据
     *
     * @param dataList 分页列表
     */
    private void assembleData(List<OtbPackagePageVO> dataList, OtbPackageQuery condition) {
        //判空
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        //填充仓库数据
        WarehouseCacheUtil.filledWarehouse(dataList);
        //填充label信息
        Set<Long> packageIds = new HashSet<>(condition.getIdList());
        List<OtbPackageLabelVO> otbPackageLabelList = otbPackageLabelService.listByPackageId(packageIds, condition.getPrintStatusList());
        //根据包裹id映射label
        Map<Long, List<OtbPackageLabelVO>> map = ObjectUtil.toMapList(otbPackageLabelList, OtbPackageLabelVO::getOtbPackageId);
        //获取拣货单refNum
        List<Long> pickingSlipIdList = dataList.stream().map(OtbPackagePageVO::getOtbPickingSlipId).toList();
        Map<Long, String> pickingSlipRefNumMap = otbPickingSlipService.getRefNumMap(pickingSlipIdList);
        //获取工单refNum
        List<Long> workOrderIdList = dataList.stream().map(OtbPackagePageVO::getOtbWorkorderId).toList();
        Map<Long, String> workorderRefNumMap = otbWorkorderService.getRefNum(workOrderIdList);
        //获取请求单 refNum requestRefNum
        List<Long> requestIdList = dataList.stream().map(OtbPackagePageVO::getOtbRequestId).toList();
        Map<Long, OtbRequest> otbRequestRefNumMap = otbRequestService.getRefNum(requestIdList);
        //填充包裹详情
        List<OtbPackageDetailVO> otbPackageDetailList = otbPackageDetailService.listByOtbPackageId(packageIds);
        //根据id映射详情包裹详情
        Map<Long, List<OtbPackageDetailVO>> detailMap = ObjectUtil.toMapList(otbPackageDetailList, OtbPackageDetailVO::getOtbPackageId);
        //遍历分页列表
        dataList.forEach(item -> {
            //填充工单refNum
            item.setOtbWorkorderRefNum(workorderRefNumMap.get(item.getOtbWorkorderId()));
            //填充拣货refNum
            item.setOtbPickingSlipRefNum(pickingSlipRefNumMap.get(item.getOtbPickingSlipId()));
            //填充label信息
            item.setOtbPackageLabelList(map.get(item.getId()));
            //填充包裹详情
            item.setOtbPackageDetailList(detailMap.get(item.getId()));
            //填充请求单refNum requestRefNum
            OtbRequest otbRequest = otbRequestRefNumMap.get(item.getOtbRequestId());
            if (ObjectUtil.isNotEmpty(otbRequest)) {
                item.setOtbRequestOfRefNum(otbRequest.getRefNum());
                item.setOtbRequestOfRequestRefNum(otbRequest.getRequestRefNum());
            }
        });
    }

    /**
     * 填充工单id
     *
     * @param condition 查询条件
     */
    private void fillIdList(OtbPackageQuery condition) {
        //填充工单id
        condition.setOtbWorkorderIdList(otbWorkorderService.getOtcWorkOrderId(condition.getOtbWorkorderRefNumList()));
        //填充拣货单id
        condition.setOtbPickingSlipIdList(otbPickingSlipService.getPickingSlipId(condition.getOtbPickingSlipRefNumList()));
        //填充请求单
        condition.setOtbRequestIdList(otbRequestService.getRequestId(condition.getRequestOfRequestRefNumList()));
        //包裹id
        // condition.setIdList(otbPackageLabelService.getPackageId(condition.getPrintStatusList()));
        condition.setIdList(otbPackageLabelService.getPackageId(condition.getPrintStatusList())
                .stream()
                .collect(Collectors.toList()));
        //打托单id
        condition.setOtbPalletId(otbPalletService.getIdByRefNum(condition.getOtbPalletSsccNum()));
    }
}
