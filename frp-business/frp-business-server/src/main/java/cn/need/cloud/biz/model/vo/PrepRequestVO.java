package cn.need.cloud.biz.model.vo;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 预请求 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "预请求 vo对象")
public class PrepRequestVO extends BaseSuperVO {


    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String prepRequestStatus;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 交易合作伙伴ID
     */
    @Schema(description = "交易合作伙伴ID")
    private Long transactionPartnerId;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

}