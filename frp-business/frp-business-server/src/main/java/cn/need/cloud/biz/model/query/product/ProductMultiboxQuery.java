package cn.need.cloud.biz.model.query.product;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;


/**
 * 产品多箱 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品多箱 query对象")
public class ProductMultiboxQuery extends SuperQuery {

    /**
     * 发货长度
     */
    @Schema(description = "发货长度")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @Schema(description = "发货宽度")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @Schema(description = "发货高度")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @Schema(description = "发货重量")
    private BigDecimal shipWeight;

    /**
     * 发货重量单位
     */
    @Schema(description = "发货重量单位")
    private String shipWeightUnit;

    /**
     * 发货尺寸单位
     */
    @Schema(description = "发货尺寸单位")
    private String shipDimensionUnit;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * UPC码
     */
    @Schema(description = "UPC码集合")
    @Condition(value = Keyword.IN, fields = {"upc"})
    private List<String> upcList;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 多箱产品版本号
     */
    @Schema(description = "多箱产品版本号")
    private Integer multiboxVersionInt;


}