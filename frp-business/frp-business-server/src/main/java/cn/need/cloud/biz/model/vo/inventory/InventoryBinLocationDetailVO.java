package cn.need.cloud.biz.model.vo.inventory;

import cn.need.cloud.biz.model.vo.base.BaseBinLocationFullVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 库存-库位详情 vo对象
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Data
@Schema(description = "库存-库位详情 vo对象")
public class InventoryBinLocationDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;
    /**
     * 库位锁定
     */
    @Schema(description = "binLocationDetailLockedList")
    public List<InventoryBinLocationDetailLockedVO> binLocationDetailLockedList;
    /**
     * 库位
     */
    @Schema(description = "库存-vo对象")
    private BaseBinLocationFullVO baseBinLocationFullVO;
    /**
     * 主键id
     */
    private Long id;
    /**
     * inStockQty
     */
    @Schema(description = "inStockQty")
    private Integer inStockQty;
    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;
    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;
    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 在货架上可用数量
     * 不能为负数
     *
     * @return 可用数量
     */
    @Schema(description = "在货架上可用数量")
    public int getAvailableQty() {
        return Math.max(inStockQty - getLockedQty(), 0);
    }

    /**
     * 在货架上锁定数量
     * 不能为负数
     *
     * @return 锁定数量
     */
    @Schema(description = "在货架上锁定数量")
    public int getLockedQty() {
        if (ObjectUtil.isEmpty(binLocationDetailLockedList)) {
            return 0;
        }
        int totalLockedQty = binLocationDetailLockedList.stream()
                .mapToInt(InventoryBinLocationDetailLockedVO::getCurrentLockedQty)
                .sum();
        return Math.max(totalLockedQty, 0);
    }
}

