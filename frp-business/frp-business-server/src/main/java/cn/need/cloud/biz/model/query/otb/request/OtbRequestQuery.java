package cn.need.cloud.biz.model.query.otb.request;

import cn.need.cloud.biz.service.base.RequestQuery;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * OTB请求 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB请求 query对象")
public class OtbRequestQuery extends SuperQuery implements RequestQuery {

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"requestRefNum"})
    private Set<String> requestRefNumList;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;

    /**
     * 渠道
     */
    @Schema(description = "渠道集合")
    @Condition(value = Keyword.IN, fields = {"channel"})
    private List<String> channelList;

    /**
     * 发货窗口开始时间
     */
    @Schema(description = "发货窗口开始时间")
    private LocalDateTime shipWindowStart;
    /**
     * 发货窗口开始时间开始
     */
    @Schema(description = "发货窗口开始时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime shipWindowStartStart;
    /**
     * 发货窗口开始时间结束
     */
    @Schema(description = "发货窗口开始时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime shipWindowStartEnd;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String otbRequestStatus;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态集合")
    @Condition(value = Keyword.IN, fields = {"otbRequestStatus"})
    private List<String> otbRequestStatusList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * 交易伙伴Id
     */
    @Schema(description = "交易伙伴Id")
    private Long transactionPartnerId;

    /**
     * 交易伙伴Id
     */
    @Schema(description = "交易伙伴Id集合")
    @Condition(value = Keyword.IN, fields = {"transactionPartnerId"})
    private List<Long> transactionPartnerIdList;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 请求发货状态
     */
    @Schema(description = "请求发货状态")
    private String requestShipmentStatus;

    /**
     * 请求发货状态
     */
    @Schema(description = "请求发货状态集合")
    @Condition(value = Keyword.IN, fields = {"requestShipmentStatus"})
    private List<String> requestShipmentStatusList;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNum;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址是否为住宅
     */
    @Schema(description = "收货地址是否为住宅")
    private Boolean shipToAddressIsResidential;

    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 发货窗口结束时间
     */
    @Schema(description = "发货窗口结束时间")
    private LocalDateTime shipWindowEnd;
    /**
     * 发货窗口结束时间开始
     */
    @Schema(description = "发货窗口结束时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime shipWindowEndStart;
    /**
     * 发货窗口结束时间结束
     */
    @Schema(description = "发货窗口结束时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime shipWindowEndEnd;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;

    /**
     * 是否住宅地址
     */
    @Schema(description = "是否住宅地址")
    private Boolean shipFromAddressIsResidential;

    /**
     * 发货人姓名
     */
    @Schema(description = "发货人姓名")
    private String shipFromAddressName;

    /**
     * 发货备注
     */
    @Schema(description = "发货备注")
    private String shipFromAddressNote;

    /**
     * 发货电话
     */
    @Schema(description = "发货电话")
    private String shipFromAddressPhone;

    /**
     * 发货州
     */
    @Schema(description = "发货州")
    private String shipFromAddressState;

    /**
     * 发货邮编
     */
    @Schema(description = "发货邮编")
    private String shipFromAddressZipCode;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    @Condition(value = Keyword.IN, fields = {"feeStatus"})
    private List<String> feeStatusList;
}