package cn.need.cloud.biz.model.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 下拉 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "下拉 vo对象")
public class DropProVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2504099879063092626L;
    @Schema(description = "字段名")
    private String countName;

    @Schema(description = "详情字段信息")
    private List<Detail> detailList;


    @Data
    public static class Detail implements Serializable {

        @Serial
        private static final long serialVersionUID = -5497278056557758370L;
        @Schema(description = "统计值")
        private Long count;

        @Schema(description = "统计字段值")
        private Map<String, Object> value;
    }

}
