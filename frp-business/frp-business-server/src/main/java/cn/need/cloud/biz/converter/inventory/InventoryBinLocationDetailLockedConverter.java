package cn.need.cloud.biz.converter.inventory;

import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailLockedVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 锁定 库位详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InventoryBinLocationDetailLockedConverter extends AbstractModelConverter<BinLocationDetailLocked, InventoryBinLocationDetailLockedVO, InventoryBinLocationDetailLockedVO> {

}
