package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.special.RollbackConstant;
import cn.need.cloud.biz.model.bo.base.ChangeQtyLogBO;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPrepPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcPrepWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcPrepWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otc.workorder.OtcWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.PrepPickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderFinishConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmVO;
import cn.need.cloud.biz.service.helper.LockedHelper;
import cn.need.cloud.biz.service.helper.workorder.OtcWorkorderHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipDetailService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipService;
import cn.need.cloud.biz.service.otc.workorder.*;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC预提工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Lazy))
public class OtcPrepWorkorderSpecialServiceImpl implements OtcPrepWorkorderSpecialService {

    private final OtcWorkorderService otcWorkorderService;
    private final OtcPrepWorkorderService otcPrepWorkorderService;
    private final OtcPrepPickingSlipSpecialService otcPrepPickingSlipSpecialService;
    private final OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;
    private final OtcPrepPutawaySlipService otcPrepPutawaySlipService;
    private final OtcPrepPutawaySlipDetailService otcPrepPutawaySlipDetailService;
    private final OtcPrepWorkorderBinLocationService otcPrepWorkorderBinLocationService;
    private final InventoryLockedService inventoryLockedService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processTriggering(WorkorderProcessBO process) {
        if (ObjectUtil.isEmpty(process.getHasPrepWorkorderIds())) {
            return;
        }
        ProcessType processType = process.getProcessType();
        switch (processType) {
            // 触发回滚
            case ROLLBACKING, CANCELLING -> this.processTriggering(process, this.getAndCheckStartWorkorder(process));
            // 触发回滚
            case NORMAL -> this.processTriggering(process, this.getAndCheckFinishWorkorder(process));
        }
    }

    @Override
    public void rollback(OtcPrepPutawaySlipPutAwayBO putawayParam) {
        OtcPrepPutawaySlip putawaySlip = putawayParam.getPutawaySlip();

        List<OtcPrepWorkorderDetail> wkDetails = otcPrepWorkorderDetailService.listByPrepWorkOrderId(putawaySlip.getPrepWorkorderId());
        OtcPrepWorkorder workorder = otcPrepWorkorderService.getById(putawaySlip.getPrepWorkorderId());

        Map<Long, OtcPrepWorkorderDetail> detailMap = StreamUtils.toMap(wkDetails, IdModel::getId);

        // Rollback
        List<ChangeQtyLogBO> changeList = putawayParam.getDetailList()
                .stream()
                .map(paramDetail -> {
                    OtcPrepPutawaySlipDetail putawaySlipDetail = paramDetail.getPutawaySlipDetail();
                    OtcPrepWorkorderDetail wkDetail = detailMap.get(putawaySlipDetail.getPrepWorkorderDetailId());
                    wkDetail.setPickedQty(wkDetail.getPickedQty() - paramDetail.getPutawayQty());

                    // 变更数量
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(wkDetail.getPickedQty() + paramDetail.getPutawayQty());
                    change.setAfterQty(wkDetail.getPickedQty());
                    change.setProductId(wkDetail.getProductId());

                    paramDetail.setWorkorderDetail(wkDetail);
                    return change;
                })
                .toList();

        // 状态变更 Picked/ReadyToShip -> IN_PICKING
        String oldStatus = workorder.getPrepWorkorderStatus();
        workorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.IN_PICKING.getStatus());
        if (!Objects.equals(oldStatus, workorder.getPrepWorkorderStatus())) {
            Validate.isTrue(otcPrepWorkorderService.update(workorder) == 1, String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrder status"));
            OtcPrepWorkorderAuditLogHelper.recordLog(workorder, null, putawayParam.getNote());
        }

        // rollback工单详情
        List<OtcPrepWorkorderDetail> rollbackDetails = putawayParam.getDetailList().stream()
                .map(OtcPrepPutawaySlipPutAwayDetailBO::getWorkorderDetail)
                .toList();

        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(rollbackDetails) == rollbackDetails.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrderDetail pickedQty")
        );
        // 记录日志
        OtcPrepWorkorderAuditLogHelper.recordLog(workorder, RollbackConstant.ROLLBACK_PICKED_QTY, JsonUtil.toJson(changeList),
                putawayParam.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );

    }

    @Override
    public List<PrepWorkorderConfirmDetailVO> rollbackList(WorkorderRollbackListQuery query) {
        Map<Long, OtcPrepWorkorder> wkMap = StreamUtils.toMap(otcPrepWorkorderService.listByIds(query.getIdList()), IdModel::getId);
        Map<Long, List<OtcPrepWorkorderDetail>> detailsMap = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(query.getIdList());

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtcPrepWorkorder workorder = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PrepWorkorderConfirmDetailVO rollback = BeanUtil.copyNew(detail, PrepWorkorderConfirmDetailVO.class);
                                PrepWorkorderConfirmVO confirm = BeanUtil.copyNew(workorder, PrepWorkorderConfirmVO.class);
                                confirm.setWorkorderStatus(workorder.getPrepWorkorderStatus());
                                rollback.setPrepWorkorder(confirm);
                                return rollback;
                            });
                })
                .toList();
    }

    @Override
    public OtcPrepWorkorder rollbackPutAwayUnits(RollbackPutawayUnitsUpdateParam param) {
        OtcPrepWorkorder prepWorkorder = otcPrepWorkorderService.getById(param.getPrepWorkorderId());

        ProcessType.checkAbnormal(prepWorkorder.getProcessType(), prepWorkorder.refNumLog(), "rollbackPutAwayUnits");

        // 工单状态校验
        OtcWorkorder workorder = otcWorkorderService.getById(prepWorkorder.getOtcWorkorderId());
        Validate.isTrue(workorder.getOtcWorkorderStatus().equals(OtcWorkorderStatusEnum.BEGIN.getStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT, workorder.refNumLog(),
                "rollbackPutAwayUnits", OtcWorkorderStatusEnum.BEGIN.getStatus(), workorder.getOtcWorkorderStatus()
        );

        // 校验
        Validate.isTrue(prepWorkorder.getPutawayQty() >= param.getRollbackQty(), String.format(ErrorMessages.INSUFFICIENT_QUANTITY,
                StringUtil.format("{} is not enough, [PutawayQty: {}] < [RollbackQty: {}]",
                        prepWorkorder.refNumLog(),
                        prepWorkorder.getPutawayQty(), param.getRollbackQty())
        ));

        // Rollback逻辑
        prepWorkorder.setPutawayQty(prepWorkorder.getPutawayQty() - param.getRollbackQty());

        List<OtcPrepWorkorderDetail> prepWorkorderDetails = otcPrepWorkorderDetailService.listByPrepWorkOrderId(prepWorkorder.getOtcWorkorderId());
        prepWorkorderDetails.forEach(obj -> {
            // Detail PutAwayQty = Detail.qty * Header.putawayQty / Header.qty
            obj.setPutawayQty(obj.getQty() * prepWorkorder.getPutawayQty() / prepWorkorder.getQty());
        });

        String oldStatus = prepWorkorder.getPrepWorkorderStatus();
        // 更新状态
        boolean isPutaway = Objects.equals(prepWorkorder.getPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.PROCESSED.getStatus());
        prepWorkorder.setPrepWorkorderStatus(isPutaway
                ? OtcPrepWorkorderStatusEnum.PICKED.getStatus()
                : oldStatus
        );

        Validate.isTrue(otcPrepWorkorderService.update(prepWorkorder) == 1, "Update Workorder PutAwayQty is fail");
        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(prepWorkorderDetails) == prepWorkorderDetails.size(),
                "Update PrepWorkorderDetail putawayQty is fail"
        );

        // 状态变更
        if (!Objects.equals(oldStatus, prepWorkorder.getPrepWorkorderStatus())) {
            OtcPrepWorkorderAuditLogHelper.recordLog(prepWorkorder, null, param.getNote());
        }

        // 记录日志
        OtcPrepWorkorderAuditLogHelper.recordLog(prepWorkorder, RollbackConstant.ROLLBACK_PUTAWAY_QTY, null,
                param.getNote(), BaseTypeLogEnum.ROLLBACK_PUTAWAY_UNITS.getType()
        );

        return prepWorkorder;

    }

    @Override
    public void cancelWithPickingSlip(List<Long> prepPickingSlipIdList) {
        // 获取拣货单下的工单
        List<OtcPrepWorkorder> workorderList = otcPrepWorkorderService.listByPrepPickingSlipIds(prepPickingSlipIdList);

        // 回滚到Begin
        workorderList.forEach(workorder -> workorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.BEGIN.getStatus()));

        // 更新工单
        Validate.isTrue(otcPrepWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update PrepWorkOrder status is fail"
        );
        OtcPrepWorkorderAuditLogHelper.recordLog(workorderList);

        // 回滚 拣货单锁 -> 工单锁
        List<Long> workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        List<OtcPrepWorkorderDetail> details = otcPrepWorkorderDetailService.listByPrepWorkOrderIds(workorderIds);
        List<Long> inventoryLockedIds = StreamUtils.distinctMap(details, OtcPrepWorkorderDetail::getInventoryLockedId);

        List<InventoryLocked> inventoryLockedList = inventoryLockedService.listByIds(inventoryLockedIds);
        inventoryLockedList.forEach(locked -> {
            locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
            locked.setFinishQty(0);
        });

        Validate.isTrue(inventoryLockedService.updateBatch(inventoryLockedList) == inventoryLockedList.size(),
                "Update InventoryLocked update fail"
        );
    }

    @Override
    public List<PrepPickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds) {
        // 处理已经创建上架单的不让他继续创建
        Map<Long, Integer> hasCreateQtyMap = otcPrepPutawaySlipDetailService.listAvailableByWorkorderIds(workorderIds)
                .stream()
                .collect(Collectors.groupingBy(OtcPrepPutawaySlipDetail::getPrepWorkorderBinLocationId, Collectors.summingInt(OtcPrepPutawaySlipDetail::getQty)));
        List<OtcPrepWorkorderBinLocation> currenWkPickInfoList = otcPrepWorkorderBinLocationService.listByPrepWorkorderIds(workorderIds);
        // 扣除上架单占用数量
        currenWkPickInfoList.forEach(obj -> obj.setQty(obj.getQty() - hasCreateQtyMap.getOrDefault(obj.getId(), 0)));

        // 赋值返回参数
        List<PrepPickingSlipUnpickDetailVO> unpickDetails = currenWkPickInfoList.stream()
                .map(obj -> {
                    PrepPickingSlipUnpickDetailVO unpick = BeanUtil.copyNew(obj, PrepPickingSlipUnpickDetailVO.class);
                    unpick.setPrepWorkorderId(obj.getOtcPrepWorkorderId());
                    unpick.setPrepWorkorderDetailId(obj.getOtcPrepWorkorderDetailId());
                    unpick.setPrepWorkorderBinLocationId(obj.getId());
                    unpick.setPrepPickingSlipId(obj.getOtcPrepPickingSlipId());
                    unpick.setPrepPickingSlipDetailId(obj.getOtcPrepPickingSlipDetailId());
                    return unpick;
                })
                .toList();

        // 拣货数量即 是Unpick时 Rollback时的 qty
        unpickDetails.stream()
                .sorted(Comparator.comparing(BasePickingSlipUnpickDetailVO::getUnpickId))
                .forEach(detail -> detail.setPickedQty(detail.getQty()));

        // 获取工单详情
        List<Long> wkDetailIds = StreamUtils.distinctMap(currenWkPickInfoList, OtcPrepWorkorderBinLocation::getOtcWorkorderDetailId);
        List<OtcPrepWorkorderDetail> wkDetails = otcPrepWorkorderDetailService.listByIds(wkDetailIds);
        Map<Long, OtcPrepWorkorderDetail> wkDetailMap = StreamUtils.toMap(wkDetails, OtcPrepWorkorderDetail::getId);

        Map<Long, Integer> putawayQtyMap = wkDetailMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, obj -> obj.getValue().getPutawayQty()));

        // 赋值putawayQty
        for (PrepPickingSlipUnpickDetailVO unpickDetail : unpickDetails) {
            Integer remaining = putawayQtyMap.getOrDefault(unpickDetail.getPrepWorkorderDetailId(), 0);
            if (remaining <= 0) {
                continue;
            }
            int putawayQty = Math.min(remaining, unpickDetail.getPickedQty());
            unpickDetail.setPutawayQty(putawayQty);
            putawayQtyMap.put(unpickDetail.getPrepWorkorderDetailId(), remaining - putawayQty);
        }

        return unpickDetails;
    }

    @Override
    public PrepWorkorderFinishConfirmVO finishRollbackConfirm(WorkorderRollbackListQuery query) {
        List<PrepWorkorderConfirmDetailVO> prepWorkorderList = this.confirmDetailList(query.getIdList());

        List<Long> prepWorkorderIds = prepWorkorderList.stream()
                .map(PrepWorkorderConfirmDetailVO::getPrepWorkorder)
                .map(WorkorderConfirmVO::getId)
                .distinct().toList();

        // Prep上架单完成确认
        List<PrepPutawaySlipConfirmDetailVO> prepPutawaySlipList = otcPrepPutawaySlipService.confirmDetailList(prepWorkorderIds);

        return new PrepWorkorderFinishConfirmVO(prepWorkorderList, prepPutawaySlipList);
    }

    @Override
    public void split(List<OtcWorkorderSplitBO> splitHolders) {
        // Prep工单：拆单
        var workorderDetailIds = splitHolders.stream()
                .map(OtcWorkorderSplitBO::getDetailHolders)
                .flatMap(Collection::stream)
                .map(OtcWorkorderSplitDetailBO::getDetail)
                .map(IdModel::getId)
                .toList();

        var prepWorkorderList = otcPrepWorkorderService.listByWorkorderDetailIds(workorderDetailIds);
        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return;
        }

        //todo: 这个是临时限制，后面再慢慢支持
        for (var prepWorkorder : prepWorkorderList) {
            if (!OtcPrepWorkorderStatusEnum.canCancelStatuses().contains(prepWorkorder.getPrepWorkorderStatus())) {
                throw new BusinessException(StringUtil.format("{} current status is {} , can not support cancel",prepWorkorder.refNumLog(),prepWorkorder.getPrepWorkorderStatus()));
            }
        }

        var prepWkDetails = otcPrepWorkorderDetailService.listByPrepWorkOrderIds(StreamUtils.distinctMap(prepWorkorderList, IdModel::getId));
        var prepWkDetailsGroupMap = StreamUtils.groupBy(prepWkDetails, OtcPrepWorkorderDetail::getOtcPrepWorkorderId);

        var splitDetailHolders = splitHolders.stream().map(OtcWorkorderSplitBO::getDetailHolders).flatMap(Collection::stream).toList();
        // 拆单，Prep工单，数量、上架数量拆分
        for (var splitHolder : splitDetailHolders) {
            // 设置Prep工单拆单信息
            splitHolder.setPrepWorkorderHolders(prepWorkorderList.stream()
                    .filter(obj -> Objects.equals(obj.getOtcWorkorderDetailId(), splitHolder.getDetail().getId()))
                    .map(prep -> splitSingle(splitHolder, prep, prepWkDetailsGroupMap))
                    .toList()
            );
        }

        // PrepWorkorder & Split PrepWorkorder:  Update 状态
        OtcWorkorderHelper.refreshPrepStatus(prepWorkorderList, prepWkDetails);
        OtcWorkorderHelper.refreshPrepStatus(splitDetailHolders.stream()
                        .flatMap(obj -> obj.getPrepWorkorderHolders().stream().map(OtcPrepWorkorderSplitBO::getSplitPrepWorkorder))
                        .toList(),
                splitDetailHolders.stream()
                        .flatMap(obj -> obj.getPrepWorkorderHolders().stream().map(OtcPrepWorkorderSplitBO::getPrepDetailHolders))
                        .flatMap(Collection::stream)
                        .map(OtcPrepWorkorderSplitDetailBO::getSplitPrepDetail)
                        .toList()
        );

        // Step: Prep拣货单 拆单
        otcPrepPickingSlipSpecialService.split(splitHolders);

        var splitPrepHolders = splitDetailHolders.stream()
                .map(OtcWorkorderSplitDetailBO::getPrepWorkorderHolders)
                .flatMap(Collection::stream)
                .toList();

        // Step: 锁拆单
        this.lockSplit(splitPrepHolders);

        // 更新工单状态
        Validate.isTrue(otcPrepWorkorderService.updateBatch(prepWorkorderList) == prepWorkorderList.size(),
                "Update PrepWorkOrder status failed"
        );

        var details = splitDetailHolders.stream()
                .flatMap(obj -> obj.getPrepWorkorderHolders().stream()
                        .flatMap(o -> o.getPrepDetailHolders().stream().map(OtcPrepWorkorderSplitDetailBO::getPrepDetail)))
                .toList();

        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(details) == details.size(),
                "Update PrepWorkOrderDetail status failed"
        );

        // 拆单入库
        var splitPrepWorkorderList = splitPrepHolders.stream()
                .map(OtcPrepWorkorderSplitBO::getSplitPrepWorkorder)
                .toList();
        otcPrepWorkorderService.insertBatch(splitPrepWorkorderList);

        // 拆单详情
        var splitDetails = splitPrepHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream().map(OtcPrepWorkorderSplitDetailBO::getSplitPrepDetail))
                .toList();

        otcPrepWorkorderDetailService.insertBatch(splitDetails);

    }

    // ////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 锁拆单
     *
     * @param splitPrepHolders prepHolder
     */
    private void lockSplit(List<OtcPrepWorkorderSplitBO> splitPrepHolders) {
        // 原单 释放锁
        var lockedIds = splitPrepHolders.stream()
                .flatMap(obj -> obj.getPrepDetailHolders().stream())
                .map(obj -> obj.getSplitPrepDetail().getInventoryLockedId())
                .toList();
        var lockedList = inventoryLockedService.listByIds(lockedIds);
        var lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // InventoryLocked 拆单
        var splitLockedList = splitPrepHolders.stream()
                .flatMap(holder -> holder.getPrepDetailHolders().stream()
                        .map(detailHolder -> {
                            var currentLocked = lockedMap.get(detailHolder.getPrepDetail().getInventoryLockedId());
                            currentLocked.setQty(currentLocked.getQty() - detailHolder.getSplitQty());
                            // FinishQty
                            var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                                    ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                            currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                            LockedHelper.statusRefresh(currentLocked);

                            // InventoryLocked 拆单
                            var splitLocked = BeanUtil.copyNew(currentLocked, InventoryLocked.class);
                            splitLocked.setId(IdWorker.getId());

                            // 设置数量更新状态
                            splitLocked.setQty(detailHolder.getSplitQty());
                            splitLocked.setFinishQty(splitFinishQty);
                            LockedHelper.statusRefresh(splitLocked);

                            splitLocked.setRefTableShowRefNum(holder.getSplitPrepWorkorder().getRefNum());
                            var splitDetail = detailHolder.getSplitPrepDetail();
                            splitLocked.setRefTableName(String.valueOf(splitDetail.getLineNum()));
                            splitLocked.setRefTableId(splitDetail.getId());
                            return splitLocked;
                        }))
                .toList();
        inventoryLockedService.insertBatch(splitLockedList);

        Validate.isTrue(inventoryLockedService.updateBatch(lockedList) == lockedList.size(),
                "Update InventoryLocked qty fail"
        );
    }

    /**
     * 单个PrepWorkorder拆单
     *
     * @param splitHolder           拆单
     * @param prep                  prep工单
     * @param prepWkDetailsGroupMap Details
     * @return /
     */
    private static OtcPrepWorkorderSplitBO splitSingle(OtcWorkorderSplitDetailBO splitHolder,
                                                       OtcPrepWorkorder prep,
                                                       Map<Long, List<OtcPrepWorkorderDetail>> prepWkDetailsGroupMap) {
        prep.setQty(prep.getQty() - splitHolder.getSplitQty());
        // 上架数量, 尽量留在原单中
        var splitPutAwayQty = prep.getQty() > prep.getPutawayQty()
                ? 0 : prep.getPutawayQty() - prep.getQty();
        prep.setPutawayQty(prep.getPutawayQty() - splitPutAwayQty);

        // 拆单
        var splitPrepWk = BeanUtil.copyNew(prep, OtcPrepWorkorder.class);
        splitPrepWk.setId(IdWorker.getId());
        splitPrepWk.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PREP_WORK_ORDER.getCode()));
        splitPrepWk.setQty(splitHolder.getSplitQty());
        splitPrepWk.setPutawayQty(splitPutAwayQty);
        // 赋值工单信息
        splitPrepWk.setOtcWorkorderId(splitHolder.getSplitDetail().getOtcWorkorderId());
        splitPrepWk.setOtcWorkorderDetailId(splitHolder.getSplitDetail().getId());

        // Details 拆
        var splitDetails = prepWkDetailsGroupMap.getOrDefault(prep.getId(), Collections.emptyList()).stream()
                .map(prepDetail -> {
                    var headerQty = prep.getQty() + splitHolder.getSplitQty();
                    // Child.SplitQty = ChildQty * SplitQty / HeaderQty
                    var childSplitQty = prepDetail.getQty() * splitHolder.getSplitQty() / headerQty;
                    // 原来的PrepDetail更新数量
                    prepDetail.setQty(prepDetail.getQty() - childSplitQty);
                    // Child.PutawayQty
                    var splitChildPutAwayQty = prepDetail.getQty() > prepDetail.getPutawayQty()
                            ? 0 : prepDetail.getPutawayQty() - prepDetail.getQty();
                    prepDetail.setPutawayQty(prepDetail.getPutawayQty() - splitChildPutAwayQty);
                    // Child.PickedQty
                    var splitChildPickedQty = prepDetail.getQty() > prepDetail.getPickedQty()
                            ? 0 : prepDetail.getPickedQty() - prepDetail.getQty();
                    prepDetail.setPickedQty(prepDetail.getPickedQty() - splitChildPickedQty);

                    // 拆单
                    var splitPrepWkDetail = BeanUtil.copyNew(prepDetail, OtcPrepWorkorderDetail.class);
                    splitPrepWkDetail.setId(IdWorker.getId());
                    // 设置HeaderId
                    splitPrepWkDetail.setOtcPrepWorkorderId(splitPrepWk.getId());
                    splitPrepWkDetail.setQty(childSplitQty);
                    splitPrepWkDetail.setPutawayQty(splitChildPutAwayQty);
                    splitPrepWkDetail.setPickedQty(splitChildPickedQty);

                    var prepDetailSplitHolder = new OtcPrepWorkorderSplitDetailBO();
                    prepDetailSplitHolder.setSplitQty(childSplitQty);
                    prepDetailSplitHolder.setPrepDetail(prepDetail);
                    prepDetailSplitHolder.setSplitPrepDetail(splitPrepWkDetail);
                    return prepDetailSplitHolder;
                })
                .toList();

        // 封装结果
        var prepSplitHolder = new OtcPrepWorkorderSplitBO();
        prepSplitHolder.setPrepWorkorder(prep);
        prepSplitHolder.setSplitPrepWorkorder(splitPrepWk);
        prepSplitHolder.setPrepDetailHolders(splitDetails);
        prepSplitHolder.setSplitQty(splitHolder.getSplitQty());

        return prepSplitHolder;
    }

    /**
     * 确认列表
     *
     * @param workorderIds 工单id
     * @return /
     */
    public List<PrepWorkorderConfirmDetailVO> confirmDetailList(Collection<Long> workorderIds) {
        List<OtcPrepWorkorder> prepWorkorderList = otcPrepWorkorderService.listByIds(workorderIds);
        Map<Long, OtcPrepWorkorder> wkMap = StreamUtils.toMap(prepWorkorderList, IdModel::getId);
        List<Long> prepWorkorderIds = StreamUtils.distinctMap(prepWorkorderList, IdModel::getId);
        Map<Long, List<OtcPrepWorkorderDetail>> detailsMap = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(prepWorkorderIds);

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtcPrepWorkorder workorder = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PrepWorkorderConfirmDetailVO rollback = BeanUtil.copyNew(detail, PrepWorkorderConfirmDetailVO.class);
                                PrepWorkorderConfirmVO confirm = BeanUtil.copyNew(workorder, PrepWorkorderConfirmVO.class);
                                confirm.setWorkorderStatus(workorder.getPrepWorkorderStatus());
                                rollback.setPrepWorkorder(confirm);
                                return rollback;
                            });
                })
                .toList();
    }

    /**
     * 流程触发
     *
     * @param process       流程参数
     * @param workorderList 工单
     */
    private void processTriggering(WorkorderProcessBO process, List<OtcPrepWorkorder> workorderList) {
        String type = process.getProcessType().getType();
        workorderList.forEach(obj -> obj.setProcessType(type));

        Validate.isTrue(otcPrepWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status [{}] failed", type
        );

        // 记录日志
        OtcPrepWorkorderAuditLogHelper.recordLog(workorderList, type, null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());

        // 拣货单 Rollback
        process.setPrepPickingSlipIds(StreamUtils.distinctMap(workorderList, OtcPrepWorkorder::getOtcPrepPickingSlipId));

        // Prep拣货单: 触发流程
        otcPrepPickingSlipSpecialService.processTriggering(process);
    }

    /**
     * 获取并校验start工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtcPrepWorkorder> getAndCheckStartWorkorder(WorkorderProcessBO process) {
        // 工单
        List<OtcPrepWorkorder> workorderList = otcPrepWorkorderService.listByWorkOrderIdList(process.getHasPrepWorkorderIds());

        Validate.notEmpty(workorderList, "idList: {} WorkOrder is empty", process.getHasPrepWorkorderIds());

        String type = process.getProcessType().getType();
        workorderList.forEach(obj -> Validate.isTrue(ProcessType.NORMAL.getType().equals(obj.getProcessType()),
                ErrorConstant.STATUS_ERROR_FORMAT,
                obj.refNumLog(), "start" + type, ProcessType.NORMAL.getType(), obj.getProcessType()
        ));

        return workorderList;
    }

    private List<OtcPrepWorkorder> getAndCheckFinishWorkorder(WorkorderProcessBO process) {
        List<OtcPrepWorkorder> workorderList = otcPrepWorkorderService.listByWorkOrderIdList(process.getHasPrepWorkorderIds());

        Validate.notEmpty(workorderList, "idList: {} WorkOrder is empty", process.getHasPrepWorkorderIds());

        // 校验上架是否全部完成
        otcPrepPutawaySlipService.finishRollback(workorderList);

        workorderList.forEach(obj -> {
            ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "finish");

            obj.setProcessType(ProcessType.NORMAL.getType());
        });
        Validate.isTrue(otcPrepWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status [{}] failed", ProcessType.NORMAL.getType()
        );
        return workorderList;
    }

}
