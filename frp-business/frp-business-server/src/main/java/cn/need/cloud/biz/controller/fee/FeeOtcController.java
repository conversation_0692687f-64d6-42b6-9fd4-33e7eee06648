package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeOtcConverter;
import cn.need.cloud.biz.model.entity.fee.FeeOtc;
import cn.need.cloud.biz.model.query.fee.FeeOtcQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtcVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtcPageVO;
import cn.need.cloud.biz.service.fee.FeeOtcService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 费用otc 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-otc")
@Tag(name = "费用otc")
public class FeeOtcController extends AbstractRestController<FeeOtcService, FeeOtc, FeeOtcConverter, FeeOtcVO> {

   
    @Operation(summary = "根据id获取费用otc详情", description = "根据数据主键id，从数据库中获取其对应的费用otc详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeOtcVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用otc详情", description = "根据数据RefNum，从数据库中获取其对应的费用otc详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeOtcVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用otc分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用otc列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeOtcPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeOtcQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}
