package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.model.param.product.update.DeleteNoteByPidParam;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/biz/product/special")
@Tag(name = "产品")
@Slf4j
public class ProductSpecialController extends AbstractController {
    @Resource
    private ProductSpecialService service;

    @Operation(summary = "根据id删除产品", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.checkAndDelete(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id删除产品扫描", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/product-scan/remove-scan")
    public Result<Integer> removeScan(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.removeScan(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id删除产品版本详情", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/product-version/remove-version")
    public Result<Integer> removeVersion(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.removeVersion(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据productId删除产品多箱")
    @PostMapping(value = "/product-multibox/remove-multibox")
    public Result<Integer> removeMultibox(@RequestBody @Parameter(description = "productId", required = true) DeleteNoteByPidParam param) {

        // 校验参数
        Validate.notNull(param.getProductId(), "The productId value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.removeMultibox(param.getProductId(), param.getDeletedNote()));
    }

    @Operation(summary = "根据productId删除产品同类")
    @PostMapping(value = "/product-group/remove-group")
    public Result<Integer> removeGroup(@RequestBody @Parameter(description = "productId", required = true) DeleteNoteByPidParam param) {

        // 校验参数
        Validate.notNull(param.getProductId(), "The productId value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.removeGroup(param.getProductId(), param.getDeletedNote()));
    }

    @Operation(summary = "根据productId删除产品组装")
    @PostMapping(value = "/product-component/remove-component")
    public Result<Integer> removeComponent(@RequestBody @Parameter(description = "productId", required = true) DeleteNoteByPidParam param) {

        // 校验参数
        Validate.notNull(param.getProductId(), "The productId value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.removeComponent(param.getProductId(), param.getDeletedNote()));
    }
}
