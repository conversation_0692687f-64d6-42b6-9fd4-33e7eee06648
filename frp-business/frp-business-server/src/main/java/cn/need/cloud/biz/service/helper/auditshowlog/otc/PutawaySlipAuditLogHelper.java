package cn.need.cloud.biz.service.helper.auditshowlog.otc;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipModel;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
public class PutawaySlipAuditLogHelper {

    /**
     * 记录日志
     *
     * @param otcPutawaySlipDetailList 包裹
     * @param note                     Note
     * @param description              描述
     */
    public static <T extends PutawaySlipModel> void recordLog(List<T> otcPutawaySlipDetailList, String description, String note) {
        otcPutawaySlipDetailList.forEach(putawaySlip -> recordLog(putawaySlip, description, note));
    }

    /**
     * 记录日志
     *
     * @param otcPutawaySlipDetailList 包裹
     * @param note                     Note
     * @param description              描述
     */
    public static <T extends PutawaySlipModel> void recordLog(List<T> otcPutawaySlipDetailList, String status, String description, String note) {
        otcPutawaySlipDetailList.forEach(putawaySlip -> recordLog(putawaySlip, status, description, note));
    }

    /**
     * 记录日志
     *
     * @param otcPutawaySlipDetailList 包裹
     */
    public static <T extends PutawaySlipModel> void recordLog(List<T> otcPutawaySlipDetailList) {
        otcPutawaySlipDetailList.forEach(PutawaySlipAuditLogHelper::recordLog);
    }

    /**
     * 记录日志
     *
     * @param putawaySlip 包裹
     */
    public static <T extends PutawaySlipModel> void recordLog(T putawaySlip) {
        recordLog(putawaySlip, null, null);
    }


    /**
     * 记录日志
     *
     * @param putawaySlip 包裹
     * @param note        Note
     * @param description 描述
     */
    public static <T extends PutawaySlipModel> void recordLog(T putawaySlip, String description, String note) {
        recordLog(putawaySlip, putawaySlip.getPutawaySlipStatus(), description, note);
    }

    /**
     * 记录日志
     *
     * @param putawaySlip 包裹
     * @param status      状态
     * @param note        Note
     * @param description 描述
     */
    public static <T extends PutawaySlipModel> void recordLog(T putawaySlip, String status, String description, String note) {
        recordLog(putawaySlip, status, description, note, BaseTypeLogEnum.STATUS.getType());
    }

    /**
     * 记录日志
     *
     * @param putawaySlip 包裹
     * @param status      状态
     * @param note        Note
     * @param description 描述
     */
    public static <T extends PutawaySlipModel> void recordLog(T putawaySlip, String status, String description, String note, String type) {
        AuditShowLog entity = AuditLogUtil.commonLog(putawaySlip)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(entity);
    }

}
