package cn.need.cloud.biz.model.entity.otc;

import cn.need.cloud.biz.model.entity.base.LabelModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * OTC运输托盘
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otc_ship_pallet")
public class OtcShipPallet extends LabelModel {


    /**
     * 运输公司
     */
    @TableField("ship_carrier")
    private String shipCarrier;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 纸箱数量
     */
    @TableField("carton_count")
    private Integer cartonCount;
}
