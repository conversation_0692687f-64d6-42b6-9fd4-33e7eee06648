package cn.need.cloud.biz.service.otb.ri;

import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstructionPalletLabel;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionCreateParam;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionPalletLabelCreateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionPalletLabelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionUpdateParam;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionPalletLabelQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPalletLabelPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionPalletLabelVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * otb发货指南托盘标签 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbRoutingInstructionPalletLabelService extends SuperService<OtbRoutingInstructionPalletLabel> {

    /**
     * 根据参数新增otb发货指南托盘标签
     *
     * @param createParam 请求创建参数，包含需要插入的otb发货指南托盘标签的相关信息
     * @return otb发货指南托盘标签ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(OtbRoutingInstructionPalletLabelCreateParam createParam);


    List<OtbRoutingInstructionPalletLabel> getListByRoutingInstructionId(Long routingInstructionId);

    /**
     * 根据参数更新otb发货指南托盘标签
     *
     * @param updateParam 请求创建参数，包含需要更新的otb发货指南托盘标签的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(OtbRoutingInstructionPalletLabelUpdateParam updateParam);

    /**
     * 根据查询条件获取otb发货指南托盘标签列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个otb发货指南托盘标签对象的列表(分页)
     */
    List<OtbRoutingInstructionPalletLabelPageVO> listByQuery(OtbRoutingInstructionPalletLabelQuery query);

    /**
     * 根据查询条件获取otb发货指南托盘标签列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个otb发货指南托盘标签对象的列表(分页)
     */
    PageData<OtbRoutingInstructionPalletLabelPageVO> pageByQuery(PageSearch<OtbRoutingInstructionPalletLabelQuery> search);

    /**
     * 根据ID获取otb发货指南托盘标签
     *
     * @param id otb发货指南托盘标签ID
     * @return 返回otb发货指南托盘标签VO对象
     */
    OtbRoutingInstructionPalletLabelVO detailById(Long id);

    /**
     * 根据参数插入记录到数据库
     *
     * @param param                   包含批量插入所需信息的参数对象，具体结构见OtbRoutingInstructionCreateParam类
     * @param otbRoutingInstructionId 需要与param参数一起使用的特定ID，用于指定或关联数据库中的相关记录
     */
    void insertBatchByParam(OtbRoutingInstructionCreateParam param, long otbRoutingInstructionId);

    /**
     * 根据参数更新批处理指令
     *
     * @param updateParam 更新参数对象，包含需要更新的批处理指令的相关信息
     */
    void updateBatchByParam(OtbRoutingInstructionUpdateParam updateParam);
}