package cn.need.cloud.biz.model.param.setting.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(description = " vo对象")
public class PrinterSettingsUpdateParam implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull
    private Long id;

    /**
     * 标签打印机相关信息
     */
    @Schema(description = "标签打印机")
    private String labelPrinter;

    /**
     * 1x3标签打印机具体信息
     */
    @Schema(description = "1x3标签打印机")
    private String oneByThreeLabelPrinter;

    /**
     * 常规打印机信息
     */
    @Schema(description = "常规打印机")
    private String regularPrinter;

    /**
     * 移动标签打印机详情
     */
    @Schema(description = "移动标签打印机")
    private String mobileLabelPrinter;

    /**
     * 移动CLodopIp打印插件
     */
    @Schema(description = "移动CLodopIp打印插件")
    private String mobileCLodopIp;

    /**
     * 扫描插件的IP地址
     */
    @Schema(description = "扫描插件IP")
    private String scannerPluginIp;

    /**
     * 扫描仪相关信息
     */
    @Schema(description = "扫描仪")
    private String scanner;
}
