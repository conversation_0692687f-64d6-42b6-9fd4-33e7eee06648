package cn.need.cloud.biz.mapper.inventory;

import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.query.inventory.InventoryLockedQuery;
import cn.need.cloud.biz.model.vo.page.InventoryLockedPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface InventoryLockedMapper extends SuperMapper<InventoryLocked> {

    /**
     * 根据条件获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值列表
     *
     * @param query 查询条件
     * @return 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值集合
     */
    default List<InventoryLockedPageVO> listByQuery(InventoryLockedQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值集合
     */
    List<InventoryLockedPageVO> listByQuery(@Param("qo") InventoryLockedQuery query, @Param("page") Page<?> page);
}