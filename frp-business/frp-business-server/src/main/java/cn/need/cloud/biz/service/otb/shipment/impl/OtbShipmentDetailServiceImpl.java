package cn.need.cloud.biz.service.otb.shipment.impl;

import cn.need.cloud.biz.converter.otb.OtbShipmentDetailConverter;
import cn.need.cloud.biz.mapper.otb.OtbShipmentDetailMapper;
import cn.need.cloud.biz.model.bo.otb.OtbBuildShipmentContextBO;
import cn.need.cloud.biz.model.entity.otb.OtbPackageDetail;
import cn.need.cloud.biz.model.entity.otb.OtbPalletDetail;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.entity.otb.OtbShipmentDetail;
import cn.need.cloud.biz.model.param.otb.create.shipment.OtbShipmentDetailCreateParam;
import cn.need.cloud.biz.model.param.otb.update.shipment.OtbShipmentDetailUpdateParam;
import cn.need.cloud.biz.model.query.otb.shipment.OtbShipmentDetailQuery;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentDetailVO;
import cn.need.cloud.biz.model.vo.page.OtbShipmentDetailPageVO;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentDetailService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB装运详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbShipmentDetailServiceImpl extends SuperServiceImpl<OtbShipmentDetailMapper, OtbShipmentDetail> implements OtbShipmentDetailService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(OtbShipmentDetailCreateParam createParam) {
        // 检查传入OTB装运详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取OTB装运详情转换器实例，用于将OTB装运详情参数对象转换为实体对象
        OtbShipmentDetailConverter converter = Converters.get(OtbShipmentDetailConverter.class);

        // 将OTB装运详情参数对象转换为实体对象并初始化
        OtbShipmentDetail entity = initOtbShipmentDetail(converter.toEntity(createParam));

        // 插入OTB装运详情实体对象到数据库
        super.insert(entity);

        // 返回OTB装运详情ID
        return entity.getId();
    }


    /**
     * 初始化OTB装运详情对象
     * 此方法用于设置OTB装运详情对象的必要参数，确保其处于有效状态
     *
     * @param entity OTB装运详情对象，不应为空
     * @return 返回初始化后的OTB装运详情
     * @throws BusinessException 如果传入的OTB装运详情为空，则抛出此异常
     */
    private OtbShipmentDetail initOtbShipmentDetail(OtbShipmentDetail entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OtbShipmentDetail cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(OtbShipmentDetailUpdateParam updateParam) {
        // 检查传入OTB装运详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取OTB装运详情转换器实例，用于将OTB装运详情参数对象转换为实体对象
        OtbShipmentDetailConverter converter = Converters.get(OtbShipmentDetailConverter.class);

        // 将OTB装运详情参数对象转换为实体对象
        OtbShipmentDetail entity = converter.toEntity(updateParam);

        // 执行更新OTB装运详情操作
        return super.update(entity);

    }

    @Override
    public List<OtbShipmentDetailPageVO> listByQuery(OtbShipmentDetailQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtbShipmentDetailPageVO> pageByQuery(PageSearch<OtbShipmentDetailQuery> search) {
        Page<OtbShipmentDetail> page = Conditions.page(search, entityClass);
        List<OtbShipmentDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbShipmentDetailVO detailById(Long id) {
        OtbShipmentDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtbShipmentDetail");
        }
        return buildOtbShipmentDetailVO(entity);
    }


    @Override
    public List<OtbShipmentDetailVO> listByOtbShipmentId(Long otbShipmentId) {
        List<OtbShipmentDetail> list = lambdaQuery().eq(OtbShipmentDetail::getOtbShipmentId, otbShipmentId).list();
        return Converters.get(OtbShipmentDetailConverter.class).toVO(list);
    }

    @Override
    public void generateShipmentDetail(OtbBuildShipmentContextBO context) {
        //获取打托单详情
        List<OtbPalletDetail> otbPalletDetailList = context.getOtbPalletDetailList();
        //获取发货单
        OtbShipment otbShipment = context.getOtbShipment();
        //按照打托单详情生成货单明细
        List<OtbShipmentDetail> list = otbPalletDetailList.stream()
                .collect(Collectors.groupingBy(item -> StringUtil.format("{}/{}/{}", item.getProductId(), item.getProductBarcode(), item.getProductChannelSku())))
                .values()
                .stream()
                .map(item -> {
                    //对qty求合
                    int sum = item.stream()
                            .mapToInt(OtbPalletDetail::getQty)
                            .sum();
                    //获取打托单明细
                    OtbPalletDetail otbPalletDetail = item
                            .stream()
                            .findFirst()
                            .orElse(new OtbPalletDetail());
                    //构建货单明细
                    OtbShipmentDetail otbShipmentDetail = new OtbShipmentDetail();
                    otbShipmentDetail.setOtbShipmentId(otbShipment.getId());
                    otbShipmentDetail.setQty(sum);
                    otbShipmentDetail.setProductId(otbPalletDetail.getProductId());
                    otbShipmentDetail.setProductBarcode(otbPalletDetail.getProductBarcode());
                    otbShipmentDetail.setProductChannelSku(otbPalletDetail.getProductChannelSku());
                    otbShipmentDetail.setLineNum(1);
                    return otbShipmentDetail;
                })
                .toList();
        //持久化发货单明细
        super.insertBatch(list);
        //加载上下文
        context.setOtbShipmentDetailList(BeanUtil.copyNew(list, OtbShipmentDetailVO.class));
    }

    @Override
    public void generateSmallDetail(OtbBuildShipmentContextBO context) {
        //获取打托单详情
        List<OtbPackageDetail> otbPackageDetailList = context.getOtbPackageDetailList();
        //获取发货单
        OtbShipment otbShipment = context.getOtbShipment();
        //按照打托单详情生成货单明细
        List<OtbShipmentDetail> list = otbPackageDetailList.stream()
                .collect(Collectors.groupingBy(item -> StringUtil.format("{}/{}/{}", item.getProductId(), item.getProductBarcode(), item.getProductChannelSku())))
                .values()
                .stream()
                .map(item -> {
                    //对qty求合
                    int sum = item.stream()
                            .mapToInt(OtbPackageDetail::getQty)
                            .sum();
                    //获取打托单明细
                    OtbPackageDetail otbPackageDetail = item
                            .stream()
                            .findFirst()
                            .orElse(new OtbPackageDetail());
                    //构建货单明细
                    OtbShipmentDetail otbShipmentDetail = new OtbShipmentDetail();
                    otbShipmentDetail.setOtbShipmentId(otbShipment.getId());
                    otbShipmentDetail.setQty(sum);
                    otbShipmentDetail.setProductId(otbPackageDetail.getProductId());
                    otbShipmentDetail.setProductBarcode(otbPackageDetail.getProductBarcode());
                    otbShipmentDetail.setProductChannelSku(otbPackageDetail.getProductChannelSku());
                    otbShipmentDetail.setLineNum(1);
                    return otbShipmentDetail;
                })
                .toList();
        //持久化发货单明细
        super.insertBatch(list);
        //加载上下文
        context.setOtbShipmentDetailList(BeanUtil.copyNew(list, OtbShipmentDetailVO.class));
    }

    @Override
    public List<OtbShipmentDetail> listByOtbShipmentIds(Collection<Long> idList) {
        return lambdaQuery()
                .in(OtbShipmentDetail::getOtbShipmentId, idList)
                .list();
    }

    /**
     * 构建OTB装运详情VO对象
     *
     * @param entity OTB装运详情对象
     * @return 返回包含详细信息的OTB装运详情VO对象
     */
    private OtbShipmentDetailVO buildOtbShipmentDetailVO(OtbShipmentDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB装运详情VO对象
        return Converters.get(OtbShipmentDetailConverter.class).toVO(entity);
    }

}
