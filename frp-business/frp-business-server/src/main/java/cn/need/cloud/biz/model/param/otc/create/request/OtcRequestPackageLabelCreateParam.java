package cn.need.cloud.biz.model.param.otc.create.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * OTC请求包裹标签 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC请求包裹标签 vo对象")
public class OtcRequestPackageLabelCreateParam implements Serializable {


    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型")
    private String labelType;

    /**
     * label RefNum
     */
    @Schema(description = "label RefNum")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型")
    private String paperType;

    /**
     * label数据类型
     */
    @Schema(description = "label数据类型")
    private String labelRawData;

    /**
     * OTC请求ID
     */
    @Schema(description = "OTC请求ID")
    private Long otcRequestId;

    /**
     * OTC请求包裹ID
     */
    @Schema(description = "OTC请求包裹ID")
    private Long otcRequestPackageId;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    private String fileIdRawDataType;

}