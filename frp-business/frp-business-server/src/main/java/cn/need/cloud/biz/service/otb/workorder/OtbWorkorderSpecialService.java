package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.model.bo.otb.OtbPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestCancelParam;
import cn.need.cloud.biz.model.param.base.WorkorderFinishUpdateParam;
import cn.need.cloud.biz.model.param.base.WorkorderStartUpdateParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderFinishConfirmVO;

import java.util.List;

/**
 * <p>
 * OTC工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbWorkorderSpecialService {

    /**
     * 拣货单触发
     *
     * @param pickingSlipIdList 拣货单
     */
    void cancelWithPickingSlip(List<Long> pickingSlipIdList);

    /**
     * Start Rollback
     *
     * @param query 启动参数
     * @return /
     */
    boolean startRollback(WorkorderStartUpdateParam query);

    /**
     * Finish Rollback
     *
     * @param query 完成参数
     * @return /
     */
    boolean finishRollback(WorkorderFinishUpdateParam query);

    /**
     * Rollback
     *
     * @param param 上架参数
     */
    void rollback(OtbPutawaySlipPutAwayBO param);

    /**
     * Rollback ReadyToShipQty
     *
     * @param rollback rollback参数
     */
    void rollbackPackedQty(OtbPackageRollbackSingleWorkorderBO rollback);

    /**
     * Rollback ReadyToShipQty
     *
     * @param rollbackList rollback参数
     */
    void rollbackPacked(List<OtbPackageRollbackSingleWorkorderBO> rollbackList);

    /**
     * 获取工单Rollback列表
     *
     * @param query query
     * @return /
     */
    List<WorkorderConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query);

    /**
     * Start Cancel
     *
     * @param query 启动参数
     */
    void startCancel(WorkorderStartUpdateParam query);

    /**
     * Finish Cancel
     *
     * @param query 完成参数
     * @return /
     */
    boolean finishCancel(WorkorderFinishUpdateParam query);

    /**
     * unpick列表
     *
     * @param workorderIds 工单id
     * @return /
     */
    List<PickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds);

    /**
     * 完成上架单列表
     *
     * @param query query
     * @return /
     */
    WorkorderFinishConfirmVO finishConfirm(WorkorderRollbackListQuery query);

    /**
     * Shipment Rollback
     *
     * @param cancelList 工单
     */
    void rollbackByShipment(List<OtbShipment> cancelList);

    /**
     * 拆单
     *
     * @param query 拆单参数
     * @return /
     */
    boolean split(List<SplitWorkorderParam> query);

    /**
     * 全部取消
     *
     * @param param 取消参数
     */
    void allCancel(OtbRequestCancelParam param);
}