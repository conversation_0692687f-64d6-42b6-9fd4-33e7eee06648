package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeOtbDetail;
import cn.need.cloud.biz.model.param.fee.create.FeeOtbDetailCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtbDetailUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtbDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtbDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtbDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用详情otb service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeOtbDetailService extends SuperService<FeeOtbDetail> {

    /**
     * 根据参数新增费用详情otb
     *
     * @param createParam 请求创建参数，包含需要插入的费用详情otb的相关信息
     * @return 费用详情otbID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeOtbDetailCreateParam createParam);


    /**
     * 根据参数更新费用详情otb
     *
     * @param updateParam 请求创建参数，包含需要更新的费用详情otb的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeOtbDetailUpdateParam updateParam);

    /**
     * 根据查询条件获取费用详情otb列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用详情otb对象的列表(分页)
     */
    List<FeeOtbDetailPageVO> listByQuery(FeeOtbDetailQuery query);

    /**
     * 根据查询条件获取费用详情otb列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用详情otb对象的列表(分页)
     */
    PageData<FeeOtbDetailPageVO> pageByQuery(PageSearch<FeeOtbDetailQuery> search);

    /**
     * 根据ID获取费用详情otb
     *
     * @param id 费用详情otbID
     * @return 返回费用详情otbVO对象
     */
    FeeOtbDetailVO detailById(Long id);

    /**
     * 根据费用详情otb唯一编码获取费用详情otb
     *
     * @param refNum 费用详情otb唯一编码
     * @return 返回费用详情otbVO对象
     */
    FeeOtbDetailVO detailByRefNum(String refNum);


    /**
     * 根据费用otbid获取费用详情otb集合
     *
     * @param feeOtbId 费用otbid
     * @return 费用详情otb集合
     */
    List<FeeOtbDetailVO> listByFeeOtbId(Long feeOtbId);

}