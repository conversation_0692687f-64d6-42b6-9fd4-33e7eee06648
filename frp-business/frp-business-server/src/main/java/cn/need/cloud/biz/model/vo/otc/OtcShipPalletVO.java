package cn.need.cloud.biz.model.vo.otc;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * OTC运输托盘 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC运输托盘 vo对象")
public class OtcShipPalletVO extends BaseSuperVO {


    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 原始托盘标签数据
     */
    @Schema(description = "原始托盘标签数据")
    private String labelRawData;

    /**
     * 原始托盘标签类型
     */
    @Schema(description = "原始托盘标签类型")
    private String rawDataType;

    /**
     * 托盘标签物流追踪码
     */
    @Schema(description = "托盘标签物流追踪码")
    private String labelRefNum;

    /**
     * 托盘标签原始数据类型文件id
     */
    @Schema(description = "托盘标签原始数据类型文件id")
    private String fileIdRawDataType;

    /**
     * 托盘标签纸张类型
     */
    @Schema(description = "托盘标签纸张类型")
    private String paperType;

    /**
     * 纸箱数量
     */
    @Schema(description = "纸箱数量")
    private Integer cartonCount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 明细
     */
    @Schema(description = "明细")
    private List<OtcShipPalletDetailVO> detailList;

}