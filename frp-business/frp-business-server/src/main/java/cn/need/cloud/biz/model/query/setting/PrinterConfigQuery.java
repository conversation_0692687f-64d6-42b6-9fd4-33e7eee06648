package cn.need.cloud.biz.model.query.setting;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 打印配置 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "打印配置 query对象")
public class PrinterConfigQuery extends SuperQuery {

    /**
     * 打印机类型
     */
    @Schema(description = "打印机类型")
    private String type;

    /**
     * 打印机类型
     */
    @Schema(description = "打印机类型集合")
    @Condition(value = Keyword.IN, fields = {"type"})
    private List<String> typeList;

    /**
     * 打印机名称
     */
    @Schema(description = "打印机名称")
    private String name;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ip;


}