package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.vo.page.OtcWorkorderPageVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
public class OtcPickingSlipFilterBuildVO {

    /**
     * 工单
     */
    private List<OtcWorkorderPageVO> workOrders;

    /**
     * FilterBuild查询参数
     */
    private OtcPickingSlipFilterBuildQuery query;
}
