package cn.need.cloud.biz.model.entity.product;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * <p>
 * 产品多箱
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_multibox")
public class ProductMultibox extends SuperModel {

    /**
     * 供应商id
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 发货长度
     */
    @TableField("ship_length")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @TableField("ship_width")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @TableField("ship_height")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @TableField("ship_weight")
    private BigDecimal shipWeight;

    /**
     * 发货重量单位
     */
    @TableField("ship_weight_unit")
    private String shipWeightUnit;

    /**
     * 发货尺寸单位
     */
    @TableField("ship_dimension_unit")
    private String shipDimensionUnit;

    /**
     * UPC码
     */
    @TableField("upc")
    private String upc;

    /**
     * 行序号
     */
    @TableField("line_num")
    @Unique(message = "LineNum is unique under productId", combined = {"productId"})
    private Integer lineNum;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 多箱产品版本号
     */
    @TableField("multibox_version_int")
    private Integer multiboxVersionInt;

}
