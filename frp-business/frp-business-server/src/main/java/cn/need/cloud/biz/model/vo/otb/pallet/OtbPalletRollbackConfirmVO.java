package cn.need.cloud.biz.model.vo.otb.pallet;

import cn.need.cloud.biz.model.vo.base.pkg.PackageConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * WorkorderRollbackListQuery
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "包裹 Rollback列表 对象")
public class OtbPalletRollbackConfirmVO implements Serializable {

    @Schema(description = "包裹详情")
    private List<PackageConfirmDetailVO> detailList;

    @Schema(description = "工单详情")
    private List<WorkorderConfirmDetailVO> workorderDetailList;
}
