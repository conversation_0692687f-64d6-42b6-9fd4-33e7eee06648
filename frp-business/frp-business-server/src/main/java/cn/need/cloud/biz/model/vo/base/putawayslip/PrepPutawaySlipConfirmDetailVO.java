package cn.need.cloud.biz.model.vo.base.putawayslip;

import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 上架单 确认页 Detail 对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "上架单 确认页 Detail 对象")
public class PrepPutawaySlipConfirmDetailVO implements Serializable, BaseProductAware, BaseBinLocationAware {

    @Schema(description = "数量")
    private PrepPutawaySlipConfirmVO prepPutawaySlip;

    @Schema(description = "数量")
    private Integer qty;

    @Schema(description = "上架数量")
    private Integer putawayQty;

    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "库位id")
    private Long binLocationId;


}
