package cn.need.cloud.biz.model.query.otc.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/***
 * OTC Prep上架 putAway query对象
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@Schema(description = "OTC Prep上架 putAway query对象")
public class OtcPrepPickingSlipPutAwayQuery {

    /**
     * prep 拣货单id
     */
    @Schema(description = "prep 拣货单id")
    @NotNull(message = "Prep Picking Slip id must be not null")
    private Long otcPrepPickingSlipId;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    @Min(value = 1, message = "Put Away qty must be greater than 1")
    @NotNull(message = "Put Away qty must be greater than 1")
    private Integer qty;
}
