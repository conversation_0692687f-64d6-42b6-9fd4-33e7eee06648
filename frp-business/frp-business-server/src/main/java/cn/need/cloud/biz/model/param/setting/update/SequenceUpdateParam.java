package cn.need.cloud.biz.model.param.setting.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 全局序列号ref create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "全局序列号ref vo对象")
public class SequenceUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 序列类型
     */
    @Schema(description = "序列类型")
    private String sequenceType;

    /**
     * 序列代码
     */
    @Schema(description = "序列代码")
    private String code;

    /**
     * 序列ID
     */
    @Schema(description = "序列ID")
    private Long sequenceId;


}