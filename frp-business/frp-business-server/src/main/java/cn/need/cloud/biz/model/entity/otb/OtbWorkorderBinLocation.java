package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.model.entity.base.BaseWorkorderBinLocationModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * otb工单仓储位置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_workorder_bin_location")
public class OtbWorkorderBinLocation extends BaseWorkorderBinLocationModel {


    @Serial
    private static final long serialVersionUID = -8936597980897905686L;
    /**
     * 发货到c端工单id
     */
    @TableField("otb_workorder_id")
    private Long otbWorkorderId;

    /**
     * 发货到c端工单详情id
     */
    @TableField("otb_workorder_detail_id")
    private Long otbWorkorderDetailId;

    /**
     * 发货到c端拣货id
     */
    @TableField("otb_picking_slip_id")
    private Long otbPickingSlipId;

    /**
     * 发货到c端拣货详情id
     */
    @TableField("otb_picking_slip_detail_id")
    private Long otbPickingSlipDetailId;

    /**
     * 库位详情id
     */
    @TableField("bin_location_detail_id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

    /**
     * 产品版本id
     */
    @TableField("product_version_id")
    private Long productVersionId;

    /**
     * 库位详情锁id
     */
    @TableField("bin_location_detail_locked_id")
    private Long binLocationDetailLockedId;

}
