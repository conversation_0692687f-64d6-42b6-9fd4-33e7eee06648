package cn.need.cloud.biz.model.vo.inbound.pallet;

import cn.need.cloud.biz.model.vo.inbound.unload.InboundAggregatedUploadVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 打托卸货信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "打托卸货信息 vo对象")
public class InboundPalletUnloadVO extends InboundAggregatedUploadVO {

    @Serial
    private static final long serialVersionUID = -8927449347278102164L;
    /**
     * 每层几个箱子
     */
    @Schema(description = "每层几个箱子")
    private Integer cartonPerLayer;

    /**
     * 多了几个箱子
     */
    @Schema(description = "多了几个箱子")
    private Integer extCarton;

    /**
     * 每箱几个产品
     */
    @Schema(description = "每箱几个产品")
    private Integer pcsPerCarton;

    /**
     * 一共多少层
     */
    @Schema(description = "一共多少层")
    private Integer layersCount;

    /**
     * 上架单id
     */
    @Schema(description = "上架单id")
    private Long putAwaySlipId;

    /**
     * 数量
     */
    @Override
    public Integer getQty() {
        return pcsPerCarton * (cartonPerLayer * layersCount + extCarton);
    }
}
