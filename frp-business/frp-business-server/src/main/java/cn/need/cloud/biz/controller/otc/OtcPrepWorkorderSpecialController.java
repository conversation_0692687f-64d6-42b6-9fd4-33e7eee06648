package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderSpecialService;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * OTC预提工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-prep-workorder/special")
@Tag(name = "OTC预提工单-Special")
@AllArgsConstructor
@Slf4j
public class OtcPrepWorkorderSpecialController extends AbstractController {

    private final OtcPrepWorkorderSpecialService otcPrepWorkorderSpecialService;

}
