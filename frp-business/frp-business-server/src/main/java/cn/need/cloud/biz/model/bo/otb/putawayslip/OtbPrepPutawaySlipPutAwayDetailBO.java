package cn.need.cloud.biz.model.bo.otb.putawayslip;

import cn.need.cloud.biz.model.bo.base.putawayslip.PutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPutawaySlipDetail;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderDetail;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OTC上架单取消对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC上架单上架对象")
public class OtbPrepPutawaySlipPutAwayDetailBO extends PutawaySlipPutAwayDetailBO<OtbPrepPutawaySlipDetail> {


    // --------------------- 参数绑定 ---------------------

    @Schema(description = "工单详情", hidden = true)
    @JsonIgnore
    private OtbPrepWorkorderDetail workorderDetail;


}
