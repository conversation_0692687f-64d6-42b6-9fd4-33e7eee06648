package cn.need.cloud.biz.model.query.inventory;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 预留库存 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "预留库存 query对象")
public class InventoryReserveQuery extends SuperQuery {

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * reserveStatus
     */
    @Schema(description = "reserveStatus")
    private String reserveStatus;

    /**
     * reserveStatus
     */
    @Schema(description = "reserveStatus集合")
    @Condition(value = Keyword.IN, fields = {"reserveStatus"})
    private List<String> reserveStatusList;

    /**
     * refTableId
     */
    @Schema(description = "refTableId")
    private Long refTableId;

    /**
     * refTableName
     */
    @Schema(description = "refTableName")
    private String refTableName;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum")
    private String refTableRefNum;

    /**
     * refTableRefNum
     */
    @Schema(description = "refTableRefNum集合")
    @Condition(value = Keyword.IN, fields = {"refTableRefNum"})
    private List<String> refTableRefNumList;

    /**
     * refTableShowName
     */
    @Schema(description = "refTableShowName")
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum")
    private String refTableShowRefNum;

    /**
     * refTableShowRefNum
     */
    @Schema(description = "refTableShowRefNum集合")
    @Condition(value = Keyword.IN, fields = {"refTableShowRefNum"})
    private List<String> refTableShowRefNumList;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * reserveType
     */
    @Schema(description = "reserveType")
    private String reserveType;

    /**
     * reserveType
     */
    @Schema(description = "reserveType集合")
    @Condition(value = Keyword.IN, fields = {"reserveType"})
    private List<String> reserveTypeList;


}