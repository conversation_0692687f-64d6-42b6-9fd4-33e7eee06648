package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundPutawaySlipDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlip;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 上架 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundPutawaySlipConverter extends AbstractModelConverter<InboundPutawaySlip, InboundPutawaySlipVO, InboundPutawaySlipDTO> {

}
