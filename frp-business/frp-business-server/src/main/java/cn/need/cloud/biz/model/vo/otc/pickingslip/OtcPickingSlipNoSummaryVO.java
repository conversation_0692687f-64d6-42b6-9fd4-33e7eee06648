package cn.need.cloud.biz.model.vo.otc.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * OTC预拣货单详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC预拣货单详情 Summary Print vo对象")
public class OtcPickingSlipNoSummaryVO {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    private String productRefNum;
    /**
     * 产品编码
     */
    @Schema(description = "产品UPC")
    private String upc;

    /**
     * 产品厂商SKU
     */
    @Schema(description = "产品厂商SKU")
    private String productSupplierSku;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 发货到c端拣货id
     */
    @Schema(description = "发货到c端拣货id")
    private Long otcPickingSlipId;

    /**
     * 产品id
     */
    @Schema(description = "产品新品id")
    private Long productId;

    /**
     * 产品新品id
     */
    @Schema(description = "产品id")
    private Long productVersionId;

    /**
     * 拣货单列表
     */
    @Schema(description = "拣货单列表")
    private List<OtcPickingSlipSummaryVO> pickingSlips;


    /**
     * 库位详情id
     */
    @Schema(description = "库位详情id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;


    /**
     * 库位编码
     */
    @Schema(description = "库位编码")
    private String binLocationRefNum;

}