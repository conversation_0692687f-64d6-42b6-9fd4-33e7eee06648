package cn.need.cloud.biz.mapper.setting;


import cn.need.cloud.biz.model.entity.setting.PrinterSettings;
import cn.need.cloud.biz.model.query.setting.PrinterSettingsQuery;
import cn.need.cloud.biz.model.vo.page.PrinterSettingsPageVO;
import cn.need.cloud.biz.model.vo.setting.PrinterSettingsVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 打印设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Mapper
public interface PrinterSettingsMapper extends SuperMapper<PrinterSettings> {
    /**
     * 不分页
     *
     * @param condition 查询条件
     * @return 打印设置的集合
     */
    List<PrinterSettingsPageVO> listByQuery(@Param("qo") PrinterSettingsQuery condition);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @param page      分页对象
     * @return 打印设置的集合
     */
    List<PrinterSettingsPageVO> listByQuery(@Param("qo") PrinterSettingsQuery condition, @Param("page") Page<?> page);

    /**
     * 根据用户id查询打印机设置
     *
     * @param userId 就是CreateBy
     */
    PrinterSettingsVO selectByUserId(Long userId);
}