package cn.need.cloud.biz.service.otc.ship;

import cn.need.cloud.biz.model.entity.otc.OtcShipPalletDetail;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletDetailQuery;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletDetailVO;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC运输托盘详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcShipPalletDetailService extends SuperService<OtcShipPalletDetail> {

    /**
     * 根据查询条件获取OTC运输托盘详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC运输托盘详情对象的列表(分页)
     */
    PageData<OtcShipPalletDetailPageVO> pageByQuery(PageSearch<OtcShipPalletDetailQuery> search);

    /**
     * 根据ID获取OTC运输托盘详情
     *
     * @param id OTC运输托盘详情ID
     * @return 返回OTC运输托盘详情VO对象
     */
    OtcShipPalletDetailVO detailById(Long id);


    /**
     * 获取发货托盘详情列表
     *
     * @param shipPalletId 发货托盘id
     * @return /
     */
    List<OtcShipPalletDetailVO> listDetailByShipPalletId(Long shipPalletId);


    /**
     * 获取发货托盘详情列表
     *
     * @param shipPalletId 发货托盘id
     * @return /
     */
    List<OtcShipPalletDetail> listByShipPalletId(Long shipPalletId);

    /**
     * 根据packageList获取OTC运输托盘详情列表
     *
     * @param packageList 包裹id列表
     * @return /
     */
    List<OtcShipPalletDetail> listByPackageIdList(List<Long> packageList);
}