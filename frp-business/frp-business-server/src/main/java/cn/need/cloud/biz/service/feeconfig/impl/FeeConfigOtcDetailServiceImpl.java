package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigOtcDetailConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigOtcDetailMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtcDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigOtcDetailCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigOtcDetailUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtcDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtcDetailPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtcDetailService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

/**
 * <p>
 * 仓库报价费用配置otc详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service("feeConfigDetailOtc")
public class FeeConfigOtcDetailServiceImpl extends SuperServiceImpl<FeeConfigOtcDetailMapper, FeeConfigOtcDetail> implements FeeConfigOtcDetailService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeConfigOtcDetailCreateParam createParam) {
        // 检查传入仓库报价费用配置otc详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将仓库报价费用配置otc详情参数对象转换为实体对象并初始化
        FeeConfigOtcDetail entity = initFeeConfigOtcDetail(createParam);

        // 插入仓库报价费用配置otc详情实体对象到数据库
        super.insert(entity);

        // 返回仓库报价费用配置otc详情ID
        return entity.getId();
    }

    /**
     * 初始化仓库报价费用配置otc详情实体对象。
     *
     * @param createParam  需要转换并初始化的仓库报价费用配置otc详情参数对象，不能为空
     * @param initConsumer 用于执行初始化操作的消费者对象
     * @return 初始化后的仓库报价费用配置otc详情实体对象
     */
    @Override
    public FeeConfigOtcDetail initFeeConfigOtcDetail(FeeConfigOtcDetailCreateParam createParam, Consumer<FeeConfigOtcDetail> initConsumer) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("FeeConfigOtcDetailCreateParam cannot be empty");
        }

        // 获取仓库报价费用配置otc详情转换器实例，用于将仓库报价费用配置otc详情参数对象转换为实体对象
        FeeConfigOtcDetailConverter converter = Converters.get(FeeConfigOtcDetailConverter.class);

        // 将仓库报价费用配置otc详情参数对象转换为实体对象并初始化
        FeeConfigOtcDetail entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_OTC_DETAIL));

        // 执行初始化操作
        initConsumer.accept(entity);
        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 批量初始化仓库报价费用配置otc详情实体对象。
     *
     * @param createParams 需要批量转换并初始化的仓库报价费用配置otc详情参数对象列表，列表及元素均不能为空
     * @param initConsumer 用于执行每个实体初始化操作的消费者对象
     * @return 包含所有初始化后的仓库报价费用配置otc详情实体对象的列表
     */

    @Override
    public List<FeeConfigOtcDetail> initFeeConfigOtcDetail(
            List<FeeConfigOtcDetailCreateParam> createParams,
            Consumer<FeeConfigOtcDetail> initConsumer) {

        // 校验参数列表是否为空
        if (ObjectUtil.isEmpty(createParams)) {
            throw new BusinessException("createParams list cannot be empty");
        }

        // 创建结果集合
        List<FeeConfigOtcDetail> entities = new ArrayList<>();

        // 遍历每个参数对象进行初始化
        for (FeeConfigOtcDetailCreateParam createParam : createParams) {
            // 调用单对象初始化方法并添加到结果集合
            FeeConfigOtcDetail entity = initFeeConfigOtcDetail(createParam, initConsumer);
            entities.add(entity);
        }

        // 返回批量初始化后的实体列表
        return entities;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeConfigOtcDetailUpdateParam updateParam) {
        // 检查传入仓库报价费用配置otc详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取仓库报价费用配置otc详情转换器实例，用于将仓库报价费用配置otc详情参数对象转换为实体对象
        FeeConfigOtcDetailConverter converter = Converters.get(FeeConfigOtcDetailConverter.class);

        // 将仓库报价费用配置otc详情参数对象转换为实体对象
        FeeConfigOtcDetail entity = converter.toEntity(updateParam);

        // 执行更新仓库报价费用配置otc详情操作
        return super.update(entity);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByFeeConfigOtcId(Long feeConfigOtcId, List<FeeConfigOtcDetailUpdateParam> updateParamList) {
        //更新Details
        List<FeeConfigOtcDetail> dbFeeConfigOtcDetailList = listEntityByFeeConfigOtcId(feeConfigOtcId);

        FeeConfigOtcDetailConverter detailConverter = Converters.get(FeeConfigOtcDetailConverter.class);
        List<FeeConfigOtcDetail> updateDetailList = detailConverter.toEntity(updateParamList);

        updateDetailList.forEach(item -> {
            item.setHeaderId(feeConfigOtcId);
        });

        update(dbFeeConfigOtcDetailList, updateDetailList);
    }


    @Override
    public List<FeeConfigOtcDetailPageVO> listByQuery(FeeConfigOtcDetailQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<FeeConfigOtcDetailPageVO> pageByQuery(PageSearch<FeeConfigOtcDetailQuery> search) {
        Page<FeeConfigOtcDetail> page = Conditions.page(search, entityClass);
        List<FeeConfigOtcDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public FeeConfigOtcDetailVO detailById(Long id) {
        FeeConfigOtcDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeConfigOtcDetail", id));
        }
        return buildFeeConfigOtcDetailVO(entity);
    }

    @Override
    public List<FeeConfigOtcDetail> listEntityByFeeConfigOtcId(Long feeConfigOtcId) {
        return lambdaQuery().eq(FeeConfigOtcDetail::getHeaderId, feeConfigOtcId).list();
    }

    @Override
    public List<FeeConfigOtcDetailVO> listByFeeConfigOtcId(Long feeConfigOtcId) {
        List<FeeConfigOtcDetail> list = lambdaQuery().eq(FeeConfigOtcDetail::getHeaderId, feeConfigOtcId).list();
        return Converters.get(FeeConfigOtcDetailConverter.class).toVO(list);
    }

    @Override
    public List<FeeConfigOtcDetailVO> listByFeeConfigOtcIdList(Collection<Long> feeConfigIds) {
        if (ObjectUtil.isEmpty(feeConfigIds)) {
            return java.util.Collections.emptyList();
        }
        List<FeeConfigOtcDetail> list = lambdaQuery().in(FeeConfigOtcDetail::getHeaderId, new HashSet<>(feeConfigIds)).list();
        return Converters.get(FeeConfigOtcDetailConverter.class).toVO(list);
    }

    @Override
    public Map<Long, List<FeeConfigOtcDetailVO>> mapByFeeConfigOtcIdList(Collection<Long> feeConfigIds) {
        return ObjectUtil.toMapList(listByFeeConfigOtcIdList(feeConfigIds), FeeConfigOtcDetailVO::getHeaderId);
    }

    /**
     * 初始化仓库报价费用配置otc详情对象
     * 此方法用于设置仓库报价费用配置otc详情对象的必要参数，确保其处于有效状态
     *
     * @param createParam 仓库报价费用配置otc详情对象，不应为空
     * @return 返回初始化后的仓库报价费用配置otc详情
     * @throws BusinessException 如果传入的仓库报价费用配置otc详情为空，则抛出此异常
     */
    private FeeConfigOtcDetail initFeeConfigOtcDetail(FeeConfigOtcDetailCreateParam createParam) {

        return initFeeConfigOtcDetail(createParam, entity -> {
        });
    }

    /**
     * 构建仓库报价费用配置otc详情VO对象
     *
     * @param entity 仓库报价费用配置otc详情对象
     * @return 返回包含详细信息的仓库报价费用配置otc详情VO对象
     */
    private FeeConfigOtcDetailVO buildFeeConfigOtcDetailVO(FeeConfigOtcDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的仓库报价费用配置otc详情VO对象
        return Converters.get(FeeConfigOtcDetailConverter.class).toVO(entity);
    }
}
