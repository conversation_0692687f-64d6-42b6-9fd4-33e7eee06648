package cn.need.cloud.biz.model.vo.fee.page;

import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * 费用otc 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "费用otc 分页列表VO对象")
public class FeeOtcPageVO extends SuperVO {

    @Serial
    private static final long serialVersionUID = 5678817938199278058L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）")
    private String currency;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id")
    private Long feeOriginalDataId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String snapshotRefNum;

    /**
     * 请求id
     */
    @Schema(description = "请求id")
    private Long snapshotRequestId;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String snapshotRequestRefNum;

    /**
     * 总费用
     */
    @Schema(description = "总费用")
    private BigDecimal totalFee;

    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id")
    private Long transactionPartnerId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;


}