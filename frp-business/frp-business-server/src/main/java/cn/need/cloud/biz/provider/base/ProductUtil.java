package cn.need.cloud.biz.provider.base;


import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.biz.model.query.product.ProductQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
public class ProductUtil {


    /**
     * 填充产品信息
     *
     * @param transactionPartner 合作伙伴
     * @param product            产品
     */
    public static void fillProduct(TenantReqDTO transactionPartner, ProductReqDTO product) {
        if (ObjectUtil.isEmpty(transactionPartner)) {
            throw new BusinessException("transactionPartner can not null");
        }
        if (ObjectUtil.isEmpty(transactionPartner.getTenantId())) {
            throw new BusinessException("tenantId can not null");
        }
        if (ObjectUtil.isEmpty(product)) {
            throw new BusinessException("product can not null");
        }

        if (ObjectUtil.isEmpty(product.getRefNum()) && ObjectUtil.isEmpty(product.getSupplierSku())) {
            throw new BusinessException("product refNum and supplierSku can not all null");
        }
        ProductService productService = SpringUtil.getBean(ProductService.class);


        final ProductQuery productQuery = new ProductQuery();
        productQuery.setTransactionPartnerId(transactionPartner.getTenantId());

        productQuery.setRefNum(product.getRefNum());
        productQuery.setSupplierSku(product.getSupplierSku());

        final List<ProductVO> products = productService.listByQuery(productQuery);
        if (products.isEmpty()) {
            throw new BusinessException(StringUtil.format("{} /{} product not found", transactionPartner.getShowString(), product.getShowString()));
        }
        if (products.size() > 1) {
            throw new BusinessException(StringUtil.format("{} /{} product not unique {}", transactionPartner.getShowString(), product.getShowString(), JsonUtil.toJson(products)));
        }
        product.setProductId(products.get(0).getId());
    }

    /**
     * 填充产品信息
     *
     * @param transactionPartner 合作伙伴
     * @param products           产品
     */
    public static void fillProduct(TenantReqDTO transactionPartner, List<ProductReqDTO> products) {
        if (ObjectUtil.isEmpty(transactionPartner)) {
            throw new BusinessException("transactionPartner can not null");
        }
        if (ObjectUtil.isEmpty(transactionPartner.getTenantId())) {
            throw new BusinessException("tenantId can not null");
        }
        if (ObjectUtil.isEmpty(products)) {
            throw new BusinessException("products can not null");
        }

        products.forEach(product -> {
            if (ObjectUtil.isEmpty(product.getRefNum()) && ObjectUtil.isEmpty(product.getSupplierSku())) {
                throw new BusinessException("product refNum and supplierSku can not all null");
            }
        });

        //todo: 重要！！！ 这里待优化，可以批量查询 & 增加缓存，应该从缓存查询
        products.forEach(product -> fillProduct(transactionPartner, product));
    }

    /**
     * 填充产品信息
     *
     * @param productList 产品信息容器
     */
    public static void fillProduct(List<ProductReqDTO> productList) {
        //产品id集合
        List<Long> productIdList = productList.stream()
                .map(ProductReqDTO::getProductId)
                .toList();
        //产品信息
        List<ProductCache> cacheList = ProductCacheUtil.listByIds(productIdList);
        //根据产品id映射产品信息
        Map<Long, ProductCache> map = ObjectUtil.toMap(cacheList, ProductCache::getId);
        //遍历产品信息容器映射产品信息
        productList.forEach(item -> {
            ProductCache productCache = map.get(item.getProductId());
            if (ObjectUtil.isNotEmpty(productCache)) {
                item.setRefNum(productCache.getRefNum());
                item.setSupplierSku(productCache.getSupplierSku());
            }
        });
    }

    public static BaseProductDTO getByProductId(Long productId) {
        if (productId == null) {
            return null;
        }
        final Map<Long, BaseProductDTO> result = getByProductId(Collections.singletonList(productId));
        return result.isEmpty() ? null : result.get(productId);
    }

    public static Map<Long, BaseProductDTO> getByProductId(Collection<Long> productIdList) {
        //产品信息
        List<ProductCache> cacheList = ProductCacheUtil.listByIds(productIdList);
        //获取租户id集合
        List<Long> tenantIdList = cacheList.stream()
                .map(ProductCache::getTransactionPartnerId)
                .toList();
        //获取租户信息
        List<TenantCache> tenantCaches = TenantCacheUtil.listByIds(tenantIdList);
        //根据租户id映射租户信息
        Map<Long, TenantCache> map = ObjectUtil.toMap(tenantCaches, TenantCache::getId);
        //遍历产品信息
        List<BaseProductDTO> list = cacheList.stream()
                .map(item -> {
                    BaseProductDTO baseProductDTO = new BaseProductDTO();
                    ProductReqDTO productReqDTO = new ProductReqDTO(
                            item.getRefNum(),
                            item.getSupplierSku(),
                            item.getId()
                    );
                    baseProductDTO.setProduct(productReqDTO);
                    TenantCache tenantCache = map.get(item.getTransactionPartnerId());
                    if (ObjectUtil.isNotEmpty(tenantCache)) {
                        TenantReqDTO tenantReqDTO = new TenantReqDTO();
                        tenantReqDTO.setRefNum(tenantCache.getTenantCode());
                        tenantReqDTO.setTenantId(tenantCache.getId());
                        tenantReqDTO.setAbbrName(tenantCache.getAbbrName());
                        baseProductDTO.setTransactionPartner(tenantReqDTO);
                    }
                    return baseProductDTO;
                }).toList();
        //返结果
        return ObjectUtil.toMap(list, BaseProductDTO::getProductId);
    }

    public static BaseProductDTO convert(BaseProductVO baseProductVO) {
        //产品信息
        BaseProductDTO baseProductDTO = BeanUtil.copyNew(baseProductVO, BaseProductDTO.class);
        //填充产品信息
        baseProductDTO.setProduct(BeanUtil.copyNew(baseProductVO, ProductReqDTO.class));
        //填充合作伙伴信息
        TenantCache tenantCache = TenantCacheUtil.getById(baseProductVO.getTransactionPartnerId());
        TenantReqDTO tenantReqDTO = BeanUtil.copyNew(tenantCache, TenantReqDTO.class);
        tenantReqDTO.setRefNum(tenantCache.getTenantCode());
        baseProductDTO.setTransactionPartner(tenantReqDTO);
        //返回结果
        return baseProductDTO;
    }
}
