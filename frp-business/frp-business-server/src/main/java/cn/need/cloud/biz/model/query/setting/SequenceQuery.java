package cn.need.cloud.biz.model.query.setting;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 全局序列号ref query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "全局序列号ref query对象")
public class SequenceQuery extends SuperQuery {

    /**
     * 序列类型
     */
    @Schema(description = "序列类型")
    private String sequenceType;

    /**
     * 序列类型
     */
    @Schema(description = "序列类型集合")
    @Condition(value = Keyword.IN, fields = {"sequenceType"})
    private List<String> sequenceTypeList;

    /**
     * 序列代码
     */
    @Schema(description = "序列代码")
    private String code;

    /**
     * 序列ID
     */
    @Schema(description = "序列ID")
    private Long sequenceId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


}