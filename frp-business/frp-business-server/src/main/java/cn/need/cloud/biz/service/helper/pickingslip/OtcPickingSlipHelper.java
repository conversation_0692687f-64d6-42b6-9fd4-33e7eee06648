package cn.need.cloud.biz.service.helper.pickingslip;

import cn.need.cloud.biz.client.constant.enums.otc.OtcPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlip;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlipDetail;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlipDetail;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.mybatis.model.IdModel;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * PickingSlipHelper
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public class OtcPickingSlipHelper {

    /**
     * 重新刷新拣货单状态
     *
     * @param prepPickingSlips 拣货单
     * @param detailGroupMap   详情
     */
    public static void refreshStatus(List<OtcPickingSlip> prepPickingSlips, Map<Long, List<OtcPickingSlipDetail>> detailGroupMap) {
        var prepPickingSlipMap = StreamUtils.toMap(prepPickingSlips, IdModel::getId);
        // 拣货单状态更新
        detailGroupMap.forEach((prepPickingSlipId, details) -> {
            var prepPickingSlip = prepPickingSlipMap.get(prepPickingSlipId);

            // 全部上架 ReadyToShip
            var allReadyToShip = details.stream().allMatch(obj -> Objects.equals(obj.getReadyToShipQty(), obj.getQty()));
            // 全部拣货 Picked
            var allPicked = details.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
            // 拣货 InPicking
            var hasPick = details.stream().anyMatch(obj -> obj.getPickedQty() > 0);

            var oldStatus = prepPickingSlip.getPickingSlipStatus();
            prepPickingSlip.setPickingSlipStatus(allReadyToShip ? OtcPickingSlipStatusEnum.READY_TO_SHIP.getStatus()
                    : allPicked ? OtcPickingSlipStatusEnum.PICKED.getStatus()
                    : hasPick ? OtcPickingSlipStatusEnum.IN_PICKING.getStatus()
                    : OtcPickingSlipStatusEnum.NEW.getStatus()
            );

            // 记录日志
            if (!Objects.equals(oldStatus, prepPickingSlip.getPickingSlipStatus())) {
                OtcPickingSlipAuditLogHelper.recordLog(prepPickingSlip);
            }
        });
    }

    /**
     * 重新刷新拣货单状态
     *
     * @param prepPickingSlips 拣货单
     * @param detailGroupMap   详情
     */
    public static void refreshPrepStatus(List<OtcPrepPickingSlip> prepPickingSlips, Map<Long, List<OtcPrepPickingSlipDetail>> detailGroupMap) {
        var prepPickingSlipMap = StreamUtils.toMap(prepPickingSlips, IdModel::getId);
        // 拣货单状态更新
        detailGroupMap.forEach((prepPickingSlipId, details) -> {
            var prepPickingSlip = prepPickingSlipMap.get(prepPickingSlipId);

            // 全部上架 Putaway
            var allPutaway = details.stream().allMatch(obj -> Objects.equals(obj.getPutawayQty(), obj.getQty()));
            // 全部拣货 Picked
            var allPicked = details.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
            // 拣货 InPicking
            var hasPick = details.stream().anyMatch(obj -> obj.getPickedQty() > 0);

            var oldStatus = prepPickingSlip.getPrepPickingSlipStatus();
            prepPickingSlip.setPrepPickingSlipStatus(allPutaway ? OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus()
                    : allPicked ? OtcPrepPickingSlipStatusEnum.PICKED.getStatus()
                    : hasPick ? OtcPrepPickingSlipStatusEnum.IN_PICKING.getStatus()
                    : OtcPrepPickingSlipStatusEnum.NEW.getStatus()
            );

            // 记录日志
            if (!Objects.equals(oldStatus, prepPickingSlip.getPrepPickingSlipStatus())) {
                OtcPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip);
            }
        });
    }
}
