package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigStorageDetailDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorageDetail;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置storage详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigStorageDetailConverter extends AbstractModelConverter<FeeConfigStorageDetail, FeeConfigStorageDetailVO, FeeConfigStorageDetailDTO> {

}
