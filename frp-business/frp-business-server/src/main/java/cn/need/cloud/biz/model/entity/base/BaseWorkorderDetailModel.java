package cn.need.cloud.biz.model.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseWorkorderDetailModel extends ProductModel {

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;

    //
    // /**
    //  * 产品id
    //  */
    // @TableField("product_id")
    // private Long productId;


    // /**
    //  * 数量
    //  */
    // @TableField("qty")
    // private Integer qty;
    //
    // /**
    //  * 拣货数量
    //  */
    // @TableField("picked_qty")
    // private Integer pickedQty;
}
