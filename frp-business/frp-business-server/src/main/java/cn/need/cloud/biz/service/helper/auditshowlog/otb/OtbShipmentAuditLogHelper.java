package cn.need.cloud.biz.service.helper.auditshowlog.otb;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 发货单日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class OtbShipmentAuditLogHelper {

    public static void recordLog(List<OtbShipment> otbShipmentList, String status, String type, String note, String description) {
        otbShipmentList.forEach(item -> recordLog(item, status, type, note, description));
    }

    public static void recordLog(List<OtbShipment> otbShipmentList, String type, String note, String description) {
        otbShipmentList.forEach(item -> recordLog(item, type, note, description));
    }

    public static void recordLog(List<OtbShipment> otbShipmentList) {
        otbShipmentList.forEach(OtbShipmentAuditLogHelper::recordLog);
    }

    public static void recordLog(OtbShipment otbShipment) {
        recordLog(otbShipment, otbShipment.getOtbShipmentStatus(), BaseTypeLogEnum.STATUS.getType(), null, null);
    }

    public static void recordLog(OtbShipment otbShipment, String type, String note, String description) {
        recordLog(otbShipment, otbShipment.getOtbShipmentStatus(), type, note, description);
    }

    public static void recordLog(OtbShipment otbShipment, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(otbShipment)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }

    public static void recordWithStatus(OtbShipment otbShipment, String status, String type, String note) {
        recordLog(otbShipment, status, type, note, null);
    }

}
