package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPrepPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipListQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipPickQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipPutAwayQuery;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtbPrepPickingSlipPageVO;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipBuildService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipPutAwayService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * otb预拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-prep-picking-slip")
@Tag(name = "otb预拣货单")
public class OtbPrepPickingSlipController extends AbstractRestController<OtbPrepPickingSlipService, OtbPrepPickingSlip, OtbPrepPickingSlipConverter, OtbPrepPickingSlipVO> {

    @Resource
    private OtbPrepPickingSlipBuildService buildService;
    @Resource
    private OtbPrepPickingSlipPutAwayService putAwayService;

    @Operation(summary = "根据id获取otb预拣货单详情", description = "根据数据主键id，从数据库中获取其对应的otb预拣货单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPrepPickingSlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取otb预拣货单详情
        OtbPrepPickingSlipVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取otb预拣货单详情", description = "根据数据RefNum，从数据库中获取其对应的otb预拣货单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPrepPickingSlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取otb预拣货单详情
        OtbPrepPickingSlipVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取otb预拣货单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的otb预拣货单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPrepPickingSlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPrepPickingSlipListQuery> search) {

        // 获取otb预拣货单分页
        PageData<OtbPrepPickingSlipPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "Prep filterBuild 新增", description = "根据传入的搜索条件参数，构建Prep拣货单")
    @PostMapping(value = "/filter-build")
    public Result<List<WorkOrderNoEnoughAvailQtyVO>> filterBuild(@RequestBody @Parameter(description = "搜索条件参数", required = true)
                                                                 @Valid OtbPrepPickingSlipFilterBuildQuery query) {

        // 返回结果
        return success(buildService.filterBuild(query));
    }

    @Operation(summary = "Prep markPrinted", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }

    @Operation(summary = "Prep拣货pick", description = "根据传入的条件参数，拣货")
    @PostMapping(value = "/pick")
    public Result<Boolean> pick(@RequestBody @Valid OtbPrepPickingSlipPickQuery query) {

        // 返回结果
        return success(service.pick(query));
    }

    @Operation(summary = "putAway上架", description = "根据传入的条件参数，拣货")
    @PostMapping(value = "/put-away")
    public Result<OtbPrepPickingSlipVO> putAway(@RequestBody @Valid OtbPrepPickingSlipPutAwayQuery query) {

        // 返回结果
        OtbPrepPickingSlipVO pickingSlipVO = putAwayService.putAway(query);
        return success(pickingSlipVO);
    }
}
