package cn.need.cloud.biz.mapper.product;

import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.query.product.ProductMultiboxQuery;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxListVO;
import cn.need.cloud.biz.model.vo.product.page.ProductMultiboxPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 产品多箱 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface ProductMultiboxMapper extends SuperMapper<ProductMultibox> {

    /**
     * 根据条件获取产品多箱列表
     *
     * @param query 查询条件
     * @return 产品多箱集合
     */
    default List<ProductMultiboxPageVO> listByQuery(ProductMultiboxQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取产品多箱分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 产品多箱集合
     */
    List<ProductMultiboxPageVO> listByQuery(@Param("qo") ProductMultiboxQuery query, @Param("page") Page<?> page);

    /**
     * 根据产品ID获取多包装列表
     *
     * @param productId 产品ID，用于查询多包装列表
     * @return 返回列表，表示多包装信息
     * 如果没有找到相关的多包装信息，将返回一个空列表
     */
    List<ProductMultiboxListVO> listByProductId(@Param("productId") Long productId);

    /**
     * 获取旧版本号
     * 主要用于在产品版本更新或比较时，需要参考旧版本信息的场景
     *
     * @param productId 产品ID，用于标识特定的产品
     * @return 旧版本号，如果找不到对应的产品，则返回null
     */
    Integer getOldVersionInt(@Param("productId") Long productId);

    /**
     * 根据产品ID和版本号查询多
     *
     * @param productId  产品id
     * @param versionInt 版本号
     * @return /
     */
    List<ProductMultibox> findListByProductAndVersionInt(@Param("productId") Long productId,
                                                         @Param("versionInt") int versionInt);

    /**
     * 根据产品ID和版本号查询多
     *
     * @param productIds 产品id
     * @param versionInt 版本号
     * @return /
     */
    List<ProductMultibox> findListByProductAndVersionIntList(@Param("productIds") List<Long> productIds,
                                                             @Param("versionInts") List<Integer> versionInt);

    /**
     * 根据产品ID和版本号查询多
     *
     * @param productId          productId
     * @param multiboxVersionInt multiboxVersionInt
     * @param upc                upc
     * @return /
     */
    ProductMultibox findOneByProductIdAndVersionIntAndUpcAndLineNum(@Param("productId") Long productId,
                                                                    @Param("multiboxVersionInt") Integer multiboxVersionInt,
                                                                    @Param("upc") String upc);
}