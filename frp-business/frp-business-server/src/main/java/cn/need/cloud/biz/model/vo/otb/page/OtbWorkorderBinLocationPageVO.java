package cn.need.cloud.biz.model.vo.otb.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * otb工单仓储位置 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTB工单仓储位置 vo对象")
public class OtbWorkorderBinLocationPageVO extends BaseSuperVO implements BaseBinLocationAware, BaseProductAware {

    @Serial
    private static final long serialVersionUID = 52464678272797158L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 主键
     */
    @Schema(description = "库位主键")
    private Long binLocationId;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long otbPickingSlipId;
    /**
     * 拣货单
     */
    @Schema(description = "拣货单")
    private RefNumVO otbPickingSlip;

    /**
     * 发货到c端工单id
     */
    @Schema(description = "发货到c端工单id")
    private Long otbWorkorderId;
    /**
     * 发货到c端工单RefNum
     */
    @Schema(description = "工单")
    private RefNumVO otbWorkOrder;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

}