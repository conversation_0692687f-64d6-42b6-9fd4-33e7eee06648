package cn.need.cloud.biz.model.vo.otb.workorder;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;


/**
 * OTB预提工单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB预提工单 vo对象")
public class OtbPrepWorkorderVO extends BaseSuperVO implements BaseBinLocationAware, BaseProductAware {

    @Serial
    private static final long serialVersionUID = 5427483732128818285L;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 预工单类型
     */
    @Schema(description = "预工单类型")
    private String otbPrepWorkorderStatus;

    /**
     * 预工单类型
     */
    @Schema(description = "预工单类型")
    private String prepWorkorderType;

    /**
     * 预工单产品版本
     */
    @Schema(description = "预工单产品版本")
    private Integer prepWorkorderVersionInt;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    @Schema(description = "渠道要求的需要贴的产品标识码")
    private String productBarcode;

    /**
     * 渠道要求的需要贴的产品标识SKU
     */
    @Schema(description = "渠道要求的需要贴的产品标识SKU")
    private String productChannelSku;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 上架数量
     */
    @Schema(description = "上架数量")
    private Integer putawayQty;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * otb工单
     */
    @Schema(description = "otb工单")
    private RefNumVO otbWorkorder;

    /**
     * 详情
     */
    @Schema(description = "详情")
    private List<OtbPrepWorkorderDetailVO> detailList;

    // ---------------------------------- 以下非详情字段 --------------------------------------

    /**
     * otb 工单详情id
     */
    @Schema(description = "otb 工单详情id")
    private Long otbWorkorderDetailId;


    /**
     * 库存预定id
     */
    @Schema(description = "库存预定id")
    private Long inventoryReserveId;

    /**
     * otb预拣货单id
     */
    @Schema(description = "otb预拣货单id")
    private Long otbPrepPickingSlipId;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Long otbRequestId;

    @Schema(description = "流程类型")
    private String processType;

}