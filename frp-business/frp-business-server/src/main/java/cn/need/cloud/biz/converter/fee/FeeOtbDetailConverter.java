package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeOtbDetailDTO;
import cn.need.cloud.biz.model.entity.fee.FeeOtbDetail;
import cn.need.cloud.biz.model.vo.fee.FeeOtbDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用详情otb 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeOtbDetailConverter extends AbstractModelConverter<FeeOtbDetail, FeeOtbDetailVO, FeeOtbDetailDTO> {

}
