package cn.need.cloud.biz.model.query.profile;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * query对象
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = " query对象")
public class ProfileSystemQuery extends ProfileBaseQuery {

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型集合")
    @Condition(value = Keyword.IN, fields = {"serviceType"})
    private List<String> serviceTypeList;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码")
    private String categoryCode;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述")
    private String categoryDesc;

    /**
     * 值类型
     */
    @Schema(description = "值类型")
    private String valueType;

    /**
     * 值类型
     */
    @Schema(description = "值类型集合")
    @Condition(value = Keyword.IN, fields = {"valueType"})
    private List<String> valueTypeList;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 值
     */
    @Schema(description = "值")
    private String value;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 激活标志
     */
    @Schema(description = "激活标志")
    private Boolean activeFlag;

    /**
     * 代码
     */
    @Schema(description = "代码")
    private String code;


}