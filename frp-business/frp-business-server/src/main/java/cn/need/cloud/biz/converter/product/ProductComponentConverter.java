package cn.need.cloud.biz.converter.product;

import cn.need.cloud.biz.client.dto.product.ProductComponentDTO;
import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.vo.product.ProductComponentVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品组装 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductComponentConverter extends AbstractModelConverter<ProductComponent, ProductComponentVO, ProductComponentDTO> {

}
