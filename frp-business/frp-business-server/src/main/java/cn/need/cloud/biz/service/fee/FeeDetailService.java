package cn.need.cloud.biz.service.fee;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeModelTypeEnum;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.cloud.biz.model.vo.fee.BaseFeeDetailVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.feeconfig.FeeConfigDetailServiceFactory;
import cn.need.cloud.biz.service.feeconfig.FeeConfigServiceFactory;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * RefNumService
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface FeeDetailService<T extends RefNumModel, S extends SuperService<T>> extends
        RefNumService<T, S> {

    default void fillData(FeeModelTypeEnum feeModelType, BaseFeeDetailVO fee) {
        fillData(feeModelType, List.of(fee));
    }

    default void fillData(FeeModelTypeEnum feeModelType, Collection<? extends BaseFeeDetailVO> feeList) {
        final List<Long> feeConfigIdList = feeList.stream()
                .map(BaseFeeDetailVO::getFeeConfigId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();


        FeeConfigServiceFactory feeConfigServiceFactory = SpringUtil.getBean(FeeConfigServiceFactory.class);

        if (feeConfigServiceFactory == null) {
            throw new BusinessException(StringUtil.format("{} feeConfigServiceFactory is null", "FeeDetailService"));
        }

        Map<Long, RefNumWithNameVO> feeConfigMap = feeConfigServiceFactory.getBuilder(feeModelType).refNumWithNameMapByIds(feeConfigIdList);
        for (var feeInboundPageVO : feeList) {
            var refNumVO = feeConfigMap.get(feeInboundPageVO.getFeeConfigId());
            feeInboundPageVO.setFeeConfig(refNumVO);
        }
        
        final List<Long> feeConfigDetailIdList = feeList.stream()
                .map(BaseFeeDetailVO::getFeeConfigDetailId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();
        FeeConfigDetailServiceFactory feeConfigDetailServiceFactory = SpringUtil.getBean(FeeConfigDetailServiceFactory.class);

        if (feeConfigDetailServiceFactory == null) {
            throw new BusinessException(StringUtil.format("{} feeConfigDetailServiceFactory is null", "FeeDetailService"));
        }

        Map<Long, RefNumVO> feeConfigDetailMap = feeConfigDetailServiceFactory.getBuilder(feeModelType).refNumMapByIds(feeConfigDetailIdList);
        for (var feeInboundPageVO : feeList) {
            var refNumVO = feeConfigDetailMap.get(feeInboundPageVO.getFeeConfigDetailId());
            feeInboundPageVO.setFeeConfigDetail(refNumVO);
        }

    }

}
