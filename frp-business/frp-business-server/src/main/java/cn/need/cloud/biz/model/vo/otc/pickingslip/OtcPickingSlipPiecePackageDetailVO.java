package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.aware.BaseProductShowVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC包裹详情 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC包裹详情 vo对象")
public class OtcPickingSlipPiecePackageDetailVO extends BaseProductShowVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4248064986448809444L;
    private Long id;

    // /**
    //  * 产品id
    //  */
    // @Schema(description = "产品id")
    // private Long productId;

    /**
     * 危险品版本号
     */
    @Schema(description = "危险品版本号")
    private String hazmatVersionRefNum;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 行
     */
    @Schema(description = "行号")
    private Integer lineNum;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 需要拣货数量
     */
    @Schema(description = "需要拣货数量")
    private Integer needPickQty;

}