package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import cn.need.cloud.biz.model.query.auto.OtbPrepWorkorderAutoQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipPutAwayContextVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.inventory.PrepWorkorderService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * OTB预提工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPrepWorkorderService extends
        SuperService<OtbPrepWorkorder>,
        RefNumService<OtbPrepWorkorder, OtbPrepWorkorderService>,
        PrepWorkorderService<OtbPrepWorkorder, OtbWorkorder, OtbWorkorderDetail> {

    /**
     * 根据查询条件获取OTB预提工单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB预提工单对象的列表(分页)
     */
    PageData<OtbPrepWorkorderPageVO> pageByQuery(PageSearch<OtbPrepWorkOrderListQuery> search);

    PageData<OtbPrepWorkorderPageVO> pageV1ByQuery(PageSearch<OtbPrepWorkorderAutoQuery> search);

    /**
     * 根据ID获取OTB预提工单
     *
     * @param id OTB预提工单ID
     * @return 返回OTB预提工单VO对象
     */
    OtbPrepWorkorderVO detailById(Long id);

    /**
     * 根据OTB预提工单唯一编码获取OTB预提工单
     *
     * @param refNum OTB预提工单唯一编码
     * @return 返回OTB预提工单VO对象
     */
    OtbPrepWorkorderVO detailByRefNum(String refNum);


    /**
     * 根据工单ID列表获取OTB预提工单
     *
     * @param workOrderIdList otb工单集合
     * @return /
     */
    List<OtbPrepWorkorder> listByOtbWorkOrderIdList(List<Long> workOrderIdList);

    /**
     * Prep 工单触发 Begin
     *
     * @param workOrderIdList 工单id集合
     */
    List<OtbPrepWorkorder> workOrderBegin(Set<Long> workOrderIdList);

    /**
     * Prep Begin
     *
     * @param prepWorkOrderIdList Prep工单id集合
     */
    void begin(Set<Long> prepWorkOrderIdList);

    /**
     * 构建拣货单 查询
     *
     * @param query 拣货条件
     * @return 工单集合
     */
    List<OtbPrepWorkorder> filterBuildByQuery(OtbPrepPickingSlipFilterBuildQuery query);

    /**
     * 过滤统计
     *
     * @param search 过滤条件
     * @return /
     */
    long filterBuildPickingSlipCount(OtbPrepWorkOrderListQuery search);

    /**
     * 分组查询
     *
     * @param prepPickingSlipIdList 拣货单id集合
     * @return /
     */
    Map<Long, List<OtbPrepWorkorder>> groupByOtbPrepPickingSlipIdList(List<Long> prepPickingSlipIdList);

    /**
     * Prep工单拣货
     *
     * @param context 拣货上下文
     */
    void pick(OtbPrepPickingSlipPickContextVO context);

    /**
     * 设置工单状态未Processed
     *
     * @param context 上下文
     */
    void updateAndSetWorkOrderProcessed(OtbPrepPickingSlipPutAwayContextVO context);

    /**
     * 根据拣货单id获取可PutAway的Prep工单集合
     *
     * @param prepPickingSlipId Prep拣货单id
     * @return /
     */
    List<OtbPrepWorkorder> putAwayListByPrepPickingSlipId(Long prepPickingSlipId);

    /**
     * 根据拣货单id集合获取可PutAway的Prep工单集合
     *
     * @param prepPickingSlipIdList Prep拣货单id集合
     * @return /
     */
    List<OtbPrepWorkorder> listByPrepPickingSlipIds(List<Long> prepPickingSlipIdList);


    /**
     * 根据工单id集合获取Prep工单集合
     *
     * @param idList 工单id集合
     * @return /
     */
    List<OtbPrepWorkorder> listByWorkOrderIdList(List<Long> idList);

    /**
     * 根据工单详情id集合获取工单集合
     *
     * @param longs 工单详情id集合
     * @return /
     */
    List<OtbPrepWorkorder> listByWorkorderDetailIds(Collection<Long> longs);
}