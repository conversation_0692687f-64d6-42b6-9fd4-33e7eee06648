package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseFullProductVersionVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * 完整产品版本展示对象
 * <p>
 * 子类中不能包含 productVersionId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseFullProductVersionShowVO implements BaseFullProductVersionAware {
    /**
     * 产品版本id
     */
    @Getter
    @Setter
    private Long productVersionId;

    /**
     * 产品版本
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    private BaseFullProductVersionVO baseFullProductVersionVO;

    @Override
    public BaseFullProductVersionVO getBaseFullProductVersionVO() {
        if (ObjectUtil.isEmpty(productVersionId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseFullProductVersionVO)) {
            return baseFullProductVersionVO;
        }
        // Retrieve from cache once and store the result
        baseFullProductVersionVO = BaseFullProductVersionAware.super.getBaseFullProductVersionVO();
        return baseFullProductVersionVO;
    }
}

