package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlip;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.cloud.biz.model.vo.page.OtbPickingSlipPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * otb拣货单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbPickingSlipMapper extends SuperMapper<OtbPickingSlip> {

    /**
     * 根据条件获取otb拣货单列表
     *
     * @param query 查询条件
     * @return otb拣货单集合
     */
    default List<OtbPickingSlipPageVO> listByQuery(OtbPickingSlipQuery query) {
        return listByQuery(query, null, null);
    }

    /**
     * 根据条件获取otb拣货单分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return otb拣货单集合
     */
    List<OtbPickingSlipPageVO> listByQuery(@Param("qo") OtbPickingSlipQuery query,
                                           @Param("wk") OtbWorkorderQuery wkQuery,
                                           @Param("page") Page<?> page);

    /**
     * OTC拣货单下拉列表
     *
     * @param columnList 查询字段名
     * @param query      查询条件
     * @return OTC拣货单下拉列表
     */
    List<Map<String, Object>> dropProList(@Param("columnList") List<DropColumnInfoBO> columnList,
                                          @Param("qo") OtbPickingSlipQuery query);
}