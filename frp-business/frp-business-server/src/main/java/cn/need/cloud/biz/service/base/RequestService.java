package cn.need.cloud.biz.service.base;

import cn.need.cloud.biz.client.dto.req.base.info.RequestReqDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.biz.model.vo.base.BaseRequestPageVO;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.model.SuperModel;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 请求服务
 *
 * @param <T> 请求模型
 * @param <S> 请求服务
 * <AUTHOR>
 */
public interface RequestService<T extends SuperModel, S extends SuperService<T>> {

    default void fillRequestId(TenantReqDTO transactionPartner, List<RequestReqDTO> requestReqList) {

        if (ObjectUtil.isEmpty(transactionPartner)) {
            throw new BusinessException(StringUtil.format("transactionPartner can not null"));
        }
        //临时这样处理，否则需要修改太多了
        if (ObjectUtil.isEmpty(transactionPartner.getTenantId())) {
            TenantUtil.fillTenant(transactionPartner);
        }

        //获取refNum
        Set<String> refNumList = requestReqList.stream()
                .map(RequestReqDTO::getRefNum)
                .filter(refNum -> refNum != null && !refNum.isEmpty())
                .collect(Collectors.toSet());
        //获取requestRefNum
        Set<String> requestRefNumList = requestReqList.stream()
                .map(RequestReqDTO::getRequestRefNum)
                .filter(requestRefNum -> requestRefNum != null && !requestRefNum.isEmpty())
                .collect(Collectors.toSet());
        S s = (S) this;

        List<T> pageList = s.query()
                .and(query -> {
                    if (ObjectUtil.isNotEmpty(refNumList)) {
                        query.in("ref_num", refNumList);
                    }
                    if (ObjectUtil.isNotEmpty(requestRefNumList)) {
                        query.in("request_ref_num", requestRefNumList);
                    }
                })
                .eq("transaction_partner_id", transactionPartner.getTenantId())
                .list();

        List<BaseRequestPageVO> list = BeanUtil.copyNew(pageList, BaseRequestPageVO.class);
        //根据refNum映射入库请求id
        Map<String, Long> refNumMap = ObjectUtil.toMap(list, BaseRequestPageVO::getRefNum, BaseRequestPageVO::getId);
        //根据requestRefNum映射入库请求id
        Map<String, Long> requestRefNumMap = ObjectUtil.toMap(list, BaseRequestPageVO::getRequestRefNum, BaseRequestPageVO::getId);
        //遍历请求列表
        requestReqList.forEach(item -> {
            //设置请求id
            Long requestId = refNumMap.get(item.getRefNum());
            if (ObjectUtil.isNotEmpty(requestId)) {
                item.setRequestId(requestId);
                return;
            }
            //设置请求id
            requestId = requestRefNumMap.get(item.getRequestRefNum());
            if (ObjectUtil.isNotEmpty(requestId)) {
                item.setRequestId(requestId);
                return;
            }
            throw new RuntimeException(
                    StringUtil.format(
                            "The request does not exist.refNum: {} , requestRefNum: {} ",
                            item.getRefNum(),
                            item.getRequestRefNum()
                    )
            );
        });
    }
}
