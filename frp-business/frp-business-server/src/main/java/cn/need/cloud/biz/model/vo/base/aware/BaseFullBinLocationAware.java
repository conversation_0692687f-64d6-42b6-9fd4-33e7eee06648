package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseBinLocationFullVO;
import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;

import java.util.Optional;

/**
 * 库位日志感知接口
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@FunctionalInterface
public interface BaseFullBinLocationAware {

    /**
     * 产品id
     *
     * @return 库位id
     */
    Long getBinLocationId();

    /**
     * 产品日志返回实体实现
     *
     * @return 产品日志
     */
    default BaseBinLocationFullVO getBaseFullBinLocationVO() {
        return Optional.ofNullable(this.getBinLocationId())
                .map(BinLocationCacheUtil::getById)
                .map(binLocationCache -> BeanUtil.copyNew(binLocationCache, BaseBinLocationFullVO.class))
                .orElse(null);
    }
}
