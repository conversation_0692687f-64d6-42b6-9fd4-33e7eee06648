package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.util.Allocation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 基础发货对象，拥有相同 拣货数量、发货数量、乐观锁版本号 字段
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTC 拣货基础 对象")
public class BaseShippedVO implements Allocation {

    /**
     * 发货对象id
     */
    @Schema(description = "拣货对象id")
    private Long id;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 发货前的数量
     */
    @Schema(description = "发货前的数量")
    private Integer shippedBeforeQty;

    /**
     * 发货数量
     */
    @Schema(description = "发货数量")
    private Integer shippedQty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    public int getChangeShippedQty() {
        return shippedQty - shippedBeforeQty;
    }

    @Override
    public int total() {
        return this.pickedQty;
    }

    @Override
    public void allocation(int allocationQty) {
        this.shippedQty = allocationQty;
    }

    @Override
    public int allocated() {
        return this.shippedQty;
    }
}