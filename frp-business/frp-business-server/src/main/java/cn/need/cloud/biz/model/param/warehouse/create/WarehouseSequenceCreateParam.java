package cn.need.cloud.biz.model.param.warehouse.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 生成仓库唯一refNum create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "生成仓库唯一refNum vo对象")
public class WarehouseSequenceCreateParam implements Serializable {


    /**
     * warehouseRefNum
     */
    @Schema(description = "warehouseRefNum")
    private String warehouseRefNum;

    /**
     * sequenceType
     */
    @Schema(description = "sequenceType")
    private String sequenceType;

    /**
     * toDay
     */
    @Schema(description = "toDay")
    private String toDay;

    /**
     * code
     */
    @Schema(description = "code")
    private String code;

    /**
     * sequenceId
     */
    @Schema(description = "sequenceId")
    private Long sequenceId;


}