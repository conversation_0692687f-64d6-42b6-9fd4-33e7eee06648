package cn.need.cloud.biz.service.product.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.product.ProductMultiboxConverter;
import cn.need.cloud.biz.mapper.product.ProductMultiboxMapper;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.entity.product.ProductMultiboxDetail;
import cn.need.cloud.biz.model.param.product.create.ProductMultiboxCreateParam;
import cn.need.cloud.biz.model.param.product.update.MultiboxDetailParam;
import cn.need.cloud.biz.model.param.product.update.ProductMultiboxCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductMultiboxQuery;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxDetailVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxListVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxWithDetailVO;
import cn.need.cloud.biz.model.vo.product.page.ProductMultiboxPageVO;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.product.*;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品多箱 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductMultiboxServiceImpl extends SuperServiceImpl<ProductMultiboxMapper, ProductMultibox> implements ProductMultiboxService {

    @Resource
    private ProductComponentService productComponentService;

    @Resource
    private ProductMultiboxDetailService productMultiboxDetailService;

    @Resource
    private ProductService productService;

    @Resource
    private ProductScanService productScanService;

    @Resource
    private OtcPrepWorkorderService otcPrepWorkorderService;

    @Resource
    private ProductHazmatService productHazmatService;


    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(ProductMultiboxCreateParam createParam) {
        // 检查传入产品多箱参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取产品多箱转换器实例，用于将产品多箱参数对象转换为实体对象
        ProductMultiboxConverter converter = Converters.get(ProductMultiboxConverter.class);

        // 将产品多箱参数对象转换为实体对象并初始化
        ProductMultibox entity = initProductMultibox(converter.toEntity(createParam));

        // 插入产品多箱实体对象到数据库
        super.insert(entity);

        // 返回产品多箱ID
        return entity.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createOrUpdate(List<ProductMultiboxCreateOrUpdateParam> paramList) {
        // 检查传入产品多箱参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(paramList) || paramList.size() < 2) {
            // throw new BusinessException("Parameter is not valid");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "paramList", "Must contain at least 2 items"));
        }
        // 多箱upc转成大写
        paramList.forEach(o -> o.setUpc(o.getUpc().toUpperCase()));
        //基本参数校验
        Long productId = paramList.get(NumberUtils.INTEGER_ZERO).getProductId();
        Long transactionPartnerId = ProductCacheUtil.getById(productId).getTransactionPartnerId();
        if (ObjectUtil.isEmpty(productId) || ObjectUtil.isEmpty(transactionPartnerId)) {
            // // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        //取多箱中的产品集ids 校验是否在同一供应商下
        List<Long> productIdList = paramList.stream().map(ProductMultiboxCreateOrUpdateParam::getDetailList).flatMap(List::stream).map(MultiboxDetailParam::getProductId).distinct().toList();
        if (!productService.isAllProductInPartnerId(transactionPartnerId, productIdList)) {
            // throw new BusinessException("Product must in same Partner");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "All products must belong to the same partner"));
        }
        // 校验产品是否为group
        if (productService.isGroup(productId)) {
            // throw new BusinessException("GroupProduct cannot be MultiBox");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Group products cannot be configured as MultiBox"));
        }
        if (!productService.isAssembly(productId)) {
            // throw new BusinessException("Only AssemblyProduct can be MultiBox");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Only assembly products can be configured as MultiBox"));
        }
        //校验lineNum
        List<Integer> lineNums = paramList.stream().map(ProductMultiboxCreateOrUpdateParam::getLineNum).toList();
        if (new HashSet<>(lineNums).size() != lineNums.size()) {
            // throw new BusinessException("LineNum Duplicate Error");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Duplicate line numbers found in MultiBox configuration"));
        }
        //校验detailLineNum
        paramList.forEach(o -> {
            List<Integer> detailLineNums = o.getDetailList().stream().map(MultiboxDetailParam::getLineNum).toList();
            if (new HashSet<>(detailLineNums).size() != detailLineNums.size()) {
                // throw new BusinessException("DetailLineNum Duplicate Error");
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Duplicate detail line numbers found in MultiBox configuration"));
            }
        });

        List<ProductComponent> componentList = productComponentService.getListByAssemblyProductId(productId);

        if (ObjectUtil.isEmpty(componentList)) {
            // throw new BusinessException("productId is not valid");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "productId", "No components found for this product"));
        }
        if (paramList.stream().anyMatch(param -> ObjectUtil.isEmpty(param.getDetailList()))) {
            // throw new BusinessException("MultiBoxDetail is not valid");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "detailList", "MultiBox details cannot be empty"));
        }
        //合并 多箱中的产品信息
        Map<Long, Integer> productMultiboxMap = paramList.stream().flatMap(param -> param.getDetailList()
                .stream()).collect(Collectors.groupingBy(MultiboxDetailParam::getProductId, Collectors.summingInt(MultiboxDetailParam::getQty)));

        //合并Component中产品信息
        Map<Long, Integer> productComponentMap = componentList.stream().collect(Collectors.groupingBy(ProductComponent::getComponentProductId, Collectors.summingInt(ProductComponent::getComponentQty)));
        //校验多箱数量匹配
        if (!productComponentMap.entrySet().equals(productMultiboxMap.entrySet())) {
            // throw new BusinessException("ProductMultiBox quantity is not valid");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox product quantities must match component quantities"));
        }
        //获取原数据集
        List<ProductMultibox> oldList = getProductMultiboxListByProductId(productId);
        //转换成id集
        List<Long> idList = oldList.stream().map(ProductMultibox::getId).toList();
        //清空原数据
        if (ObjectUtil.isNotEmpty(idList)) {
            removeByIds(idList);
            productMultiboxDetailService.deleteByProductMultiboxIds(idList);
        }
        //校验本身入参upc
        Set<String> upcList = paramList.stream().map(ProductMultiboxCreateOrUpdateParam::getUpc).collect(Collectors.toSet());
        if (upcList.size() != paramList.size()) {
            // throw new BusinessException("Param Upc Duplicate Error");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Duplicate UPC codes found in MultiBox configuration"));
        }
        //根据Upc集 校验Upc唯一
        productService.checkUniqueUpcByList(upcList, transactionPartnerId);

        //获取versionInt版本号
        Integer versionInt = getVersionInt(productId, oldList);

        // 获取产品多箱转换器实例，用于将产品多箱参数对象转换为实体对象
        ProductMultiboxConverter converter = Converters.get(ProductMultiboxConverter.class);

        //收集新增多箱并参数映射
        Map<ProductMultibox, ProductMultiboxCreateOrUpdateParam> map = paramList.stream().map(param -> {
            // 填充多箱实体对象
            ProductMultibox entity = converter.toEntity(param);
            long id = IdWorker.getId();
            entity.setId(id);
            entity.setMultiboxVersionInt(versionInt);
            entity.setTransactionPartnerId(transactionPartnerId);
            return new AbstractMap.SimpleEntry<>(entity, param);
        }).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
        ));
        //处理成id 对应参数map
        Map<Long, ProductMultiboxCreateOrUpdateParam> idMap = map.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().getId(),
                        Map.Entry::getValue
                ));


        //批量新增多箱实体
        super.insertBatch(map.keySet());
        //批量新增或更新产品扫描中数据
        productScanService.insertOrUpdateByMultibox(map.keySet(), productId);

        //批量新增多箱详情
        productMultiboxDetailService.insertBatchMultiboxDetailByMultiboxParam(idMap);

        //更新产品多箱标记
        if (ObjectUtil.isEmpty(idList)) {
            productService.updateMultiboxFlagById(productId, Boolean.TRUE);
        }

        // 执行更新产品多箱操作
        return map.size();

    }

    @Override
    public List<ProductMultiboxPageVO> listByQuery(ProductMultiboxQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ProductMultiboxPageVO> pageByQuery(PageSearch<ProductMultiboxQuery> search) {
        Page<ProductMultibox> page = Conditions.page(search, entityClass);
        List<ProductMultiboxPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ProductMultiboxVO detailById(Long id) {
        ProductMultibox entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // // throw new BusinessException("id: " + id + " not found in ProductMultiBox");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "ProductMultiBox", id));
        }
        return buildProductMultiboxVO(entity);
    }

    @Override
    public List<ProductMultiboxListVO> listByProductId(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            // // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        return mapper.listByProductId(productId);
    }

    @Override
    public List<ProductMultibox> getProductMultiboxListByProductId(Long productId) {
        return lambdaQuery().eq(ProductMultibox::getProductId, productId).list();
    }

    @Override
    public ProductMultibox getFirstProductMultiboxByProductId(Long productId) {
        return lambdaQuery()
                .eq(ProductMultibox::getProductId, productId)
                .orderByAsc(IdModel::getId)
                .last("LIMIT 1")
                .one();
    }

    @Override
    public Integer deleteByProductId(Long productId, String deletedNote) {
        LambdaUpdateWrapper<ProductMultibox> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProductMultibox::getRemoveFlag, DataState.ENABLED)
                .set(ObjectUtil.isNotEmpty(deletedNote), ProductMultibox::getDeletedNote, deletedNote)
                .eq(ProductMultibox::getProductId, productId);
        return mapper.update(wrapper);
    }

    @Override
    public List<ProductMultibox> getListByPartnerId(Long partnerId) {
        return lambdaQuery().eq(ProductMultibox::getTransactionPartnerId, partnerId).list();
    }

    @Override
    public List<ProductMultiboxWithDetailVO> multiBoxByWorkOrderDetailId(Long otcWorkOrderDetailId) {
        // 根据Prep工单明细ID查询工单
        OtcPrepWorkorder prepWorkOrder = otcPrepWorkorderService.multiBoxPrepByWorkOrderDetailId(otcWorkOrderDetailId);
        if (ObjectUtil.isNull(prepWorkOrder)) {
            return Collections.emptyList();
        }
        // 获取多盒
        List<ProductMultibox> multiboxList
                = this.getListByProductAndVersionInt(prepWorkOrder.getProductId(), prepWorkOrder.getPrepWorkorderVersionInt());

        List<Long> multiBoxIdList = StreamUtils.distinctMap(multiboxList, IdModel::getId);
        // 获取多箱详情
        Map<Long, List<ProductMultiboxDetail>> detailMap = productMultiboxDetailService.groupByMultiBoxIdList(multiBoxIdList);

        return multiboxList.stream()
                .map(obj -> {
                    // 设置详情
                    ProductMultiboxWithDetailVO multibox = BeanUtil.copyNew(obj, ProductMultiboxWithDetailVO.class);
                    multibox.setDetailList(BeanUtil.copyNew(detailMap.getOrDefault(obj.getId(), Collections.emptyList()),
                            ProductMultiboxDetailVO.class)
                    );
                    // 填充产品
                    ProductCacheUtil.filledProduct(multibox.getDetailList());
                    return multibox;
                })
                .toList();
    }

    @Override
    public List<ProductMultibox> getListByProductAndVersionInt(Long productId, int versionInt) {
        return mapper.findListByProductAndVersionInt(productId, versionInt);
    }

    @Override
    public ProductMultibox findOneByProductIdAndVersionIntAndUpc(Long productId, Integer multiboxVersionInt, String upc) {

        return mapper.findOneByProductIdAndVersionIntAndUpcAndLineNum(productId, multiboxVersionInt, upc);
    }

    @Override
    public List<ProductMultibox> listByProductIdAndVersionIntList(List<Long> multiBoxProductIdList, List<Integer> multiBoxVersionIntList) {
        if (ObjectUtil.isEmpty(multiBoxVersionIntList) || ObjectUtil.isEmpty(multiBoxProductIdList)) {
            return Collections.emptyList();
        }
        return mapper.findListByProductAndVersionIntList(multiBoxProductIdList, multiBoxVersionIntList);
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 构建产品多箱VO对象
     *
     * @param entity 产品多箱对象
     * @return 返回包含详细信息的产品多箱VO对象
     */
    private ProductMultiboxVO buildProductMultiboxVO(ProductMultibox entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品多箱VO对象
        return Converters.get(ProductMultiboxConverter.class).toVO(entity);
    }

    /**
     * 获取版本号
     *
     * @param productId 产品id
     * @param list      产品多箱列表
     * @return 版本号
     */
    private Integer getVersionInt(Long productId, List<ProductMultibox> list) {
        //不为空 直接取值
        if (ObjectUtil.isNotEmpty(list)) {
            return list.stream().map(ProductMultibox::getMultiboxVersionInt).max(Integer::compareTo)
                    .map(integer -> integer + NumberUtils.INTEGER_ONE).orElse(NumberUtils.INTEGER_ZERO);
        }
        //为空 从删除数据中取老版本号最大值
        Integer oldVersionInt = mapper.getOldVersionInt(productId);
        if (Objects.isNull(oldVersionInt)) {
            //最新 从0开始
            return NumberUtils.INTEGER_ZERO;
        }
        return oldVersionInt + NumberUtils.INTEGER_ONE;
    }

    /**
     * 初始化产品多箱对象
     * 此方法用于设置产品多箱对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品多箱对象，不应为空
     * @return 返回初始化后的产品多箱
     * @throws BusinessException 如果传入的产品多箱为空，则抛出此异常
     */
    private ProductMultibox initProductMultibox(ProductMultibox entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ProductMultiBox"));
        }


        // 返回初始化后的配置对象
        return entity;
    }
}
