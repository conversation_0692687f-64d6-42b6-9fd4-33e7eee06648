package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.hutool.core.lang.Pair;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.inbound.BinLocationDetailContextBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipPutAwayQuery;
import cn.need.cloud.biz.model.vo.base.BasePutAwayVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderDetailPutAwayVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtcPrepWorkorderPutAwayVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipDetailPutAwayVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipPutAwayContextVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPutAwayLogVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipPutAwayService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.workorder.*;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * OTC预提货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@Slf4j
@AllArgsConstructor
public class OtcPrepPickingSlipPutAwayServiceImpl implements OtcPrepPickingSlipPutAwayService {


    private final OtcPrepPickingSlipService otcPrepPickingSlipService;

    private final ProductVersionService productVersionService;

    private final BinLocationDetailService binLocationDetailService;

    private final BinLocationDetailLockedService binLocationDetailLockedService;

    private final InventoryReserveService inventoryReserveService;

    private final OtcPrepPickingSlipDetailService otcPrepPickingSlipDetailService;

    private final OtcPrepWorkorderService otcPrepWorkorderService;

    private final OtcWorkorderService otcWorkorderService;

    private final OtcWorkorderDetailService otcWorkorderDetailService;

    private final OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;

    private final OtcPrepWorkorderBinLocationService otcPrepWorkorderBinLocationService;

    private final PickingSlipService pickingSlipService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcPrepPickingSlipVO putAway(OtcPrepPickingSlipPutAwayQuery query) {
        OtcPrepPickingSlip prepPickingSlip = otcPrepPickingSlipService.getById(query.getOtcPrepPickingSlipId());
        Validate.notNull(prepPickingSlip, "id: {} not found in Prep Picking Slip", query.getOtcPrepPickingSlipId());
        // 已经上架
        Validate.isTrue(!Objects.equals(OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus(), prepPickingSlip.getPrepPickingSlipStatus()),
                "PickingsSlip: {} current is {} Status", prepPickingSlip.getRefNum(), prepPickingSlip.getPrepPickingSlipStatus()
        );

        ProcessType.checkNormalAvailability(prepPickingSlip.getProcessType(), prepPickingSlip.getRefNum(), "putAway");

        int putAwayQty = ObjectUtil.nullToDefault(prepPickingSlip.getPutawayQty(), 0);
        // 上架库存不足
        int currentPutAway = putAwayQty + query.getQty();
        int canPutAwayQty = ObjectUtil.nullToDefault(prepPickingSlip.getAllocatePutawayQty(), 0);
        Validate.isTrue(canPutAwayQty >= query.getQty(), "PutAway qty is not enough");
        // 上架数量不能大于总数
        Validate.isTrue(currentPutAway <= prepPickingSlip.getQty(), "PutAway qty is not enough");

        boolean hasPutAway = currentPutAway == prepPickingSlip.getQty();
        // 设置状态
        prepPickingSlip.setPrepPickingSlipStatus(hasPutAway
                ? OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus()
                : prepPickingSlip.getPrepPickingSlipStatus()
        );
        // 已经上架数量
        prepPickingSlip.setPutawayQty(currentPutAway);

        OtcPrepPickingSlipPutAwayContextVO context = new OtcPrepPickingSlipPutAwayContextVO();
        context.setQuery(query);
        context.setPrepPickingSlip(prepPickingSlip);

        // Prep工单上架处理
        this.putAwayPrepWorkOrderList(context);

        // 更新Prep拣货单
        otcPrepPickingSlipService.update(prepPickingSlip);

        // 上架库位
        this.putAwayVirtualBinLocation(context);

        // 更新并设置工单状态
        otcPrepWorkorderService.updateAndSetWorkOrderProcessed(context);

        // 锁定Workorder库位
        this.buildBinLocationLockedList(context);

        // 释放预定锁
        this.putAwayReserveLocked(context);

        // 更新Prep工单详情
        List<OtcPrepPickingSlipDetail> prepPickingSlipDetailList = context.getPrepPickingSlipDetailList();
        Validate.isTrue(otcPrepPickingSlipDetailService.updateBatch(prepPickingSlipDetailList) == prepPickingSlipDetailList.size(),
                "Update PrepPickingSlipDetail Fail"
        );

        // 更新Prep工单详情
        List<OtcPrepWorkorderDetail> putAwayPrepWorkorderDetailList = context.getPutAwayPrepWorkorderDetailList();
        Validate.isTrue(otcPrepWorkorderDetailService.updateBatch(putAwayPrepWorkorderDetailList) == putAwayPrepWorkorderDetailList.size(),
                "Update PrepWorkorderDetail putawayQty Fail"
        );

        // 日志记录
        this.recordPutAwayLog(context);

        return otcPrepPickingSlipService.buildOtcPrepPickingSlipVO(prepPickingSlip);
    }

    /**
     * 上架虚拟库位
     *
     * @param context 上下文
     */
    private void putAwayVirtualBinLocation(OtcPrepPickingSlipPutAwayContextVO context) {
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 获取产品版本
        ProductVersion productVersion = productVersionService.findLatestProductVersionByProductId(prepPickingSlip.getProductId());

        OtcPrepPickingSlipPutAwayQuery query = context.getQuery();
        // 构建上架参数
        BinLocationDetailContextBO putAwayContext = new BinLocationDetailContextBO();
        putAwayContext.setBinLocationId(prepPickingSlip.getBinLocationId());
        putAwayContext.setProductId(productVersion.getProductId());
        putAwayContext.setProductVersionId(productVersion.getId());
        putAwayContext.setRefNumModel(prepPickingSlip);
        putAwayContext.setInStockQty(query.getQty());

        context.setPutAwayBinLocationDetail(binLocationDetailService.putAwayVirtual(putAwayContext));
    }

    /**
     * 释放预定锁
     *
     * @param context 上下文
     */
    private void putAwayReserveLocked(OtcPrepPickingSlipPutAwayContextVO context) {
        List<OtcPrepWorkorder> prepWorkorderList = context.getPutAwayPrepWorkOrderList();

        // 获取工单详情
        List<OtcPrepWorkorderPutAwayVO> prepWorkorderPutawayList = context.getPrepWorkorderPutawayList();
        List<Long> workOrderDetailIdList = StreamUtils.distinctMap(prepWorkorderPutawayList, OtcPrepWorkorderPutAwayVO::getOtcWorkorderDetailId);
        List<OtcWorkorderDetail> workorderDetailList = otcWorkorderDetailService.listByIds(workOrderDetailIdList);

        // 不存在释放锁的工单详情
        List<OtcWorkorderDetail> notReserveWorkorderDetailList = workorderDetailList.stream()
                .filter(obj -> ObjectUtil.isNull(obj.getInventoryReserveId()))
                .toList();

        // 存在没有锁的详情
        if (ObjectUtil.isNotEmpty(notReserveWorkorderDetailList)) {
            List<Long> workOrderIdList = StreamUtils.distinctMap(prepWorkorderList, OtcPrepWorkorder::getOtcWorkorderId);
            Map<Long, OtcWorkorder> workorderMap = StreamUtils.toMap(otcWorkorderService.listByIds(workOrderIdList), IdModel::getId);
            String workorderMessage = notReserveWorkorderDetailList.stream()
                    .map(obj -> Optional.ofNullable(workorderMap.get(obj.getOtcWorkorderId()))
                            .map(OtcWorkorder::getRefNum)
                            .orElse(String.valueOf(obj.getLineNum())))
                    .collect(Collectors.joining(StringPool.COMMA));

            throw new BusinessException(StringUtil.format("Workorder [{}] InventoryReserveLock lack", workorderMessage));
        }

        Map<Long, Integer> detailPutAwayQtyMap = prepWorkorderPutawayList.stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorderPutAwayVO::getOtcWorkorderDetailId,
                        Collectors.mapping(BasePutAwayVO::getChangePutAwayQty, Collectors.summingInt(Integer::intValue)))
                );

        // 工单详情增加ReserveQty
        List<InventoryReleaseLockedParam> reserveInventoryReleaseList = workorderDetailList
                .stream()
                .filter(obj -> detailPutAwayQtyMap.containsKey(obj.getId()))
                .map(obj -> {
                    InventoryReleaseLockedParam lockedParam = new InventoryReleaseLockedParam();
                    lockedParam.setId(obj.getInventoryReserveId());
                    Integer detailPutAwayQty = detailPutAwayQtyMap.getOrDefault(obj.getId(), 0);
                    lockedParam.setQty(detailPutAwayQty);

                    obj.setFinishReserveQty(obj.getFinishReserveQty() + detailPutAwayQty);
                    Validate.isTrue(obj.getFinishReserveQty() <= obj.getReserveQty(),
                            "Workorder reserveQty [{}] must less than workorder qty [{}]",
                            obj.getFinishReserveQty(), obj.getReserveQty()
                    );
                    return lockedParam;
                })
                .toList();
        // 更新预定数量
        Validate.isTrue(otcWorkorderDetailService.updateBatch(workorderDetailList) == workorderDetailList.size(),
                "Update Workorder reserveQty fail"
        );
        // 更新预定
        inventoryReserveService.putAwayReserveInventory(reserveInventoryReleaseList);
    }

    /**
     * 上架Prep工单
     *
     * @param context 上下文
     */
    private void putAwayPrepWorkOrderList(OtcPrepPickingSlipPutAwayContextVO context) {
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();

        // 处理工单
        List<OtcPrepWorkorder> prepWorkorderList = otcPrepWorkorderService.putAwayListByPrepPickingSlipId(prepPickingSlip.getId());
        // Prep工单详情映射
        Map<Long, List<OtcPrepWorkorderDetail>> prepDetailGroupByWkMap
                = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(StreamUtils.distinctMap(prepWorkorderList, IdModel::getId));

        // 全部工单详情
        context.setPrepWorkOrderList(prepWorkorderList);
        context.setPrepDetailGroupByWkMap(prepDetailGroupByWkMap);

        // 分配工单上架数量
        List<OtcPrepWorkorderPutAwayVO> workorderPutawayList = this.allocationWorkOrder(context);

        // 分配工单详情上架数量
        List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList = this.allocationWkDetail(context, workorderPutawayList);

        // 分配拣货单详情上架数量
        this.allocationPsDetail(context, wkDetailPutAwayList);

        // 分配锁释放数量
        this.allocationBinLocationDetailLocked(context, wkDetailPutAwayList);

        // 赋值上架库位
        context.getPutAwayPrepWorkOrderList().forEach(obj -> obj.setBinLocationId(prepPickingSlip.getBinLocationId()));
    }

    /**
     * 构建工单仓库锁
     *
     * @param context 上下文
     */
    private void buildBinLocationLockedList(OtcPrepPickingSlipPutAwayContextVO context) {
        // 工单详情
        List<Long> workorderDetailIdList = context.getPrepWorkorderPutawayList().stream()
                .map(OtcPrepWorkorderPutAwayVO::getOtcWorkorderDetailId)
                .toList();
        List<OtcWorkorderDetail> workorderDetails = otcWorkorderDetailService.listByIds(workorderDetailIdList);

        // 工单
        List<Long> workorderIdList = StreamUtils.distinctMap(workorderDetails, OtcWorkorderDetail::getOtcWorkorderId);
        Map<Long, OtcWorkorder> workorderMap = StreamUtils.toMap(otcWorkorderService.listByIds(workorderIdList), IdModel::getId);

        // 上架的虚拟库位
        BinLocationDetail putAwayBinLocationDetail = context.getPutAwayBinLocationDetail();
        // 工单详情上架数量信息
        Map<Long, Integer> workorderDetailPutAwayQtyMap = context.getPrepWorkorderPutawayList().stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorderPutAwayVO::getOtcWorkorderDetailId,
                        Collectors.mapping(BasePutAwayVO::getChangePutAwayQty, Collectors.summingInt(Integer::intValue))));

        // 锁Workorder BinLocationLocked
        List<BinLocationDetailLocked> lockedList = workorderDetails.stream()
                .filter(detail -> workorderDetailPutAwayQtyMap.containsKey(detail.getId()))
                .map(detail -> {
                    // 构建锁定 库位详情实体对象
                    BinLocationDetailLocked locked = new BinLocationDetailLocked();
                    locked.setId(IdWorker.getId());
                    locked.setBinLocationDetailId(putAwayBinLocationDetail.getId());
                    locked.setBinLocationId(putAwayBinLocationDetail.getBinLocationId());
                    locked.setProductId(putAwayBinLocationDetail.getProductId());
                    locked.setProductVersionId(putAwayBinLocationDetail.getProductVersionId());
                    locked.setQty(workorderDetailPutAwayQtyMap.getOrDefault(detail.getId(), 0));
                    locked.setFinishQty(0);
                    locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
                    locked.setRefTableId(detail.getId());
                    locked.setRefTableName(OtcWorkorderDetail.class.getSimpleName());
                    locked.setRefTableRefNum(String.valueOf(detail.getLineNum()));
                    locked.setRefTableShowName(OtcWorkorder.class.getSimpleName());
                    locked.setRefTableShowRefNum(Optional.ofNullable(workorderMap.get(detail.getOtcWorkorderId()))
                            .map(OtcWorkorder::getRefNum)
                            .orElse(String.valueOf(detail.getOtcWorkorderId())));
                    return locked;
                })
                .toList();

        binLocationDetailLockedService.insertBatch(lockedList);
    }

    /**
     * 分配拣货单详情上架数量
     *
     * @param context             上下文
     * @param wkDetailPutAwayList 工单上架信息
     */
    private void allocationPsDetail(OtcPrepPickingSlipPutAwayContextVO context, List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList) {

        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // Prep拣货单详情映射
        Map<Long, List<OtcPrepPickingSlipDetail>> prepDetailGroupByPsMap
                = otcPrepPickingSlipDetailService.groupByPrepPickingSlipId(prepPickingSlip.getId());

        List<OtcPrepPickingSlipDetail> psDetailList = prepDetailGroupByPsMap.values()
                .stream()
                .flatMap(Collection::stream)
                .toList();
        // 产品上架分组
        Map<Long, Integer> productPutAwayMap = wkDetailPutAwayList.stream()
                .collect(Collectors.groupingBy(PrepWorkorderDetailPutAwayVO::getProductId,
                        Collectors.summingInt(BasePutAwayVO::getChangePutAwayQty))
                );

        // 按照产品分组分配
        Map<Long, List<OtcPrepPickingSlipDetail>> groupByProductMap = StreamUtils.groupBy(psDetailList, OtcPrepPickingSlipDetail::getProductId);
        Map<Long, OtcPrepPickingSlipDetail> psDetailMap = StreamUtils.toMap(psDetailList, IdModel::getId);
        // 拣货单详情分配
        List<OtcPrepPickingSlipDetail> allocationPsDetailList = productPutAwayMap.entrySet()
                .stream()
                .map(entry -> {
                    List<OtcPickingSlipDetailPutAwayVO> psd = BeanUtil.copyNew(groupByProductMap.get(entry.getKey()), OtcPickingSlipDetailPutAwayVO.class);
                    AllocationUtil.checkAndAllocationPutAwayQty(psd, entry.getValue());
                    return psd;
                })
                .flatMap(Collection::stream)
                // 分配了上架数量的
                .filter(putAway -> putAway.getChangePutAwayQty() > 0)
                // 赋值拣货单详情 上架数量
                .map(putAway -> {
                    OtcPrepPickingSlipDetail detail = psDetailMap.get(putAway.getId());
                    detail.setPutawayQty(putAway.getPutawayQty());
                    return detail;
                })
                .toList();

        // 绑定到上下文
        context.setPrepPickingSlipDetailList(allocationPsDetailList);
    }

    /**
     * 分配工单详情上架数量
     *
     * @param workorderPutawayList 工单上架信息
     * @return /
     */
    private List<PrepWorkorderDetailPutAwayVO> allocationWkDetail(OtcPrepPickingSlipPutAwayContextVO context, List<OtcPrepWorkorderPutAwayVO> workorderPutawayList) {

        Map<Long, List<OtcPrepWorkorderDetail>> prepDetailGroupByWkMap = context.getPrepDetailGroupByWkMap();
        // 工单详情分配
        List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList = workorderPutawayList.stream()
                .map(wk -> BeanUtil.copyNew(prepDetailGroupByWkMap.get(wk.getId()), PrepWorkorderDetailPutAwayVO.class)
                        .stream()
                        .peek(putAway -> AllocationUtil.checkAndAllocationPutAwayQty(Collections.singletonList(putAway),
                                // detail.putAwayQty = detail.qty * header.putAwayQty / header.qty
                                putAway.getQty() * wk.getChangePutAwayQty() / wk.getQty()))
                        .toList())
                .flatMap(Collection::stream)
                .toList();
        // 工单详情映射
        Map<Long, OtcPrepWorkorderDetail> wkDetailMap = prepDetailGroupByWkMap.values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        // 上架数量赋值
        List<Pair<OtcPrepWorkorderDetail, PrepWorkorderDetailPutAwayVO>> pairList = wkDetailPutAwayList.stream()
                .filter(wkDetail -> wkDetail.getChangePutAwayQty() > 0)
                // 赋值工单详情上架数量
                .map(wkDetail -> {
                    OtcPrepWorkorderDetail detail = wkDetailMap.get(wkDetail.getId());
                    detail.setPutawayQty(wkDetail.getPutawayQty());
                    return Pair.of(detail, wkDetail);
                })
                .toList();

        // 绑定到上下文
        context.setPutAwayPrepWorkorderDetailList(StreamUtils.distinctMap(pairList, Pair::getKey));

        // 返回工单详情上架信息
        return pairList.stream()
                .map(Pair::getValue)
                // Parent 不参与后续拣货
                .filter(obj -> !Objects.equals(obj.getPrepWorkorderDetailType(), PrepWorkOrderDetailTypeEnum.PREPCONVERT.getStatus()))
                .toList();
    }

    /**
     * 分配工单上架数量
     *
     * @param context 上下文
     * @return /
     */
    private List<OtcPrepWorkorderPutAwayVO> allocationWorkOrder(OtcPrepPickingSlipPutAwayContextVO context) {
        List<OtcPrepWorkorder> prepWorkorderList = context.getPrepWorkOrderList();
        Map<Long, List<OtcPrepWorkorderDetail>> prepDetailGroupByWkMap = context.getPrepDetailGroupByWkMap();
        // 工单上架信息
        List<OtcPrepWorkorderPutAwayVO> workorderPutawayList = prepWorkorderList.stream()
                .map(obj -> {
                    OtcPrepWorkorderPutAwayVO putAway = BeanUtil.copyNew(obj, OtcPrepWorkorderPutAwayVO.class);
                    int workorderPickedQty = prepDetailGroupByWkMap.getOrDefault(obj.getId(), Collections.emptyList())
                            .stream()
                            // header.pickedQty = header.qty * detail.pickedQty / detail.qty
                            .mapToInt(detail -> obj.getQty() * detail.getPickedQty() / detail.getQty())
                            .min()
                            .orElse(0);
                    // 工单没有拣货数量，为了符合通用计算逻辑
                    putAway.setPickedQty(workorderPickedQty);
                    return putAway;
                })
                .toList();
        // 工单分配
        AllocationUtil.checkAndAllocationPutAwayQty(workorderPutawayList, context.getQuery().getQty());
        // 工单映射
        Map<Long, OtcPrepWorkorder> prepWorkorderMap = StreamUtils.toMap(prepWorkorderList, IdModel::getId);

        // 赋值上架数量
        List<Pair<OtcPrepWorkorder, OtcPrepWorkorderPutAwayVO>> workorderPairList = workorderPutawayList.stream()
                .filter(wk -> wk.getChangePutAwayQty() > 0)
                // 赋值工单上架数量
                .map(wk -> {
                    OtcPrepWorkorder prepWorkorder = prepWorkorderMap.get(wk.getId());
                    prepWorkorder.setPutawayQty(wk.getPutawayQty());
                    prepWorkorder.setPrepWorkorderStatus(Objects.equals(prepWorkorder.getQty(), prepWorkorder.getPutawayQty())
                            // Prep工单 Processed
                            ? OtcPrepWorkorderStatusEnum.PROCESSED.getStatus()
                            : prepWorkorder.getPrepWorkorderStatus());
                    return Pair.of(prepWorkorder, wk);
                })
                .toList();

        // 绑定到上下文中
        context.setPutAwayPrepWorkOrderList(StreamUtils.distinctMap(workorderPairList, Pair::getKey));
        context.setPrepWorkorderPutawayList(StreamUtils.distinctMap(workorderPairList, Pair::getValue));

        // 返回工单上架信息
        return StreamUtils.distinctMap(workorderPairList, Pair::getValue);
    }

    /**
     * 分配库位锁，释放，扣减
     *
     * @param context             上下文
     * @param wkDetailPutAwayList 工单上架信息
     */
    private void allocationBinLocationDetailLocked(OtcPrepPickingSlipPutAwayContextVO context,
                                                   List<PrepWorkorderDetailPutAwayVO> wkDetailPutAwayList) {
        // 工单详情分配仓储信息
        List<OtcPrepWorkorderBinLocation> prepWorkorderBinLocationList = otcPrepWorkorderBinLocationService.listByOtcWorkorderDetailIdList(
                StreamUtils.distinctMap(wkDetailPutAwayList, PrepWorkorderDetailPutAwayVO::getId)
        );

        log.info("工单上架：{}，释放: {}", wkDetailPutAwayList, prepWorkorderBinLocationList);

        // LineNum映射
        Map<Long, OtcPrepPickingSlipDetail> detailMap = context.getPrepPickingSlipDetailList()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        // 上架释放ReadyToGo锁
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();

        BiConsumer<Long, BinLocationDetailLockedChangeBO> longBinLocationDetailLockedChangeBOBiConsumer = (detailId, change) -> {
            change.setChangeType(StringUtil.format("{} {}",
                    prepPickingSlip.getPrepPickingSlipType(),
                    BinLocationLogEnum.OTC_PREP_PUT_AWAY.getStatus())
            );
            String lineNum = Optional.ofNullable(detailMap.get(detailId))
                    .map(OtcPrepPickingSlipDetail::getLineNum)
                    .map(String::valueOf)
                    .orElse(StringPool.EMPTY);

            // 关联信息
            RefTableBO refTable = new RefTableBO();
            refTable.setRefTableId(detailId);
            refTable.setRefTableRefNum(lineNum);
            refTable.setRefTableName(OtcPrepPickingSlipDetail.class.getSimpleName());

            refTable.setRefTableShowRefNum(prepPickingSlip.getRefNum());
            refTable.setRefTableShowName(OtcPrepPickingSlip.class.getSimpleName());
            change.setRefTable(refTable);
        };

        pickingSlipService.putAwayReleaseLocked(wkDetailPutAwayList,
                prepWorkorderBinLocationList,
                // 根据Prep工单详情分组释放
                OtcPrepWorkorderBinLocation::getOtcPrepWorkorderDetailId,
                // 库位日志获取函数
                longBinLocationDetailLockedChangeBOBiConsumer
        );
    }

    /**
     * 记录上架日志
     *
     * @param context 上下文
     */
    private void recordPutAwayLog(OtcPrepPickingSlipPutAwayContextVO context) {
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        int putAwayQty = context.getQuery().getQty();

        // Prep拣货单: PutAway 日志
        OtcPutAwayLogVO putAwayLog = new OtcPutAwayLogVO(prepPickingSlip.getProductId(), prepPickingSlip.getBinLocationId(), putAwayQty);

        // PutAway 上架 库位 产品 数量
        OtcPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus(),
                JsonUtil.toJson(putAwayLog), null, BaseTypeLogEnum.OPERATION.getType()
        );

        // PutAway Completed 上架完成
        if (Objects.equals(prepPickingSlip.getPrepPickingSlipStatus(), OtcPrepPickingSlipStatusEnum.PUT_AWAY.getStatus())) {
            OtcPrepPickingSlipAuditLogHelper.recordLog(prepPickingSlip, PickingSlipLogConstant.PUT_AWAY_COMPLETED_STATUS, null);
        }

        // Prep工单: Processing -> Processed 日志
        context.getPutAwayPrepWorkOrderList()
                .stream()
                .filter(obj -> Objects.equals(obj.getPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.PROCESSED.getStatus()))
                .forEach(OtcPrepWorkorderAuditLogHelper::recordLog);

        // 工单: workorder_prep_status Processed
        OtcWorkOrderAuditLogHelper.recordLog(context.getWorkorderList(), WorkorderLogConstant.PREP_WORK_ORDER_PROCESSED_DESCRIPTION, null, null);
    }
}
