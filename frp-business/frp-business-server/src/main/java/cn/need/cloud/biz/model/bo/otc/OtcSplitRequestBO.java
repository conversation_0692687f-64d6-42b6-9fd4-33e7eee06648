package cn.need.cloud.biz.model.bo.otc;

import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * OtcSplitRequestBO
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Schema(description = "OtcSplitRequestBO")
public class OtcSplitRequestBO implements Serializable {

    /**
     * slapAndGoRequestList
     */
    @Schema(description = "slapAndGoRequestList")
    private List<OtcSplitRequestDetailBO> slapAndGoRequestList;

    /**
     * sompRequestList
     */
    @Schema(description = "sompRequestList")
    private List<OtcSplitRequestDetailBO> sompRequestList;

    /**
     * sospRequestList
     */
    @Schema(description = "sospRequestList")
    private List<OtcSplitRequestDetailBO> sospRequestList;

    /**
     * multiBoxRequestList
     */
    @Schema(description = "multiBoxRequestList")
    private List<OtcSplitRequestDetailBO> multiBoxRequestList;

    /**
     * request
     */
    @Schema(description = "request")
    private OtcRequestVO request;


    public OtcSplitRequestBO(OtcRequestVO request) {
        this.slapAndGoRequestList = new ArrayList<>();
        this.sompRequestList = new ArrayList<>();
        this.sospRequestList = new ArrayList<>();
        this.multiBoxRequestList = new ArrayList<>();

        this.request = request;
    }

}

