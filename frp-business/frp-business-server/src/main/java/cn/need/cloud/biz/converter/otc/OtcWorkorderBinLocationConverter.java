package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcWorkorderBinLocationDTO;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderBinLocation;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderBinLocationVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC工单仓储位置 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcWorkorderBinLocationConverter extends AbstractModelConverter<OtcWorkorderBinLocation, OtcWorkorderBinLocationVO, OtcWorkorderBinLocationDTO> {

}
