package cn.need.cloud.biz.model.query.otc.request;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * OTC请求包裹标签 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC请求包裹标签 query对象")
public class OtcRequestPackageLabelQuery extends SuperQuery {

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型")
    private String labelType;

    /**
     * 面单类型
     */
    @Schema(description = "面单类型集合")
    @Condition(value = Keyword.IN, fields = {"labelType"})
    private List<String> labelTypeList;

    /**
     * label RefNum
     */
    @Schema(description = "label RefNum")
    private String labelRefNum;

    /**
     * label RefNum
     */
    @Schema(description = "label RefNum集合")
    @Condition(value = Keyword.IN, fields = {"labelRefNum"})
    private List<String> labelRefNumList;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型")
    private String paperType;

    /**
     * 纸张类型
     */
    @Schema(description = "纸张类型集合")
    @Condition(value = Keyword.IN, fields = {"paperType"})
    private List<String> paperTypeList;

    /**
     * label数据类型
     */
    @Schema(description = "label数据类型")
    private String labelRawData;

    /**
     * OTC请求ID
     */
    @Schema(description = "OTC请求ID")
    private Long otcRequestId;

    /**
     * OTC请求包裹ID
     */
    @Schema(description = "OTC请求包裹ID")
    private Long otcRequestPackageId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String rawDataType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型集合")
    @Condition(value = Keyword.IN, fields = {"rawDataType"})
    private List<String> rawDataTypeList;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型")
    private String fileIdRawDataType;

    /**
     * 文件系统数据类型
     */
    @Schema(description = "文件系统数据类型集合")
    @Condition(value = Keyword.IN, fields = {"fileIdRawDataType"})
    private List<String> fileIdRawDataTypeList;


}