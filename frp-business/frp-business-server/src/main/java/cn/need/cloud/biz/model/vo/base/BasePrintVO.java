package cn.need.cloud.biz.model.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 打印对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = " vo对象")
public class BasePrintVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "打印状态")
    private Integer printStatus;

    @Schema(description = "打印备注")
    private String note;
}
