package cn.need.cloud.biz.service.otb.pickingslip;

import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipListQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipPickQuery;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtbPrepPickingSlipPageVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

/**
 * <p>
 * otb预拣货单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPrepPickingSlipService extends SuperService<OtbPrepPickingSlip>,
        RefNumService<OtbPrepPickingSlip, OtbPrepPickingSlipService>,
        HeaderPrintedService<OtbPrepPickingSlip, OtbPrepPickingSlipService, PrintQuery> {

    /**
     * 根据查询条件获取otb预拣货单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个otb预拣货单对象的列表(分页)
     */
    PageData<OtbPrepPickingSlipPageVO> pageByQuery(PageSearch<OtbPrepPickingSlipListQuery> search);

    /**
     * 根据ID获取otb预拣货单
     *
     * @param id otb预拣货单ID
     * @return 返回otb预拣货单VO对象
     */
    OtbPrepPickingSlipVO detailById(Long id);

    /**
     * 根据otb预拣货单唯一编码获取otb预拣货单
     *
     * @param refNum otb预拣货单唯一编码
     * @return 返回otb预拣货单VO对象
     */
    OtbPrepPickingSlipVO detailByRefNum(String refNum);

    /**
     * Prep拣货
     *
     * @param query 拣货条件
     * @return /
     */
    boolean pick(OtbPrepPickingSlipPickQuery query);

    /**
     * 构建otb预拣货单VO对象
     *
     * @param entity otb预拣货单对象
     * @return 返回包含详细信息的otb预拣货单VO对象
     */
    OtbPrepPickingSlipVO buildOtbPrepPickingSlipVO(OtbPrepPickingSlip entity);
}