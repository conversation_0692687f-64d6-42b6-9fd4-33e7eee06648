package cn.need.cloud.biz.model.bo.common;

import lombok.Getter;
import lombok.Setter;

/***
 * 工单父子关系业务对象
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Getter
@Setter
public class WorkOrderMembershipBO {

    /**
     * 父节点
     */
    private Long parentId;

    /**
     * 子节点
     */
    private Long id;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 预处理工单详情类型
     */
    private String prepWorkorderDetailType;

    /**
     * 预处理工单详情产品版本
     */
    private Integer prepWorkorderDetailVersionInt;
}
