package cn.need.cloud.biz.model.param.otb.create.shipment;

import cn.need.cloud.biz.client.constant.enums.base.DetailProductTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.util.Set;


/**
 * OTB装运 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB装运 vo对象")
public class OtbShipmentCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 8246311367143988118L;
    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * otb 装运类型
     */
    @Schema(description = "otb 装运类型")
    private String otbShipmentType;

    /**
     * otb 打托单ssccNum
     */
    @Schema(description = "otb 打托单ssccNum")
    private Set<String> ssccNumList;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    @Size(max = 64, message = "detailProductType cannot exceed 64 characters")
    private String detailProductType = DetailProductTypeEnum.NORMAL.getCode();

}