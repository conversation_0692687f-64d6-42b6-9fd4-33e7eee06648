package cn.need.cloud.biz.model.vo.inventory;

import cn.need.cloud.biz.client.constant.enums.inbound.InboundWorkOrderStatusEnum;
import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 库存-库位 vo对象
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
@Data
@Schema(description = "库存-库位 vo对象")
@EqualsAndHashCode(of = "inventoryId")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "inventoryId",
        scope = InventoryVO.class // 可选
)
public class InventoryInStockVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;
    /**
     * inventoryId
     */
    @Schema(description = "inventoryId")
    private String inventoryId;
    /**
     * warehouseId
     */
    @Schema(description = "warehouseId")
    private Long warehouseId;
    /**
     * baseWarehouseVO
     */
    @Schema(description = "baseWarehouseVO")
    private BaseWarehouseVO baseWarehouseVO;
    /**
     * productId
     */
    @Schema(description = "productId")
    private Long productId;
    /**
     * 产品基本信息
     */
    @Schema(description = "产品基本信息")
    private BaseFullProductVO baseFullProductVO;
    /**
     * binLocationDetails
     */
    @Schema(description = "binLocationDetailList")
    private List<InventoryBinLocationDetailVO> binLocationDetailList;
    /**
     * inventoryLockedList
     */
    @Schema(description = "inventoryLockedList")
    private List<InventoryInventoryLockedVO> inventoryLockedList;
    /**
     * inventoryILockedList
     */
    @Schema(description = "inventoryReserveList")
    private List<InventoryInventoryReserveVO> inventoryReserveList;
    /**
     * inboundWorkorderDetailList
     */
    @Schema(description = "inboundWorkorderDetailList")
    @JsonIgnore
    private List<InventoryInboundWorkorderDetailVO> inboundWorkorderDetailList;

    public InventoryInStockVO() {
        inventoryId = UUID.randomUUID().toString();
        inventoryLockedList = new ArrayList<>();
        inventoryReserveList = new ArrayList<>();
        binLocationDetailList = new ArrayList<>();
        inboundWorkorderDetailList = new ArrayList<>();
    }

    /**
     * 在货架上实际数量（动态计算）
     *
     * @return inStockQty
     */
    @Schema(description = "inStockQty")
    public Integer getInStockQty() {
        if (ObjectUtil.isEmpty(binLocationDetailList)) {
            return 0;
        }
        return binLocationDetailList.stream()
                .mapToInt(InventoryBinLocationDetailVO::getInStockQty)
                .sum();
    }

    /**
     * 在货架上实际可分配数量（动态计算）
     *
     * @return inStockCanAllocateQty
     */
    @Schema(description = "inStockCanAllocateQty")
    public Integer getInStockCanAllocateQty() {
        return Math.max(getInStockAvailQty() - getActualInventoryLockedQty(), 0);
    }

    /**
     * 当前产品仓库实际上锁定的库存（动态计算）
     *
     * @return lockedQty
     */
    @Schema(description = "actualInventoryLockedQty")
    public Integer getActualInventoryLockedQty() {
        return Math.max(getInventoryLockedQty() - getInventoryReserveQty(), 0);
    }


    /**
     * 在货架上实际可用数量（动态计算）
     *
     * @return inStockAvailQty
     */
    @Schema(description = "inStockAvailQty")
    public Integer getInStockAvailQty() {
        if (ObjectUtil.isEmpty(binLocationDetailList)) {
            return 0;
        }
        return binLocationDetailList.stream()
                .mapToInt(InventoryBinLocationDetailVO::getAvailableQty)
                .sum();
    }

    /**
     * 在仓库层面 锁定 数量（动态计算）
     * InventoryLockedQty = Sum(InventoryLocked.Qty - InventoryLocked.FinishQty)
     *
     * @return inventoryLockedQty
     */
    @Schema(description = "inventoryLockedQty")
    public Integer getInventoryLockedQty() {
        if (ObjectUtil.isEmpty(inventoryLockedList)) {
            return 0;
        }
        return inventoryLockedList.stream()
                .mapToInt(InventoryInventoryLockedVO::getCurrentLockedQty)
                .sum();
    }

    /**
     * 在仓库层面 锁定 数量（动态计算）
     * InventoryReservedQty = Sum(InventoryReserved.Qty - InventoryReserved.FinishQty)
     *
     * @return inventoryReserveQty
     */
    @Schema(description = "inventoryReserveQty")
    public Integer getInventoryReserveQty() {
        if (ObjectUtil.isEmpty(inventoryReserveList)) {
            return 0;
        }
        return inventoryReserveList.stream()
                .mapToInt(InventoryInventoryReserveVO::getCurrentReserveQty)
                .sum();
    }

    /**
     * inReceiveQty 数量（动态计算）
     * InReceiveQty = Sum(InReceiveWorkOrder.NeedPutAwayQty)
     *
     * @return inventoryReserveQty
     */
    @Schema(description = "inReceiveQty")
    public Integer getInReceiveQty() {

        List<InventoryInboundWorkorderDetailVO> inReceiveInboundWorkorderDetails = getInReceiveInboundWorkorderDetailList();
        if (ObjectUtil.isEmpty(inReceiveInboundWorkorderDetails)) {
            return 0;
        }
        return inReceiveInboundWorkorderDetails.stream()
                .mapToInt(InventoryInboundWorkorderDetailVO::getNeedPutAwayQty)
                .sum();
    }

    /**
     * inReceiveInboundWorkorderDetailList（动态计算）
     *
     * @return inventoryReserveQty
     */
    @Schema(description = "inReceiveInboundWorkorderDetailList")
    public List<InventoryInboundWorkorderDetailVO> getInReceiveInboundWorkorderDetailList() {
        if (ObjectUtil.isEmpty(inboundWorkorderDetailList)) {
            return Collections.emptyList();
        }
        return inboundWorkorderDetailList.stream()
                .filter(detail -> ObjectUtil.isNotEmpty(detail.getInboundWorkOrder()) &&
                        InboundWorkOrderStatusEnum.getInReceive()
                                .contains(detail.getInboundWorkOrder().getInboundWorkorderStatus()))
                .collect(Collectors.toList());
    }

    /**
     * inTransitQty 数量（动态计算）
     * inTransitQty = Sum(inTransitWorkOrder.needReceiveQty)
     *
     * @return inventoryReserveQty
     */
    @Schema(description = "inTransitQty")
    public Integer getInTransitQty() {

        List<InventoryInboundWorkorderDetailVO> inTransitInboundWorkorderDetails = getInTransitInboundWorkorderDetailList();
        if (ObjectUtil.isEmpty(inTransitInboundWorkorderDetails)) {
            return 0;
        }
        return inTransitInboundWorkorderDetails.stream()
                .mapToInt(InventoryInboundWorkorderDetailVO::getNeedReceiveQty)
                .sum();
    }

    /**
     * inTransitInboundWorkorderDetails（动态计算）
     *
     * @return inventoryReserveQty
     */
    @Schema(description = "inTransitInboundWorkorderDetailList")
    public List<InventoryInboundWorkorderDetailVO> getInTransitInboundWorkorderDetailList() {
        if (ObjectUtil.isEmpty(inboundWorkorderDetailList)) {
            return Collections.emptyList();
        }
        return inboundWorkorderDetailList.stream()
                .filter(detail -> ObjectUtil.isNotEmpty(detail.getInboundWorkOrder()) &&
                        InboundWorkOrderStatusEnum.getInTransit()
                                .contains(detail.getInboundWorkOrder().getInboundWorkorderStatus()))
                .collect(Collectors.toList());
    }
}