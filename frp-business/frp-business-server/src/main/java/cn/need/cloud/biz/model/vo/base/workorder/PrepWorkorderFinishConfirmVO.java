package cn.need.cloud.biz.model.vo.base.workorder;

import cn.need.cloud.biz.model.vo.base.putawayslip.PrepPutawaySlipConfirmDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 工单 Finish 确认页面
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "工单 Finish 确认页面")
@AllArgsConstructor
@NoArgsConstructor
public class PrepWorkorderFinishConfirmVO implements Serializable {

    @Schema(description = "工单列表")
    private List<PrepWorkorderConfirmDetailVO> prepWorkorderList;

    @Schema(description = "上架单列表")
    private List<PrepPutawaySlipConfirmDetailVO> prepPutawaySlipList;
}
