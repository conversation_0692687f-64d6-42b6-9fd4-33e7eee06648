package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.FeeConfigOtcDetailConverter;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtcDetail;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtcDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtcDetailPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtcDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 仓库报价费用配置otc详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/fee-config-otc-detail")
@Tag(name = "仓库报价费用配置otc详情")
public class FeeConfigOtcDetailController extends AbstractRestController<FeeConfigOtcDetailService, FeeConfigOtcDetail, FeeConfigOtcDetailConverter, FeeConfigOtcDetailVO> {

    
    @Operation(summary = "根据id获取仓库报价费用配置otc详情详情", description = "根据数据主键id，从数据库中获取其对应的仓库报价费用配置otc详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeConfigOtcDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取仓库报价费用配置otc详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库报价费用配置otc详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeConfigOtcDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeConfigOtcDetailQuery> search) {

        // 获取仓库报价费用配置otc详情分页
        PageData<FeeConfigOtcDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
