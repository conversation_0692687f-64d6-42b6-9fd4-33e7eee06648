package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.util.Allocation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 基础拣货对象，拥有相同 拣货数量、数量、乐观锁版本号 字段
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTB 拣货基础 对象")
public class BasePackVO implements Allocation {

    /**
     * 拣货对象id
     */
    @Schema(description = "拣货对象id")
    private Long id;

    /**
     * 打包数量
     */
    @Schema(description = "打包数量")
    private Integer packedQty;

    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    private Integer pickedQty;

    /**
     * 拣货前的数量
     */
    @Schema(description = "拣货前的数量")
    private Integer packedBeforeQty;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;


    public int getChangePickQty() {
        return packedQty - packedBeforeQty;
    }

    @Override
    public int total() {
        return this.pickedQty - this.packedQty;
    }

    @Override
    public void allocation(int allocationQty) {
        this.packedQty = allocationQty;
    }

    @Override
    public int allocated() {
        return this.packedQty;
    }
}