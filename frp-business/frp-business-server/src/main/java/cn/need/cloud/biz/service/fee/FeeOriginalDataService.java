package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeOriginalData;
import cn.need.cloud.biz.model.param.fee.create.FeeOriginalDataCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOriginalDataUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOriginalDataQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOriginalDataVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOriginalDataPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用原始数据表 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeOriginalDataService extends SuperService<FeeOriginalData>,
        RefNumService<FeeOriginalData, FeeOriginalDataService> {

    /**
     * 构建费用原始数据
     */
    void build();

    /**
     * 根据参数新增费用原始数据表
     *
     * @param createParam 请求创建参数，包含需要插入的费用原始数据表的相关信息
     * @return 费用原始数据表ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeOriginalDataCreateParam createParam);

    /**
     * 根据参数更新费用原始数据表
     *
     * @param updateParam 请求创建参数，包含需要更新的费用原始数据表的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeOriginalDataUpdateParam updateParam);

    /**
     * 根据查询条件获取费用原始数据表列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用原始数据表对象的列表(分页)
     */
    List<FeeOriginalDataPageVO> listByQuery(FeeOriginalDataQuery query);

    /**
     * 根据查询条件获取费用原始数据表列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用原始数据表对象的列表(分页)
     */
    PageData<FeeOriginalDataPageVO> pageByQuery(PageSearch<FeeOriginalDataQuery> search);

    /**
     * 根据ID获取费用原始数据表
     *
     * @param id 费用原始数据表ID
     * @return 返回费用原始数据表VO对象
     */
    FeeOriginalDataVO detailById(Long id);

    /**
     * 根据费用原始数据表唯一编码获取费用原始数据表
     *
     * @param refNum 费用原始数据表唯一编码
     * @return 返回费用原始数据表VO对象
     */
    FeeOriginalDataVO detailByRefNum(String refNum);


}