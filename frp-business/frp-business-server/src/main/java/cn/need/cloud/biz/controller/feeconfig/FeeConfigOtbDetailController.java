package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.FeeConfigOtbDetailConverter;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtbDetail;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtbDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtbDetailPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtbDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 仓库报价费用配置otb详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/fee-config-otb-detail")
@Tag(name = "仓库报价费用配置otb详情")
public class FeeConfigOtbDetailController extends AbstractRestController<FeeConfigOtbDetailService, FeeConfigOtbDetail, FeeConfigOtbDetailConverter, FeeConfigOtbDetailVO> {

    
    @Operation(summary = "根据id获取仓库报价费用配置otb详情详情", description = "根据数据主键id，从数据库中获取其对应的仓库报价费用配置otb详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeConfigOtbDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取仓库报价费用配置otb详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库报价费用配置otb详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeConfigOtbDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeConfigOtbDetailQuery> search) {

        // 获取仓库报价费用配置otb详情分页
        PageData<FeeConfigOtbDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
