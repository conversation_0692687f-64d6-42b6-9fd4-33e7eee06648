package cn.need.cloud.biz.model.param.feeconfig.update;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeConditionTypeAndUnitTypeInboundEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 仓库报价费用配置otb UpdateParam对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@Schema(description = "仓库报价费用配置otb UpdateParam对象")
public class FeeConfigOtbUpdateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -3720373479575939443L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    @NotNull(message = "activeFlag cannot be null")
    private Boolean activeFlag;
    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @Schema(description = "计费条件类型（决定区间判断的依据）")
    @NotNull(message = "conditionType cannot be Null")
    private FeeConditionTypeAndUnitTypeInboundEnum conditionType;
    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）")
    @NotEmpty(message = "currency cannot be empty")
    @Size(max = 3, message = "currency cannot exceed 3 characters")
    private String currency;

    /**
     * 额外计费判断字段
     */
    @Schema(description = "额外计费判断字段")
    @Size(max = 512, message = "extraFeeJudgeFields cannot exceed 512 characters")
    private String extraFeeJudgeFields;
    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @Schema(description = "费用计费类型(detail得出的结果，是否要乘condition_type得出的值)")
    @NotEmpty(message = "feeCalculationType cannot be empty")
    @Size(max = 32, message = "feeCalculationType cannot exceed 32 characters")
    private String feeCalculationType;
    /**
     * 名称
     */
    @Schema(description = "名称")
    @Size(max = 32, message = "name cannot exceed 32 characters")
    private String name;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 512, message = "note cannot exceed 512 characters")
    private String note;
    /**
     * 仓库报价id
     */
    @Schema(description = "仓库报价id")
    private Long quoteId;
    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    @NotNull(message = "warehouseId cannot be null")
    private Long warehouseId;

    /**
     * 详情
     */
    @Schema(description = "仓库报价详情")
    private List<FeeConfigOtbDetailUpdateParam> detailList;

}