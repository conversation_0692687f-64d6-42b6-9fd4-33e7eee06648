package cn.need.cloud.biz.service.product.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.product.ProductComponentConverter;
import cn.need.cloud.biz.mapper.product.ProductComponentMapper;
import cn.need.cloud.biz.model.bo.product.ProductComponentDicBO;
import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.param.product.update.ProductComponentCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductComponentQuery;
import cn.need.cloud.biz.model.vo.product.AssemblyProductListVO;
import cn.need.cloud.biz.model.vo.product.ComponentProductListVO;
import cn.need.cloud.biz.model.vo.product.ProductComponentVO;
import cn.need.cloud.biz.model.vo.product.page.ProductComponentPageVO;
import cn.need.cloud.biz.service.product.ProductComponentService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.ProductCheckUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品组件服务实现类
 * </p>
 * <p>
 * 该类实现了产品组件的核心业务逻辑，负责产品组件的创建、查询、更新和管理。
 * 主要功能包括：
 * 1. 产品组件的创建和管理，包括单个组件和批量组件的创建和更新
 * 2. 产品组件的查询功能，支持根据ID、组装产品ID等多种方式查询
 * 3. 产品组件的版本管理，包括版本号的生成和更新
 * 4. 产品组件的关系管理，包括组装产品和组件产品的关联关系
 * 5. 产品组件的循环引用检查，防止产品组件之间形成循环依赖
 * </p>
 * <p>
 * 产品组件是产品管理的重要组成部分，用于描述产品之间的组装关系。
 * 一个组装产品可以包含多个组件产品，每个组件产品有对应的数量和组装说明。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductComponentServiceImpl extends SuperServiceImpl<ProductComponentMapper, ProductComponent> implements ProductComponentService {


    /**
     * 产品服务，用于获取产品信息和管理产品属性
     */
    @Resource
    private ProductService productService;


    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 创建或更新产品组件信息
     * <p>
     * 该方法用于批量创建或更新产品组件信息。
     * 主要流程包括：
     * 1. 验证参数是否为空和基本参数是否有效
     * 2. 校验产品是否属于同一个交易伙伴
     * 3. 校验组装产品和组件产品的合法性，如组装产品不能是组、多箱或组件
     * 4. 合并相同组件产品的参数，将数量相加
     * 5. 清除原有的组件数据（如果存在）
     * 6. 生成新的版本号并创建新的组件实体
     * 7. 更新组装产品的组装标记
     * </p>
     * <p>
     * 该方法在事务中执行，确保数据的一致性。
     * 它会进行多种验证，包括循环引用检查、产品类型验证等，以确保产品组件的数据完整性。
     * </p>
     *
     * @param paramList 产品组件创建或更新参数列表，每个参数对象包含组装产品ID、组件产品ID、组装说明和数量
     * @return 创建或更新的产品组件数量
     * @throws BusinessException 如果参数无效、产品不属于同一交易伙伴、存在循环引用等情况，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createOrUpdate(List<ProductComponentCreateOrUpdateParam> paramList) {
        // 检查传入产品组装参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(paramList)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        //获取基本参数
        Long assemblyProductId = paramList.get(NumberUtils.INTEGER_ZERO).getAssemblyProductId();
        Long transactionPartnerId = ProductCacheUtil.getById(assemblyProductId).getTransactionPartnerId();
        //校验基本参数
        if (ObjectUtil.isEmpty(assemblyProductId) || ObjectUtil.isEmpty(transactionPartnerId)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        //校验
        boolean allProductInPartnerId = productService.isAllProductInPartnerId(transactionPartnerId, paramList.stream().map(ProductComponentCreateOrUpdateParam::getComponentProductId).toList());
        if (!allProductInPartnerId) {
            throw new BusinessException("Product must in same Partner");
        }


        //校验主件是否是group 是group的时候无法更新
        if (productService.isGroup(assemblyProductId)) {
            throw new BusinessException("Group product can not update component");
        }
        //校验主件是否是multiBox 是multiBox的时候无法更新
        if (productService.isMultibox(assemblyProductId)) {
            throw new BusinessException("MultiBox product can not update component");
        }
        //校验 assembly 是否是component
        if (isComponent(assemblyProductId)) {
            throw new BusinessException("Component can not be assembly");
        }

        // 合并可能相同的参数
        List<ProductComponentCreateOrUpdateParam> mergeParams = getMergeParams(paramList, assemblyProductId);
        //校验配件是否有child
        if (mergeParams.stream().anyMatch(param -> productService.isChildGroup(param.getComponentProductId()))) {
            throw new BusinessException("Can not add ProductGroups is child product");
        }

        //校验是否引用自身
        if (mergeParams.stream().anyMatch(param -> Objects.equals(param.getComponentProductId(), assemblyProductId))) {
            throw new BusinessException("Can not add self to components");
        }

        //校验component是否是combo
        if (mergeParams.stream().anyMatch(param -> productService.isAssembly(param.getComponentProductId()))) {
            throw new BusinessException("Assembly can not be component");
        }

        //获取配件产品的ids
        List<ProductComponent> oldComponents = getListByAssemblyProductId(assemblyProductId);

        if (ObjectUtil.isNotEmpty(oldComponents)) {
            //清原数据
            deleteByAssemblyProductId(assemblyProductId, null);
        }

        // 获取产品组装转换器实例，用于将产品组装参数对象转换为实体对象
        ProductComponentConverter converter = Converters.get(ProductComponentConverter.class);

        List<ProductComponent> entityList = converter.toEntity(paramList);
        // 获取版本号
        int versionInt = getVersionInt(assemblyProductId, oldComponents);
        // 将产品组装参数对象转换为实体对象
        entityList.forEach(entity -> entity.setComponentVersionInt(versionInt));
        //更新产品组装标记
        if (ObjectUtil.isEmpty(oldComponents)) {
            productService.updateAssemblyFlagById(assemblyProductId, Boolean.TRUE);
        }

        // 危险品校验
        Set<Long> componentIdList = paramList.stream()
                .map(ProductComponentCreateOrUpdateParam::getComponentProductId)
                .collect(Collectors.toSet());
        ProductCheckUtil.checkProductType(assemblyProductId, componentIdList);

        // 执行更新产品组装操作
        return super.insertBatch(entityList);

    }


    /**
     * 根据查询条件获取产品组件列表
     * <p>
     * 该方法用于根据指定的查询条件获取产品组件列表，不带分页。
     * 查询条件可以包括组装产品ID、组件产品ID、版本号等多种条件。
     * </p>
     *
     * @param query 产品组件查询条件对象
     * @return 产品组件列表，包含产品组件基本信息和关联的产品信息
     */
    @Override
    public List<ProductComponentPageVO> listByQuery(ProductComponentQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取产品组件列表
     * <p>
     * 该方法用于根据指定的查询条件和分页参数获取产品组件列表。
     * 主要流程包括：
     * 1. 根据分页参数创建分页对象
     * 2. 执行分页查询并获取数据列表
     * 3. 返回带分页信息的数据列表
     * </p>
     *
     * @param search 包含查询条件和分页参数的对象
     * @return 带分页信息的产品组件列表，包含产品组件基本信息和关联的产品信息
     */
    @Override
    public PageData<ProductComponentPageVO> pageByQuery(PageSearch<ProductComponentQuery> search) {
        Page<ProductComponent> page = Conditions.page(search, entityClass);
        List<ProductComponentPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取产品组件详情
     * <p>
     * 该方法用于根据产品组件ID获取产品组件的详细信息。
     * 如果指定ID的产品组件不存在，则抛出业务异常。
     * </p>
     *
     * @param id 产品组件ID
     * @return 产品组件VO对象，包含产品组件基本信息和关联的产品信息
     * @throws BusinessException 如果产品组件不存在，则抛出业务异常
     */
    @Override
    public ProductComponentVO detailById(Long id) {
        ProductComponent entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in ProductComponent");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "ProductComponent", id));
        }
        return buildProductComponentVO(entity);
    }


    /**
     * 获取产品的组件列表
     * <p>
     * 该方法用于获取指定产品的组件列表。
     * 返回的是作为组件的产品列表，包含组件产品的基本信息和数量等。
     * </p>
     *
     * @param productId 产品ID，即组装产品ID
     * @return 组件产品列表VO对象，包含组件产品的基本信息和数量
     * @throws BusinessException 如果产品ID为空，则抛出异常
     */
    @Override
    public List<ComponentProductListVO> componentList(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        return mapper.componentList(productId);
    }

    /**
     * 获取产品的组装列表
     * <p>
     * 该方法用于获取指定产品作为组件被组装到的产品列表。
     * 返回的是作为组装产品的列表，包含组装产品的基本信息和数量等。
     * </p>
     *
     * @param productId 产品ID，即组件产品ID
     * @return 组装产品列表VO对象，包含组装产品的基本信息和数量
     * @throws BusinessException 如果产品ID为空，则抛出异常
     */
    @Override
    public List<AssemblyProductListVO> assemblyList(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        return mapper.assemblyList(productId);
    }

    /**
     * 根据组装产品ID获取组件列表
     * <p>
     * 该方法用于获取指定组装产品的所有组件实体列表。
     * 返回的是产品组件实体列表，包含组件产品ID、数量、版本号等信息。
     * </p>
     *
     * @param assemblyProductId 组装产品ID
     * @return 产品组件实体列表，如果没有组件则返回空列表
     */
    @Override
    public List<ProductComponent> getListByAssemblyProductId(Long assemblyProductId) {
        return lambdaQuery().eq(ProductComponent::getAssemblyProductId, assemblyProductId).list();
    }

    /**
     * 获取组装产品的第一个组件
     * <p>
     * 该方法用于获取指定组装产品的第一个组件实体。
     * 返回的是按ID升序排序后的第一个产品组件实体。
     * </p>
     * <p>
     * 该方法主要用于快速检查产品是否有组件，或者获取第一个组件的信息。
     * </p>
     *
     * @param assemblyProductId 组装产品ID
     * @return 第一个产品组件实体，如果没有组件则返回null
     */
    @Override
    public ProductComponent getFirstByAssemblyProductId(Long assemblyProductId) {
        return lambdaQuery()
                .eq(ProductComponent::getAssemblyProductId, assemblyProductId)
                .orderByAsc(IdModel::getId)
                .last("LIMIT 1")
                .one();
    }

    /**
     * 根据组装产品ID和版本号获取组件列表
     * <p>
     * 该方法用于获取指定组装产品和版本号的所有组件实体列表。
     * 返回的是符合指定版本号的产品组件实体列表。
     * </p>
     * <p>
     * 该方法主要用于获取特定版本的产品组件信息，支持产品组件的版本管理。
     * </p>
     *
     * @param assemblyProductId 组装产品ID
     * @param versionInt        版本号
     * @return 符合指定版本号的产品组件实体列表，如果参数无效或没有符合条件的组件则返回空列表
     */
    @Override
    public List<ProductComponent> getListByAssemblyProductIdAndVersionInt(Long assemblyProductId, int versionInt) {
        if (ObjectUtil.isEmpty(assemblyProductId) || ObjectUtil.isEmpty(versionInt)) {
            return Collections.emptyList();
        }
        return mapper.findListByAssemblyProductIdAndVersionInt(assemblyProductId, versionInt);
    }

    /**
     * 根据组装产品ID删除组件
     * <p>
     * 该方法用于删除指定组装产品的所有组件。
     * 实际上是将组件的删除标志设置为已删除，而不是物理删除。
     * </p>
     * <p>
     * 该方法主要用于更新产品组件时先清除原有的组件数据。
     * </p>
     *
     * @param assemblyProductId 组装产品ID
     * @param deletedNote       删除备注，可以为空
     * @return 删除的记录数量
     */
    @Override
    public Integer deleteByAssemblyProductId(Long assemblyProductId, String deletedNote) {
        LambdaUpdateWrapper<ProductComponent> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProductComponent::getRemoveFlag, DataState.ENABLED)
                .set(ObjectUtil.isNotEmpty(deletedNote), ProductComponent::getDeletedNote, deletedNote)
                .eq(ProductComponent::getAssemblyProductId, assemblyProductId);
        return mapper.update(wrapper);
    }

    /**
     * 根据组装产品ID列表获取所有组件
     * <p>
     * 该方法用于获取多个组装产品的所有组件实体列表。
     * 主要流程包括：
     * 1. 对组装产品ID列表进行去重处理
     * 2. 验证去重后的列表是否为空
     * 3. 查询所有符合条件的产品组件实体
     * 4. 返回查询结果，如果没有符合条件的组件则返回空列表
     * </p>
     * <p>
     * 该方法主要用于批量获取多个组装产品的组件信息，提高查询效率。
     * </p>
     *
     * @param assemblyProductIdList 组装产品ID列表
     * @return 产品组件实体列表，如果参数无效或没有符合条件的组件则返回空列表
     */
    @Override
    public List<ProductComponent> getListComponentsByAssemblyProductIdList(List<Long> assemblyProductIdList) {
        // 确保列表去重
        Set<Long> assemblyProductIdSet = new HashSet<>(assemblyProductIdList);
        if (ObjectUtil.isEmpty(assemblyProductIdSet)) {
            return Collections.emptyList();
        }
        List<ProductComponent> components = lambdaQuery()
                .in(ProductComponent::getAssemblyProductId, assemblyProductIdSet)
                .list();
        if (ObjectUtil.isEmpty(components)) {
            return Collections.emptyList();
        }
        return components;
    }

    /**
     * 根据组装产品ID列表获取组件字典
     * <p>
     * 该方法用于获取多个组装产品的组件字典。
     * 主要流程包括：
     * 1. 获取所有符合条件的产品组件实体
     * 2. 创建组件字典列表
     * 3. 对每个组装产品ID，过滤出其对应的组件列表
     * 4. 将组装产品ID和其组件列表添加到字典中
     * 5. 返回组件字典列表
     * </p>
     * <p>
     * 该方法返回的是一个字典列表，每个字典对象包含一个组装产品ID和其对应的组件列表。
     * 这种结构便于前端展示和处理。
     * </p>
     *
     * @param assemblyProductIdList 组装产品ID列表
     * @return 组件字典列表，每个字典对象包含一个组装产品ID和其组件列表
     */
    @Override
    public List<ProductComponentDicBO> getListComponentDicByAssemblyProductIdList(List<Long> assemblyProductIdList) {

        List<ProductComponent> components = getListComponentsByAssemblyProductIdList(assemblyProductIdList);

        List<ProductComponentDicBO> dic = new ArrayList<>();

        Set<Long> assemblyProductIdSet = new HashSet<>(assemblyProductIdList);

        for (Long assemblyProductId : assemblyProductIdSet) {

            List<ProductComponent> componentList = components.stream()
                    .filter(component -> ObjectUtil.equal(component.getAssemblyProductId(), assemblyProductId))
                    .toList();
            dic.add(new ProductComponentDicBO(assemblyProductId, componentList));
        }
        return dic;
    }


    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 合并List中相同的参数将qty相加
     *
     * @param params 包含多个产品组件创建或更新参数的列表，每个参数对象都封装了一个产品组件的相关信息
     * @return 返回合并后的参数列表
     */
    private List<ProductComponentCreateOrUpdateParam> getMergeParams(List<ProductComponentCreateOrUpdateParam> params, Long assemblyProductId) {
        if (ObjectUtil.isEmpty(params)) {
            throw new BusinessException("Params cannot be empty");
        }
        return params.stream().collect(Collectors.groupingBy(
                        ProductComponentCreateOrUpdateParam::getComponentProductId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    // 取assemblyInstructionNote
                                    String assemblyInstructionNote = list.get(NumberUtils.INTEGER_ZERO).getAssemblyInstructionNote();
                                    // 计算componentQty的总和
                                    int totalComponentQty = list.stream()
                                            .mapToInt(ProductComponentCreateOrUpdateParam::getComponentQty)
                                            .sum();
                                    // 创建新的合并后的对象
                                    return new ProductComponentCreateOrUpdateParam(
                                            assemblyProductId,
                                            list.get(NumberUtils.INTEGER_ZERO).getComponentProductId(),
                                            assemblyInstructionNote,
                                            totalComponentQty
                                    );
                                }
                        )
                ))
                .values().stream()
                .toList();
    }

    /**
     * 初始化产品组装对象
     * 此方法用于设置产品组装对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品组装对象，不应为空
     * @return 返回初始化后的产品组装
     * @throws BusinessException 如果传入的产品组装为空，则抛出此异常
     */
    private ProductComponent initProductComponent(ProductComponent entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("ProductComponent cannot be empty");
        }

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建产品组装VO对象
     *
     * @param entity 产品组装对象
     * @return 返回包含详细信息的产品组装VO对象
     */
    private ProductComponentVO buildProductComponentVO(ProductComponent entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品组装VO对象
        return Converters.get(ProductComponentConverter.class).toVO(entity);
    }

    /**
     * 获取版本号
     *
     * @param productId 产品id
     * @param list      产品组装列表
     * @return 版本号
     */
    private Integer getVersionInt(Long productId, List<ProductComponent> list) {
        //不为空 直接取值
        if (ObjectUtil.isNotEmpty(list)) {
            return list.stream().map(ProductComponent::getComponentVersionInt).max(Integer::compareTo)
                    .map(integer -> integer + NumberUtils.INTEGER_ONE).orElse(NumberUtils.INTEGER_ZERO);
        }
        //为空 从删除数据中取老版本号最大值
        Integer oldVersionInt = mapper.getOldVersionInt(productId);
        if (Objects.isNull(oldVersionInt)) {
            //最新 从0开始
            return NumberUtils.INTEGER_ZERO;
        }
        return oldVersionInt + NumberUtils.INTEGER_ONE;
    }

    /**
     * 判断产品是否是组件
     * <p>
     * 该方法用于判断指定产品是否作为组件被用于其他组装产品中。
     * 判断依据是查询产品组件表中是否存在以该产品作为组件产品的记录。
     * </p>
     * <p>
     * 该方法主要用于验证产品的类型和关系，防止循环引用和非法组装。
     * </p>
     *
     * @param productId 产品ID
     * @return 如果产品作为组件被用于其他组装产品中则返回true，否则返回false
     */
    @Override
    public boolean isComponent(Long productId) {
        return lambdaQuery().eq(ProductComponent::getComponentProductId, productId).count() > 0;
    }

    /**
     * 获取组装产品的组件列表
     * <p>
     * 该方法用于获取指定组装产品的所有组件实体列表。
     * 与 getListByAssemblyProductId 方法功能类似，但命名更简洁。
     * </p>
     * <p>
     * 该方法会验证组装产品ID是否为空，如果为空则抛出异常。
     * </p>
     *
     * @param assemblyProductId 组装产品ID，不能为空
     * @return 产品组件实体列表，如果没有组件则返回空列表
     * @throws IllegalArgumentException 如果组装产品ID为空，则抛出异常
     */
    @Override
    public List<ProductComponent> list(Long assemblyProductId) {
        Validate.notNull(assemblyProductId, "The assemblyProductId value cannot be null.");
        return lambdaQuery()
                .eq(ProductComponent::getAssemblyProductId, assemblyProductId)
                .list();
    }

    /**
     * 校验是否存在循环引用
     * <p>
     * 该方法用于递归检查产品组件之间是否存在循环引用。
     * 主要流程包括：
     * 1. 获取组件产品作为组件的所有组装产品ID列表
     * 2. 如果列表为空，说明没有循环引用，直接返回
     * 3. 如果列表中包含要检查的产品ID，说明存在循环引用，抛出异常
     * 4. 对列表中的每个组装产品ID，递归检查是否存在循环引用
     * </p>
     * <p>
     * 该方法使用递归算法检查产品组件关系图中是否存在环，防止产品组件之间形成循环依赖。
     * 当检测到循环引用时，抛出业务异常。
     * </p>
     *
     * @param componentProductId 组件产品ID，当前要检查的组件产品
     * @param checkProductId     要检查的产品ID，即最初的组装产品ID
     * @throws BusinessException 如果检测到循环引用，则抛出异常
     *                           <p>
     *                                                                                                                                                                                                                 TODO: 考虑使用非递归算法或者缓存中间结果来提高性能，避免深层递归导致的栈溢出风险
     */
    private void checkCircle(Long componentProductId, Long checkProductId) {
        List<Long> assemblyProductIdList = mapper.getAssemblyProductIdByComponent(componentProductId);
        if (ObjectUtil.isEmpty(assemblyProductIdList)) {
            return;
        }
        if (ObjectUtil.isNotEmpty(assemblyProductIdList) && assemblyProductIdList.contains(checkProductId)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Circular reference detected in component hierarchy"));
        }
        assemblyProductIdList.forEach(id -> checkCircle(id, checkProductId));
    }
}
