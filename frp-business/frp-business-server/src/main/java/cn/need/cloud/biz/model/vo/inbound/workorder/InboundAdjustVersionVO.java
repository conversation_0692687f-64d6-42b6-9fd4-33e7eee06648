package cn.need.cloud.biz.model.vo.inbound.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 调整产品版本数量 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "调整产品版本数量 vo对象")
public class InboundAdjustVersionVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 版本产品id
     */
    @Schema(description = "版本产品id")
    private Long productVersionId;

    /**
     * 入库工单详情id
     */
    @Schema(description = "入库工单详情id")
    private Long id;
}
