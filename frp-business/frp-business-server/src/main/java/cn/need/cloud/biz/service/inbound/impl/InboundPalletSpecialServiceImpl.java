package cn.need.cloud.biz.service.inbound.impl;

import cn.need.cloud.biz.client.constant.enums.inbound.InboundPalletEnum;
import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundPalletAuditLogHelper;
import cn.need.cloud.biz.service.inbound.InboundPalletService;
import cn.need.cloud.biz.service.inbound.InboundPalletSpecialService;
import cn.need.cloud.biz.service.inbound.InboundUnloadService;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 入库托盘特殊处理服务实现类
 * </p>
 * <p>
 * 该服务主要处理入库托盘的特殊操作，包括：
 * 1. 托盘回滚（取消托盘操作并恢复卸货记录）
 * </p>
 * <p>
 * 特殊处理服务的目的是将复杂或需要多个服务协作的业务逻辑集中处理，
 * 确保数据一致性和业务完整性
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Service
public class InboundPalletSpecialServiceImpl implements InboundPalletSpecialService {

    /**
     * 入库托盘服务，用于查询和更新托盘记录
     */
    @Resource
    private InboundPalletService inboundPalletService;

    /**
     * 入库卸货服务，用于处理卸货记录回滚
     */
    @Resource
    private InboundUnloadService inboundUnloadService;

    /**
     * 托盘回滚
     * <p>
     * 该方法用于回滚（取消）入库托盘操作，执行以下步骤：
     * 1. 更新托盘状态为已取消
     * 2. 回滚关联的卸货记录，恢复卸货数量
     * </p>
     * <p>
     * 方法在事务中执行，确保数据一致性，如果任何步骤失败，所有操作将回滚
     * </p>
     *
     * @param palletId 托盘ID
     * @param note     回滚备注
     * @return 标准成功标志（1表示成功）
     * <p>
     * TODO: 返回类型使用Integer而不是更明确的类型（如boolean），可能造成混淆
     * 优化建议：修改返回类型为void或boolean，更准确表达方法结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer palletRollBack(Long palletId, String note) {
        //更新打托单状态
        updateStatus(palletId, note);
        //回滚卸货单打托数量
        inboundUnloadService.rollBackByPalletId(palletId, note);
        return DataState.ENABLED;
    }

    /**
     * 更新托盘状态
     * <p>
     * 该方法用于更新托盘状态为已取消，并记录操作日志。
     * 只有处于"新建"状态的托盘才能被取消，否则将抛出异常。
     * </p>
     *
     * @param palletId 托盘ID
     * @param note     操作备注
     * @throws IllegalArgumentException 如果托盘状态不为"新建"状态
     *                                  <p>
     *                                                                                                                                                                                                                                        TODO: 方法中硬编码了状态值的比较，不利于维护
     *                                                                                                                                                                                                                                        优化建议：使用枚举类型的equals方法进行比较，而不是字符串比较
     */
    private void updateStatus(Long palletId, String note) {
        //获取打托单状态
        InboundPallet inboundPallet = inboundPalletService.getById(palletId);
        //状态校验
        Validate.isTrue(StringUtil.equals(InboundPalletEnum.NEW.getStatus(), inboundPallet.getPalletStatus()),
                "current status is " + inboundPallet.getPalletStatus() + ",can not be canceled");
        // 更新状态
        inboundPallet.setPalletStatus(InboundPalletEnum.CANCELLED.getStatus());
        inboundPallet.setNote(note);
        inboundPalletService.update(inboundPallet);
        //记录日志
        InboundPalletAuditLogHelper.recordLog(inboundPallet, null, note);
    }
}
