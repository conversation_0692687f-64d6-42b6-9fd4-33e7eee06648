package cn.need.cloud.biz.model.param.setting.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 打印配置 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "打印配置 vo对象")
public class PrinterConfigCreateParam implements Serializable {


    /**
     * 打印机类型
     */
    @Schema(description = "打印机类型")
    private String type;

    /**
     * 打印机名称
     */
    @Schema(description = "打印机名称")
    private String name;


    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ip;

}