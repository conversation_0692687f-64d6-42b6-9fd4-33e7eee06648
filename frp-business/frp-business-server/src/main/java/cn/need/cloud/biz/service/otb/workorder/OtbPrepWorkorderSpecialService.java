package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPrepPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbWorkorderSplitBO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pickingslip.PrepPickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderFinishConfirmVO;

import java.util.List;

/**
 * <p>
 * OTC预提工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPrepWorkorderSpecialService {

    /**
     * Start Rollback
     *
     * @param query 启动参数
     */
    void processTriggering(WorkorderProcessBO query);

    /**
     * Rollback
     *
     * @param param 上架参数
     */
    void rollback(OtbPrepPutawaySlipPutAwayBO param);

    /**
     * 获取Rollback列表
     *
     * @param query query
     * @return /
     */
    List<PrepWorkorderConfirmDetailVO> rollbackList(WorkorderRollbackListQuery query);

    /**
     * 工单Rollback PutAway Units
     *
     * @param param param
     * @return /
     */
    OtbPrepWorkorder rollbackPutAwayUnits(RollbackPutawayUnitsUpdateParam param);

    /**
     * 拣货单触发工单Rollback
     *
     * @param prepPickingSlipIdList Prep拣货单
     */
    void cancelWithPickingSlip(List<Long> prepPickingSlipIdList);

    /**
     * unpick列表
     *
     * @param workorderIds 工单id
     * @return /
     */
    List<PrepPickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds);

    /**
     * Prep完成确认页面
     *
     * @param query 工单查询参数
     * @return /
     */
    PrepWorkorderFinishConfirmVO finishRollbackConfirm(WorkorderRollbackListQuery query);

    /**
     * 拆单
     *
     * @param splitHolders 拆单参数
     */
    void split(List<OtbWorkorderSplitBO> splitHolders);
}