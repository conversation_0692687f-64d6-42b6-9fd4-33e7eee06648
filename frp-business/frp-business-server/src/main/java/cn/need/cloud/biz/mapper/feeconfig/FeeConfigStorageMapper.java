package cn.need.cloud.biz.mapper.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorage;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigStorageQuery;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigStoragePageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置storage Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Mapper
public interface FeeConfigStorageMapper extends SuperMapper<FeeConfigStorage> {

    /**
     * 根据条件获取仓库报价费用配置storage列表
     *
     * @param query 查询条件
     * @return 仓库报价费用配置storage集合
     */
    default List<FeeConfigStoragePageVO> listByQuery(@Param("qofcs") FeeConfigStorageQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取仓库报价费用配置storage分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 仓库报价费用配置storage集合
     */
    List<FeeConfigStoragePageVO> listByQuery(@Param("qofcs") FeeConfigStorageQuery query, @Param("page") Page<?> page);
}