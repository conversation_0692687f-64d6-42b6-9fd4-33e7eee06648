package cn.need.cloud.biz.service.otc.request.impl;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseShowLogStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.*;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.otc.OtcRequestConverter;
import cn.need.cloud.biz.mapper.otc.OtcRequestMapper;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.entity.product.ProductMultiboxDetail;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestCreateParam;
import cn.need.cloud.biz.model.param.otc.create.workorder.OtcWorkorderCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO;
import cn.need.cloud.biz.model.vo.otc.request.*;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcRequestAuditLogHelper;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.service.otc.request.OtcRequestDetailService;
import cn.need.cloud.biz.service.otc.request.OtcRequestPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBuildService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.product.ProductMultiboxDetailService;
import cn.need.cloud.biz.service.product.ProductMultiboxService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import cn.need.framework.starter.warehouse.util.WarehouseUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC请求服务实现类
 * </p>
 * <p>
 * 该类实现了OTC（Order to Customer）请求的核心业务逻辑，包括请求的创建、查询、更新、审核及状态管理等功能。
 * 提供了对OTC请求的全生命周期管理，支持复杂的请求结构，如多箱产品、包装信息等。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. OTC请求的创建与更新
 * 2. OTC请求的审核与状态管理
 * 3. OTC请求的查询与分页展示
 * 4. OTC请求与工单的关联管理
 * 5. 多箱产品和包装信息的处理
 * 6. 请求日志记录与审计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcRequestServiceImpl extends SuperServiceImpl<OtcRequestMapper, OtcRequest> implements OtcRequestService {

    @Resource
    private AuditShowLogService auditShowLogService;

    /// //////////////////////////////// 公共方法 //////////////////////////////////////

    /**
     * OTC请求详情服务
     * <p>
     * 负责管理OTC请求的详细信息，包括请求中的产品、数量等。
     * 提供了请求详情的批量创建、更新和查询功能。
     * </p>
     */
    @Resource
    private OtcRequestDetailService otcRequestDetailService;

    /**
     * OTC请求包装服务
     * <p>
     * 负责管理OTC请求的包装信息，包括包装的创建、更新和查询。
     * 处理包装标签、包装详情等信息，支持批量操作。
     * </p>
     */
    @Resource
    private OtcRequestPackageService otcRequestPackageService;

    /**
     * 产品服务
     * <p>
     * 负责管理产品信息，包括产品的创建、更新和查询。
     * 在OTC请求中用于验证产品信息、检查产品与合作伙伴的关系等。
     * </p>
     */
    @Resource
    private ProductService productService;

    /**
     * 产品多箱服务
     * <p>
     * 负责管理产品的多箱信息，包括多箱产品的创建、更新和查询。
     * 在OTC请求中用于处理多箱产品的包装和发货信息。
     * </p>
     */
    @Resource
    private ProductMultiboxService productMultiboxService;

    /**
     * 产品多箱详情服务
     * <p>
     * 负责管理产品多箱的详细信息，如多箱中包含的具体产品、数量等。
     * 在OTC请求中用于验证多箱产品的内部组成和数量匹配。
     * </p>
     */
    @Resource
    private ProductMultiboxDetailService productMultiboxDetailService;

    /**
     * 租户缓存服务
     * <p>
     * 负责缓存和提供租户信息，提高系统性能，减少数据库访问。
     * 在OTC请求中用于获取合作伙伴的基本信息，如名称、编码等。
     * </p>
     */
    @Resource
    private TenantCacheService tenantCacheService;

    /**
     * OTC工单构建服务，使用懒加载避免循环依赖
     * <p>
     * 负责根据OTC请求构建工单，实现请求到工单的转换。
     * 在请求审核通过时被调用，创建相应的工单。
     * </p>
     */
    @Lazy
    @Resource
    private OtcWorkorderBuildService otcWorkorderBuildService;

    /**
     * OTC工单服务，使用懒加载避免循环依赖
     * <p>
     * 负责管理OTC工单信息，包括工单的创建、更新和查询。
     * 在OTC请求中用于获取工单状态，并根据工单状态更新请求状态。
     * </p>
     */
    @Lazy
    @Resource
    private OtcWorkorderService otcWorkorderService;
    @Resource
    private OtcWorkorderDetailService otcWorkorderDetailService;

    /**
     * OTC请求服务自身的引用，使用懒加载避免循环依赖
     * <p>
     * 用于在事务中调用自身的方法，特别是需要新事务的方法。
     * 主要用于审核失败时的状态更新和日志记录。
     * </p>
     * <p>
     * // TODO: 重构事务处理方式，避免自引用 by ldz 2024-12-31
     * </p>
     */
    @Resource
    @Lazy
    private OtcRequestServiceImpl otcRequestService;


    /**
     * 根据参数创建新的OTC请求
     * <p>
     * 该方法在事务中执行，确保数据一致性。创建OTC请求时会进行一系列验证：
     * - 检查参数是否完整
     * - 检查产品是否属于指定的合作伙伴
     * - 检查请求参考编号是否唯一
     * - 检查发货方式和运输方式是否匹配
     * - 检查最后发货日期是否有效
     * - 检查请求详情是否完整
     * </p>
     * <p>
     * 创建过程包括：
     * 1. 初始化OTC请求基本信息
     * 2. 插入OTC请求记录
     * 3. 创建请求产品详情
     * 4. 如果需要，创建请求包装信息
     * 5. 设置请求状态和订单类型
     * 6. 记录审计日志
     * </p>
     *
     * @param param OTC请求创建参数，包含请求的基本信息、产品详情和包装信息
     * @return 创建的OTC请求ID
     * @throws BusinessException 如果参数为空或验证失败，则抛出业务异常
     *                           <p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcRequest insertByParam(OtcRequestCreateParam param) {
        // 检查传入OTC请求参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(param)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        if (ObjectUtil.isEmpty(param.getShipFromAddressAddr1())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ShipFromAddress"));
        }
        if (ObjectUtil.isEmpty(param.getShipToAddressAddr1())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ShipToAddress"));
        }
        //todo: 校验放到 param 中
        Long transactionPartnerId = param.getTransactionPartnerId();
        if (ObjectUtil.isEmpty(transactionPartnerId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "transactionPartnerId"));
        }
        //校验产品是否匹配partner
        boolean partnerFlag = productService.isAllProductInPartnerId(transactionPartnerId, param.getDetailList()
                .stream().map(OtcRequestProductVO::getProductId).toList());
        if (!partnerFlag) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "All products must belong to the same partner"));
        }
        // 检查请求参考编号是否有效或唯一
        checkRequestRefNum(param.getRequestRefNum(), transactionPartnerId);

        //校验 shipCarrier 下 shipMethod 是否合规
        // if (!param.getProvideShippingLabelFlag()) {
        //     CheckShipUtil.checkShipCarrierAndMethod(param.getShipCarrier(), param.getShipMethod());
        // }

        //校验 lastShipDate 不能早于7天前
        LocalDateTime lastShipDate = param.getLastShipDate();
        checkLastShipDate(lastShipDate);

        //调用 ship  返回 ship_from_address_is_residential

        //校验 detailList
        List<OtcRequestProductVO> detailList = param.getDetailList();
        if (ObjectUtil.isEmpty(detailList)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "detailList"));
        }
        if (detailList.stream().anyMatch(o -> ObjectUtil.isEmpty(o.getQty()))) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "detailQty"));
        }
        //校验 detailLineNum 唯一
        Set<Integer> detailLineNum = detailList.stream().map(OtcRequestProductVO::getLineNum).collect(Collectors.toSet());
        if (detailLineNum.size() != detailList.size()) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Detail line numbers must be unique"));
        }


        // 获取OTC请求转换器实例，用于将OTC请求参数对象转换为实体对象
        OtcRequestConverter converter = Converters.get(OtcRequestConverter.class);


        // 将OTC请求参数对象转换为实体对象并初始化
        OtcRequest entity = initOtcRequest(converter.toEntity(param));

        //获取新的OTC请求ID
        long otcRequestId = IdWorker.getId();
        entity.setId(otcRequestId);

        // 在事务中执行操作
        WarehouseUtils.execute(entity.getWarehouseId(), () -> {
            // 新增请求产品详情
            otcRequestDetailService.insertBatchByParam(param, otcRequestId);

            // 判断是否需要新增请求包裹
            if (param.getProvideShippingLabelFlag()) {
                insertRequestPackages(param, otcRequestId);
            }
            // otcOrderType
            fillOrderType(param, entity);

            // 是否客户运输要求
            fillCusShipRequire(param, entity);

            //设置状态
            entity.setOtcRequestStatus(RequestStatusEnum.NEW.getStatus());

            //记录log
            recordLog(entity, null);

            // 插入OTC请求实体对象到数据库
            super.insert(entity);

        });
        // 返回OTC请求实体
        return entity;
    }

    /**
     * 根据参数更新OTC请求
     * <p>
     * 该方法在事务中执行，确保数据一致性。更新OTC请求时会进行一系列验证：
     * - 检查参数是否完整
     * - 检查请求是否存在
     * - 检查请求状态是否允许更新（只有新建或拒绝状态可以更新）
     * - 检查产品是否属于指定的合作伙伴
     * - 检查请求参考编号是否唯一
     * - 检查最后发货日期是否有效
     * - 检查发货方式和运输方式是否匹配
     * </p>
     * <p>
     * 更新过程包括：
     * 1. 更新请求产品详情
     * 2. 如果需要，更新请求包装信息
     * 3. 更新请求基本信息
     * 4. 记录变更日志
     * </p>
     *
     * @param param OTC请求更新参数，包含请求的基本信息、产品详情和包装信息
     * @throws BusinessException 如果参数为空、请求不存在或验证失败，则抛出业务异常
     *                           <p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcRequest updateByParam(OtcRequestUpdateParam param) {
        // 检查传入OTC请求参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(param) || ObjectUtil.isEmpty(param.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        OtcRequest otcRequest = super.getById(param.getId());
        //校验参数
        if (ObjectUtil.isEmpty(otcRequest)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcRequest", param.getId()));
        }
        //校验输入状态
        if (!otcRequest.getOtcRequestStatus().equals(RequestStatusEnum.NEW.getStatus()) && !otcRequest.getOtcRequestStatus().equals(RequestStatusEnum.REJECTED.getStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "Request", "New/Rejected", otcRequest.getOtcRequestStatus()));
        }
        //todo: Check 放到 Param 里面
        Long transactionPartnerId = param.getTransactionPartnerId();
        if (ObjectUtil.isEmpty(transactionPartnerId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "TransactionPartnerId"));
        }
        //校验 detailList
        List<OtcRequestProductVO> detailList = param.getDetailList();
        if (ObjectUtil.isEmpty(detailList)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "detailList"));
        }
        //校验产品是否匹配partner
        boolean partnerFlag = productService.isAllProductInPartnerId(transactionPartnerId, detailList
                .stream().map(OtcRequestProductVO::getProductId).toList());
        if (!partnerFlag) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Product must belong to this Partner"));
        }
        // 校验输入的requestRefNum和transactionPartnerId是否与数据库中的数据匹配
        if (!otcRequest.getRequestRefNum().equals(param.getRequestRefNum()) || !otcRequest.getTransactionPartnerId().equals(transactionPartnerId)) {
            checkRequestRefNum(param.getRequestRefNum(), transactionPartnerId);
        }
        //校验 lastShipDate 不能早于7天前
        LocalDateTime lastShipDate = param.getLastShipDate();
        checkLastShipDate(lastShipDate);

        // //校验 shipCarrier 下 shipMethod 是否合规
        // if (!param.getProvideShippingLabelFlag()) {
        //     CheckShipUtil.checkShipCarrierAndMethod(param.getShipCarrier(), param.getShipMethod());
        // }

        //校验 detailLineNum 唯一
        Set<Integer> detailLineNum = detailList.stream().map(OtcRequestProductVO::getLineNum).collect(Collectors.toSet());
        if (detailLineNum.size() != detailList.size()) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Detail line numbers must be unique"));
        }

        //获取更新前的vo
        OtcRequestVO oldVo = detailById(param.getId());

        WarehouseUtils.executeIgnore(() -> {
            // 判断是否需要更新请求包裹
            if (param.getProvideShippingLabelFlag()) {
                // 检查包裹列表，确保包裹信息完整且符合更新条件
                checkPackageList(param);

                // 批量修改包裹信息，根据请求参数进行更新
                otcRequestPackageService.updateBatchByRequestParam(param);
            }

            // 新增或更新请求产品详情，根据参数信息进行批量处理
            otcRequestDetailService.updateBatchByParam(param);

            // 获取OTC请求转换器实例，用于将OTC请求参数对象转换为实体对象
            OtcRequestConverter converter = Converters.get(OtcRequestConverter.class);

            // 使用转换器将OTC请求参数对象转换为实体对象，以便进行后续操作
            OtcRequest entity = converter.toEntity(param);

            // 填充订单类型信息，根据请求参数更新实体对象中的订单类型字段
            fillOrderType(param, entity);

            // 填充客户发货要求信息，根据请求参数更新实体对象中的客户发货要求字段
            fillCusShipRequire(param, entity);

            // 设置仓库ID，直接将请求参数中的仓库ID赋值给实体对象
            entity.setWarehouseId(param.getWarehouseId());
            // 执行更新OTC请求操作
            super.update(entity);
            // 如果由投保变为不投保，则更新投保金为null
            if (ObjectUtil.isNotEmpty(otcRequest.getInsuranceAmountAmount()) && ObjectUtil.isEmpty(entity.getInsuranceAmountAmount())) {
                mapper.updateInsuranceAmount(entity);
            }

            //获取更新后的Vo
            OtcRequestVO newVo = detailById(entity.getId());
            //记录日志
            OtcRequestAuditLogHelper.recordLog(
                    BeanUtil.copyNew(newVo, OtcRequest.class),
                    BaseShowLogStatusEnum.MODIFIED.getStatus(),
                    BaseTypeLogEnum.STATUS.getType(),
                    ModifyCompareUtil.recordModifyLog(newVo, oldVo),
                    null
            );
        });
        return otcRequest;
    }

    @Override
    public List<OtcRequestPageVO> listByQuery(OtcRequestQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 分页查询OTC请求
     * <p>
     * 该方法实现了OTC请求的分页查询功能，支持根据多种条件进行过滤和排序。
     * 查询结果会填充合作伙伴信息，便于前端展示。
     * </p>
     * <p>
     * 查询过程包括：
     * 1. 根据分页参数和查询条件构建分页对象
     * 2. 调用Mapper执行分页查询
     * 3. 为查询结果填充合作伙伴信息
     * 4. 返回带分页信息的结果集
     * </p>
     *
     * @param search 包含查询条件和分页信息的对象
     * @return 带分页信息的OTC请求列表
     * <p>
     * // TODO: 统一使用WarehouseCache方式填充合作伙伴信息 by ldz 2024-11-30
     * </p>
     */
    @Override
    public PageData<OtcRequestPageVO> pageByQuery(PageSearch<OtcRequestQuery> search) {
        Page<OtcRequest> page = Conditions.page(search, entityClass);
        List<OtcRequestPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        dataList.forEach(otcRequestPageVO ->
                {
                    // 填充交易伙伴信息
                    // 注意：这里应该使用与WarehouseCache相同的方式进行填充
                    if (ObjectUtil.isNotEmpty(tenantCacheService.getById(otcRequestPageVO.getTransactionPartnerId()))) {
                        otcRequestPageVO.setTransactionPartnerVO(BeanUtil.copyNew(tenantCacheService.getById(otcRequestPageVO.getTransactionPartnerId())
                                , BasePartnerVO.class));
                    }
                }
        );
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取OTC请求详情
     * <p>
     * 该方法获取指定ID的OTC请求详细信息，包括请求的基本信息、产品详情和包装信息。
     * 查询结果会填充产品信息、合作伙伴信息和仓库信息，便于前端展示。
     * </p>
     * <p>
     * 查询过程包括：
     * 1. 调用Mapper获取请求详情
     * 2. 检查请求是否存在
     * 3. 填充产品信息
     * 4. 设置支付标志
     * 5. 填充包装信息
     * 6. 填充合作伙伴信息
     * 7. 填充仓库信息
     * </p>
     *
     * @param id OTC请求的ID
     * @return 包含详细信息的OTC请求视图对象
     * @throws BusinessException 如果请求不存在，则抛出业务异常
     */
    @Override
    public OtcRequestVO detailById(Long id) {
        OtcRequestVO otcRequestVO = mapper.detailById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(otcRequestVO)) {
            throw new BusinessException("id: " + id + " not found in OtcRequest");
        }
        //填充产品信息
        ProductCacheUtil.filledProduct(otcRequestVO.getDetailList());
        //填充SetPayment
        otcRequestVO.setSetPaymentFlag(StringUtil.isEmpty(otcRequestVO.getInsuranceAmountCurrency()) ? Boolean.FALSE : Boolean.TRUE);
        //填充包裹信息
        otcRequestVO.getPackageList().forEach(o -> {
            if (ObjectUtil.isNotEmpty(o.getPackageMultiboxProductId())) {
                o.setPackageMultiboxProductVo(
                        BeanUtil.copyNew(ProductCacheUtil.getById(o.getPackageMultiboxProductId()), BaseProductVO.class)
                );
            }
            ProductCacheUtil.filledProduct(o.getDetailList());
        });
        //填充商家信息
        otcRequestVO.setTransactionPartnerVO(BeanUtil.copyNew(tenantCacheService.getById(otcRequestVO.getTransactionPartnerId())
                , BasePartnerVO.class));
        //填充仓库信息
        WarehouseCacheUtil.filledWarehouse(otcRequestVO);
        return otcRequestVO;
    }

    @Override
    public OtcRequestVO detailByRefNum(String refNum) {
        OtcRequest entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtcRequest");
        }
        return buildOtcRequestVO(entity);
    }

    @Override
    public Map<Long, OtcRequestVO> detailByIds(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        //todo: 需要修改 返回类的 类型，这个并没有返回所有的实体
        List<OtcRequest> requests = lambdaQuery()
                .in(IdModel::getId, ids)
                .list();
        return requests.stream()
                .map(this::buildOtcRequestVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OtcRequestVO::getId, obj -> obj));
    }

    @Override
    public void updateRequestStatus(String requestRefNum, RequestStatusEnum status) {
        OtcRequest one = lambdaQuery().eq(OtcRequest::getRequestRefNum, requestRefNum).one();
        one.setOtcRequestStatus(status.getStatus());
        if (ObjectUtil.isEmpty(one)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Request", requestRefNum));
        }
        //更新状态
        super.update(one);
        //记录日志
        recordLog(one, null);
    }

    /**
     * 批量审核OTC请求
     * <p>
     * 该方法在事务中执行，确保数据一致性。审核OTC请求时会进行以下操作：
     * - 检查请求ID列表是否有效
     * - 检查请求状态是否允许审核（只有新建或拒绝状态可以审核）
     * - 根据审核类型进行不同的处理
     * </p>
     * <p>
     * 审核类型包括：
     * 1. 拒绝（REJECTED）：将请求状态更新为拒绝，并记录日志
     * 2. 批准（APPROVED）：尝试创建工单，如果成功则将请求状态更新为批准，否则更新为拒绝并记录失败原因
     * </p>
     * <p>
     * 批准过程使用分布式锁确保并发安全，避免重复处理同一请求。
     * </p>
     *
     * @param type 审核类型，可以是“拒绝”或“批准”
     * @param note 审核备注，可以是拒绝原因或其他说明
     * @param ids  要审核的OTC请求ID列表
     * @throws BusinessException 如果ID列表为空、请求不存在、状态不允许审核或创建工单失败，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAudit(String type, String note, List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ids"));
        }
        if (ids.size() > 1) {
            throw new BusinessException("Current Only support one request at a time");
        }
        List<OtcRequest> otcRequests = mapper.selectBatchIds(ids);
        if (otcRequests.size() != ids.size()) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcRequest", ids));
        }
        //todo: 移动到 可以AuditStatus 到 StatusEnnum 里面
        //校验输入状态
        Optional<OtcRequest> invalidRequest = otcRequests.stream()
                .filter(r -> !r.getOtcRequestStatus().equals(RequestStatusEnum.NEW.getStatus())
                        && !r.getOtcRequestStatus().equals(RequestStatusEnum.REJECTED.getStatus()))
                .findFirst();

        if (invalidRequest.isPresent()) {
            throw new BusinessException(String.format(
                    ErrorMessages.STATUS_REQUIRED,
                    "Request",
                    "New/Rejected",
                    invalidRequest.get().getOtcRequestStatus()
            ));
        }
        // 赋值
        if (OtcAuditTypeEnum.REJECTED.getType().equals(type)) {
            for (OtcRequest otcRequest : otcRequests) {
                RedissonKit.getInstance().lock(RedisConstant.OTC_REQUEST_REJECT_LOCK_PREFIX + otcRequest.getId(), lock -> {
                    otcRequest.setOtcRequestStatus(RequestStatusEnum.REJECTED.getStatus());
                    // 记录日志
                    recordLog(otcRequest, note);
                });
            }
            // 更新otcRequest
            super.updateBatch(otcRequests);
        } else {
            // try catch, 审核失败是抛异常
            List<Long> requestIdList = otcRequests.stream().map(OtcRequest::getId).toList();
            for (Long requestId : requestIdList) {
                RedissonKit.getInstance().lock(RedisConstant.OTC_WORK_ORDER_CREATE_LOCK_PREFIX + requestId, lock -> {
                    OtcWorkorderCreateParam otcWorkorderCreateParam = new OtcWorkorderCreateParam();
                    otcWorkorderCreateParam.setOtcRequestId(requestId);
                    Optional<OtcRequest> otcRequestOptional = otcRequests.stream().filter(request -> request.getId().equals(requestId)).findFirst();
                    try {
                        otcWorkorderBuildService.insertByParam(otcWorkorderCreateParam);
                    } catch (Exception e) {

                        // 将对应的otcRequest状态改为rejected
                        otcRequestOptional.ifPresent(otcRequest -> {
                            otcRequest.setOtcRequestStatus(RequestStatusEnum.REJECTED.getStatus());
                            //审核失败 更新状态
                            otcRequestService.updateByApproveFail(otcRequest, e.getMessage(), note);
                        });
                        String requestRefNum = otcRequestOptional.map(OtcRequest::getRequestRefNum).orElse("null");
                        throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Review failed for request " + requestRefNum + ": " + e.getMessage()));
                    }
                });

            }
            for (OtcRequest otcRequest : otcRequests) {
                RedissonKit.getInstance().lock(RedisConstant.OTC_REQUEST_APPROVE_LOCK_PREFIX + otcRequest.getId(), lock -> {
                    otcRequest.setOtcRequestStatus(RequestStatusEnum.APPROVED.getStatus());
                    // 记录日志
                    recordLog(otcRequest, note);
                });
            }
            // 更新otcRequest
            super.updateBatch(otcRequests);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateByApproveFail(OtcRequest otcRequest, String reason, String note) {
        super.update(otcRequest);
        // 记录状态日志
        // 额外添加 失败原因
        OtcRequestAuditLogHelper.recordLog(
                otcRequest,
                RequestStatusEnum.APPROVED.getStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                null,
                note
        );
        OtcRequestAuditLogHelper.recordLog(
                otcRequest,
                RequestStatusEnum.REJECTED.getStatus(),
                BaseTypeLogEnum.STATUS.getType(),
                reason,
                null
        );

        //获取并清空线程日志
        List<AuditShowLog> andClearShowLog = AuditLogHolder.getAndClearShowLog();

        //持久化日志
        auditShowLogService.insertBatch(andClearShowLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shipped(List<Long> requestIdList) {
        List<OtcRequest> requests = this.listByIds(requestIdList);
        if (ObjectUtil.isEmpty(requests)) {
            return;
        }

        List<OtcWorkorder> workorderList = otcWorkorderService.listByRequestIds(requestIdList);
        var workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);
        var requestMap = StreamUtils.toMap(requests, IdModel::getId);

        final String unionKey = "{}:{}";
        var productFinishQtyMap = otcWorkorderDetailService.listByWorkOrderIds(workorderMap.keySet())
                .stream()
                .collect(Collectors.groupingBy(obj -> StringUtil.format(unionKey,
                                // 根据请求单id + 产品id 分组
                                requestMap.get(workorderMap.get(obj.getOtcWorkorderId()).getOtcRequestId()).getId(),
                                obj.getProductId()
                        ),
                        Collectors.summingInt(OtcWorkorderDetail::getFinishQty)
                ));

        var requestDetails = otcRequestDetailService.listByRequestIds(requestIdList);

        // 设置FinishQty
        requestDetails.forEach(obj -> obj.setFinishQty(productFinishQtyMap.getOrDefault(
                StringUtil.format(unionKey, obj.getOtcRequestId(), obj.getProductId()), 0))
        );

        // 更新Details
        Validate.isTrue(otcRequestDetailService.updateBatch(requestDetails) == requestDetails.size(),
                "Update OtcRequestDetail FinishQty Failed"
        );

        // 处理请求状态
        var statusList = Collections.singletonList(OtcWorkorderStatusEnum.SHIPPED.getStatus());
        var requestStatus = RequestStatusEnum.PROCESSED;
        List<Long> shippedIdList = workorderList.stream()
                .collect(Collectors.groupingBy(OtcWorkorder::getOtcRequestId))
                .entrySet()
                .stream()
                // 工单全部准备发货
                .filter(entry -> entry.getValue()
                        .stream()
                        .allMatch(obj -> statusList.contains(obj.getOtcWorkorderStatus())))
                .map(Map.Entry::getKey)
                .toList();
        if (ObjectUtil.isEmpty(shippedIdList)) {
            return;
        }
        // 设置Shipped
        List<OtcRequest> shippedRequestList = requests.stream()
                .filter(obj -> shippedIdList.contains(obj.getId()))
                .collect(Collectors.toList());

        shippedRequestList.forEach(obj -> {
            obj.setOtcRequestStatus(requestStatus.getStatus());
            obj.setProcessEndTime(TimeUtils.now());
            obj.setFeeStatus(FeeStatusEnum.NEW.getStatus());
        });
        Validate.isTrue(super.updateBatch(shippedRequestList) == shippedRequestList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update all OtcRequest statuses to " + requestStatus.getStatus())
        );

        // 请求单：READY_TO_SHIP日志
        OtcRequestAuditLogHelper.recordLog(shippedRequestList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readyToShip(Long otcRequestId) {
        this.updateStatusWithAllWorkOrderList(Collections.singletonList(otcRequestId),
                Arrays.asList(OtcWorkorderStatusEnum.READY_TO_SHIP.getStatus(), OtcWorkorderStatusEnum.SHIPPED.getStatus()),
                RequestStatusEnum.READY_TO_SHIP
        );
    }

    @Override
    public Boolean existUnfinishedOrder(Long warehouseId) {
        return WarehouseUtils.execute(warehouseId, () -> {
            Long count = lambdaQuery().ne(OtcRequest::getOtcRequestStatus, RequestStatusEnum.PROCESSED.getStatus()).count();
            return count > 0;
        });
    }

    /**
     * 填充 CusShipRequire
     *
     * @param param  参数
     * @param entity 实体
     */
    private void fillCusShipRequire(OtcRequestCreateParam param, OtcRequest entity) {
        boolean cusShipRequireFlag = param.getPackageList().stream().anyMatch(o -> o.getLabelList().size() > 1)
                || ObjectUtil.isNotEmpty(param.getNote());
        entity.setHasCusShipRequire(cusShipRequireFlag);
    }

    /**
     * 填充 OrderType
     *
     * @param param  参数
     * @param entity 实体
     */
    private void fillOrderType(OtcRequestCreateParam param, OtcRequest entity) {
        boolean otcOrderTypeFlag = param.getDetailList().size() == NumberUtils.INTEGER_ONE
                && Objects.equals(param.getDetailList().get(NumberUtils.INTEGER_ZERO).getQty(), NumberUtils.INTEGER_ONE);
        String otcOrderType = otcOrderTypeFlag ? OtcOrderTypeEnum.SOSP.getStatus()
                : OtcOrderTypeEnum.SOMP.getStatus();
        //填充实体
        entity.setOrderType(otcOrderType);
    }

    /**
     * 新增请求包裹
     *
     * @param param 包含请求包详情相关信息的参数对象这包括了请求包的必要信息，如请求包ID、请求类型等
     */
    private void insertRequestPackages(OtcRequestCreateParam param, Long otcRequestId) {
        //校验包裹集有效性
        checkPackageList(param);
        // 获取仓库ID
        Long warehouseId = WarehouseContextHolder.getWarehouseId();
        //批量新增包裹
        otcRequestPackageService.insertBatchByRequestParam(param.getPackageList(), otcRequestId, warehouseId);
    }

    /**
     * 校验包裹集有效性 参数是否匹配
     *
     * @param param 包含包名列表的请求创建参数对象，用于创建OTC请求
     */
    private void checkPackageList(OtcRequestCreateParam param) {
        List<OtcRequestPackageFullVO> packageList = param.getPackageList();

        //todo: 移动一些Check到 Param 中

        // 校验list
        if (ObjectUtil.isEmpty(packageList)) {

            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "packageList"));
        }
        packageList.forEach(o -> {
            if (ObjectUtil.isEmpty(o.getDetailList()) || ObjectUtil.isEmpty(o.getLabelList())) {

                throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "packageDetailList and packageLabelList"));
            }
        });
        //校验packageLineNum 唯一
        Set<Integer> packageLineNum = packageList.stream().map(OtcRequestPackageFullVO::getLineNum).collect(Collectors.toSet());
        if (packageLineNum.size() != packageList.size()) {

            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Package line numbers must be unique"));
        }
        //校验package 中detailLineNum 唯一
        packageList.forEach(o -> {
            //校验package 中detailLineNum 唯一
            Set<Integer> detailLineNumList = o.getDetailList().stream().map(OtcRequestPackageDetailFullVO::getLineNum).collect(Collectors.toSet());
            if (o.getDetailList().size() != detailLineNumList.size()) {

                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Package detail line numbers must be unique"));
            }
            //校验package 中labelLineNum 唯一
            Set<Integer> labelLineNumList = o.getLabelList().stream().map(OtcRequestPackageLabelFullVO::getLineNum).collect(Collectors.toSet());
            if (o.getLabelList().size() != labelLineNumList.size()) {
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Package label line numbers must be unique"));
            }
            //校验package 中label 规范
            boolean b = o.getLabelList().stream().anyMatch(label -> label.getLabelType().equals(OtcLabelTypeEnum.SHIPPING_LABEL.getType())
                    && label.getPaperType().equals(OtcPaperTypeEnum.LABEL_4X6.getType()));
            if (!b) {
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Shipping label type and paper type must be matched"));
            }
        });

        //校验产品详情和包详情信息匹配
        checkPackagesDetail(param);
    }

    /**
     * 校验产品详情和包详情信息匹配
     *
     * @param param 创建参数
     */
    private void checkPackagesDetail(OtcRequestCreateParam param) {
        //取出两个集合
        List<OtcRequestProductVO> productDetailList = param.getDetailList();
        List<OtcRequestPackageFullVO> packageList = param.getPackageList();
        //不是多箱的产品
        Map<Long, Integer> productDetailMap = productDetailList.stream().filter(o -> !ProductCacheUtil.getById(o.getProductId()).getMultiboxFlag())
                .collect(Collectors.groupingBy(OtcRequestProductVO::getProductId, Collectors.summingInt(OtcRequestProductVO::getQty)));
        Map<Long, Integer> packageMap = packageList.stream().filter(o -> !o.getMultiBoxPackageFlag())
                .flatMap(packageInfoVO -> packageInfoVO.getDetailList().stream())
                .collect(Collectors.groupingBy(OtcRequestPackageDetailFullVO::getProductId, Collectors.summingInt(OtcRequestPackageDetailFullVO::getQty)));
        //校验不是多箱的产品，包裹数量匹配
        if (!productDetailMap.entrySet().equals(packageMap.entrySet())) {
            Collection<Map.Entry<Long, Integer>> symmetricDiff = CollectionUtils.disjunction(productDetailMap.entrySet(), packageMap.entrySet());
            // 遍历对称差集，分析具体差异
            for (Map.Entry<Long, Integer> entry : symmetricDiff) {
                Long key = entry.getKey();
                String refNum = ProductCacheUtil.getById(key).getRefNum();
                Integer valueInProductDetailMap = productDetailMap.get(key);
                Integer valueInPackageMap = packageMap.get(key);
                if (ObjectUtil.isEmpty(valueInProductDetailMap)) {
                    throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "Product", "RefNum", refNum));
                } else if (ObjectUtil.isEmpty(valueInPackageMap)) {
                    throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "Package Detail", "RefNum", refNum));
                } else {
                    throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Product " + refNum + " quantity mismatch: Details Total Qty is " + valueInProductDetailMap + ", but Package Detail Total Qty is " + valueInPackageMap));
                }
            }
        }
        Map<Long, Integer> productMultiboxMap = productDetailList.stream().filter(o -> ProductCacheUtil.getById(o.getProductId()).getMultiboxFlag())
                .collect(Collectors.groupingBy(OtcRequestProductVO::getProductId, Collectors.summingInt(OtcRequestProductVO::getQty)));
        if (!productMultiboxMap.isEmpty()) {
            //校验多箱的产品
            checkMultiboxPart(productMultiboxMap, packageList);
        }

    }

    /**
     * 检查多箱产品部分的正确性
     * <p>
     * 该方法用于验证OTC请求中多箱产品的数量和组成是否符合要求。
     * 验证过程包括以下几个关键步骤：
     * 1. 根据产品ID获取其多箱信息和多箱详情
     * 2. 根据包装信息构建多箱产品的映射关系
     * 3. 检查多箱产品的数量是否匹配
     * 4. 检查多箱产品的UPC是否匹配
     * 5. 检查多箱产品内部详情是否匹配
     * </p>
     * <p>
     * 该方法的复杂性在于需要处理多层嵌套的数据结构，包括产品、多箱、UPC和子产品等多个层次。
     * </p>
     *
     * @param productMultiboxMap 一个映射，包含产品ID和对应的多箱数量
     * @param packageList        一个列表，包含包装的详细信息，用于验证多箱包装的数量和组成
     * @throws BusinessException 如果多箱产品的数量或组成不匹配，则抛出业务异常
     *                           <p>
     *                           // TODO: 优化lambda表达式中的数据库查询，避免多次查询 by ldz 2024-11-15
     *                           优化建议：将方法调用提取到lambda外部，使用批量查询方法减少数据库访问
     */
    private void checkMultiboxPart(Map<Long, Integer> productMultiboxMap, List<OtcRequestPackageFullVO> packageList) {
        // TODO: 优化lambda表达式中的方法调用，使用批量查询方法减少数据库访问 by ldz 2024-11-15
        //todo: 这里要做非空验证，前面没有对 MultiBox 必须字段做Check
        // 构建主产品ID下多箱UPC对应的配产品ID及数量的嵌套Map
        // 结构：主产品ID -> UPC -> (配产品ID -> 数量)
        Map<Long, Map<String, Map<Long, Integer>>> productMap = productMultiboxMap.keySet().stream().collect(Collectors.toMap(o -> o
                , o -> productMultiboxService.getProductMultiboxListByProductId(o).stream().collect(Collectors.toMap(ProductMultibox::getUpc
                        , o1 -> productMultiboxDetailService.getListByMultiboxId(o1.getId()).stream()
                                .collect(Collectors.groupingBy(ProductMultiboxDetail::getProductId, Collectors.summingInt(ProductMultiboxDetail::getQty)))))));

        Set<OtcRequestPackageFullVO> packageMultiBoxList = packageList.stream().filter(OtcRequestPackageFullVO::getMultiBoxPackageFlag).collect(Collectors.toSet());
        // 校验包裹中的多箱产品与产品详情内容是否匹配
        // 构建包裹信息Map，结构：产品ID -> (UPC -> 数量)
        Map<Long, Map<String, Long>> packageInfoMap = packageMultiBoxList.stream()
                .collect(Collectors.groupingBy(
                        OtcRequestPackageFullVO::getPackageMultiboxProductId,
                        Collectors.groupingBy(
                                OtcRequestPackageFullVO::getPackageMultiboxUpc,
                                Collectors.counting()
                        )
                ));


        Map<String, Map<Long, Integer>> upcCheckMap = packageMultiBoxList.stream()
                // 先根据packageMultiboxUpc进行外层分组
                .collect(Collectors.groupingBy(OtcRequestPackageFullVO::getPackageMultiboxUpc,
                        // 针对每个外层分组的元素，进一步处理detailList
                        Collectors.mapping(
                                pkg -> pkg.getDetailList().stream()
                                        // 对每个detailList，根据productId进行分组并聚合qty
                                        .collect(Collectors.groupingBy(
                                                OtcRequestPackageDetailFullVO::getProductId,
                                                Collectors.summingInt(OtcRequestPackageDetailFullVO::getQty)))
                                , Collectors.toList())))
                // 进行校验并转换结构
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            List<Map<Long, Integer>> list = entry.getValue();
                            // 校验list中的元素是否都相同
                            if (list.stream().distinct().count() == NumberUtils.INTEGER_ONE) {
                                return list.get(NumberUtils.INTEGER_ZERO);
                            }
                            throw new IllegalStateException("The same UPC:" + entry.getKey() + " has different productList");
                        }
                ));

        // 初步校验
        if (packageInfoMap.size() != productMap.size()) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox product configuration mismatch"));
        }
        // 层层校验
        packageInfoMap.forEach((productId, upcMap) -> {
            if (!productMap.containsKey(productId)) {
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox product configuration mismatch"));
            }
            Map<String, Map<Long, Integer>> upcChildProductMap = productMap.get(productId);
            // 产品多箱里面的upc是否相同
            if (!upcMap.keySet().equals(upcChildProductMap.keySet())) {
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox product configuration mismatch"));
            }
            List<Long> list = upcMap.values().stream().distinct().toList();
            if (list.size() != 1) {
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox product configuration mismatch"));
            }
            Long qty = list.get(NumberUtils.INTEGER_ZERO);
            //校验产品数量是否匹配
            if (!productMultiboxMap.get(productId).equals(qty.intValue())) {
                ProductCache productCache = ProductCacheUtil.getById(productId);
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox product " + productCache.getRefNum() + " quantity mismatch"));
            }
            //校验多箱upc内部详情是否匹配
            boolean multiBoxDetailMatchFlag = upcMap.keySet().stream().anyMatch(upc ->
                    !productMap.get(productId).get(upc).entrySet()
                            .equals(upcCheckMap.get(upc).entrySet())
            );
            if (multiBoxDetailMatchFlag) {
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "MultiBox detail configuration mismatch"));
            }
        });
    }

    /**
     * 记录日志状态
     * 如果备注为null则无备注
     *
     * @param otcRequest 请求
     */
    private void recordLog(OtcRequest otcRequest, String note) {
        OtcRequestAuditLogHelper.recordLog(otcRequest, null, note);
    }


    /**
     * 检查请求参考编号是否有效或唯一
     *
     * @param requestRefNum        请求的参考编号，用于标识一个特定的请求
     * @param transactionPartnerId 交易伙伴的ID，表示与请求相关联的特定交易伙伴
     */
    private void checkRequestRefNum(String requestRefNum, Long transactionPartnerId) {
        //忽略仓库
        boolean ignore = WarehouseContextHolder.isIgnore();
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        List<OtcRequest> requestList = null;
        try {
            requestList = lambdaQuery()
                    //.eq(OtcRequest::getTransactionPartnerId, transactionPartnerId)
                    .eq(OtcRequest::getRequestRefNum, requestRefNum)
                    .list();
        } catch (Exception e) {
            // 处理异常
            log.error("Error occurred while checking requestRefNum", e);
        } finally {
            WarehouseContextHolder.setIgnore(ignore);
        }
        if (ObjectUtil.isNotEmpty(requestList)) {
            String refNumLog = requestList.stream()
                    .map(OtcRequest::refNumLog)
                    .collect(Collectors.joining(","));
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "OtcRequest", "requestRefNum", "the provided value. Existing refNums: " + refNumLog));
        }
    }

    /**
     * 检查发货日期
     *
     * @param lastShipDate 该日期用于确定是否满足发货的时间要求
     */
    private void checkLastShipDate(LocalDateTime lastShipDate) {
        if (ObjectUtil.isEmpty(lastShipDate)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "lastShipDate"));
        }
        if (lastShipDate.isBefore(TimeUtils.now().minusDays(7))) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_INVALID, "lastShipDate", "Cannot be earlier than 7 days from the current time"));
        }
    }

    /**
     * 构建OTC请求VO对象
     *
     * @param entity OTC请求对象
     * @return 返回包含详细信息的OTC请求VO对象
     */
    private OtcRequestVO buildOtcRequestVO(OtcRequest entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC请求VO对象
        return Converters.get(OtcRequestConverter.class).toVO(entity);
    }

    /**
     * 初始化OTC请求对象
     * <p>
     * 该方法用于设置OTC请求对象的必要参数，确保其处于有效状态。
     * 主要工作包括：
     * 1. 检查传入的OTC请求对象是否为空
     * 2. 生成唯一的请求编号（RefNum）
     * </p>
     * <p>
     * 请求编号的生成规则基于仓库前缀和请求类型，确保在系统中的唯一性。
     * </p>
     *
     * @param entity OTC请求对象，不应为空
     * @return 返回初始化后的OTC请求对象
     * @throws BusinessException 如果传入的OTC请求对象为空，则抛出业务异常
     */
    private OtcRequest initOtcRequest(OtcRequest entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "OtcRequest"));
        }

        // 生成RefNum
        entity.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.OTC_REQUEST.code, WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 根据工单状态更新请求单状态
     * <p>
     * 该方法用于根据工单的状态更新相关联的OTC请求状态。
     * 实现了工单状态与请求状态的同步更新，确保数据一致性。
     * </p>
     * <p>
     * 更新过程包括：
     * 1. 根据请求ID列表获取相关的工单信息
     * 2. 按请求ID分组工单信息
     * 3. 筛选出所有工单状态都符合指定状态的请求ID
     * 4. 更新这些请求的状态
     * 5. 记录状态变更日志
     * </p>
     *
     * @param otcRequestIdList 要更新的OTC请求ID列表
     * @param statusList       工单状态列表，用于筛选符合条件的工单
     * @param requestStatus    要更新到的请求状态
     *                         <p>
     */
    private void updateStatusWithAllWorkOrderList(List<Long> otcRequestIdList,
                                                  List<String> statusList,
                                                  RequestStatusEnum requestStatus) {
        // 根据请求单分组
        Map<Long, List<OtcWorkorder>> workOrderMap = otcWorkorderService.groupByRequestIdList(otcRequestIdList);
        List<Long> shippedIdList = workOrderMap.entrySet()
                .stream()
                // 工单全部准备发货
                .filter(entry -> entry.getValue()
                        .stream()
                        .allMatch(obj -> statusList.contains(obj.getOtcWorkorderStatus())))
                .map(Map.Entry::getKey)
                .toList();
        if (ObjectUtil.isEmpty(shippedIdList)) {
            return;
        }
        List<OtcRequest> shippedRequestList = listByIds(shippedIdList);
        shippedRequestList.forEach(obj -> {
            obj.setOtcRequestStatus(requestStatus.getStatus());
            if (requestStatus == RequestStatusEnum.PROCESSED) {
                obj.setProcessEndTime(TimeUtils.now());
                obj.setFeeStatus(FeeStatusEnum.NEW.getStatus());
            }
        });
        Validate.isTrue(super.updateBatch(shippedRequestList) == shippedRequestList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update all OtcRequest statuses to " + requestStatus.getStatus())
        );

        // 请求单：READY_TO_SHIP日志
        OtcRequestAuditLogHelper.recordLog(shippedRequestList);
    }

}
