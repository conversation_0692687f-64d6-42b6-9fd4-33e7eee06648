package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeOtc;
import cn.need.cloud.biz.model.param.fee.create.FeeOtcCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtcUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtcQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtcVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtcPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用otc service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeOtcService extends SuperService<FeeOtc> {

    /**
     * 根据参数新增费用otc
     *
     * @param createParam 请求创建参数，包含需要插入的费用otc的相关信息
     * @return 费用otcID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeOtcCreateParam createParam);


    /**
     * 根据参数更新费用otc
     *
     * @param updateParam 请求创建参数，包含需要更新的费用otc的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeOtcUpdateParam updateParam);

    /**
     * 根据查询条件获取费用otc列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用otc对象的列表(分页)
     */
    List<FeeOtcPageVO> listByQuery(FeeOtcQuery query);

    /**
     * 根据查询条件获取费用otc列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用otc对象的列表(分页)
     */
    PageData<FeeOtcPageVO> pageByQuery(PageSearch<FeeOtcQuery> search);

    /**
     * 根据ID获取费用otc
     *
     * @param id 费用otcID
     * @return 返回费用otcVO对象
     */
    FeeOtcVO detailById(Long id);

    /**
     * 根据费用otc唯一编码获取费用otc
     *
     * @param refNum 费用otc唯一编码
     * @return 返回费用otcVO对象
     */
    FeeOtcVO detailByRefNum(String refNum);


}