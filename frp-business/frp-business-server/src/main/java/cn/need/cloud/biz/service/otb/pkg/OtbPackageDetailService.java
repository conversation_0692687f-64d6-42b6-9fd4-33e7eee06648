package cn.need.cloud.biz.service.otb.pkg;

import cn.need.cloud.biz.model.bo.otb.OtbBuildPackageContextBo;
import cn.need.cloud.biz.model.entity.otb.OtbPackageDetail;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageDetailQuery;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageDetailVO;
import cn.need.cloud.biz.model.vo.page.OtbPackageDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * OTB包裹详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbPackageDetailService extends SuperService<OtbPackageDetail> {

    /**
     * 根据查询条件获取OTB包裹详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB包裹详情对象的列表(分页)
     */
    List<OtbPackageDetailPageVO> listByQuery(OtbPackageDetailQuery query);

    /**
     * 根据查询条件获取OTB包裹详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB包裹详情对象的列表(分页)
     */
    PageData<OtbPackageDetailPageVO> pageByQuery(PageSearch<OtbPackageDetailQuery> search);

    /**
     * 根据ID获取OTB包裹详情
     *
     * @param id OTB包裹详情ID
     * @return 返回OTB包裹详情VO对象
     */
    OtbPackageDetailVO detailById(Long id);


    /**
     * 根据OTB包裹id获取OTB包裹详情集合
     *
     * @param otbPackageId OTB包裹id
     * @return OTB包裹详情集合
     */
    List<OtbPackageDetailVO> listByOtbPackageId(Long otbPackageId);


    /**
     * 生成包裹详情
     *
     * @param contextBo 上下文信息
     */
    void generateDetail(OtbBuildPackageContextBo contextBo);

    /**
     * 根据包裹id获取详情
     *
     * @param otbPackageIdList 包裹id
     * @return 包裹详情集合
     */
    List<OtbPackageDetail> listByOtbPackageId(List<Long> otbPackageIdList);

    /**
     * 根据包裹id获取详情
     *
     * @param idList 包裹id
     * @return 包裹详情
     */
    List<OtbPackageDetailVO> listByOtbPackageId(Set<Long> idList);
}