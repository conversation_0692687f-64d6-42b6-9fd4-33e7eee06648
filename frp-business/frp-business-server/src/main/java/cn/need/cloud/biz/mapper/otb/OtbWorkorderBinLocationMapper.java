package cn.need.cloud.biz.mapper.otb;

import cn.need.cloud.biz.model.entity.otb.OtbWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbWorkorderBinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OTC工单仓储位置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface OtbWorkorderBinLocationMapper extends SuperMapper<OtbWorkorderBinLocation> {

    /**
     * 根据条件获取OTC工单仓储位置分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return OTC工单仓储位置集合
     */
    List<OtbWorkorderBinLocationPageVO> listByQuery(@Param("qo") OtbWorkOrderBinLocationQuery query, @Param("page") Page<?> page);
}