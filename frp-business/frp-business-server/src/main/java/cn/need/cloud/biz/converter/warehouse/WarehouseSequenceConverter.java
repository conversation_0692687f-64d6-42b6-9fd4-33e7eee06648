package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.warehouse.WarehouseSequenceDTO;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseSequence;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseSequenceVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 生成仓库唯一refNum 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class WarehouseSequenceConverter extends AbstractModelConverter<WarehouseSequence, WarehouseSequenceVO, WarehouseSequenceDTO> {

}
