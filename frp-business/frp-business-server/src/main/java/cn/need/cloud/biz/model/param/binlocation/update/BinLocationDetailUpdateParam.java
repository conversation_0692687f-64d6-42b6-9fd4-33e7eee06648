package cn.need.cloud.biz.model.param.binlocation.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 库位详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "库位详情 vo对象")
public class BinLocationDetailUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * inStockQty
     */
    @Schema(description = "inStockQty")
    private Integer inStockQty;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品版本id
     */
    @Schema(description = "产品版本id")
    private Long productVersionId;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

}