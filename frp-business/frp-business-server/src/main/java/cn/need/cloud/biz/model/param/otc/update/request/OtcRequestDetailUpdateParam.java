package cn.need.cloud.biz.model.param.otc.update.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC请求详情 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC请求详情 vo对象")
public class OtcRequestDetailUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 351472342190596488L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * OTC请求ID
     */
    @Schema(description = "OTC请求ID")
    private Long otcRequestId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * finishQty
     */
    @Schema(description = "finishQty")
    private Integer finishQty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

}