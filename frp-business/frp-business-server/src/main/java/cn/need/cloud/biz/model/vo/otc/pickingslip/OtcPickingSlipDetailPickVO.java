package cn.need.cloud.biz.model.vo.otc.pickingslip;

import cn.need.cloud.biz.model.vo.base.ReadyToGoProductBinLocationPickVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 拣货单详情拣货信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC 拣货单详情拣货信息 DTO对象")
public class OtcPickingSlipDetailPickVO extends ReadyToGoProductBinLocationPickVO {

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long otcPickingSlipId;

    /**
     * 拣货单详情行号
     */
    @Schema(description = "拣货单详情行号")
    private Integer lineNum;
}