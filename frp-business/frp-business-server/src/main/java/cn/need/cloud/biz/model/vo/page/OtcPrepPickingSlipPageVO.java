package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC预提货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC预提货单 vo对象")
public class OtcPrepPickingSlipPageVO extends BaseSuperVO implements BaseBinLocationAware, BaseProductAware {

    @Serial
    private static final long serialVersionUID = -506535570943447446L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;

    /**
     * 拣货id
     */
    @Schema(description = "拣货id")
    private Long otcPickingSlipId;

    /**
     * 拣货单
     */
    @Schema(description = "拣货单")
    private RefNumVO pickingSlip;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    // TODO pickingSlipProductTypes

    /**
     * 预拣货类型
     */
    @Schema(description = "预拣货状态, SubPickingSlipType")
    private String prepPickingSlipType;

    /**
     * 预拣货状态
     */
    @Schema(description = "预拣货状态,subPickingSlipStatus")
    private String prepPickingSlipStatus;

    /**
     * 产品版本id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 库位id
     */
    @Schema(description = "库位id")
    private Long binLocationId;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 预拣货单类型
     */
    @Schema(description = "预拣货单产品类型")
    private String prepPickingSlipProductType;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;
}