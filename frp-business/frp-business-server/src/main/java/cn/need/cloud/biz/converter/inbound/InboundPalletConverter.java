package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundPalletDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库单打托 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundPalletConverter extends AbstractModelConverter<InboundPallet, InboundPalletVO, InboundPalletDTO> {

}
