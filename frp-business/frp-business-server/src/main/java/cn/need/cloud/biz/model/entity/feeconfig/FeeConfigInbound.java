package cn.need.cloud.biz.model.entity.feeconfig;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeConditionTypeAndUnitTypeInboundEnum;
import cn.need.cloud.biz.model.entity.base.FeeConfigModel;
import cn.need.cloud.biz.model.entity.base.SwitchActiveModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * 仓库报价费用配置inbound
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fee_config_inbound")
public class FeeConfigInbound extends FeeConfigModel implements SwitchActiveModel {


    @Serial
    private static final long serialVersionUID = 5090707852482790488L;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @TableField("condition_type")
    private FeeConditionTypeAndUnitTypeInboundEnum conditionType;

    /**
     * 货币代码（如USD、CNY）
     */
    @TableField("currency")
    private String currency;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 额外计费判断字段
     */
    @TableField("extra_fee_judge_fields")
    private String extraFeeJudgeFields;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    @TableField("fee_calculation_type")
    private String feeCalculationType;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 仓库报价id
     */
    @TableField("quote_id")
    private Long quoteId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

}
