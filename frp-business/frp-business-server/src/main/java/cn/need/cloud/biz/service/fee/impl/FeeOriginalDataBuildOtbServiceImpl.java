package cn.need.cloud.biz.service.fee.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundRequestStatusEnum;
import cn.need.cloud.biz.model.bo.fee.inbound.FodExtraDataInBoundBO;
import cn.need.cloud.biz.model.bo.fee.inbound.FodInboundPalletBO;
import cn.need.cloud.biz.model.bo.fee.inbound.FodInboundUnloadBO;
import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.service.fee.FeeOriginalDataBuildService;
import cn.need.cloud.biz.service.inbound.*;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Service("fodBuildOtb")
@AllArgsConstructor
public class FeeOriginalDataBuildOtbServiceImpl implements FeeOriginalDataBuildService {

    private final InboundWorkorderService inboundWorkorderService;
    private final InboundRequestService inboundRequestService;
    private final InboundPalletService inboundPalletService;
    private final InboundPutawaySlipService inboundPutawaySlipService;
    private final InboundUnloadService inboundUnloadservice;

    @Override
    public FodExtraDataInBoundBO buildFeeOriginalData() {

        //todo: 这里要根据 FeeStatus & InboundRequestStatus来获取

        PageSearch<InboundRequestQuery> inboundRequestQueryPageSearch = new PageSearch<>();
        inboundRequestQueryPageSearch.setCurrent(1);
        inboundRequestQueryPageSearch.setSize(1);
        InboundRequestQuery inboundRequestQuery = new InboundRequestQuery();
        inboundRequestQuery.setInboundRequestStatus(InboundRequestStatusEnum.PROCESSED.getStatus());
        inboundRequestQueryPageSearch.setCondition(inboundRequestQuery);

        final PageData<InboundRequestPageVO> inboundRequestPageVOPageData = inboundRequestService.pageByQuery(inboundRequestQueryPageSearch);

        if (ObjectUtil.isEmpty(inboundRequestPageVOPageData.getRecords())) {
            return null;
        }
        //todo:这里使用Redis锁
        return buildFeeOriginalData(inboundRequestPageVOPageData.getRecords().get(0).getId());
    }


    @Override
    public FodExtraDataInBoundBO buildFeeOriginalData(Long requestId) {

        if (ObjectUtil.isEmpty(requestId)) {
            return null;
        }

        InboundRequestVO inboundRequestVO = inboundRequestService.detailById(requestId);

        return buildFeeOriginalData(inboundRequestVO);
    }

    @NotNull
    private FodExtraDataInBoundBO buildFeeOriginalData(InboundRequestVO inboundRequestVO) {
        if (!inboundRequestVO.getInboundRequestStatus().equals(InboundRequestStatusEnum.PROCESSED.getStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "Processed",
                    inboundRequestVO.getInboundRequestStatus()));
        }

        InboundWorkorder inboundWorkorder = inboundWorkorderService.getByRequestId(inboundRequestVO.getId());

        if (ObjectUtil.isEmpty(inboundWorkorder)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InboundWorkorder", "requestId", inboundRequestVO.getId()));
        }
        List<InboundUnload> inboundUnloads = inboundUnloadservice.listByRequestId(inboundRequestVO.getId());
        List<InboundPallet> inboundPallets = inboundPalletService.listByWorkorderId(inboundWorkorder.getId());


        FodExtraDataInBoundBO extraData = new FodExtraDataInBoundBO();

        extraData.setSnapshotRequestId(inboundRequestVO.getId());
        extraData.setSnapshotRequestRefNum(inboundRequestVO.getRefNum());
        extraData.setSnapshotRefNum(inboundRequestVO.getRefNum());
        extraData.setTransactionPartnerId(inboundRequestVO.getTransactionPartnerId());
        extraData.setWarehouseId(inboundRequestVO.getWarehouseId());
        extraData.setRefNum(inboundRequestVO.getRefNum());
        //extraData.setProcessStartTime(inboundRequestVO.getProcessStartTime());
        //extraData.setProcessEndTime(inboundRequestVO.getProcessEndTime());
        extraData.setNote(null);

        extraData.setUnloadList(BeanUtil.copyNew(inboundUnloads, FodInboundUnloadBO.class));
        extraData.setPutAwayPalletList(BeanUtil.copyNew(inboundPallets, FodInboundPalletBO.class));

        return extraData;
    }
}
