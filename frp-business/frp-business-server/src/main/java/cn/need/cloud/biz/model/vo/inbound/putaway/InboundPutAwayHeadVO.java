package cn.need.cloud.biz.model.vo.inbound.putaway;

import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 上架信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "上架信息 vo对象")
public class InboundPutAwayHeadVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 上架单id
     */
    @Schema(description = "上架单id")
    private Long id;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 上架状态
     */
    @Schema(description = "上架状态")
    private String inboundPutawaySlipStatus;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseVO baseWarehouseVO;
}
