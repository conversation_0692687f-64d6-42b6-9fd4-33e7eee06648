package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtc;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigOtcCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigOtcUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtcQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtcPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.base.RefNumWithNameService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置otc service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface FeeConfigOtcService extends SuperService<FeeConfigOtc>,
        RefNumService<FeeConfigOtc, FeeConfigOtcService>,
        RefNumWithNameService<FeeConfigOtc, FeeConfigOtcService>,
        FeeConfigService<FeeConfigOtc, FeeConfigOtcService> {

    /**
     * 根据参数新增仓库报价费用配置otc
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价费用配置otc的相关信息
     * @return 仓库报价费用配置otcID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeConfigOtcCreateParam createParam);


    /**
     * 根据参数更新仓库报价费用配置otc
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价费用配置otc的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeConfigOtcUpdateParam updateParam);

    /**
     * 根据查询条件获取仓库报价费用配置otc列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置otc对象的列表(分页)
     */
    List<FeeConfigOtcPageVO> listByQuery(FeeConfigOtcQuery query);

    /**
     * 根据查询条件获取仓库报价费用配置otc列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置otc对象的列表(分页)
     */
    PageData<FeeConfigOtcPageVO> pageByQuery(PageSearch<FeeConfigOtcQuery> search);

    /**
     * 根据ID获取仓库报价费用配置otc
     *
     * @param id 仓库报价费用配置otcID
     * @return 返回仓库报价费用配置otcVO对象
     */
    FeeConfigOtcVO detailById(Long id);


    /**
     * 根据仓库报价费用配置otc唯一编码获取仓库报价费用配置otc
     *
     * @param refNum 仓库报价费用配置otc唯一编码
     * @return 返回仓库报价费用配置otcVO对象
     */
    FeeConfigOtcVO detailByRefNum(String refNum);

    /**
     * 根据报价ID获取仓库报价费用配置otc列表
     *
     * @param quoteId 报价ID
     * @return 返回仓库报价费用配置otcVO对象列表
     */
    List<FeeConfigOtcVO> listDetailByQuoteId(Long quoteId);
}