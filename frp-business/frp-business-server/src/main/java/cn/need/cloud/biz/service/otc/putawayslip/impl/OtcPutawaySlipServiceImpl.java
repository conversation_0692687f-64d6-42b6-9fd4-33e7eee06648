package cn.need.cloud.biz.service.otc.putawayslip.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.PutAwaySlipStatus;
import cn.need.cloud.biz.converter.otc.OtcPutawaySlipConverter;
import cn.need.cloud.biz.mapper.otc.OtcPutawaySlipMapper;
import cn.need.cloud.biz.model.bo.otc.pickingslip.OtcPickingSlipUnpickBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otc.putawayslip.OtcPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.base.putawayslip.NormalPutawaySlipModel;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlip;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlipDetail;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.putawayslip.OtcPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.query.otc.putawayslip.OtcPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PutawaySlipConfirmVO;
import cn.need.cloud.biz.model.vo.otc.page.OtcPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPutawaySlipDetailVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPutawaySlipVO;
import cn.need.cloud.biz.service.binlocation.BinLocationSpecialService;
import cn.need.cloud.biz.service.helper.PutawaySlipHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipSpecialService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPutawaySlipDetailService;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPutawaySlipService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBinLocationSpecialService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderSpecialService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <p>
 * OTC上架单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Service
public class OtcPutawaySlipServiceImpl extends SuperServiceImpl<OtcPutawaySlipMapper, OtcPutawaySlip> implements OtcPutawaySlipService {

    @Resource
    private OtcPutawaySlipDetailService otcPutawaySlipDetailService;
    @Resource
    private BinLocationSpecialService binLocationSpecialService;
    @Resource
    private OtcWorkorderBinLocationSpecialService otcWorkorderBinLocationSpecialService;
    @Resource
    private OtcWorkorderService otcWorkorderService;
    @Resource
    private OtcPickingSlipService otcPickingSlipService;
    @Resource
    @Lazy
    private OtcPickingSlipSpecialService otcPickingSlipSpecialService;
    @Resource
    @Lazy
    private OtcWorkorderSpecialService otcWorkorderSpecialService;

    @Override
    public PageData<OtcPutawaySlipPageVO> pageByQuery(PageSearch<OtcPutawaySlipQuery> search) {
        Page<OtcPutawaySlip> page = Conditions.page(search, entityClass);
        List<OtcPutawaySlipPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        this.fillPageList(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcPutawaySlipVO detailById(Long id) {
        OtcPutawaySlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcPutawaySlip");
        }
        return buildOtcPutawaySlipVO(entity);
    }

    @Override
    public OtcPutawaySlipVO detailByRefNum(String refNum) {
        OtcPutawaySlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtcPutawaySlip");
        }
        return buildOtcPutawaySlipVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancel(PutawaySlipCancelUpdateParam param) {
        this.cancelPutaway(param);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean putAway(OtcPutawaySlipPutAwayUpdateParam putawayParam) {

        OtcPutawaySlipPutAwayBO param = BeanUtil.copyNew(putawayParam, OtcPutawaySlipPutAwayBO.class);
        param.setDetailList(BeanUtil.copyNew(putawayParam.getDetailList(), OtcPutawaySlipPutAwayDetailBO.class));

        // 检查上架
        OtcPutawaySlip putawaySlip = this.getById(param.getId());
        Validate.notNull(putawaySlip, "PutAwaySlipId {} is not exist", param.getId());

        Validate.isTrue(PutAwaySlipStatus.canPutAway(putawaySlip.getPutawaySlipStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT, putawaySlip.refNumLog(), "checkAndPutaway",
                PutAwaySlipStatus.canPutAwayStatuses(), putawaySlip.getPutawaySlipStatus()
        );
        param.setPutawaySlip(putawaySlip);

        List<OtcPutawaySlipDetail> details = otcPutawaySlipDetailService.listByPutawaySlipId(param.getId());
        // 校验
        PutawaySlipHelper.checkAndPutaway(param, details);

        // 拣货单 Rollback
        otcPickingSlipSpecialService.rollback(param);

        // 工单 Rollback
        otcWorkorderSpecialService.rollback(param);

        // 工单分配仓储 Rollback
        otcWorkorderBinLocationSpecialService.rollback(param);

        // 库位 Rollback
        binLocationSpecialService.rollback(param.getDetailList());

        Validate.isTrue(super.update(putawaySlip) == 1, "Update PutAwaySlip PutAwayQty is fail");
        Validate.isTrue(otcPutawaySlipDetailService.updateBatch(details) == details.size(),
                "Update PutAwaySlip PutAwayQty is fail"
        );

        return true;
    }

    @Override
    public void unpick(OtcPickingSlipUnpickBO query) {
        query.setPutawaySlipRefNumType(RefNumTypeEnum.OTC_PUT_AWAY_SLIP);
        this.unpick(query, (putawaySlip, putawaySlipDetailList) -> {
            otcPutawaySlipDetailService.insertBatch(putawaySlipDetailList);
            super.insert(putawaySlip);
        });
    }

    @Override
    public List<PutawaySlipConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query) {
        List<OtcPutawaySlip> putawaySlipList = this.listByWorkorderIds(query.getIdList());
        Map<Long, OtcPutawaySlip> wkMap = StreamUtils.toMap(putawaySlipList, IdModel::getId);

        List<Long> putawaySlipIds = StreamUtils.distinctMap(putawaySlipList, IdModel::getId);

        Map<Long, List<OtcPutawaySlipDetail>> detailsMap = otcPutawaySlipDetailService.groupByPutawaySlipId(putawaySlipIds);

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtcPutawaySlip putawaySlip = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PutawaySlipConfirmDetailVO rollback = BeanUtil.copyNew(detail, PutawaySlipConfirmDetailVO.class);
                                rollback.setPutawaySlip(BeanUtil.copyNew(putawaySlip, PutawaySlipConfirmVO.class));
                                rollback.setBinLocationId(detail.getSourceBinLocationId());
                                return rollback;
                            });
                })
                .toList();
    }

    private List<OtcPutawaySlip> listByWorkorderIds(List<Long> workorderIds) {
        if (ObjectUtil.isEmpty(workorderIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(NormalPutawaySlipModel::getWorkorderId, workorderIds).list();
    }

    /**
     * 构建OTC上架单VO对象
     *
     * @param entity OTC上架单对象
     * @return 返回包含详细信息的OTC上架单VO对象
     */
    private OtcPutawaySlipVO buildOtcPutawaySlipVO(OtcPutawaySlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        // 构建detail
        List<OtcPutawaySlipDetailVO> details = BeanUtil.copyNew(
                otcPutawaySlipDetailService.listByPutawaySlipId(entity.getId()),
                OtcPutawaySlipDetailVO.class
        );
        // 填充仓库
        List<Long> binLocationIds = Stream.concat(
                        details.stream().map(OtcPutawaySlipDetailVO::getSourceBinLocationId),
                        details.stream().map(OtcPutawaySlipDetailVO::getDestBinLocationId)
                )
                .distinct().toList();
        Map<Long, BinLocationCache> cacheMap = StreamUtils.toMap(BinLocationCacheUtil.listByIds(binLocationIds), BinLocationCache::getId);
        details.forEach(obj -> {
            obj.setSourceBaseBinLocationVO(BeanUtil.copyNew(cacheMap.get(obj.getSourceBinLocationId()), BaseBinLocationVO.class));
            obj.setDestBaseBinLocationVO(BeanUtil.copyNew(cacheMap.get(obj.getDestBinLocationId()), BaseBinLocationVO.class));
        });

        // 返回包含详细信息的OTC上架单VO对象
        OtcPutawaySlipVO vo = Converters.get(OtcPutawaySlipConverter.class).toVO(entity);
        vo.setPickingSlip(otcPickingSlipService.refNumById(entity.getPickingSlipId()));
        vo.setWorkorder(otcWorkorderService.refNumById(entity.getWorkorderId()));
        vo.setDetailList(details);
        return vo;
    }

    /**
     * 填充字段
     *
     * @param dataList 列表
     */
    private void fillPageList(List<OtcPutawaySlipPageVO> dataList) {
        List<Long> workorderIds = StreamUtils.distinctMap(dataList, OtcPutawaySlipPageVO::getWorkorderId);
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtcPutawaySlipPageVO::getPickingSlipId);

        Map<Long, RefNumVO> wkMap = otcWorkorderService.refNumMapByIds(workorderIds);
        Map<Long, RefNumVO> psMap = otcPickingSlipService.refNumMapByIds(pickingSlipIds);

        dataList.forEach(obj -> {
            obj.setWorkorder(wkMap.get(obj.getWorkorderId()));
            obj.setPickingSlip(psMap.get(obj.getPickingSlipId()));
        });
    }

}
