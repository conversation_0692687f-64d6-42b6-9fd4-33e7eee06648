package cn.need.cloud.biz.model.query.otc.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * OTC拣货单包裹情况 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC拣货单包裹情况query对象")
public class OtcPickingSlipPiecePackageQuery {

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    @NotNull(message = "otcPickingSlipId id is must not null")
    private Long otcPickingSlipId;


    /**
     * 产品id
     */
    @Schema(description = "产品id")
    @NotNull(message = "productId is must not null")
    private Long productId;

}