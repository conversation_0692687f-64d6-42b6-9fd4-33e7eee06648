package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcPrepPutawaySlipConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlip;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.putawayslip.OtcPrepPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.putawayslip.OtcPrepPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcPrepPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPrepPutawaySlipVO;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPrepPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC上架单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@RestController
@RequestMapping("/api/biz/otc-prep-putaway-slip")
@Tag(name = "OTC Prep上架单")
public class OtcPrepPutawaySlipController extends AbstractRestController<OtcPrepPutawaySlipService, OtcPrepPutawaySlip, OtcPrepPutawaySlipConverter, OtcPrepPutawaySlipVO> {

    @Operation(summary = "根据id获取OTC上架单详情", description = "根据数据主键id，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPrepPutawaySlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取OTC上架单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPrepPutawaySlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "获取OTC上架单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC上架单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcPrepPutawaySlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPrepPutawaySlipQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "Cancel", description = "取消上架")
    @PostMapping(value = "/cancel")
    public Result<Boolean> cancel(@RequestBody @Valid PutawaySlipCancelUpdateParam param) {
        // 返回结果
        return success(service.cancel(param));
    }

    @Operation(summary = "PutAway", description = "上架")
    @PostMapping(value = "/put-away")
    public Result<Boolean> putAway(@RequestBody @Valid OtcPrepPutawaySlipPutAwayUpdateParam param) {
        // 返回结果
        return success(service.putAway(param));
    }

    @Operation(summary = "Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }
}
