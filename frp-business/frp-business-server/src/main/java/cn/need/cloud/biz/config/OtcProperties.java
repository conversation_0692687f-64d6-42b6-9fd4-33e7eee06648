package cn.need.cloud.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2024-11-07
 */
@ConfigurationProperties(prefix = "frp.otc", ignoreUnknownFields = false)
@Data
public class OtcProperties {


    /**
     * 工单构建拣货单批次大小
     */
    private Integer pickingSlipFilterBuildBatchSize = 3000;
    /**
     * 包裹导出批次大小
     */
    private Integer packageExportBatchSize = 1000;

    /**
     * 导出包裹Sheet名称
     */
    private String packageExportSheetName = "Export Result";

    /**
     * 导出包裹文件名前缀
     */
    private String packageExportFilePrefixName = "OutBoundToCPackage_";
}
