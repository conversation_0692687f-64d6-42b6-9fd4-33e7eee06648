package cn.need.cloud.biz.service.base;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.model.IdModel;

import java.util.*;

/**
 * <p>
 * RefNumService
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface RefNumService<T extends RefNumModel, S extends SuperService<T>> {

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param ids 需要查询的主键ID列表
     * @return RefNumVO列表
     */
    @SuppressWarnings("unchecked")
    default List<RefNumVO> refNumByIds(Set<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 确保S是当前接口的实现类，此处转换安全
        S service = (S) this;

        return service.query()
                .select("id", "ref_num")
                .in("id", ids)
                .list()
                .stream()
                .map(obj -> BeanUtil.copyNew(obj, RefNumVO.class))
                .toList();
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param ids 需要查询的主键ID列表
     * @return RefNumVO列表
     */
    default List<RefNumVO> refNumByIds(List<Long> ids) {
        return refNumByIds(new HashSet<>(ids));
    }

    /**
     * 根据给定的ID查询RefNumModel数据，
     *
     * @param id 需要查询的主键ID
     * @return RefNumVO
     */
    default RefNumVO refNumById(Long id) {
        if (ObjectUtil.isEmpty(id)) {
            return null;
        }

        List<Long> ids = Collections.singletonList(id);
        Map<Long, RefNumVO> map = refNumMapByIds(ids);
        if (ObjectUtil.isEmpty(map)) {
            throw new BusinessException("id: " + id + " not found in RefNum");
        }
        return map.get(id);
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，并转换为ID到RefNumVO对象的映射关系
     *
     * @param ids 需要查询的主键ID列表
     * @return 包含ID到RefNumVO列表的映射，若输入ids为空则返回空Map
     */
    default Map<Long, RefNumVO> refNumMapByIds(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        final List<RefNumVO> list = refNumByIds(ids);

        if (ObjectUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return ObjectUtil.toMap(list, RefNumVO::getId);
    }


    /**
     * 根据给定的ID列表查询RefNumModel数据，并转换为ID到RefNumVO对象的映射关系
     *
     * @param idModels 需要查询的主键ID列表
     * @return 包含ID到RefNumVO列表的映射，若输入ids为空则返回空Map
     */
    default Map<Long, RefNumVO> refNumMapByIdModels(List<? extends IdModel> idModels) {
        if (ObjectUtil.isEmpty(idModels)) {
            return Collections.emptyMap();
        }
        return refNumMapByIds(idModels.stream()
                .map(IdModel::getId)
                .toList());
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param idModels 需要查询的主键ID列表
     * @return RefNumVO列表
     */
    default List<RefNumVO> refNumByIdModels(List<? extends IdModel> idModels) {
        if (ObjectUtil.isEmpty(idModels)) {
            return Collections.emptyList();
        }

        return refNumByIds(idModels.stream()
                .map(IdModel::getId)
                .toList());
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param idModel 需要查询的主键ID
     * @return RefNumVO
     */
    default RefNumVO refNumByIdModel(IdModel idModel) {
        if (ObjectUtil.isEmpty(idModel)) {
            return null;
        }

        List<Long> ids = Collections.singletonList(idModel.getId());
        List<RefNumVO> vos = refNumByIds(ids);
        return ObjectUtil.isEmpty(vos) ? null : vos.get(0);
    }

}
