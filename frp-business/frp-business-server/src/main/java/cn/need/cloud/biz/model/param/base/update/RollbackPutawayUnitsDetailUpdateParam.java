package cn.need.cloud.biz.model.param.base.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * OtcPickingSlipRollbackUpdateParam
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@Schema(description = "Prep拣货单-Rollback PutAway Units Detail参数")
public class RollbackPutawayUnitsDetailUpdateParam implements Serializable {

    @NotNull(message = "prepPickingSlipDetailId is must not null")
    @Schema(description = "Prep工单详情id")
    private Long prepWorkorderDetailId;

    @NotNull(message = "prepPickingSlipDetailId is must not null")
    @Schema(description = "Prep拣货单详情id")
    private Long prepPickingSlipDetailId;

    @NotNull(message = "rollbackQty is must not null")
    @Min(value = 1, message = "rollbackQty is must not empty")
    private Integer rollbackQty;
}
