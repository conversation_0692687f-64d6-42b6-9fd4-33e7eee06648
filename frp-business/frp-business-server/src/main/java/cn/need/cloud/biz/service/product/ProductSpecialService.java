package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.client.constant.enums.product.ProductConfigTypeEnum;
import cn.need.cloud.biz.model.entity.product.ProductHazmat;

/**
 * <p>
 * 产品 Special service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface ProductSpecialService {

    /**
     * 根据产品ID和配置类型获取产品版本号。
     *
     * @param productId             产品ID
     * @param productConfigTypeEnum 产品配置类型枚举
     * @return 产品版本号，如果不存在返回null
     */
    Integer getProductVersionInt(Long productId, ProductConfigTypeEnum productConfigTypeEnum);

    /**
     * 根据id校验并删除
     *
     * @param id 产品ID
     * @return 删除行数
     */
    Integer checkAndDelete(Long id, String deletedNote);

    /**
     * 根据id校验并删除productScan
     *
     * @param id 产品ID
     * @return 删除行数
     */
    Integer removeScan(Long id, String deletedNote);

    /**
     * 根据id校验并删除productVersion
     *
     * @param id 产品ID
     * @return 删除行数
     */
    Integer removeVersion(Long id, String deletedNote);

    /**
     * 根据id校验并删除productMultibox
     *
     * @param id 产品ID
     * @return 删除行数
     */
    Integer removeMultibox(Long id, String deletedNote);

    /**
     * 根据id校验并删除productGroup
     *
     * @param id 产品ID
     * @return 删除行数
     */
    Integer removeGroup(Long id, String deletedNote);

    /**
     * 根据id校验并删除productComponent
     *
     * @param id 产品ID
     * @return 删除行数
     */
    Integer removeComponent(Long id, String deletedNote);

    void createOrUpdateHazmat(Long productId, ProductHazmat hazmat);

    void removeHazmat(Long productId);
}