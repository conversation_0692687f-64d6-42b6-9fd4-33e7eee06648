package cn.need.cloud.biz.model.param.profile.create;

import cn.need.cloud.biz.jackson.UpperCase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 仓库档案 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "仓库档案 vo对象")
public class ProfileWarehouseCreateParam implements Serializable {


    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码")
    @UpperCase
    private String categoryCode;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述")
    private String categoryDesc;

    /**
     * 值类型
     */
    @Schema(description = "值类型")
    private String valueType;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 值
     */
    @Schema(description = "值")
    private String value;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;


    /**
     * 代码
     */
    @Schema(description = "代码")
    @UpperCase
    private String code;

}