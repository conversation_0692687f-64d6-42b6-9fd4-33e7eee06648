package cn.need.cloud.biz.service.otb.putawayslip.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.base.PutAwaySlipStatus;
import cn.need.cloud.biz.converter.otb.OtbPutawaySlipConverter;
import cn.need.cloud.biz.mapper.otb.OtbPutawaySlipMapper;
import cn.need.cloud.biz.model.bo.otb.pickingslip.OtbPickingSlipUnpickBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.base.putawayslip.NormalPutawaySlipModel;
import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlip;
import cn.need.cloud.biz.model.entity.otb.OtbPutawaySlipDetail;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.putawayslip.OtbPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.query.otb.putawayslip.OtbPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PutawaySlipConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.putawayslip.PutawaySlipConfirmVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otb.putawayslip.OtbPutawaySlipDetailVO;
import cn.need.cloud.biz.model.vo.otb.putawayslip.OtbPutawaySlipVO;
import cn.need.cloud.biz.service.binlocation.BinLocationSpecialService;
import cn.need.cloud.biz.service.helper.PutawaySlipHelper;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipSpecialService;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPutawaySlipDetailService;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPutawaySlipService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderBinLocationSpecialService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderSpecialService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <p>
 * OTC上架单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Service
public class OtbPutawaySlipServiceImpl extends SuperServiceImpl<OtbPutawaySlipMapper, OtbPutawaySlip> implements OtbPutawaySlipService {

    @Resource
    private OtbPutawaySlipDetailService otbPutawaySlipDetailService;
    @Resource
    private BinLocationSpecialService binLocationSpecialService;
    @Resource
    private OtbWorkorderBinLocationSpecialService otbWorkorderBinLocationSpecialService;
    @Resource
    private OtbWorkorderService otbWorkorderService;
    @Resource
    private OtbPickingSlipService otbPickingSlipService;
    @Resource
    @Lazy
    private OtbPickingSlipSpecialService otbPickingSlipSpecialService;
    @Resource
    @Lazy
    private OtbWorkorderSpecialService otbWorkorderSpecialService;

    @Override
    public PageData<OtbPutawaySlipPageVO> pageByQuery(PageSearch<OtbPutawaySlipQuery> search) {
        Page<OtbPutawaySlip> page = Conditions.page(search, entityClass);
        List<OtbPutawaySlipPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        this.fillPageList(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbPutawaySlipVO detailById(Long id) {
        OtbPutawaySlip entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtbPutawaySlip");
        }
        return buildOtbPutawaySlipVO(entity);
    }

    @Override
    public OtbPutawaySlipVO detailByRefNum(String refNum) {
        OtbPutawaySlip entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtbPutawaySlip");
        }
        return buildOtbPutawaySlipVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancel(PutawaySlipCancelUpdateParam param) {
        this.cancelPutaway(param);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean putAway(OtbPutawaySlipPutAwayUpdateParam putawayParam) {

        OtbPutawaySlipPutAwayBO param = BeanUtil.copyNew(putawayParam, OtbPutawaySlipPutAwayBO.class);
        param.setDetailList(BeanUtil.copyNew(putawayParam.getDetailList(), OtbPutawaySlipPutAwayDetailBO.class));

        // 检查上架
        OtbPutawaySlip putawaySlip = this.getById(param.getId());
        Validate.notNull(putawaySlip, "PutAwaySlipId {} is not exist", param.getId());

        Validate.isTrue(PutAwaySlipStatus.canPutAway(putawaySlip.getPutawaySlipStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT, putawaySlip.refNumLog(), "checkAndPutaway",
                PutAwaySlipStatus.canPutAwayStatuses(), putawaySlip.getPutawaySlipStatus()
        );
        param.setPutawaySlip(putawaySlip);

        List<OtbPutawaySlipDetail> details = otbPutawaySlipDetailService.listByPutawaySlipId(param.getId());
        // 校验
        PutawaySlipHelper.checkAndPutaway(param, details);

        // 拣货单 Rollback
        otbPickingSlipSpecialService.rollback(param);

        // 工单 Rollback
        otbWorkorderSpecialService.rollback(param);

        // 工单分配仓储 Rollback
        otbWorkorderBinLocationSpecialService.rollback(param);

        // 库位 Rollback
        binLocationSpecialService.rollback(param.getDetailList());

        Validate.isTrue(super.update(putawaySlip) == 1, "Update PutAwaySlip PutAwayQty is fail");
        Validate.isTrue(otbPutawaySlipDetailService.updateBatch(details) == details.size(),
                "Update PutAwaySlip PutAwayQty is fail"
        );

        return true;
    }

    @Override
    public void unpick(OtbPickingSlipUnpickBO query) {
        query.setPutawaySlipRefNumType(RefNumTypeEnum.OTB_PUT_AWAY_SLIP);
        this.unpick(query, (putawaySlip, putawaySlipDetailList) -> {
            otbPutawaySlipDetailService.insertBatch(putawaySlipDetailList);
            super.insert(putawaySlip);
        });
    }

    @Override
    public List<PutawaySlipConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query) {
        List<OtbPutawaySlip> putawaySlipList = this.listByWorkorderIds(query.getIdList());
        Map<Long, OtbPutawaySlip> wkMap = StreamUtils.toMap(putawaySlipList, IdModel::getId);

        List<Long> putawaySlipIds = StreamUtils.distinctMap(putawaySlipList, IdModel::getId);

        Map<Long, List<OtbPutawaySlipDetail>> detailsMap = otbPutawaySlipDetailService.groupByPutawaySlipId(putawaySlipIds);

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtbPutawaySlip putawaySlip = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                PutawaySlipConfirmDetailVO rollback = BeanUtil.copyNew(detail, PutawaySlipConfirmDetailVO.class);
                                rollback.setPutawaySlip(BeanUtil.copyNew(putawaySlip, PutawaySlipConfirmVO.class));
                                rollback.setBinLocationId(detail.getSourceBinLocationId());
                                return rollback;
                            });
                })
                .toList();
    }

    private List<OtbPutawaySlip> listByWorkorderIds(List<Long> workorderIds) {
        if (ObjectUtil.isEmpty(workorderIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(NormalPutawaySlipModel::getWorkorderId, workorderIds).list();
    }

    /**
     * 构建OTC上架单VO对象
     *
     * @param entity OTC上架单对象
     * @return 返回包含详细信息的OTC上架单VO对象
     */
    private OtbPutawaySlipVO buildOtbPutawaySlipVO(OtbPutawaySlip entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        // 构建detail
        List<OtbPutawaySlipDetailVO> details = BeanUtil.copyNew(
                otbPutawaySlipDetailService.listByPutawaySlipId(entity.getId()),
                OtbPutawaySlipDetailVO.class
        );
        // 填充仓库
        List<Long> binLocationIds = Stream.concat(
                        details.stream().map(OtbPutawaySlipDetailVO::getSourceBinLocationId),
                        details.stream().map(OtbPutawaySlipDetailVO::getDestBinLocationId)
                )
                .distinct().toList();
        Map<Long, BinLocationCache> cacheMap = StreamUtils.toMap(BinLocationCacheUtil.listByIds(binLocationIds), BinLocationCache::getId);
        details.forEach(obj -> {
            obj.setSourceBaseBinLocationVO(BeanUtil.copyNew(cacheMap.get(obj.getSourceBinLocationId()), BaseBinLocationVO.class));
            obj.setDestBaseBinLocationVO(BeanUtil.copyNew(cacheMap.get(obj.getDestBinLocationId()), BaseBinLocationVO.class));
        });

        // 返回包含详细信息的OTC上架单VO对象
        OtbPutawaySlipVO vo = Converters.get(OtbPutawaySlipConverter.class).toVO(entity);
        vo.setPickingSlip(otbPickingSlipService.refNumById(entity.getPickingSlipId()));
        vo.setWorkorder(otbWorkorderService.refNumById(entity.getWorkorderId()));
        vo.setDetailList(details);
        return vo;
    }

    /**
     * 填充字段
     *
     * @param dataList 列表
     */
    private void fillPageList(List<OtbPutawaySlipPageVO> dataList) {
        List<Long> workorderIds = StreamUtils.distinctMap(dataList, OtbPutawaySlipPageVO::getWorkorderId);
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtbPutawaySlipPageVO::getPickingSlipId);

        Map<Long, RefNumVO> wkMap = otbWorkorderService.refNumMapByIds(workorderIds);
        Map<Long, RefNumVO> psMap = otbPickingSlipService.refNumMapByIds(pickingSlipIds);

        dataList.forEach(obj -> {
            obj.setWorkorder(wkMap.get(obj.getWorkorderId()));
            obj.setPickingSlip(psMap.get(obj.getPickingSlipId()));
        });
    }

}
