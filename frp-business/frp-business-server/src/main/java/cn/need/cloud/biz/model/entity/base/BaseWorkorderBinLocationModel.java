package cn.need.cloud.biz.model.entity.base;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 分配工单仓储位置，基础Model
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseWorkorderBinLocationModel extends SuperModel {

    /**
     * 库位详情id
     */
    @TableField("bin_location_detail_id")
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @TableField("bin_location_id")
    private Long binLocationId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 产品版本id
     */
    @TableField("product_version_id")
    private Long productVersionId;

    /**
     * 库位详情锁id
     */
    @TableField("bin_location_detail_locked_id")
    private Long binLocationDetailLockedId;

}
