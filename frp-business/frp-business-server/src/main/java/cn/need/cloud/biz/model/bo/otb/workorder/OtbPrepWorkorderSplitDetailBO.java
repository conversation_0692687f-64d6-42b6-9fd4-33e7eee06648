package cn.need.cloud.biz.model.bo.otb.workorder;

import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderDetail;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderDetail;
import lombok.Data;

/**
 * OtcWorkorderSplitBO
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class OtbPrepWorkorderSplitDetailBO {

    /**
     * 原单
     */
    private OtbPrepWorkorderDetail prepDetail;

    /**
     * 拆单
     */
    private OtbPrepWorkorderDetail splitPrepDetail;

    /**
     * 拆单数量
     */
    private Integer splitQty;


}
