package cn.need.cloud.biz.mapper.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.Quote;
import cn.need.cloud.biz.model.query.feeconfig.QuoteQuery;
import cn.need.cloud.biz.model.vo.feeconfig.page.QuotePageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仓库报价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Mapper
public interface QuoteMapper extends SuperMapper<Quote> {

    /**
     * 根据条件获取仓库报价列表
     *
     * @param query 查询条件
     * @return 仓库报价集合
     */
    default List<QuotePageVO> listByQuery(@Param("qoq") QuoteQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取仓库报价分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 仓库报价集合
     */
    List<QuotePageVO> listByQuery(@Param("qoq") QuoteQuery query, @Param("page") Page<?> page);
}