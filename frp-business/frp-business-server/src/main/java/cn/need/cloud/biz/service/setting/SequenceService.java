package cn.need.cloud.biz.service.setting;

import cn.need.cloud.biz.model.entity.setting.Sequence;
import cn.need.cloud.biz.model.param.setting.create.SequenceCreateParam;
import cn.need.cloud.biz.model.param.setting.update.SequenceUpdateParam;
import cn.need.cloud.biz.model.query.setting.SequenceQuery;
import cn.need.cloud.biz.model.vo.page.SequencePageVO;
import cn.need.cloud.biz.model.vo.setting.SequenceVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 全局序列号ref service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface SequenceService extends SuperService<Sequence> {

    /**
     * 根据参数新增全局序列号ref
     *
     * @param createParam 请求创建参数，包含需要插入的全局序列号ref的相关信息
     * @return 全局序列号refID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(SequenceCreateParam createParam);


    /**
     * 根据参数更新全局序列号ref
     *
     * @param updateParam 请求创建参数，包含需要更新的全局序列号ref的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(SequenceUpdateParam updateParam);

    /**
     * 根据查询条件获取全局序列号ref列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个全局序列号ref对象的列表(分页)
     */
    List<SequencePageVO> listByQuery(SequenceQuery query);

    /**
     * 根据查询条件获取全局序列号ref列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个全局序列号ref对象的列表(分页)
     */
    PageData<SequencePageVO> pageByQuery(PageSearch<SequenceQuery> search);

    /**
     * 根据ID获取全局序列号ref
     *
     * @param id 全局序列号refID
     * @return 返回全局序列号refVO对象
     */
    SequenceVO detailById(Long id);


}