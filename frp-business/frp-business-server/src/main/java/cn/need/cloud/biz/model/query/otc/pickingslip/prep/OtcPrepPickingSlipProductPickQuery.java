package cn.need.cloud.biz.model.query.otc.pickingslip.prep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


/**
 * OTC Prep拣货单 pick query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC Prep拣货单 pick query对象")
public class OtcPrepPickingSlipProductPickQuery {


    /**
     * 拣货数量
     */
    @Schema(description = "拣货数量")
    @Min(value = 1)
    @NotNull(message = "Pick qty is must not null")
    private Integer qty;

    /**
     * Prep拣货单详情id
     */
    @Schema(description = "Prep拣货单详情id")
    @NotNull(message = "PrepPickingSlipDetailId is must not null")
    private Long otcPrepPickingSlipDetailId;

}