package cn.need.cloud.biz.mapper.feeconfig;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuoteQuery;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierQuotePageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 供应商-仓库报价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Mapper
public interface SupplierQuoteMapper extends SuperMapper<SupplierQuote> {

    /**
     * 根据条件获取供应商-仓库报价列表
     *
     * @param query 查询条件
     * @return 供应商-仓库报价集合
     */
    default List<SupplierQuotePageVO> listByQuery(@Param("qosq") SupplierQuoteQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取供应商-仓库报价分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 供应商-仓库报价集合
     */
    List<SupplierQuotePageVO> listByQuery(@Param("qosq") SupplierQuoteQuery query,
                                          @Param("page") Page<?> page);

    /**
     * 供应商-仓库报价下拉列表
     *
     * @param columnList 查询字段名
     * @param query      查询条件
     * @return 供应商-仓库报价下拉列表
     */
    List<Map<String, Object>> dropProList(
            @Param("columnList") List<DropColumnInfoBO> columnList,
            @Param("qosq") SupplierQuoteQuery query
    );
}