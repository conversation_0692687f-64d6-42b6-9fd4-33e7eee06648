package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

/**
 * 产品程序对象
 * <p>
 * 子类中不能包含 productId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseProductProgramVO implements BaseProductAware {
    /**
     * 产品id
     */
    @Getter
    @Setter
    private Long productId;

    /**
     * 产品
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    @JsonIgnore
    private BaseProductVO baseProductVO;

    @Override
    @JsonIgnore
    public BaseProductVO getBaseProductVO() {
        if (ObjectUtil.isEmpty(productId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseProductVO)) {
            return baseProductVO;
        }
        // Retrieve from cache once and store the result
        baseProductVO = BaseProductAware.super.getBaseProductVO();
        return baseProductVO;
    }
}
