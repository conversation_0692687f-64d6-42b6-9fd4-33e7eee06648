package cn.need.cloud.biz.service.otb.workorder;

import cn.need.cloud.biz.client.constant.enums.otb.OtbWorkorderEnum;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderAdjustShipQtyQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtbWorkorderPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * OTB工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtbWorkorderService extends SuperService<OtbWorkorder>, RefNumService<OtbWorkorder, OtbWorkorderService> {

    /**
     * 根据查询条件获取OTB工单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTB工单对象的列表(分页)
     */
    PageData<OtbWorkorderPageVO> pageByQuery(PageSearch<OtbWorkOrderListQuery> search);

    /**
     * 根据ID获取OTB工单
     *
     * @param id OTB工单ID
     * @return 返回OTB工单VO对象
     */
    OtbWorkorderVO detailById(Long id);

    /**
     * 根据OTB工单唯一编码获取OTB工单
     *
     * @param refNum OTB工单唯一编码
     * @return 返回OTB工单VO对象
     */
    OtbWorkorderVO detailByRefNum(String refNum);


    /**
     * 开始处理工单
     *
     * @param ids 工单id集合
     */
    void begin(Set<Long> ids);

    /**
     * 统计列表条数
     *
     * @param query 查询条件
     * @return 统计列表条数
     */
    Integer filterBuildPickingSlipCount(OtbWorkOrderListQuery query);

    /**
     * 根据拣货单id获取出库工单
     *
     * @param pickingSlipId 拣货单id
     * @return 出库工单
     */
    OtbWorkorder getByPickSlipId(Long pickingSlipId);

    /**
     * 根据工单id映射工单refNum
     *
     * @param workOrderIdList 工单集合
     * @return 工单id映射工单refNum关系
     */
    Map<Long, String> getRefNum(Collection<Long> workOrderIdList);

    /**
     * 工单refNum
     *
     * @param workOrderId 工单id
     * @return 工单refNum
     */
    String getRefNum(Long workOrderId);

    /**
     * 根据refNum获取工单id
     *
     * @param otbWorkorderRefNumList 工单refNum
     * @return 工单id
     */
    Set<Long> getOtcWorkOrderId(Set<String> otbWorkorderRefNumList);

    /**
     * 调整发货数量
     *
     * @param query 调整发货数量查询条件
     * @return /
     */
    boolean adjustShipQty(OtbWorkorderAdjustShipQtyQuery query);

    /**
     * 根据构建拣货单条件获取工单集合
     *
     * @param query 拣货单条件
     * @return /
     */
    List<OtbWorkorder> filterBuildByQuery(OtbPickingSlipFilterBuildQuery query);

    /**
     * 工单拣货
     *
     * @param context 上下文
     */
    void pick(OtbPickingSlipPickContextVO context);

    /**
     * 下拉列表pro 带Group的
     *
     * @param query 查询条件
     * @return /
     */
    List<DropProVO> distinctValuePro(OtbWorkorderQuery query);

    /**
     * 下拉列表pro 统计
     *
     * @param query 查询条件
     * @return /
     */
    List<DropProVO> countPreDay(OtbWorkorderQuery query);

    /**
     * 工单refNum
     *
     * @param workOrderRefNumList 工单refNum集合
     * @return 工单id
     */
    Set<Long> listByRefNum(Set<String> workOrderRefNumList);


    OtbWorkorder buildOtbWorkorder(
            String note,
            OtbRequestVO request,
            List<OtbRequestDetailVO> detailList
    );

    /**
     * 根据工单id获取未完成工单
     *
     * @param binLocationId 库位id
     * @return /
     */
    Boolean existUnfinishedOrder(Long binLocationId);

    /**
     * 校验工单状态
     *
     * @param item             工单
     * @param otbWorkOrderEnum 状态
     */
    void checkWithSetStatus(OtbWorkorder item, OtbWorkorderEnum otbWorkOrderEnum);

    /**
     * 更新渠道确认状态
     *
     * @param otbWorkorder     工单
     * @param otbWorkOrderEnum 状态
     */
    void updateChannelConfirmed(OtbWorkorder otbWorkorder, OtbWorkorderEnum otbWorkOrderEnum);

    /**
     * 根据拣货单id获取工单
     *
     * @param pickingSlipIdList 拣货单id集合
     * @return /
     */
    List<OtbWorkorder> listByPickingSlipIds(List<Long> pickingSlipIdList);

    /**
     * 根据请求id获取工单
     *
     * @param requestIdList 请求id集合
     * @return /
     */
    List<OtbWorkorder> listByRequestIds(List<Long> requestIdList);

    /**
     * 根据请求id分组获取工单
     *
     * @param requestIdList 请求id集合
     * @return /
     */
    default Map<Long, List<OtbWorkorder>> groupByRequestIdList(List<Long> requestIdList) {
        var workorderList = this.listByRequestIds(requestIdList);
        return StreamUtils.groupBy(workorderList, OtbWorkorder::getOtbRequestId);
    }
}