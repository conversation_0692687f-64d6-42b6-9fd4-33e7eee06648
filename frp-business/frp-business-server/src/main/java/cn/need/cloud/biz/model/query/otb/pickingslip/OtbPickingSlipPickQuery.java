package cn.need.cloud.biz.model.query.otb.pickingslip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * OTC拣货单 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTB拣货单pick query对象")
public class OtbPickingSlipPickQuery {

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    @NotNull(message = "otbPickingSlipId id is must not null")
    private Long otbPickingSlipId;


    /**
     * 拣货详情参数
     */
    @Schema(description = "拣货详情参数")
    @NotEmpty(message = "pick detail not null")
    @Valid
    private List<OtbPickingSlipProductPickQuery> pickDetailList;

}