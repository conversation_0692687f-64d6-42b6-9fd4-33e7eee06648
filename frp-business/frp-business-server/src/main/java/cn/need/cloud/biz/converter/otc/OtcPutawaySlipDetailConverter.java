package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPutawaySlipDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlipDetail;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPutawaySlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 上架详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public class OtcPutawaySlipDetailConverter extends AbstractModelConverter<OtcPutawaySlipDetail, OtcPutawaySlipDetailVO, OtcPutawaySlipDetailDTO> {

}
