package cn.need.cloud.biz.model.vo.binlocation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 库位变化 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "库位变化 vo对象")
public class BinLocationChangeVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 变化数量
     */
    @Schema(description = "变化数量")
    private Integer changeInStockQty;

    /**
     * 变化库位
     */
    @Schema(description = "变化库位")
    private Long changeBinLocationId;

    /**
     * 变化库位详情
     */
    @Schema(description = "变化库位详情")
    private Long changeBinLocationDetailId;
}
