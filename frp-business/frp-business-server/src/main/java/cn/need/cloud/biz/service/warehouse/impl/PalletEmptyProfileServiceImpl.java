package cn.need.cloud.biz.service.warehouse.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.warehouse.PalletEmptyProfileConverter;
import cn.need.cloud.biz.mapper.warehouse.PalletEmptyProfileMapper;
import cn.need.cloud.biz.model.entity.warehouse.PalletEmptyProfile;
import cn.need.cloud.biz.model.param.otb.update.pallet.PalletEmptyProfileUpdateParam;
import cn.need.cloud.biz.model.param.warehouse.create.PalletEmptyProfileCreateParam;
import cn.need.cloud.biz.model.query.warehouse.PalletEmptyProfileQuery;
import cn.need.cloud.biz.model.vo.page.PalletEmptyProfilePageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletEmptyProfileVO;
import cn.need.cloud.biz.service.warehouse.PalletEmptyProfileService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 托盘信息 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class PalletEmptyProfileServiceImpl extends SuperServiceImpl<PalletEmptyProfileMapper, PalletEmptyProfile> implements PalletEmptyProfileService {

    @Resource
    private NumberGenerateClient numberGenerateClient;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(PalletEmptyProfileCreateParam createParam) {
        // 检查传入托盘信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取托盘信息转换器实例，用于将托盘信息参数对象转换为实体对象
        PalletEmptyProfileConverter converter = Converters.get(PalletEmptyProfileConverter.class);

        // 将托盘信息参数对象转换为实体对象并初始化
        PalletEmptyProfile entity = initPalletEmptyProfile(converter.toEntity(createParam));

        // 插入托盘信息实体对象到数据库
        super.insert(entity);

        // 返回托盘信息ID
        return entity.getId();
    }


    /**
     * 初始化托盘信息对象
     * 此方法用于设置托盘信息对象的必要参数，确保其处于有效状态
     *
     * @param entity 托盘信息对象，不应为空
     * @return 返回初始化后的托盘信息
     * @throws BusinessException 如果传入的托盘信息为空，则抛出此异常
     */
    private PalletEmptyProfile initPalletEmptyProfile(PalletEmptyProfile entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "PalletEmptyProfile"));
        }
        // 生成RefNum
        entity.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.PALLET_EMPTY.getCode(), WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        // 填充warehouseId
        entity.setWarehouseId(WarehouseContextHolder.getRequiredWarehouseId());
        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(PalletEmptyProfileUpdateParam updateParam) {
        // 检查传入托盘信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取托盘信息转换器实例，用于将托盘信息参数对象转换为实体对象
        PalletEmptyProfileConverter converter = Converters.get(PalletEmptyProfileConverter.class);

        // 将托盘信息参数对象转换为实体对象
        PalletEmptyProfile entity = converter.toEntity(updateParam);

        // 填充warehouseId
        entity.setWarehouseId(WarehouseContextHolder.getRequiredWarehouseId());

        // 执行更新托盘信息操作
        return super.update(entity);

    }

    @Override
    public List<PalletEmptyProfilePageVO> listByQuery(PalletEmptyProfileQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<PalletEmptyProfilePageVO> pageByQuery(PageSearch<PalletEmptyProfileQuery> search) {
        Page<PalletEmptyProfile> page = Conditions.page(search, entityClass);
        List<PalletEmptyProfilePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public PalletEmptyProfileVO detailById(Long id) {
        PalletEmptyProfile entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "PalletEmptyProfile", id));
        }
        return buildPalletEmptyProfileVO(entity);
    }

    @Override
    public PalletEmptyProfileVO detailByRefNum(String refNum) {
        PalletEmptyProfile entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "PalletEmptyProfile", "refNum", refNum));
        }
        return buildPalletEmptyProfileVO(entity);
    }

    @Override
    public void updateInUser(Long id) {
        //空托盘
        PalletEmptyProfile palletEmptyProfile = super.getById(id);
        //更新使用状态
        palletEmptyProfile.setInUseFlag(Boolean.TRUE);
        //更新
        mapper.updateById(palletEmptyProfile);
    }

    /**
     * 构建托盘信息VO对象
     *
     * @param entity 托盘信息对象
     * @return 返回包含详细信息的托盘信息VO对象
     */
    private PalletEmptyProfileVO buildPalletEmptyProfileVO(PalletEmptyProfile entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的托盘信息VO对象
        return Converters.get(PalletEmptyProfileConverter.class).toVO(entity);
    }

}
