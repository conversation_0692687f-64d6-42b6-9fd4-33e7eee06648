package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipPutAwayQuery;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderPutAwayVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/***
 * 上架上下文信息
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class OtbPrepPickingSlipPutAwayContextVO {

    /**
     * 上架参数信息
     */
    private OtbPrepPickingSlipPutAwayQuery query;

    /**
     * 上架的Prep拣货单
     */
    private OtbPrepPickingSlip prepPickingSlip;

    /**
     * 上架的Prep工单
     */
    private List<OtbPrepWorkorder> putAwayPrepWorkOrderList = new ArrayList<>();

    /**
     * 该拣货单下的Prep工单
     */
    private List<OtbPrepWorkorder> prepWorkOrderList = new ArrayList<>();

    /**
     * Processed工单信息
     */
    private List<OtbWorkorder> workorderList = new ArrayList<>();

    /**
     * 上架库位信息
     */
    private BinLocationDetail putAwayBinLocationDetail;

    /**
     * 更新的拣货单详情
     */
    private List<OtbPrepPickingSlipDetail> prepPickingSlipDetailList;

    /**
     * 更新的工单详情
     */
    private List<OtbPrepWorkorderDetail> putAwayPrepWorkorderDetailList;

    /**
     * 所有工单详情
     */
    private List<OtbPrepWorkorderDetail> prepWorkorderDetailList;

    /**
     * 所有Prep工单详情
     */
    private Map<Long, List<OtbPrepWorkorderDetail>> prepDetailGroupByWkMap;

    /**
     * 上架Prep工单的信息
     */
    private List<OtbPrepWorkorderPutAwayVO> prepWorkorderPutawayList = new ArrayList<>();
}
