package cn.need.cloud.biz.mapper.fee;

import cn.need.cloud.biz.model.bo.common.DropColumnInfoBO;
import cn.need.cloud.biz.model.entity.fee.FeeStorageDetail;
import cn.need.cloud.biz.model.query.fee.FeeStorageDetailQuery;
import cn.need.cloud.biz.model.vo.fee.page.FeeStorageDetailPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 费用详情storage Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Mapper
public interface FeeStorageDetailMapper extends SuperMapper<FeeStorageDetail> {

    /**
     * 根据条件获取费用详情storage列表
     *
     * @param query 查询条件
     * @return 费用详情storage集合
     */
    default List<FeeStorageDetailPageVO> listByQuery(@Param("qofsd") FeeStorageDetailQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取费用详情storage分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 费用详情storage集合
     */
    List<FeeStorageDetailPageVO> listByQuery(
            @Param("qofsd") FeeStorageDetailQuery query,
            @Param("page") Page<?> page);

    /**
     * 费用详情storage下拉列表
     *
     * @param columnList 查询字段名
     * @param query      查询条件
     * @return 费用详情storage下拉列表
     */
    List<Map<String, Object>> dropProList(
            @Param("columnList") List<DropColumnInfoBO> columnList,
            @Param("qofsd") FeeStorageDetailQuery query
    );
}