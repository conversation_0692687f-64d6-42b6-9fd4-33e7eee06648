package cn.need.cloud.biz.model.vo.base.aware;

import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

/**
 * 库位程序对象
 * <p>
 * 子类中不能包含 binLocationId
 * </p>
 *
 * <AUTHOR>
 */
public class BaseBinLocationProgramVO implements BaseBinLocationAware {
    /**
     * 库位id
     */
    @Getter
    @Setter
    private Long binLocationId;

    /**
     * 库位
     */
    @Setter(lombok.AccessLevel.PRIVATE)
    @JsonIgnore
    private BaseBinLocationVO baseBinLocationVO;

    @Override
    @JsonIgnore
    public BaseBinLocationVO getBaseBinLocationVO() {
        if (ObjectUtil.isEmpty(binLocationId)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(baseBinLocationVO)) {
            return baseBinLocationVO;
        }
        // Retrieve from cache once and store the result
        baseBinLocationVO = BaseBinLocationAware.super.getBaseBinLocationVO();
        return baseBinLocationVO;
    }
}
