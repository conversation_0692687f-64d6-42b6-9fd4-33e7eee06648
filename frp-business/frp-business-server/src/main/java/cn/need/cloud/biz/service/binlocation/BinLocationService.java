package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.client.constant.enums.binlocation.BinTypeEnum;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailListQuery;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.vo.base.BaseCheckOrderVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationMoveVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 库位 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface BinLocationService extends SuperService<BinLocation> {

    /**
     * 根据参数新增库位
     *
     * @param createParam 请求创建参数，包含需要插入的库位的相关信息
     * @return 库位ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(BinLocationCreateParam createParam);


    /**
     * 根据参数更新库位
     *
     * @param updateParam 请求创建参数，包含需要更新的库位的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(BinLocationUpdateParam updateParam);

    /**
     * 根据查询条件获取库位列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个库位id的列表(分页)
     */
    List<BinLocationPageVO> listByQuery(BinLocationQuery query);

    /**
     * 根据查询条件获取库位列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个库位对象的列表(分页)
     */
    PageData<BinLocationPageVO> pageByQuery(PageSearch<BinLocationQuery> search);

    /**
     * 根据ID获取库位
     *
     * @param id 库位ID
     * @return 返回库位VO对象
     */
    BinLocationVO detailById(Long id);

    /**
     * 根据ID获取库位
     *
     * @param query 库位ID
     * @return 返回库位VO对象
     */
    List<BinLocationDetailVO> detailListByIds(BinLocationDetailListQuery query);

    /**
     * 根据库位唯一编码获取库位
     *
     * @param refNum 库位唯一编码
     * @return 返回库位VO对象
     */
    BinLocationVO detailByRefNum(String refNum);

    /**
     * 删除库位
     *
     * @param deletedNoteParam 库位id及删除备注
     * @return 影响行数
     */
    Integer removeBinLocation(DeletedNoteParam deletedNoteParam);

    /**
     * 库位字段去重下拉
     *
     * @param query 库位查询条件
     * @return 库位字段去重下拉
     */
    List<DropProVO> distinctValue(BinLocationQuery query);

    /**
     * 根据仓库id获取库位信息
     *
     * @param id 仓库id
     */
    List<BinLocation> listByWarehouse(Long id);

    /**
     * 根据库位id获取库位VO映射
     *
     * @param binLocationIds 库位id集合
     * @return /
     */
    Map<Long, BinLocationVO> binLocationByIds(List<Long> binLocationIds);

    /**
     * 根据仓库id删除库位，并删除库位详情
     *
     * @param id 仓库id
     */
    void removeBinByWarehouseId(Long id);

    /**
     * 根据仓库id删除库位，不删除库位详情
     *
     * @param id 仓库id
     */
    void removeBywarehouseId(Long id);

    /**
     * 新增默认库位
     *
     * @param warehouse 仓库信息
     */
    List<BinLocation> insertDefaultBinLocation(Warehouse warehouse);

    /**
     * 获取库位id根据库位名称查询
     *
     * @param condition 查询条件
     * @return 库位id集合
     */
    Set<Long> getBinLocationIds(InboundPalletQuery condition);

    /**
     * 根据binTypeEnum获取库位
     *
     * @param binTypeEnum binTypeEnum
     * @return /
     */
    BinLocation findVirtualBinLocationByType(BinTypeEnum binTypeEnum);

    /**
     * 启用禁用
     *
     * @param id 库位id
     * @return 影响行数
     */
    Integer switchActive(Long id);

    /**
     * 所有虚拟库位
     *
     * @return /
     */
    Map<Long, BinLocation> allVirtualBinLocationList();

    /**
     * 库位禁用
     *
     * @param warehouseId 仓库id
     */
    void switchActive(Long warehouseId, Boolean activeFlag);

    /**
     * 是否存在未完成订单
     *
     * @param param 前端参数
     * @return true/false
     */
    Boolean existUnfinishedOrder(BaseCheckOrderVO param);

    /**
     * 获取id基于baseQuery
     *
     * @param warehouseId 仓库id
     * @param activeFlag  是否等于
     * @return /
     */
    List<BinLocation> listByWarehouseId(Long warehouseId, boolean activeFlag);

    /**
     * 获取id基于baseQuery
     *
     * @param productId 产品id
     * @return /
     */
    List<BinLocationVO> detailByProduct(Long productId);

    /**
     * 获取id基于baseQuery
     *
     * @param productVersionId 产品版本id
     * @return /
     */
    List<BinLocationVO> detailByProductVersion(Long productVersionId);

    /**
     * 移动产品到指定库位
     *
     * @param param 前端参数
     */
    void productMove(BinLocationMoveVO param);
}