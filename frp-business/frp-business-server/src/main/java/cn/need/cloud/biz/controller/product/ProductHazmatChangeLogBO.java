package cn.need.cloud.biz.controller.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "产品Hazmat变更日志")
public class ProductHazmatChangeLogBO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1332451233123456789L;

    @Schema(description = "packageInstruction")
    private String packageInstruction;

    @Schema(description = "transportationRegulatoryClass")
    private String transportationRegulatoryClass;

    @Schema(description = "unRegulatoryId")
    private String unRegulatoryId;
}
