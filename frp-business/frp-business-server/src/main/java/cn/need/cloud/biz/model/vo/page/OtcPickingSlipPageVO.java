package cn.need.cloud.biz.model.vo.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC拣货单 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "OTC拣货单 vo对象")
public class OtcPickingSlipPageVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = 5490260679883574830L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String detailProductType;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;
    /**
     * 是否有特定运输要求
     */
    @Schema(description = "是否有特定运输要求")
    private Boolean hasCusShipRequire;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private String orderType;

    /**
     * 拣货状态
     */
    @Schema(description = "拣货状态")
    private String pickingSlipStatus;

    /**
     * 谁构建拣货单
     */
    @Schema(description = "谁构建拣货单")
    private String buildFromType;

    /**
     * 从哪里拣货拣货单
     */
    @Schema(description = "谁拣货")
    private String pickFromType;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    @Schema(description = "拣货产品类型")
    private String pickingSlipProductType;

    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String processType;
}