package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeInbound;
import cn.need.cloud.biz.model.param.fee.create.FeeInboundCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeInboundUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeInboundQuery;
import cn.need.cloud.biz.model.vo.fee.FeeInboundVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeInboundPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用inbound service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeInboundService extends SuperService<FeeInbound>,
        FeeService<FeeInbound, FeeInboundService> {

    /**
     * 根据参数新增费用inbound
     *
     * @param createParam 请求创建参数，包含需要插入的费用inbound的相关信息
     * @return 费用inboundID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeInboundCreateParam createParam);


    /**
     * 根据参数更新费用inbound
     *
     * @param updateParam 请求创建参数，包含需要更新的费用inbound的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeInboundUpdateParam updateParam);

    /**
     * 根据查询条件获取费用inbound列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用inbound对象的列表(分页)
     */
    List<FeeInboundPageVO> listByQuery(FeeInboundQuery query);

    /**
     * 根据查询条件获取费用inbound列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用inbound对象的列表(分页)
     */
    PageData<FeeInboundPageVO> pageByQuery(PageSearch<FeeInboundQuery> search);

    /**
     * 根据ID获取费用inbound
     *
     * @param id 费用inboundID
     * @return 返回费用inboundVO对象
     */
    FeeInboundVO detailById(Long id);

    /**
     * 根据费用inbound唯一编码获取费用inbound
     *
     * @param refNum 费用inbound唯一编码
     * @return 返回费用inboundVO对象
     */
    FeeInboundVO detailByRefNum(String refNum);


}