package cn.need.cloud.biz.service.otc.request.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.otc.OtcRequestDetailConverter;
import cn.need.cloud.biz.mapper.otc.OtcRequestDetailMapper;
import cn.need.cloud.biz.model.entity.otc.OtcRequestDetail;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestCreateParam;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestDetailUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestDetailQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestDetailPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestDetailVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestProductVO;
import cn.need.cloud.biz.service.otc.request.OtcRequestDetailService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC请求详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcRequestDetailServiceImpl extends SuperServiceImpl<OtcRequestDetailMapper, OtcRequestDetail> implements OtcRequestDetailService {

    /// //////////////////////////////// 公共方法 //////////////////////////////////////
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(OtcRequestDetailCreateParam createParam) {
        // 检查传入OTC请求详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取OTC请求详情转换器实例，用于将OTC请求详情参数对象转换为实体对象
        OtcRequestDetailConverter converter = Converters.get(OtcRequestDetailConverter.class);

        // 将OTC请求详情参数对象转换为实体对象并初始化
        OtcRequestDetail entity = initOtcRequestDetail(converter.toEntity(createParam));

        // 插入OTC请求详情实体对象到数据库
        super.insert(entity);

        // 返回OTC请求详情ID
        return entity.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(OtcRequestDetailUpdateParam updateParam) {
        // 检查传入OTC请求详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取OTC请求详情转换器实例，用于将OTC请求详情参数对象转换为实体对象
        OtcRequestDetailConverter converter = Converters.get(OtcRequestDetailConverter.class);

        // 将OTC请求详情参数对象转换为实体对象
        OtcRequestDetail entity = converter.toEntity(updateParam);

        // 执行更新OTC请求详情操作
        return super.update(entity);
    }

    @Override
    public List<OtcRequestDetailPageVO> listByQuery(OtcRequestDetailQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtcRequestDetailPageVO> pageByQuery(PageSearch<OtcRequestDetailQuery> search) {
        Page<OtcRequestDetail> page = Conditions.page(search, entityClass);
        List<OtcRequestDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcRequestDetailVO detailById(Long id) {
        OtcRequestDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtcRequestDetail");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcRequestDetail", id));
        }
        return buildOtcRequestDetailVO(entity);
    }


    @Override
    public List<OtcRequestDetailVO> listByOtcRequestId(Long otcRequestId) {
        List<OtcRequestDetail> list = lambdaQuery().eq(OtcRequestDetail::getOtcRequestId, otcRequestId).list();
        return Converters.get(OtcRequestDetailConverter.class).toVO(list);
    }

    @Override
    public void insertBatchByParam(OtcRequestCreateParam param, long otcRequestId) {
        // 获取OTC请求详情转换器实例，用于将OTC请求详情参数对象转换为实体对象
        OtcRequestDetailConverter converter = Converters.get(OtcRequestDetailConverter.class);

        List<OtcRequestDetail> entityList = converter.toEntity(param.getDetailList());
        //校验lineNum唯一
        long count = entityList.stream().map(OtcRequestDetail::getLineNum).distinct().count();
        if (count != entityList.size()) {
            // throw new BusinessException("DetailProductLineNum must be unique");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "OtcRequestDetail", "lineNum", "product line numbers"));
        }
        entityList.forEach(e -> {
            e.setOtcRequestId(otcRequestId);
        });
        super.insertBatch(entityList);
    }

    @Override
    public void updateBatchByParam(OtcRequestUpdateParam param) {
        List<OtcRequestProductVO> detailList = param.getDetailList();
        //校验参数有效性
        checkParamValid(detailList);

        List<OtcRequestDetail> oldList = getListByRequestId(param.getId());

        //根据lineNum映射 oldId
        Map<Integer, Long> oldMap = oldList.stream().collect(Collectors.toMap(OtcRequestDetail::getLineNum, OtcRequestDetail::getId));

        // 获取OTC请求详情转换器实例，用于将OTC请求详情参数对象转换为实体对象
        OtcRequestDetailConverter converter = Converters.get(OtcRequestDetailConverter.class);
        List<OtcRequestDetail> newList = converter.toEntity(detailList);
        //根据lineNum映射 新对象
        Map<Integer, OtcRequestDetail> newMap = newList.stream().collect(Collectors.toMap(OtcRequestDetail::getLineNum, o -> o));
        //取lineNum 交集
        Set<Integer> intersection = new HashSet<>(oldMap.keySet());
        intersection.retainAll(newMap.keySet());
        //批量更新
        List<OtcRequestDetail> updateList = newList.stream().filter(o -> intersection.contains(o.getLineNum())).toList();
        updateList.forEach(o -> {
            o.setId(oldMap.get(o.getLineNum()));
            //设置仓库ID
            o.setWarehouseId(param.getWarehouseId());
        });
        super.updateBatch(updateList);
        // 需要删除的数据的lineNum
        Set<Integer> delList = new HashSet<>(oldMap.keySet());
        delList.removeAll(newMap.keySet());
        if (ObjectUtil.isNotEmpty(delList)) {
            List<Long> idList = oldList.stream().filter(o -> delList.contains(o.getLineNum())).map(OtcRequestDetail::getId).toList();
            //批量删除
            super.removeByIds(idList);
        }
        // 需要新增的数据的lineNum
        Set<Integer> addList = new HashSet<>(newMap.keySet());
        addList.removeAll(oldMap.keySet());
        if (ObjectUtil.isNotEmpty(addList)) {
            List<OtcRequestDetail> list = newList.stream().filter(o -> addList.contains(o.getLineNum())).toList();
            list.forEach(o -> {
                o.setOtcRequestId(param.getId());
                //设置仓库ID
                o.setWarehouseId(param.getWarehouseId());
            });
            //批量新增
            super.insertBatch(list);
        }
    }

    /**
     * 校验参数有效性
     *
     * @param detailList list
     */
    private void checkParamValid(List<OtcRequestProductVO> detailList) {
        if (ObjectUtil.isEmpty(detailList)) {
            // throw new BusinessException("detailList cannot be empty");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "detailList"));
        }
        //校验lineNum唯一
        long count = detailList.stream().map(OtcRequestProductVO::getLineNum).distinct().count();
        if (count != detailList.size()) {
            // throw new BusinessException("DetailProductLineNum must be unique");
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "OtcRequestDetail", "lineNum", "product line numbers"));
        }
        //校验内部参数
        boolean detailFlag = detailList.stream().anyMatch(o -> ObjectUtil.isEmpty(o.getQty()));
        if (detailFlag) {
            // throw new BusinessException("DetailQty cannot be empty");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "detailQty"));
        }
    }

    @Override
    public List<OtcRequestDetail> getListByRequestId(Long requestId) {
        return lambdaQuery().eq(OtcRequestDetail::getOtcRequestId, requestId).list();
    }

    @Override
    public List<OtcRequestDetail> listByRequestIds(List<Long> idList) {
        if (ObjectUtil.isNotEmpty(idList)) {
            return lambdaQuery().in(OtcRequestDetail::getOtcRequestId, idList).list();
        }
        return List.of();
    }

    @Override
    public List<OtcRequestDetail> listByLineNum(Long id, Set<Integer> lineNumList) {
        return lambdaQuery()
                .in(OtcRequestDetail::getLineNum,lineNumList)
                .eq(OtcRequestDetail::getOtcRequestId,id)
                .list();
    }

    /**
     * 构建OTC请求详情VO对象
     *
     * @param entity OTC请求详情对象
     * @return 返回包含详细信息的OTC请求详情VO对象
     */
    private OtcRequestDetailVO buildOtcRequestDetailVO(OtcRequestDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC请求详情VO对象
        return Converters.get(OtcRequestDetailConverter.class).toVO(entity);
    }

    /**
     * 初始化OTC请求详情对象
     * 此方法用于设置OTC请求详情对象的必要参数，确保其处于有效状态
     *
     * @param entity OTC请求详情对象，不应为空
     * @return 返回初始化后的OTC请求详情
     * @throws BusinessException 如果传入的OTC请求详情为空，则抛出此异常
     */
    private OtcRequestDetail initOtcRequestDetail(OtcRequestDetail entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("OtcRequestDetail cannot be empty");
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "OtcRequestDetail"));
        }


        // 返回初始化后的配置对象
        return entity;
    }

}
