package cn.need.cloud.biz.model.query.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 拆单
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@Schema(description = "拆单详情信息")
public class SplitWorkorderDetailParam {

    @Schema(description = "工单详情")
    @NotNull(message = "workorderDetailId must not null")
    private Long id;

    @Schema(description = "拆单数量")
    @Min(value = 1, message = "splitQty must be greater than 0")
    private Integer splitQty;
}
