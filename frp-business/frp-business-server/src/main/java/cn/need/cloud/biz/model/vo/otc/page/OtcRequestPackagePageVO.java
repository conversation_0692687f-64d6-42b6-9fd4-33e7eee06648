package cn.need.cloud.biz.model.vo.otc.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * OTC请求包裹 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC请求包裹 vo对象")
public class OtcRequestPackagePageVO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 是否快递标志
     */
    @Schema(description = "是否快递标志")
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输箱子-长
     */
    @Schema(description = "运输箱子-长")
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    @Schema(description = "运输箱子-宽")
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    @Schema(description = "运输箱子-高")
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    @Schema(description = "运输箱子-重量")
    private BigDecimal shipSizeWeight;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * OTC请求ID
     */
    @Schema(description = "OTC请求ID")
    private Long otcRequestId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 运输箱子-长度单位
     */
    @Schema(description = "运输箱子-长度单位")
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    @Schema(description = "运输箱子-重量单位")
    private String shipSizeWeightUnit;

    /**
     * 多箱UPC
     */
    @Schema(description = "多箱UPC")
    private String packageMultiboxUpc;

    /**
     * 多箱行号
     */
    @Schema(description = "多箱行号")
    private Integer packageMultiboxLineNum;

    /**
     * 多箱产品ID
     */
    @Schema(description = "多箱产品ID")
    private Long packageMultiboxProductId;

    /**
     * 多箱版本号
     */
    @Schema(description = "多箱版本号")
    private Integer packageMultiboxVersionInt;

}