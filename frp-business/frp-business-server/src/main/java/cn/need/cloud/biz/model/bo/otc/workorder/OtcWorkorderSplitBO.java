package cn.need.cloud.biz.model.bo.otc.workorder;

import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import lombok.Data;

import java.util.List;

/**
 * OtcWorkorderSplitBO
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class OtcWorkorderSplitBO {

    /**
     * 原单
     */
    private OtcWorkorder workorder;

    /**
     * 拆单
     */
    private OtcWorkorder splitWorkorder;

    /**
     * 拆单详情
     */
    private List<OtcWorkorderSplitDetailBO> detailHolders;

}
