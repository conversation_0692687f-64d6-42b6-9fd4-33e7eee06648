package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeOriginalDataDTO;
import cn.need.cloud.biz.model.entity.fee.FeeOriginalData;
import cn.need.cloud.biz.model.vo.fee.FeeOriginalDataVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用原始数据表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeOriginalDataConverter extends AbstractModelConverter<FeeOriginalData, FeeOriginalDataVO, FeeOriginalDataDTO> {

}
