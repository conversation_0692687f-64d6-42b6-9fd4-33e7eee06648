package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderDetail;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.param.otc.create.workorder.prep.OtcPrepWorkorderDetailCreateParam;
import cn.need.cloud.biz.model.param.otc.update.workorder.prep.OtcPrepWorkorderDetailUpdateParam;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderDetailQuery;
import cn.need.cloud.biz.model.vo.base.RelatedProductVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcPrepWorkorderDetailVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderDetailPageVO;
import cn.need.cloud.biz.service.inventory.PrepWorkorderDetailService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC预提工单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPrepWorkorderDetailService extends
        SuperService<OtcPrepWorkorderDetail>,
        PrepWorkorderDetailService<OtcPrepWorkorderDetail, OtcPrepWorkorder> {

    /**
     * 根据参数新增OTC预提工单详情
     *
     * @param createParam 请求创建参数，包含需要插入的OTC预提工单详情的相关信息
     * @return OTC预提工单详情ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(OtcPrepWorkorderDetailCreateParam createParam);


    /**
     * 根据参数更新OTC预提工单详情
     *
     * @param updateParam 请求创建参数，包含需要更新的OTC预提工单详情的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(OtcPrepWorkorderDetailUpdateParam updateParam);

    /**
     * 根据查询条件获取OTC预提工单详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC预提工单详情对象的列表(分页)
     */
    List<OtcPrepWorkorderDetailPageVO> listByQuery(OtcPrepWorkorderDetailQuery query);

    /**
     * 根据查询条件获取OTC预提工单详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC预提工单详情对象的列表(分页)
     */
    PageData<OtcPrepWorkorderDetailPageVO> pageByQuery(PageSearch<OtcPrepWorkorderDetailQuery> search);

    /**
     * 根据ID获取OTC预提工单详情
     *
     * @param id OTC预提工单详情ID
     * @return 返回OTC预提工单详情VO对象
     */
    OtcPrepWorkorderDetailVO detailById(Long id);


    /**
     * 根据工单详情id集合获取关联产品映射
     *
     * @param prepWorkOrderDetailIds Prep工单id集合
     * @return 映射
     */
    Map<Long, RelatedProductVO> relatedProductByIds(List<Long> prepWorkOrderDetailIds);

    /**
     * 根据prepWorkOrderId获取OTC预提工单详情集合
     *
     * @param id Prep工单id
     * @return 集合
     */
    List<OtcPrepWorkorderDetail> listByPrepWorkOrderId(Long id);

    /**
     * 根据工单id集合获取锁定库存参数
     *
     * @param prepWorkOrderIdList 工单集合
     * @return /
     */
    List<InventoryReleaseLockedParam> findInventoryReleaseLockedParam(List<Long> prepWorkOrderIdList);

    /**
     * 根据Prep工单id集合分组Prep工单详情
     *
     * @param prepWorkOrderIdList Prep工单id集合
     * @return /
     */
    Map<Long, List<OtcPrepWorkorderDetail>> groupByOtcPrepWorkOrderIdList(List<Long> prepWorkOrderIdList);

    /**
     * 需要Prep Convert 的Product
     *
     * @param prepWorkOrderIdList 工单id集合
     * @return /
     */
    Map<Long, List<OtcPrepWorkorderDetail>> convertAfterProductsGroupByWorkOrderIdList(List<Long> prepWorkOrderIdList);

    /**
     * 根据Prep工单id集合Prep工单详情
     *
     * @param prepWorkorderIds Prep工单
     * @return /
     */
    List<OtcPrepWorkorderDetail> listByPrepWorkOrderIds(List<Long> prepWorkorderIds);
}