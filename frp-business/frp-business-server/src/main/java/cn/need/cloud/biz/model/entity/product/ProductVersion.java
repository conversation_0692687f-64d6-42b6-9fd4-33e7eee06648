package cn.need.cloud.biz.model.entity.product;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.framework.common.core.lang.StringUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * <p>
 * 产品版本详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_version")
public class ProductVersion extends RefNumModel {


    /**
     * 产品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 供应商SKU
     */
    @TableField("supplier_sku")
    private String supplierSku;

    /**
     * UPC码
     */
    @TableField("upc")
    private String upc;

    /**
     * 产品标题
     */
    @TableField("title")
    private String title;

    /**
     * 净长度
     */
    @TableField("net_length")
    private BigDecimal netLength;

    /**
     * 净宽度
     */
    @TableField("net_width")
    private BigDecimal netWidth;

    /**
     * 净高度
     */
    @TableField("net_height")
    private BigDecimal netHeight;

    /**
     * 净重量
     */
    @TableField("net_weight")
    private BigDecimal netWeight;

    /**
     * 发货长度
     */
    @TableField("ship_length")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @TableField("ship_width")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @TableField("ship_height")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @TableField("ship_weight")
    private BigDecimal shipWeight;

    /**
     * 纸箱长度
     */
    @TableField("carton_length")
    private BigDecimal cartonLength;

    /**
     * 纸箱宽度
     */
    @TableField("carton_width")
    private BigDecimal cartonWidth;

    /**
     * 纸箱高度
     */
    @TableField("carton_height")
    private BigDecimal cartonHeight;

    /**
     * 纸箱重量
     */
    @TableField("carton_weight")
    private BigDecimal cartonWeight;

    /**
     * 每箱数量
     */
    @TableField("pcs_per_carton")
    private Integer pcsPerCarton;

    /**
     * 产品版本号
     */
    @TableField("product_version_int")
    private Integer productVersionInt;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 纸箱尺寸单位
     */
    @TableField("carton_dimension_unit")
    private String cartonDimensionUnit;

    /**
     * 纸箱重量单位
     */
    @TableField("carton_weight_unit")
    private String cartonWeightUnit;

    /**
     * 净尺寸单位
     */
    @TableField("net_dimension_unit")
    private String netDimensionUnit;

    /**
     * 净重量单位
     */
    @TableField("net_weight_unit")
    private String netWeightUnit;

    /**
     * 发货尺寸单位
     */
    @TableField("ship_dimension_unit")
    private String shipDimensionUnit;

    /**
     * 发货重量单位
     */
    @TableField("ship_weight_unit")
    private String shipWeightUnit;

    /**
     * 交易伙伴ID
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 产品重新测量类型
     */
    @TableField("product_remeasure_type")
    private String productRemeasureType;

    @Override
    public String toLog() {
        return StringUtil.format(
                "{}/{}/{}/{}",
                this.getRefNum(),
                this.getSupplierSku(),
                this.getUpc(),
                this.productVersionInt
        );
    }

}
