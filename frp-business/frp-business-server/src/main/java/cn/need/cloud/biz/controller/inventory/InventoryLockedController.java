package cn.need.cloud.biz.controller.inventory;

import cn.need.cloud.biz.converter.inventory.InventoryLockedConverter;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.query.inventory.InventoryLockedQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryLockedVO;
import cn.need.cloud.biz.model.vo.page.InventoryLockedPageVO;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inventory-locked")
@Tag(name = "锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值")
public class InventoryLockedController extends AbstractRestController<InventoryLockedService, InventoryLocked, InventoryLockedConverter, InventoryLockedVO> {

    @Operation(summary = "根据id获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值详情", description = "根据数据主键id，从数据库中获取其对应的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InventoryLockedVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值详情
        InventoryLockedVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值详情", description = "根据数据RefNum，从数据库中获取其对应的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InventoryLockedVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值详情
        InventoryLockedVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值列表")
    @PostMapping(value = "/list")
    public Result<PageData<InventoryLockedPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InventoryLockedQuery> search) {

        // 获取锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值分页
        PageData<InventoryLockedPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
