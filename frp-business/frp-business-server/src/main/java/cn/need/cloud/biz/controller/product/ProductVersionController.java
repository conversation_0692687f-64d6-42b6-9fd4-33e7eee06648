package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.converter.product.ProductVersionConverter;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.param.product.create.ProductVersionCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductVersionUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductVersionQuery;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 产品版本详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/product-version")
@Tag(name = "产品版本详情")
@Validated
public class ProductVersionController extends AbstractRestController<ProductVersionService, ProductVersion, ProductVersionConverter, ProductVersionVO> {

    @Operation(summary = "新增产品版本详情", description = "接收产品版本详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProductVersionCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改产品版本详情", description = "接收产品版本详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProductVersionUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id获取产品版本详情详情", description = "根据数据主键id，从数据库中获取其对应的产品版本详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<ProductVersionVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取产品版本详情详情
        ProductVersionVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取产品版本详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的产品版本详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<ProductVersionVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<ProductVersionQuery> search) {

        // 获取产品版本详情分页
        PageData<ProductVersionVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }




}
