package cn.need.cloud.biz.model.param.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * OtcWorkorderStartRollbackUpdateParam
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@Schema(description = "StartRollback 对象")
public class WorkorderStartUpdateParam implements Serializable {

    @Schema(description = "请求id集合", hidden = true)
    private List<Long> requestIdList;

    @Schema(description = "工单id集合")
    @NotEmpty(message = "WorkOrder idList is not empty")
    @NotNull(message = "WorkOrder idList is not null")
    private List<Long> idList;

    @Schema(description = "Rollback Note")
    @NotNull(message = "rollback note is not null")
    @NotBlank(message = "rollback note is not blank")
    private String note;

}
