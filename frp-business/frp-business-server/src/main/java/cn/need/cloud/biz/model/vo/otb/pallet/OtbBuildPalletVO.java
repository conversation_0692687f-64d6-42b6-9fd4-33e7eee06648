package cn.need.cloud.biz.model.vo.otb.pallet;

import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletEmptyProfileVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@Schema(description = " Otb构建打托单 vo对象")
public class OtbBuildPalletVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    /**
     * 包裹信息集合
     */
    @Schema(description = "包裹信息集合")
    private List<OtbPackageVO> packageList;

    /**
     * 打托label
     */
    @Schema(description = "打托label")
    private List<OtbPalletLabelVO> labelList;

    /**
     * 空托盘详情
     */
    @Schema(description = "打托详情")
    private List<OtbPalletDetailVO> detailList;

    /**
     * 空托盘信息
     */
    @Schema(description = "空托盘信息")
    private PalletEmptyProfileVO palletEmptyProfile;
    /**
     * 空托盘配置id
     */
    @Schema(description = "空托盘配置id")
    private Long palletEmptyProfileId;

    /**
     * otb请求单refNum
     */
    @Schema(description = "otb请求单refNum")
    private String otbRequestOfRefNum;

    /**
     * otb请求单requestRefNum
     */
    @Schema(description = "otb请求单requestRefNum")
    private String otbRequestOfRequestRefNum;

    /**
     * otb请求id
     */
    @Schema(description = "otb请求id")
    private Long otbRequestId;

    /**
     * 工单refNum
     */
    @Schema(description = "otb工单refNum")
    private String otbWorkOrderRefNum;

    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;
    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;

    /**
     * 发货地址是否为住宅
     */
    @Schema(description = "发货地址是否为住宅")
    private Boolean shipFromAddressIsResidential;

    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址是否为住宅
     */
    @Schema(description = "收货地址是否为住宅")
    private Boolean shipToAddressIsResidential;

    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;
    /**
     * otb 托盘状态
     */
    @Schema(description = "otb 托盘状态")
    private String otbPalletStatus;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNum;
    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @Schema(description = "Serial Shipping Container Code，序列化货运容器代码")
    private String ssccNum;
    /**
     * 短ssccnum
     */
    @Schema(description = "短ssccnum")
    private String shortSsccNum;
    /**
     * 托盘-长
     */
    @Schema(description = "托盘-长")
    private BigDecimal palletSizeLength;

    /**
     * 托盘-宽
     */
    @Schema(description = "托盘-宽")
    private BigDecimal palletSizeWidth;

    /**
     * 托盘-高
     */
    @Schema(description = "托盘-高")
    private BigDecimal palletSizeHeight;

    /**
     * 托盘-重量
     */
    @Schema(description = "托盘-重量")
    private BigDecimal palletSizeWeight;

    /**
     * 托盘-重量单位
     */
    @Schema(description = "托盘-重量单位")
    private String palletSizeWeightUnit;

    /**
     * 托盘-长度单位
     */
    @Schema(description = "托盘-长度单位")
    private String palletSizeDimensionUnit;

    /**
     * 托盘类型
     */
    @Schema(description = "托盘类型")
    private String otbPalletType;

    /**
     * 拣货单id
     */
    @Schema(description = "拣货单id")
    private Long otbPickingSlipId;

    /**
     * 拣货单refNum
     */
    @Schema(description = "拣货单refNum")
    private String otbPickingSlipRefNum;

    /**
     * 打托单refNum
     */
    @Schema(description = "打托单refNum")
    private String refNum;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库信息
     */
    @Schema(description = "仓库信息")
    private BaseWarehouseVO baseWarehouseVO;

    /**
     * 打托单id
     */
    @Schema(description = "打托单id")
    private Long id;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createBy;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
