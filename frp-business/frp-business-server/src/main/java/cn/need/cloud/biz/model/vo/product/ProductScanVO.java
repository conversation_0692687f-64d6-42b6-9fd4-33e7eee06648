package cn.need.cloud.biz.model.vo.product;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseFullProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 产品扫描 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品扫描 vo对象")
public class ProductScanVO extends BaseSuperVO implements BaseFullProductAware {


    /**
     * 扫描编号
     */
    @Schema(description = "扫描编号")
    private String scanNum;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;


    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long transactionPartnerId;


    /**
     * 产品属性
     */
    @Schema(description = "产品属性")
    private String productAttribute;

    /**
     * 多箱行号
     */
    @Schema(description = "多箱行号")
    private Integer lineNum;


    /**
     * 是否是系统默认
     */
    @Schema(description = "是否是系统默认")
    private boolean defaultFlag;

}