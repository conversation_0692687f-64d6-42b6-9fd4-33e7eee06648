package cn.need.cloud.biz.model.bo.inbound;

import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestAuditVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 入库请求审批上下文信息
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@Schema(description = "入库请求审批上下文信息")
public class InboundRequestAuditContextBO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    private InboundRequestAuditVO param;

    private String status;

    private String inboundWorkOrderRefNum;

    private String description;
}
