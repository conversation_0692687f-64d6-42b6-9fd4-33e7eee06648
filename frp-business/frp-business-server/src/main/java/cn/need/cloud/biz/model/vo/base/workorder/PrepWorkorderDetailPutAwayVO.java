package cn.need.cloud.biz.model.vo.base.workorder;

import cn.need.cloud.biz.model.vo.base.BasePutAwayVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/***
 * 工单上架详情
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PrepWorkorderDetailPutAwayVO extends BasePutAwayVO {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 上架数量
     */
    @Schema(description = "总数量")
    private Integer qty;

    /**
     * 预工单详情类型
     */
    @Schema(description = "预工单详情类型")
    private String prepWorkorderDetailType;

    /**
     * 释放锁列表
     */
    private List<Long> lockedList;
}
