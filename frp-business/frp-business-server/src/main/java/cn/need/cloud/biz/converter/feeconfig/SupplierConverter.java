package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.SupplierDTO;
import cn.need.cloud.biz.model.entity.feeconfig.Supplier;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 供应商信息 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class SupplierConverter extends AbstractModelConverter<Supplier, SupplierVO, SupplierDTO> {

}
