package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationAware;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductAware;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/***
 * 产品+库位聚合对象
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@Schema(description = "OTB拣货单 PrintSummary 汇总vo对象")
public class OtbPickingSlipSummaryVO implements Serializable, BaseBinLocationAware, BaseProductAware {

    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "库位id")
    private Long binLocationId;

    @Schema(description = "数量")
    private Integer qty;

}
