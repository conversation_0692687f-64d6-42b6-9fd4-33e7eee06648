package cn.need.cloud.biz.service.otc.pkg.impl;

import cn.hutool.core.lang.Pair;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.client.constant.enums.otc.*;
import cn.need.cloud.biz.client.constant.pickingslip.PickingSlipLogConstant;
import cn.need.cloud.biz.client.constant.pkg.PackageLogConstant;
import cn.need.cloud.biz.client.dto.otc.ContentInputDTO;
import cn.need.cloud.biz.client.dto.otc.FileResultDTO;
import cn.need.cloud.biz.client.dto.otc.ShipResultDTO;
import cn.need.cloud.biz.client.dto.otc.ShippingLabelDTO;
import cn.need.cloud.biz.client.dto.req.otc.OtcPackageDetailDTO;
import cn.need.cloud.biz.converter.otc.OtcPackageConverter;
import cn.need.cloud.biz.mapper.otc.OtcPackageMapper;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedChangeBO;
import cn.need.cloud.biz.model.bo.common.CommonShipRespBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.bo.otc.OtcBuildByWarehouseContextBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.entity.product.ProductMultiboxDetail;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcMultiBoxPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcPackageBatchCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.param.otc.create.pkg.OtcSompPackageWarehouseCreateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageListQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageShippedQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageTrackingNumQuery;
import cn.need.cloud.biz.model.vo.base.*;
import cn.need.cloud.biz.model.vo.otc.pkg.*;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageDetailFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageFullVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageLabelFullVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderBinLocationQtyVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderDetailPickVO;
import cn.need.cloud.biz.model.vo.page.OtcPackagePageVO;
import cn.need.cloud.biz.service.base.FileStringUploadService;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPackageAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageBinLocationService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageDetailService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageLabelService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.product.ProductMultiboxDetailService;
import cn.need.cloud.biz.service.product.ProductMultiboxService;
import cn.need.cloud.biz.service.ship.CommonClientService;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.ship.client.dto.base.*;
import cn.need.cloud.ship.client.dto.common.CommonPackageResDTO;
import cn.need.cloud.ship.client.dto.common.CommonShipCreateReqDTO;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <p>
 * OTC包裹服务实现类
 * </p>
 * <p>
 * 该类负责处理OTC（Outbound To Customer）出库包裹相关的业务逻辑，包括包裹的创建、
 * 查询、打印、发货、装箱等功能。OTC包裹是出库业务流程中重要的实体，记录了待发货商品的包装信息。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 包裹的创建和批量创建
 * 2. 包裹及其明细、标签等关联信息的查询
 * 3. 包裹的拣货和发货状态更新
 * 4. 包裹与托盘、工单、拣货单等关联实体的交互处理
 * 5. 包裹的装箱和多箱处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPackageServiceImpl extends SuperServiceImpl<OtcPackageMapper, OtcPackage> implements OtcPackageService {

    /**
     * 包裹明细服务，用于管理包裹内的具体商品信息
     */
    @Resource
    private OtcPackageDetailService otcPackageDetailService;

    /**
     * 包裹标签服务，用于管理包裹的物流标签
     */
    @Resource
    private OtcPackageLabelService otcPackageLabelService;

    /**
     * 包裹库位服务，用于管理包裹与库位的关联信息
     */
    @Resource
    private OtcPackageBinLocationService otcPackageBinLocationService;

    /**
     * 仓库服务，用于获取和管理仓库信息
     */
    @Resource
    private WarehouseService warehouseService;

    /**
     * 工单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcWorkorderService otcWorkorderService;

    /**
     * 工单明细服务，用于获取工单的明细信息
     */
    @Resource
    private OtcWorkorderDetailService otcWorkorderDetailService;

    /**
     * 工单库位服务，用于获取工单的库位信息
     */
    @Resource
    private OtcWorkorderBinLocationService otcWorkorderBinLocationService;

    /**
     * 拣货单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcPickingSlipService otcPickingSlipService;

    /**
     * 出库请求服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcRequestService otcRequestService;

    /**
     * 产品多箱服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private ProductMultiboxService productMultiboxService;

    /**
     * 产品多箱明细服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private ProductMultiboxDetailService productMultiboxDetailService;

    /**
     * 租户缓存服务，用于获取租户信息
     */
    @Resource
    private TenantCacheService tenantCacheService;

    /**
     * 拣货单服务，用于管理拣货单信息
     */
    @Resource
    private PickingSlipService pickingSlipService;

    /**
     * 库位明细锁定服务，用于管理库位的锁定状态
     */
    @Resource
    private BinLocationDetailLockedService binLocationDetailLockedService;

    /**
     * 通用客户端服务，用于处理与外部系统的交互
     */
    @Resource
    private CommonClientService commonClientService;

    /**
     * 文件上传服务，用于处理文件上传
     */
    @Resource
    private FileStringUploadService fileStringUploadService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 批量创建包裹及关联信息
     * <p>
     * 该方法根据提供的批量创建参数创建OTC包裹及其关联的明细和标签信息。
     * 主要处理步骤包括：
     * 1. 将参数转换为实体对象
     * 2. 设置包裹的基本信息和地址信息
     * 3. 创建包裹明细记录
     * 4. 创建包裹标签记录
     * 5. 批量保存所有生成的实体对象
     * </p>
     *
     * @param createParamList 包裹批量创建参数列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertByParam(List<OtcPackageBatchCreateParam> createParamList) {

        List<OtcPackage> packageList = new ArrayList<>();
        List<OtcPackageDetail> packageDetailList = new ArrayList<>();
        List<OtcPackageLabel> packageLabellList = new ArrayList<>();

        // todo: 拆分 Param to Entity
        for (OtcPackageBatchCreateParam createParam : createParamList) {

            OtcRequest request = BeanUtil.copyNew(createParam.getRequest(), OtcRequest.class);
            OtcWorkorder workorder = BeanUtil.copyNew(createParam.getWorkorder(), OtcWorkorder.class);

            List<OtcRequestPackageFullVO> requestPackageList = createParam.getRequestPackageList();
            for (int j = 0; j < requestPackageList.size(); j++) {
                OtcRequestPackageFullVO requestPackage = requestPackageList.get(j);

                // OtcPackage packageEntity = BeanUtil.copyNew(otbRequestPackage, OtcPackage.class);
                OtcPackage packageEntity = new OtcPackage();
                packageEntity.setId(IdWorker.getId());

                packageEntity.setLineNum(j + 1);
                packageEntity.setTrackingNum(requestPackage.getTrackingNum());
                packageEntity.setInsuranceAmountAmount(workorder.getRequestSnapshotInsuranceAmountAmount());
                packageEntity.setSignatureType(workorder.getRequestSnapshotSignatureType());
                // packageEntity.setShipToAddressName();
                // packageEntity.setShipToAddressCompany();
                // packageEntity.setShipToAddressCountry();
                // packageEntity.setShipToAddressState();
                // packageEntity.setShipToAddressCity();
                // packageEntity.setShipToAddressZipCode();
                // packageEntity.setShipToAddressAddr1();
                // packageEntity.setShipToAddressAddr2();
                // packageEntity.setShipToAddressAddr3();
                // packageEntity.setShipToAddressEmail();
                // packageEntity.setShipToAddressPhone();
                // packageEntity.setShipToAddressNote();
                // packageEntity.setShipFromAddressName();
                // packageEntity.setShipFromAddressCompany();
                // packageEntity.setShipFromAddressCountry();
                // packageEntity.setShipFromAddressState();
                // packageEntity.setShipFromAddressCity();
                // packageEntity.setShipFromAddressZipCode();
                // packageEntity.setShipFromAddressAddr1();
                // packageEntity.setShipFromAddressAddr2();
                // packageEntity.setShipFromAddressAddr3();
                // packageEntity.setShipFromAddressEmail();
                // packageEntity.setShipFromAddressPhone();
                // packageEntity.setShipFromAddressNote();
                packageEntity.setOtcWorkorderId(workorder.getId());
                packageEntity.setShipExpressFlag(workorder.getRequestSnapshotShipExpressFlag());
                packageEntity.setShipMethod(requestPackage.getShipMethod());
                packageEntity.setShipCarrier(requestPackage.getShipCarrier());
                packageEntity.setShipSizeLength(requestPackage.getShipSizeLength());
                packageEntity.setShipSizeWidth(requestPackage.getShipSizeWidth());
                packageEntity.setShipSizeHeight(requestPackage.getShipSizeHeight());
                packageEntity.setShipSizeWeight(requestPackage.getShipSizeWeight());
                packageEntity.setNote(requestPackage.getNote());
                packageEntity.setBuildShipStrategy(workorder.getBuildShipPackageType());
                packageEntity.setPackageStatus(OtcPackageStatusEnum.NEW.getStatus());
                // packageEntity.setDeletedNote();
                // packageEntity.setTenantId();
                // packageEntity.setWarehouseId();
                packageEntity.setRefNum(getRefNum());
                packageEntity.setOtcPickingSlipId(null);
                packageEntity.setReadyToShipTime(null);
                packageEntity.setShippedTime(null);
                // packageEntity.setShipFromAddressIsResidential();
                packageEntity.setShipSizeDimensionUnit(requestPackage.getShipSizeDimensionUnit());
                packageEntity.setShipSizeWeightUnit(requestPackage.getShipSizeWeightUnit());
                // packageEntity.setShipToAddressIsResidential();
                packageEntity.setInsuranceAmountCurrency(workorder.getRequestSnapshotInsuranceAmountCurrency());
                packageEntity.setShipApiProfileRefNum(workorder.getRequestSnapshotShipApiProfileRefNum());
                packageEntity.setPackageMultiboxUpc(requestPackage.getPackageMultiboxUpc());
                packageEntity.setPackageType(workorder.getOrderType());
                packageEntity.setPackageMultiboxLineNum(requestPackage.getPackageMultiboxLineNum());
                packageEntity.setPackageMultiboxProductId(requestPackage.getPackageMultiboxProductId());
                packageEntity.setPackageMultiboxVersionInt(requestPackage.getPackageMultiboxVersionInt());
                // packageEntity.setCreateBy();
                // packageEntity.setUpdateBy();
                // packageEntity.setCreateTime();
                // packageEntity.setUpdateTime();
                // packageEntity.setRemoveFlag();
                // packageEntity.setVersion();

                // 构建收货地址
                buildShipToAddress(packageEntity, request);
                // 构建发货地址
                buildShipFromAddress(packageEntity, workorder, request);

                List<OtcRequestPackageDetailFullVO> detailList = requestPackage.getDetailList();
                for (int i = 0; i < detailList.size(); i++) {
                    OtcRequestPackageDetailFullVO requestPackageDetail = detailList.get(i);
                    OtcPackageDetail packageDetailEntity = BeanUtil.copyNew(requestPackageDetail, OtcPackageDetail.class);
                    packageDetailEntity.setId(IdWorker.getId());
                    packageDetailEntity.setOtcPackageId(packageEntity.getId());
                    packageDetailEntity.setPickedQty(0);
                    packageDetailEntity.setLineNum(i + 1);
                    packageDetailList.add(packageDetailEntity);
                }

                List<OtcRequestPackageLabelFullVO> requestPackageLabelList = requestPackage.getLabelList();
                for (int i = 0; i < requestPackageLabelList.size(); i++) {
                    OtcRequestPackageLabelFullVO requestPackageLabel = requestPackageLabelList.get(i);
                    OtcPackageLabel packageLabelEntity = BeanUtil.copyNew(requestPackageLabel, OtcPackageLabel.class);

                    packageLabelEntity.setId(IdWorker.getId());
                    packageLabelEntity.setOtcPackageId(packageEntity.getId());
                    packageLabelEntity.setLineNum(i + 1);
                    packageLabelEntity.setPrintStatus(PrintStatusEnum.NONE.getStatus());

                    packageLabellList.add(packageLabelEntity);
                }

                packageList.add(packageEntity);
            }
        }

        super.insertBatch(packageList);
        otcPackageDetailService.insertBatch(packageDetailList);
        otcPackageLabelService.insertBatch(packageLabellList);

        OtcPackageAuditLogHelper.recordLog(packageList);
    }

    /**
     * 根据查询条件获取包裹列表
     * <p>
     * 该方法根据指定的查询条件返回符合条件的OTC包裹列表，不包含分页信息。
     * </p>
     *
     * @param query 包含查询条件的OTC包裹查询对象
     * @return 符合条件的OTC包裹分页视图对象列表
     * <p>
     * TODO: 缺少对查询结果的数量限制，可能在数据量大时影响性能
     * 优化建议：添加最大返回条数限制，或强制使用分页方式获取数据
     */
    @Override
    public List<OtcPackagePageVO> listByQuery(OtcPackageListQuery query) {
        List<OtcPackagePageVO> dataList = mapper.listByQuery(query.getOtcPackageQuery(), query.getOtcWorkorderQuery(), null);
        // 填充字段
        fillField(dataList);
        return dataList;
    }

    /**
     * 分页查询包裹列表
     * <p>
     * 该方法根据查询条件和分页参数获取OTC包裹列表，并填充关联的信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的OTC包裹分页视图对象
     */
    @Override
    public PageData<OtcPackagePageVO> pageByQuery(PageSearch<OtcPackageListQuery> search) {
        Page<OtcPackage> page = Conditions.page(search, entityClass);
        OtcPackageListQuery condition = search.getCondition();
        List<OtcPackagePageVO> dataList = mapper.listByQuery(condition.getOtcPackageQuery(), condition.getOtcWorkorderQuery(), page);
        // 填充字段
        fillField(dataList);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取包裹详情
     * <p>
     * 该方法获取指定ID的OTC包裹详细信息，包括包裹明细、标签等关联信息。
     * 如果找不到对应ID的包裹，则抛出业务异常。
     * </p>
     *
     * @param id OTC包裹ID
     * @return 包含详细信息的OTC包裹视图对象
     * @throws BusinessException 如果找不到指定ID的包裹记录
     */
    @Override
    public OtcPackageVO detailById(Long id) {
        OtcPackage entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtcPackage");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcPackage", id));
        }
        return buildOtcPackageVO(entity);
    }

    public List<OtcPackageVO> listByIds(List<Long> idList) {
        return lambdaQuery()
                .in(OtcPackage::getId, idList)
                .list().stream()
                .map(this::buildOtcPackageVO)
                .toList();
    }

    @Override
    public List<OtcPackageVO> listDetailByRequestId(Long requestId) {
        if (ObjectUtil.isEmpty(requestId)) {
            return Collections.emptyList();
        }

        List<RefNumVO> workorderList = otcWorkorderService.listRefNumByRequestId(requestId);

        return listDetailByWorkorderIds(workorderList.stream().map(RefNumVO::getId).toList());
    }

    public List<OtcPackageVO> listDetailByWorkorderIds(List<Long> workorderIds) {

        if (ObjectUtil.isEmpty(workorderIds)) {
            return Collections.emptyList();
        }

        return lambdaQuery()
                .in(OtcPackage::getOtcWorkorderId, workorderIds)
                .list().stream()
                .map(this::buildOtcPackageVO)
                .toList();
    }


    /**
     * 根据参考编号获取包裹详情
     * <p>
     * 该方法根据包裹的参考编号获取包裹详细信息，包括包裹明细、标签等关联信息。
     * 如果找不到对应参考编号的包裹，则抛出业务异常。
     * </p>
     *
     * @param refNum OTC包裹参考编号
     * @return 包含详细信息的OTC包裹视图对象
     * @throws BusinessException 如果找不到指定参考编号的包裹记录
     */
    @Override
    public OtcPackageVO detailByRefNum(String refNum) {
        OtcPackage entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in OtcPackage");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtcPackage", "refNum", refNum));
        }
        return buildOtcPackageVO(entity);
    }

    /**
     * 根据跟踪号获取包裹详情
     * <p>
     * 该方法根据包裹的跟踪号获取包裹详细信息，主要用于查询物流状态。
     * </p>
     *
     * @param query 包含跟踪号的查询对象
     * @return 符合条件的OTC包裹跟踪视图对象列表
     * <p>
     * TODO: 可能需要处理跟踪号不存在的情况
     * 优化建议：添加对跟踪号不存在情况的处理，例如返回空列表或抛出异常
     */
    @Override
    public List<OtcPackageByTrackingNumVO> detailByTrackingNum(OtcPackageTrackingNumQuery query) {
        List<OtcPackageVO> dataList = lambdaQuery()
                .eq(OtcPackage::getTrackingNum, query.getTrackingNum())
                .eq(StringUtil.isNotBlank(query.getPackageStatus()), OtcPackage::getPackageStatus, query.getPackageStatus())
                .in(ObjectUtil.isNotEmpty(query.getPackageStatusList()), OtcPackage::getPackageStatus, query.getPackageStatusList())
                .list()
                .stream()
                .map(this::buildOtcPackageVO)
                .toList();
        return BeanUtil.copyNew(dataList, OtcPackageByTrackingNumVO.class);
    }

    /**
     * 查找是否所有行工单包裹都已创建
     * <p>
     * 该方法检查给定包裹所属工单的所有行是否都已创建了包裹。
     * 主要用于多箱包装的完整性检查。
     * </p>
     *
     * @param pkg 需要检查的包裹
     * @return 同一工单下的所有包裹列表
     */
    @Override
    public List<OtcPackage> findIsAllBuildOwnerLineWorkorderPkgList(OtcPackage pkg) {
        // 一次查询
        List<ProductMultibox> boxList = productMultiboxService.getListByProductAndVersionInt(
                pkg.getPackageMultiboxProductId(), pkg.getPackageMultiboxVersionInt()
        );
        int boxCount = boxList.size();

        List<OtcPackage> allPkgInWorkorderList = lambdaQuery()
                .eq(OtcPackage::getOtcWorkorderId, pkg.getOtcWorkorderId())
                .list();

        return singleProductOwnerPackageList(allPkgInWorkorderList).stream()
                .filter(lines -> lines.size() == boxCount)
                .filter(lines -> lines.stream().anyMatch(line -> Objects.equals(line.getId(), pkg.getId())))
                .findAny()
                .orElse(Collections.emptyList());
    }

    /**
     * 多箱包装后处理
     * <p>
     * 该方法在多箱包装完成后，处理关联的包裹明细信息。
     * </p>
     *
     * @param pkg 多箱包裹
     * @return 处理后的包裹明细列表
     * <p>
     * TODO: 方法名称不够清晰，无法直观地了解其功能
     * 优化建议：重命名为更具描述性的名称，如processMultiBoxPackageDetails
     */
    @Override
    public List<OtcPackageDetail> afterProcessingMultiBox(OtcPackage pkg) {
        List<OtcPackageDetail> packageDetailList = otcPackageDetailService.groupByPackageId(Collections.singletonList(pkg.getId()))
                .values()
                .stream()
                .flatMap(Collection::stream)
                .toList();
        // MultiBox处理逻辑
        if (ObjectUtil.isNotEmpty(pkg.getPackageMultiboxProductId())) {
            // 当前工单下包含所有包裹
            List<String> canReadyToShipStatues = Arrays.asList(OtcPackageStatusEnum.READY_TO_SHIP.getStatus(), OtcPackageStatusEnum.SHIPPED.getStatus());
            List<OtcPackage> inCurrentWorkorderPkgList = findIsAllBuildOwnerLineWorkorderPkgList(pkg);
            boolean allReadyToShip = ObjectUtil.isNotEmpty(inCurrentWorkorderPkgList) && inCurrentWorkorderPkgList.stream()
                    .allMatch(obj -> canReadyToShipStatues.contains(obj.getPackageStatus()));

            OtcPackageDetail detail = new OtcPackageDetail();
            // 赋值第一个Detail的id
            detail.setId(0L);
            detail.setQty(allReadyToShip ? 1 : 0);
            detail.setPickedQty(detail.getQty());
            detail.setLineNum(1);
            detail.setOtcPackageId(pkg.getId());
            detail.setProductId(pkg.getPackageMultiboxProductId());
            packageDetailList = Collections.singletonList(detail);
        }
        return packageDetailList;
    }

    /**
     * 分批查询包裹列表
     * <p>
     * 该方法使用批处理方式查询包裹列表，可用于处理大量数据的情况。
     * </p>
     *
     * @param query        查询条件
     * @param startCurrent 起始页码
     * @param batchSize    每批大小
     * @return 符合条件的OTC包裹分页视图对象列表
     */
    @Override
    public List<OtcPackagePageVO> pageByQuery(OtcPackageListQuery query, int startCurrent, int batchSize) {
        Page<OtcPackage> page = new Page<>(startCurrent, batchSize);
        page.setSearchCount(false);
        return mapper.listByQuery(query.getOtcPackageQuery(), query.getOtcWorkorderQuery(), page);
    }

    /**
     * 标记包裹为已发货
     * <p>
     * 该方法将指定的包裹标记为已发货状态，并更新相关信息。
     * </p>
     *
     * @param query 包含要标记的包裹ID的查询对象
     * @return 操作是否成功
     * <p>
     * TODO: 方法内部实现不完整，缺少对多种发货场景的处理
     * 优化建议：完善方法实现，增加对不同发货状态和场景的处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean markSipped(OtcPackageShippedQuery query) {
        Validate.notEmpty(query.getIdList(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "idList"));
        List<OtcPackage> packages = lambdaQuery()
                .in(OtcPackage::getId, query.getIdList())
                .list();
        return shipCheckAndUpdate(packages);
    }

    /**
     * 根据拣货单ID查询包裹列表
     * <p>
     * 该方法查询指定拣货单ID下的所有包裹。
     * </p>
     *
     * @param id 拣货单ID
     * @return 该拣货单下的包裹列表
     */
    @Override
    public List<OtcPackage> listByPickingSlipId(Long id) {
        return lambdaQuery()
                .eq(OtcPackage::getOtcPickingSlipId, id)
                .list();
    }

    /**
     * 通过仓库创建包裹
     * <p>
     * 该方法根据仓库创建参数创建新的包裹，包括包裹基本信息、明细信息等。
     * 主要用于普通包裹的创建。
     * </p>
     *
     * @param warehouseCreateParam 仓库创建参数
     * @return 包含创建结果的完整输出视图对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcPackageFullOutputVO buildByWarehouse(OtcPackageWarehouseCreateParam warehouseCreateParam) {
        // SlapAndGo、SOSP类型处理
        return buildByWarehouse(warehouseCreateParam,
                context -> buildSingleProductPackage(warehouseCreateParam, context),
                context -> buildPackageDetail(context, warehouseCreateParam.getDetailList())
        ).getResult();
    }

    /**
     * 通过仓库创建多箱包裹
     * <p>
     * 该方法根据多箱创建参数创建新的多箱包裹，包括包裹基本信息、明细信息等。
     * 主要用于多箱包装场景。
     * </p>
     *
     * @param multiBoxCreateParam 多箱创建参数
     * @return 包含创建结果的完整输出视图对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcPackageFullOutputVO multiBoxBuildByWarehouse(OtcMultiBoxPackageWarehouseCreateParam multiBoxCreateParam) {
        // MultiBox类型处理
        OtcBuildByWarehouseContextBO resultContext = buildByWarehouse(multiBoxCreateParam,
                context -> buildMultiBoxPackage(multiBoxCreateParam, context.getWorkOrder()),
                context -> buildMultiBoxPackageDetail(context, multiBoxCreateParam)
        );

        return resultContext.getResult();
    }

    /**
     * 通过仓库创建SOMP包裹
     * <p>
     * 该方法根据SOMP创建参数创建新的SOMP包裹，包括包裹基本信息、明细信息等。
     * 主要用于SOMP(Single Order Multiple Package)场景。
     * </p>
     *
     * @param sompWarehouseCreateParam SOMP创建参数
     * @return 包含创建结果的完整输出视图对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcPackageFullOutputVO sompBuildByWarehouse(OtcSompPackageWarehouseCreateParam sompWarehouseCreateParam) {
        // SOMP类型处理
        return buildByWarehouse(sompWarehouseCreateParam,
                context -> buildCommonPackage(sompWarehouseCreateParam, context.getWorkOrder()),
                context -> buildPackageDetail(context, sompWarehouseCreateParam.getDetailList())
        ).getResult();
    }

    /**
     * 根据工单ID列表查询包裹
     * <p>
     * 该方法查询指定工单ID列表下的所有包裹。
     * </p>
     *
     * @param workOrderIdList 工单ID列表
     * @return 符合条件的包裹列表
     */
    @Override
    public List<OtcPackage> listByWorkOrderIdList(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtcPackage::getOtcWorkorderId, workOrderIdList).list();
    }

    /**
     * 包裹拣货处理
     * <p>
     * 该方法实现了包裹的拣货操作，是包裹从“新建”到“已拣货”状态的关键流程。
     * 主要处理步骤包括：
     * 1. 获取工单下的可拣货包裹
     * 2. 处理多箱包裹的特殊拣货逻辑
     * 3. 分配产品到包裹详情
     * 4. 处理包裹拣货信息
     * 5. 更新并分配包裹仓储位信息
     * 6. 更新包裹状态
     * 7. 记录拣货操作日志
     * </p>
     * <p>
     * 该方法的复杂性在于需要处理多种类型的包裹（普通包裹、多箱包裹等），
     * 并且需要处理复杂的库存分配逻辑，确保拣货数量的准确性。
     * </p>
     *
     * @param pickList 拣货列表，包含要拣货的工单详情信息
     * @return 处理后的包裹列表，包含状态已更新为“已拣货”的包裹
     * <p>
     * TODO: 该方法的逻辑复杂度高，职责过多，应该拆分为多个小方法
     * 优化建议：将包裹获取、多箱处理、拣货分配、状态更新等逻辑分离为独立的方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OtcPackage> pick(List<OtcWorkorderDetailPickVO> pickList) {
        // 获取拣货后工单详情下的所有包裹
        List<Long> workOrderIdList = StreamUtils.distinctMap(pickList, OtcWorkorderDetailPickVO::getOtcWorkorderId);
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        Map<Long, OtcPackage> packageMap = this.findCanPickMap(workOrderIdList);
        if (ObjectUtil.isEmpty(packageMap)) {
            return Collections.emptyList();
        }

        // 工单下产品与包裹详情的映射
        List<OtcPackageDetail> packageDetailList = this.processPickMultiBoxDetails(workOrderIdList, packageMap);
        Map<String, List<OtcPackageDetail>> detailGroupByProductIdMap = packageDetailList
                .stream()
                // 工单 + 产品
                .collect(Collectors.groupingBy(obj -> obj.getProductId() + StringPool.COLON + packageMap.get(obj.getOtcPackageId()).getOtcWorkorderId()));
        // 根据包裹RefNum排序 分配产品
        detailGroupByProductIdMap.forEach((key, detailList) ->
                detailList.sort(Comparator.comparing(o -> packageMap.get(o.getOtcPackageId()).getRefNum())));

        // 处理包裹拣货信息
        List<OtcPackageDetailPickVO> packageDetailUpdatePickList = this.processPackageDetailPickList(pickList, detailGroupByProductIdMap);

        // 更新并分配包裹仓储位信息
        this.updateAndAllocatePackageDetail(detailGroupByProductIdMap, packageDetailUpdatePickList);

        // 更新包裹状态
        List<OtcPackage> updatePackageList = packageDetailList.stream()
                .collect(Collectors.groupingBy(OtcPackageDetail::getOtcPackageId))
                .entrySet()
                .stream()
                .map(entry -> {
                    // 全部拣货完
                    List<OtcPackageDetail> details = entry.getValue();
                    boolean allPicked = details.stream()
                            .allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()))
                            || ObjectUtil.isEmpty(details);
                    OtcPackage pkg = packageMap.get(entry.getKey());
                    // 更新状态
                    pkg.setPackageStatus(allPicked
                            ? OtcPackageStatusEnum.PICKED.getStatus()
                            : pkg.getPackageStatus()
                    );
                    return pkg;
                })
                .filter(pkg -> Objects.equals(pkg.getPackageStatus(), OtcPackageStatusEnum.PICKED.getStatus()))
                .toList();

        Validate.isTrue(updateBatch(updatePackageList) == updatePackageList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Failed to update all package statuses"));

        // 包裹: New -> InPicking -> Picked 日志
        OtcPackageAuditLogHelper.recordLog(updatePackageList);

        return updatePackageList;
    }

    /**
     * 筛选并标记包裹为已发货
     * <p>
     * 该方法筛选符合条件的包裹并标记为已发货状态。
     * </p>
     *
     * @param query 查询条件
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean filterMarkShipped(OtcPackageListQuery query) {
        // 未传入查询条件
        OtcPackageQuery otcPackageQuery = query.getOtcPackageQuery();
        if (ObjectUtil.isNull(otcPackageQuery)) {
            otcPackageQuery = new OtcPackageQuery();
            query.setOtcPackageQuery(otcPackageQuery);
        }
        // ReadyToShip 才可Ship
        Validate.isTrue(Objects.equals(OtcPackageStatusEnum.READY_TO_SHIP.getStatus(), otcPackageQuery.getPackageStatus()),
                String.format(ErrorMessages.STATUS_REQUIRED, "OtcPackage", "READY_TO_SHIP", otcPackageQuery.getPackageStatus())
        );

        // 查询
        List<OtcPackagePageVO> packagePageVOList = listByQuery(query);
        // ship 并更新
        return shipCheckAndUpdate(BeanUtil.copyNew(packagePageVOList, OtcPackage.class));
    }

    /**
     * 标记标签为已打印
     * <p>
     * 该方法将指定的包裹标签标记为已打印状态。
     * </p>
     *
     * @param query 打印查询条件
     * @return 操作是否成功
     */
    @Override
    public boolean labelMarkPrinted(PrintQuery query) {
        PrintStatusEnum.checkStatus(query.getPrintStatus());

        OtcPackageLabel label = otcPackageLabelService.getById(query.getId());
        Validate.notNull(label, String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcPackageLabel", "for the specified package"));
        // 获取包裹
        OtcPackage pkg = getById(label.getOtcPackageId());
        Validate.notNull(pkg, String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtcPackage", "for the specified label"));

        // 分布式锁排队
        RedissonKit.getInstance()
                .lock(RedisConstant.PACKAGE_READY_TO_SHIP_LOCKED + pkg.getOtcPickingSlipId(),
                        lock -> otcPackageLabelService.markPrinted(query)
                );
        return true;
    }

    /**
     * 根据拣货单ID列表查询包裹
     * <p>
     * 该方法查询指定拣货单ID列表下的所有包裹。
     * </p>
     *
     * @param pickingSlipIds 拣货单ID列表
     * @return 符合条件的包裹列表
     */
    @Override
    public List<OtcPackage> listByPickingSlipIdList(List<Long> pickingSlipIds) {
        if (ObjectUtil.isEmpty(pickingSlipIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtcPackage::getOtcPickingSlipId, pickingSlipIds).list();
    }

    @Override
    public List<OtcPackage> listByWorkorderIdAndIds(List<Long> idList, Long workorderId) {
        return lambdaQuery()
                .in(ObjectUtil.isNotEmpty(idList), IdModel::getId, idList)
                .eq(ObjectUtil.isNotNull(workorderId), OtcPackage::getOtcWorkorderId, workorderId)
                .list();
    }

    @Override
    public List<OtcPackageDetail> findShippedPkgDetails(List<OtcPackage> packageList) {
        var pkgShippedIds = packageList.stream()
                .filter(obj -> Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.SHIPPED.getStatus()))
                .filter(obj -> ObjectUtil.isEmpty(obj.getPackageMultiboxProductId()))
                .map(IdModel::getId)
                .toList();

        var shippedPkgDetails = otcPackageDetailService.listByPackageIds(pkgShippedIds);

        // MultiBox的处理
        var multiBoxPkgList = packageList.stream()
                .filter(obj -> Objects.equals(obj.getPackageStatus(), OtcPackageStatusEnum.SHIPPED.getStatus()))
                .filter(obj -> ObjectUtil.isNotEmpty(obj.getPackageMultiboxProductId()))
                .toList();

        var result = new ArrayList<>(shippedPkgDetails);
        result.addAll(this.findMultiBoxVirtualDetails(multiBoxPkgList));
        return result;
    }

    @Override
    public List<OtcPackageDetail> findMultiBoxVirtualDetails(List<OtcPackage> multiBoxPkgList) {
        multiBoxPkgList = Optional.ofNullable(multiBoxPkgList).stream()
                .flatMap(Collection::stream)
                .filter(obj -> ObjectUtil.isNotEmpty(obj.getPackageMultiboxProductId()))
                .toList();
        if (ObjectUtil.isEmpty(multiBoxPkgList)) {
            return Collections.emptyList();
        }

        var boxSizeMap = productMultiboxService.listByProductIdAndVersionIntList(
                        StreamUtils.distinctMap(multiBoxPkgList, OtcPackage::getPackageMultiboxProductId),
                        StreamUtils.distinctMap(multiBoxPkgList, OtcPackage::getPackageMultiboxVersionInt)
                )
                .stream()
                .collect(Collectors.groupingBy(obj -> obj.getProductId() + StringPool.COLON + obj.getMultiboxVersionInt(), Collectors.counting()));

        // 每个MultiBox产品下的盒子
        return multiBoxPkgList.stream()
                .collect(Collectors.groupingBy(OtcPackage::getOtcWorkorderId))
                .entrySet()
                .stream()
                .flatMap(entry -> {
                    var currentPkgList = entry.getValue();
                    var pkg = currentPkgList.get(0);

                    var boxSize = boxSizeMap.get(pkg.getPackageMultiboxProductId() + StringPool.COLON + pkg.getPackageMultiboxVersionInt());

                    // 每个MultiBox产品下的盒子
                    return this.singleProductOwnerPackageList(currentPkgList)
                            .stream()
                            .filter(obj -> obj.size() == boxSize)
                            .map(obj -> {
                                var multiBoxDetail = new OtcPackageDetail();
                                multiBoxDetail.setProductId(pkg.getPackageMultiboxProductId());
                                multiBoxDetail.setQty(1);
                                return multiBoxDetail;
                            });
                })
                .toList();
    }

    @Override
    public void fillPackageId(OtcPackageDetailDTO dto) {
        OtcPackage otcPackage = super.lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(dto.getRefNum()), OtcPackage::getRefNum, dto.getRefNum())
                .eq(ObjectUtil.isNotEmpty(dto.getTrackingNum()), OtcPackage::getTrackingNum, dto.getTrackingNum())
                .one();
        dto.setPackageId(otcPackage.getId());
    }

    // ////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 构建多箱包裹
     * <p>
     * 该方法根据多箱创建参数和工单信息构建多箱包裹实体。
     * </p>
     *
     * @param warehouseCreateParam 多箱创建参数
     * @param workOrder            工单信息
     * @return 构建的多箱包裹实体
     * <p>
     * TODO: 该方法与buildCommonPackage存在代码重复
     * 优化建议：提取共同代码，减少重复
     */
    private OtcPackage buildMultiBoxPackage(OtcMultiBoxPackageWarehouseCreateParam warehouseCreateParam, OtcWorkorder workOrder) {
        OtcPackage pkg = buildCommonPackage(warehouseCreateParam, workOrder);
        // PackageMultiBox
        OtcMultiBoxPackageWarehouseCreateParam.PackageMultibox multiBox = warehouseCreateParam.getPackageMultibox();
        Validate.notNull(multiBox, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "packageMultiBox"));
        pkg.setPackageMultiboxProductId(multiBox.getProductId());
        pkg.setPackageMultiboxLineNum(multiBox.getLineNum());
        pkg.setPackageMultiboxUpc(multiBox.getUpc());
        pkg.setPackageMultiboxVersionInt(multiBox.getMultiboxVersionInt());

        return pkg;
    }

    /**
     * 构建通用包裹
     * <p>
     * 该方法根据通用创建参数和工单信息构建包裹实体。
     * 是一个泛型方法，可以处理多种类型的创建参数。
     * </p>
     *
     * @param warehouseCreateParam 创建参数
     * @param workOrder            工单信息
     * @param <T>                  创建参数类型
     * @return 构建的包裹实体
     */
    private <T extends OtcPackageWarehouseCreateParam> OtcPackage buildCommonPackage(T warehouseCreateParam, OtcWorkorder workOrder) {
        if (ObjectUtil.isEmpty(warehouseCreateParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        OtcPackage pkg = new OtcPackage();
        pkg.setId(IdWorker.getId());
        pkg.setOtcWorkorderId(workOrder.getId());

        // 设置基本信息
        pkg.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PACKAGE.getCode()));
        pkg.setShipCarrier(workOrder.getShipCarrier());
        pkg.setShipMethod(workOrder.getShipMethod());
        pkg.setOtcPickingSlipId(workOrder.getOtcPickingSlipId());
        pkg.setPackageStatus(OtcPackageStatusEnum.NEW.getStatus());
        pkg.setBuildShipStrategy(OtcBuildShipPackageEnum.BY_WAREHOUSE.getStatus());

        // ShipSize
        OtcSompPackageWarehouseCreateParam.ShipSize shipSize = warehouseCreateParam.getShipSize();
        Validate.notNull(shipSize, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "shipSize"));
        pkg.setShipSizeLength(shipSize.getLength());
        pkg.setShipSizeWidth(shipSize.getWidth());
        pkg.setShipSizeHeight(shipSize.getHeight());
        pkg.setShipSizeWeight(shipSize.getWeight());
        pkg.setShipSizeWeightUnit(StringUtil.isBlank(shipSize.getWeightUnit())
                ? OtcWeightUnitEnum.LB.getUnit()
                : shipSize.getWeightUnit()
        );
        pkg.setShipSizeDimensionUnit(StringUtil.isBlank(shipSize.getDimensionUnit())
                ? OtcDimensionUnitEnum.IN.getUnit()
                : shipSize.getDimensionUnit());

        pkg.setInsuranceAmountAmount(workOrder.getRequestSnapshotInsuranceAmountAmount());
        pkg.setPackageType(workOrder.getBuildShipPackageType());

        OtcRequest request = otcRequestService.getById(workOrder.getOtcRequestId());
        // 构建收货地址
        buildShipToAddress(pkg, request);
        // 构建发货地址
        buildShipFromAddress(pkg, workOrder, request);
        // 构建发货账户信息
        buildShipPayment(pkg, workOrder);
        return pkg;
    }

    /**
     * 构建单产品包裹
     * <p>
     * 该方法根据创建参数和上下文信息构建单个产品的包裹实体。
     * </p>
     *
     * @param warehouseCreateParam 创建参数
     * @param context              上下文信息
     * @param <T>                  创建参数类型
     * @return 构建的单产品包裹实体
     */
    private <T extends OtcPackageWarehouseCreateParam> OtcPackage buildSingleProductPackage(T warehouseCreateParam, OtcBuildByWarehouseContextBO context) {
        OtcWorkorder workOrder = context.getWorkOrder();
        // 工单详情
        List<OtcWorkorderDetail> workorderDetailList = context.getWorkorderDetailList();
        Validate.notEmpty(workorderDetailList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "workorderDetailList"));
        OtcWorkorderDetail workorderDetail = workorderDetailList.get(0);

        // 打包数量不能超过工单需要数量
        Validate.isTrue(workorderDetail.getQty() > countByWorkorderId(workOrder.getId()),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Workorder " + workOrder.getRefNum() + " package has already been created")
        );
        return buildCommonPackage(warehouseCreateParam, workOrder);
    }

    /**
     * 根据工单ID统计包裹数量
     * <p>
     * 该方法统计指定工单ID下的包裹数量，用于生成行号。
     * </p>
     *
     * @param workOrderId 工单ID
     * @return 包裹数量
     */
    private Long countByWorkorderId(Long workOrderId) {
        return lambdaQuery()
                .eq(OtcPackage::getOtcWorkorderId, workOrderId)
                .ne(OtcPackage::getPackageStatus, OtcPackageStatusEnum.CANCELLED.getStatus())
                .count();
    }

    /**
     * 构建包裹的收货地址信息
     * <p>
     * 该方法根据请求信息构建包裹的收货地址信息。
     * </p>
     *
     * @param pkg     包裹实体
     * @param request 请求信息
     */
    private void buildShipToAddress(OtcPackage pkg, OtcRequest request) {
        pkg.setShipToAddressName(request.getShipToAddressName());
        pkg.setShipToAddressCompany(request.getShipToAddressCompany());
        pkg.setShipToAddressCountry(request.getShipToAddressCountry());
        pkg.setShipToAddressState(request.getShipToAddressState());
        pkg.setShipToAddressCity(request.getShipToAddressCity());
        pkg.setShipToAddressZipCode(request.getShipToAddressZipCode());
        pkg.setShipToAddressAddr1(request.getShipToAddressAddr1());
        pkg.setShipToAddressAddr2(request.getShipToAddressAddr2());
        pkg.setShipToAddressAddr3(request.getShipToAddressAddr3());
        pkg.setShipToAddressEmail(request.getShipToAddressEmail());
        pkg.setShipToAddressPhone(request.getShipToAddressPhone());
        pkg.setShipToAddressNote(request.getShipToAddressNote());
        pkg.setShipToAddressIsResidential(request.getShipToAddressIsResidential());

        pkg.setShipExpressFlag(request.getShipExpressFlag());
        pkg.setSignatureType(request.getSignatureType());
        pkg.setShipApiProfileRefNum(request.getShipApiProfileRefNum());
        pkg.setInsuranceAmountCurrency(request.getInsuranceAmountCurrency());
        pkg.setInsuranceAmountAmount(request.getInsuranceAmountAmount());
    }

    /**
     * 构建包裹的支付信息
     * <p>
     * 该方法根据工单信息构建包裹的支付信息。
     * </p>
     *
     * @param pkg       包裹实体
     * @param workOrder 工单信息
     */
    private void buildShipPayment(OtcPackage pkg, OtcWorkorder workOrder) {
        // TODO PaymentAccountNum、PaymentAddress   ShipProfileManager.GetShipProfile
    }

    /**
     * 构建包裹的发货地址信息
     * <p>
     * 该方法根据工单和请求信息构建包裹的发货地址信息。
     * </p>
     *
     * @param pkg       包裹实体
     * @param workOrder 工单信息
     * @param request   请求信息
     */
    private void buildShipFromAddress(OtcPackage pkg, OtcWorkorder workOrder, OtcRequest request) {
        // ShipFromAddress
        Validate.notNull(request, "WorkOrder: {} Request is not exists", workOrder.getRefNum());
        String shipFromAddressName = request.getShipFromAddressName();
        String shipFromAddressCountry = request.getShipFromAddressCountry();
        // 未指定则使用仓库地址
        if (StringUtil.isBlank(shipFromAddressName) || StringUtil.isBlank(shipFromAddressCountry)) {
            Warehouse warehouse = warehouseService.getById(request.getWarehouseId());
            Validate.notNull(warehouse, "WorkOrder: {} Warehouse is not exists", workOrder.getRefNum());
            pkg.setShipFromAddressName(warehouse.getAddressName());
            pkg.setShipFromAddressCompany(warehouse.getAddressCompany());
            pkg.setShipFromAddressCountry(warehouse.getAddressCountry());
            pkg.setShipFromAddressState(warehouse.getAddressState());
            pkg.setShipFromAddressCity(warehouse.getAddressCity());
            pkg.setShipFromAddressZipCode(warehouse.getAddressZipCode());
            pkg.setShipFromAddressAddr1(warehouse.getAddressAddr1());
            pkg.setShipFromAddressAddr2(warehouse.getAddressAddr2());
            pkg.setShipFromAddressAddr3(warehouse.getAddressAddr3());
            pkg.setShipFromAddressEmail(warehouse.getAddressEmail());
            pkg.setShipFromAddressPhone(warehouse.getAddressPhone());
            pkg.setShipFromAddressNote(warehouse.getAddressNote());
            pkg.setShipFromAddressIsResidential(!ObjectUtil.isEmpty(warehouse.getAddressIsResidential()) && warehouse.getAddressIsResidential() == 1);
        } else {
            pkg.setShipFromAddressName(shipFromAddressName);
            pkg.setShipFromAddressCompany(request.getShipFromAddressCompany());
            pkg.setShipFromAddressCountry(shipFromAddressCountry);
            pkg.setShipFromAddressState(request.getShipFromAddressState());
            pkg.setShipFromAddressCity(request.getShipFromAddressCity());
            pkg.setShipFromAddressZipCode(request.getShipFromAddressZipCode());
            pkg.setShipFromAddressAddr1(request.getShipFromAddressAddr1());
            pkg.setShipFromAddressAddr2(request.getShipFromAddressAddr2());
            pkg.setShipFromAddressAddr3(request.getShipFromAddressAddr3());
            pkg.setShipFromAddressEmail(request.getShipFromAddressEmail());
            pkg.setShipFromAddressPhone(request.getShipFromAddressPhone());
            pkg.setShipFromAddressNote(request.getShipFromAddressNote());
            pkg.setShipFromAddressIsResidential(request.getShipFromAddressIsResidential());
        }

    }

    /**
     * 创建包裹详情参数
     *
     * @param detailList 仓库人员创建参数
     * @return /
     */
    private List<OtcPackageDetail> buildPackageDetail(OtcBuildByWarehouseContextBO context, List<OtcPackageWarehouseCreateParam.Detail> detailList) {
        Validate.notEmpty(detailList, "PackageDetail cannot be empty");
        // 构建[ 0 - size-1 ]的流
        return IntStream.range(0, detailList.size())
                .boxed()
                .map(idx -> {
                    OtcPackageWarehouseCreateParam.Detail detail = detailList.get(idx);
                    return new OtcPackageDetail()
                            .setLineNum(idx + 1)
                            .setOtcPackageId(context.getPkg().getId())
                            .setProductId(detail.getProductId())
                            .setPickedQty(0)
                            .setQty(detail.getQty());
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建Multi包裹详情参数
     *
     * @return /
     */
    private List<OtcPackageDetail> buildMultiBoxPackageDetail(OtcBuildByWarehouseContextBO context, OtcMultiBoxPackageWarehouseCreateParam multiBoxCreateParam) {
        // 校验
        checkBuildMultiBoxParam(context, multiBoxCreateParam);

        OtcPackage pkg = context.getPkg();
        // 是否填充所有箱子
        boolean isFullAllBox = context.getCanBuildMap().values().stream().noneMatch(can -> can);

        // 方便后续工单打包数量更新
        context.setAfterBuildConsumer(ctx -> {
            OtcPackageDetail detail = new OtcPackageDetail();
            detail.setQty(isFullAllBox ? 1 : 0);
            detail.setPickedQty(0);
            detail.setLineNum(0);
            detail.setOtcPackageId(pkg.getId());
            detail.setProductId(pkg.getPackageMultiboxProductId());
            ctx.setPackageDetailList(Collections.singletonList(detail));
        });

        // 构建[ 0 - size-1 ]的流
        List<OtcPackageWarehouseCreateParam.Detail> detailList = multiBoxCreateParam.getDetailList();
        return IntStream.range(0, detailList.size())
                .boxed()
                .map(idx -> {
                    OtcPackageWarehouseCreateParam.Detail detail = detailList.get(idx);
                    return new OtcPackageDetail()
                            .setLineNum(idx + 1)
                            .setOtcPackageId(context.getPkg().getId())
                            .setProductId(detail.getProductId())
                            .setPickedQty(detail.getQty())
                            .setQty(detail.getQty());
                })
                .collect(Collectors.toList());

    }

    /**
     * 校验MultiBox盒子构建参数
     *
     * @param context             上下文
     * @param multiBoxCreateParam 盒子创建参数
     */
    private void checkBuildMultiBoxParam(OtcBuildByWarehouseContextBO context, OtcMultiBoxPackageWarehouseCreateParam multiBoxCreateParam) {
        OtcWorkorder workOrder = context.getWorkOrder();
        OtcMultiBoxPackageWarehouseCreateParam.PackageMultibox packageMultiBox = multiBoxCreateParam.getPackageMultibox();
        // 获取当前MultiBox信息
        ProductMultibox productMultibox = productMultiboxService.findOneByProductIdAndVersionIntAndUpc(
                packageMultiBox.getProductId(), packageMultiBox.getMultiboxVersionInt(), packageMultiBox.getUpc()
        );
        context.setCurrentBox(productMultibox);

        Validate.notNull(productMultibox, "WorkOrder: {} ProductMultiBoxDetail {} is not exists",
                workOrder.getRefNum(), packageMultiBox.getUpc()
        );
        // 校验箱子可创建的数量
        List<OtcWorkorderDetail> workorderDetailList = context.getWorkorderDetailList();
        Validate.notEmpty(workorderDetailList, "WorkOrder: {} ProductMultiBoxDetail {} is not match WorkorderDetail",
                workOrder.getRefNum(), productMultibox.getUpc()
        );
        // MultiBox 工单详情只有一种产品
        OtcWorkorderDetail multiBoxDetail = workorderDetailList.get(0);
        Integer canBuildMultiBoxCount = ObjectUtil.nullToDefault(multiBoxDetail.getQty(), 0);

        Long hasBuildMultiBox = alreadyBuildProductMultiBoxCount(workOrder, productMultibox);
        // 该盒子已经创建
        Validate.isTrue(hasBuildMultiBox <= canBuildMultiBoxCount,
                "WorkOrder: {}， The {} of packages created in the box has exceeded the limit",
                workOrder.getRefNum(), productMultibox.getUpc()
        );

        // 多箱不存在
        Map<Long, List<ProductMultiboxDetail>> detailMap = productMultiboxDetailService.groupByMultiBoxIdList(Collections.singletonList(productMultibox.getId()));
        Validate.notEmpty(detailMap, "WorkOrder: {} ProductMultiBoxDetail {} is not exists",
                workOrder.getRefNum(), productMultibox.getUpc()
        );
        // 多箱详情信息
        Map<Long, Integer> boxProductQtyMap = detailMap.get(productMultibox.getId())
                .stream()
                .collect(Collectors.groupingBy(ProductMultiboxDetail::getProductId,
                        Collectors.summingInt(ProductMultiboxDetail::getQty)
                ));
        // 产品已经拣货数量
        Map<Long, Integer> productBuildQtyMap = multiBoxCreateParam.getDetailList()
                .stream()
                .collect(Collectors.groupingBy(OtcPackageWarehouseCreateParam.Detail::getProductId,
                        Collectors.summingInt(OtcPackageWarehouseCreateParam.Detail::getQty)
                ));

        // 校验数量，这里仅仅只是一个箱子
        boolean allPicked = boxProductQtyMap.entrySet()
                .stream()
                // 判断是否全部拣货
                .allMatch(entry -> Objects.equals(productBuildQtyMap.getOrDefault(entry.getKey(), 0), entry.getValue()));

        Validate.isTrue(allPicked, "Not all products in this box [{}/{}] have been picked up",
                productMultibox.getLineNum(), productMultibox.getUpc()
        );

        // 校验 需要先填充完一个MultiBox才能下一个
        checkFirstFullAllBox(context, packageMultiBox);

    }

    /**
     * 校验 MultiBox是否已经创建完
     *
     * @param packageMultiBox 包裹MultiBox
     */
    private void checkFirstFullAllBox(OtcBuildByWarehouseContextBO context, OtcMultiBoxPackageWarehouseCreateParam.PackageMultibox packageMultiBox) {
        // 当前箱子
        ProductMultibox productMultibox = context.getCurrentBox();
        List<ProductMultibox> boxList = productMultiboxService.getListByProductAndVersionInt(
                productMultibox.getProductId(), productMultibox.getMultiboxVersionInt()
        );
        // 初始化箱子数量
        final String udxBoxFormat = "{}:{}:{}:{}";
        Map<String, Integer> boxQtyMap = boxList.stream()
                .collect(Collectors.groupingBy(box -> StringUtil.format(udxBoxFormat,
                                box.getProductId(), box.getMultiboxVersionInt(), box.getUpc(), box.getLineNum()
                        ), Collectors.summingInt(pkg -> 0)
                ));
        List<OtcPackage> packageList = new ArrayList<>(context.getInWorkorderPackageList());
        // 已经构建箱子数量
        packageList.stream()
                .collect(Collectors.groupingBy(pkg -> StringUtil.format(udxBoxFormat,
                        pkg.getPackageMultiboxProductId(), pkg.getPackageMultiboxVersionInt(),
                        pkg.getPackageMultiboxUpc(), pkg.getPackageMultiboxLineNum()), Collectors.summingInt(pkg -> 1)
                ))
                .entrySet()
                .stream()
                .filter(entry -> boxQtyMap.containsKey(entry.getKey()))
                // 赋值每个箱子的数量
                .forEach(entry -> boxQtyMap.put(entry.getKey(), entry.getValue()));

        // 所有数量与最小的数量对其，否则无法创建
        int min = boxQtyMap.values().stream().mapToInt(Integer::intValue).min().orElse(0);
        Map<String, Boolean> canBuildMap = boxQtyMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue() == min));

        // 当前箱子Key
        final String currentBoxKey = StringUtil.format(udxBoxFormat,
                packageMultiBox.getProductId(), packageMultiBox.getMultiboxVersionInt(),
                packageMultiBox.getUpc(), packageMultiBox.getLineNum());

        boolean currentCanBuildBox = canBuildMap.containsKey(currentBoxKey)
                && Boolean.TRUE.equals(canBuildMap.put(currentBoxKey, Boolean.FALSE));

        Validate.isTrue(currentCanBuildBox, "WorkOrder: {} This {} box has already been created. Please create another box first",
                context.getWorkOrder().getRefNum(), packageMultiBox.getUpc()
        );
        context.setCanBuildMap(canBuildMap);
    }

    /**
     * 已经构建过的盒子
     *
     * @param workOrder       工单
     * @param productMultibox 盒子
     * @return /
     */
    private Long alreadyBuildProductMultiBoxCount(OtcWorkorder workOrder, ProductMultibox productMultibox) {
        return lambdaQuery()
                .eq(OtcPackage::getOtcWorkorderId, workOrder.getId())
                .eq(OtcPackage::getPackageMultiboxProductId, productMultibox.getProductId())
                .eq(OtcPackage::getPackageMultiboxUpc, productMultibox.getUpc())
                .eq(OtcPackage::getPackageMultiboxLineNum, productMultibox.getLineNum())
                .count();
    }

    /**
     * 分配工单包装数量
     *
     * @param pkg       包裹
     * @param workOrder 工单
     */
    private void generateLineNum(OtcPackage pkg, OtcWorkorder workOrder) {
        // 获取已存在包裹
        List<OtcPackage> packageInStoreList = listByWorkOrderIdList(Collections.singletonList(workOrder.getId()));
        // 包裹行数
        int dbPackageLineNum = packageInStoreList.stream()
                .mapToInt(OtcPackage::getLineNum)
                .max()
                .orElse(0);
        pkg.setLineNum(dbPackageLineNum + 1);
    }

    /**
     * 分配工单包装数量
     */
    private void doWorkorderPackAndPick(OtcBuildByWarehouseContextBO context) {
        List<OtcPackageDetail> packageDetailList = context.getPackageDetailList();
        // 工单详情
        List<OtcWorkorderDetail> workorderDetailList = context.getWorkorderDetailList();
        // 产品分组
        Map<Long, List<OtcWorkorderDetail>> wkDetailGroupByProductMap = workorderDetailList
                .stream()
                .collect(Collectors.groupingBy(OtcWorkorderDetail::getProductId));
        // 详情映射
        Map<Long, OtcWorkorderDetail> detailMap = StreamUtils.toMap(workorderDetailList, IdModel::getId);

        // 分配校验打包数量
        List<BasePackedVO> packedList = AuditLogUtil.allocationPackedList(packageDetailList, wkDetailGroupByProductMap);
        context.setPackedList(packedList);

        // 需要更新Packed的工单
        List<OtcWorkorderDetail> wkDetailUpdateList = packedList.stream()
                // 存在分配
                .filter(obj -> obj.getChangePackedQty() > 0)
                .map(obj -> {
                    OtcWorkorderDetail workorderDetail = detailMap.get(obj.getId());
                    // 更新工单详情打包数量
                    workorderDetail.setPackedQty(obj.getPackedQty());
                    return workorderDetail;
                })
                .toList();

        // 更新打包数量
        Validate.isTrue(otcWorkorderDetailService.updateBatch(wkDetailUpdateList) == wkDetailUpdateList.size(),
                "Update WorkOrderDetail PackQty Failed"
        );

        // 打包信息
        List<OtcWorkorderDetailPickVO> detailPickList = buildWorkorderDetailPick(context);

        // 走包裹拣货逻辑
        pick(detailPickList);
    }

    /**
     * 构建工单拣货信息
     *
     * @param context 上下文
     * @return /
     */
    private List<OtcWorkorderDetailPickVO> buildWorkorderDetailPick(OtcBuildByWarehouseContextBO context) {
        // Package 分配到的锁数量
        List<OtcPackageBinLocation> packageBinLocationList = otcPackageBinLocationService.listByOtcPackageIdList(Collections.singletonList(context.getPkg().getId()));

        // 工单已经分配 Package 锁的数量
        Map<Long, Integer> wkBinLocationAllocationQtyMap = packageBinLocationList.stream()
                .collect(Collectors.groupingBy(OtcPackageBinLocation::getOtcWorkorderBinLocationId,
                        Collectors.mapping(OtcPackageBinLocation::getQty, Collectors.summingInt(Integer::intValue)))
                );

        List<BasePackedVO> packedList = context.getPackedList();
        List<Long> detailIdList = StreamUtils.distinctMap(packedList, BasePackedVO::getId);

        // 分配ReadyToGo锁
        Map<Long, Integer> packedQtyMap = packedList.stream()
                .collect(Collectors.groupingBy(BasePackedVO::getId,
                        Collectors.mapping(BasePackedVO::getChangePackedQty, Collectors.summingInt(Integer::intValue))
                ));
        // 构建工单拣货对象信息
        return StreamUtils.groupBy(otcWorkorderBinLocationService.listByOtcWorkorderDetailIdList(detailIdList), OtcWorkorderBinLocation::getOtcWorkorderDetailId)
                .entrySet()
                .stream()
                .filter(entry -> packedQtyMap.containsKey(entry.getKey()))
                .map(entry -> {
                    List<OtcWorkorderBinLocationQtyVO> wkBinAllocationList = entry.getValue()
                            .stream()
                            .map(obj -> {
                                OtcWorkorderBinLocationQtyVO wkBinAllocation = BeanUtil.copyNew(obj, OtcWorkorderBinLocationQtyVO.class);
                                // 设置分配数量0
                                wkBinAllocation.setAllocatedQty(wkBinLocationAllocationQtyMap.getOrDefault(wkBinAllocation.getId(), 0));
                                wkBinAllocation.setAllocatedBeforeQty(wkBinAllocation.getAllocatedQty());
                                return wkBinAllocation;
                            })
                            .toList();
                    AllocationUtil.checkAndAllocationQty(wkBinAllocationList, packedQtyMap.get(entry.getKey()));
                    // 设置 Detail 更新后的拣货数量
                    wkBinAllocationList.stream()
                            .filter(obj -> obj.getChangeAllocatedQty() > 0)
                            // 更新分配后的值
                            .forEach(obj -> wkBinLocationAllocationQtyMap.put(obj.getId(), obj.getAllocatedQty()));
                    return wkBinAllocationList;
                })
                .flatMap(Collection::stream)
                .map(obj -> {
                    // 产品 / 拣货单 / 工单 / 库位
                    OtcWorkorderDetailPickVO wkDetailPick = BeanUtil.copyNew(obj, OtcWorkorderDetailPickVO.class);
                    // 分配数量就是对应拣货数量
                    wkDetailPick.setPickedQty(obj.getAllocatedQty());
                    wkDetailPick.setPickedBeforeQty(obj.getAllocatedBeforeQty());
                    wkDetailPick.setOtcWorkorderBinLocationId(obj.getId());
                    wkDetailPick.setId(obj.getOtcWorkorderDetailId());
                    return wkDetailPick;
                })
                .toList();
    }

    /**
     * 构建包裹标签
     *
     * @param context 包裹上下文信息
     * @return /
     */
    private OtcPackageLabel buildPackageLabel(OtcBuildByWarehouseContextBO context) {
        // 获取包裹信息
        OtcPackage pkg = context.getPkg();

        // 请求ship
        CommonShipRespBO respBO = commonClientService.createShipment(buildCreateShipmentReq(context));
        List<CommonPackageResDTO> packageList = respBO.getPackages();
        Validate.notEmpty(
                packageList,
                StringUtil.format(" otcPackageRefNum:{} No packages can be shipped  ", pkg.getRefNum())
        );
        // 根据refNum
        Map<String, CommonPackageResDTO> resMap = ObjectUtil.toMap(packageList, CommonPackageResDTO::getPackageRefNum);
        CommonPackageResDTO packageResDTO = resMap.get(pkg.getRefNum());
        Validate.notNull(
                packageResDTO,
                StringUtil.format(" otcPackageRefNum:{} No packages can be shipped  ", pkg.getRefNum())
        );

        // 上传标签内容
        OtcPackageLabel packageLabel = new OtcPackageLabel();
        packageLabel.setId(IdWorker.getId());
        packageLabel.setOtcPackageId(pkg.getId());
        packageLabel.setLabelType(OtcLabelTypeEnum.SHIPPING_LABEL.getType());
        packageLabel.setLabelRefNum(packageResDTO.getTrackingNum());
        packageLabel.setPaperType(OtcPaperTypeEnum.LABEL_4X6.getType());
        packageLabel.setFileIdRawDataType(FileDataTypeEnum.BASE_64_STRING_PNG.getType());
        packageLabel.setLineNum(1);
        packageLabel.setRawDataType(FileDataTypeEnum.BASE_64_STRING_PNG.getType());
        packageLabel.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        packageLabel.setLabelRawData(packageResDTO.getEncodedLabel());

        // 包裹设置值
        pkg.setTrackingNum(packageResDTO.getTrackingNum());

        pkg.setShipCarrier(respBO.getActualShipCarrier() != null
                ? respBO.getActualShipCarrier()
                : pkg.getShipCarrier()
        );
        pkg.setShipMethod(respBO.getActualShipMethod() != null
                ? respBO.getActualShipMethod()
                : pkg.getShipMethod()
        );

        // 文件上传并填充LabelRawData
        fileStringUploadService.uploadLabelBatch(Collections.singletonList(packageLabel));

        return packageLabel;
    }

    /**
     * 构建包裹标签
     *
     * @param pkg 包裹
     * @return /
     */
    private OtcPackageLabel buildPackageLabel(OtcPackage pkg) {

        // TODO 获取快递标签 shipHttpService.ShipSync
        ShipResultDTO shipResult = new ShipResultDTO();

        ShippingLabelDTO shippingLabel = new ShippingLabelDTO();
        shippingLabel.setEncodedLabel("");

        shippingLabel.setTrackingNum(UUID.randomUUID().toString());

        ContentInputDTO contentInput = new ContentInputDTO();
        contentInput.setName(String.format(
                "%s.%s",
                shippingLabel.getTrackingNum(),
                "png"
        ));
        contentInput.setContent(shippingLabel.getEncodedLabel());

        FileResultDTO result = new FileResultDTO();
        // 文件link
        result.setData("https://acnoss.caiyunman.com/sinoacn-recycle-dev/upload/20241207/1466813ce5644a89b993e562eeaadf39.png");

        // 上传标签内容
        OtcPackageLabel packageLabel = new OtcPackageLabel();
        packageLabel.setOtcPackageId(pkg.getId());
        packageLabel.setLabelType(OtcLabelTypeEnum.SHIPPING_LABEL.getType());
        packageLabel.setLabelRefNum(shippingLabel.getTrackingNum());
        packageLabel.setPaperType(OtcPaperTypeEnum.LABEL_4X6.getType());
        packageLabel.setFileIdRawDataType("png");
        packageLabel.setLineNum(1);
        packageLabel.setRawDataType(FileDataTypeEnum.FILE_LINK.getType());
        packageLabel.setPrintStatus(PrintStatusEnum.NONE.getStatus());
        packageLabel.setLabelRawData(result.getData());

        // 包裹设置值
        pkg.setTrackingNum(shippingLabel.getTrackingNum());

        pkg.setShipCarrier(shipResult.getActualShipCarrier() != null
                ? shipResult.getActualShipCarrier()
                : pkg.getShipCarrier()
        );
        pkg.setShipMethod(shipResult.getActualShipMethod() != null
                ? shipResult.getActualShipMethod()
                : pkg.getShipMethod()
        );
        // Mock TrackingNum
        pkg.setTrackingNum(TrackingNumberUtil.mock(pkg.getShipCarrier()));

        // 文件上传并填充LabelRawData
        // fileStringUploadService.uploadLabelBatch(Collections.singletonList(packageLabel));

        return packageLabel;
    }

    /**
     * 构建创建ship请求
     *
     * @param context /
     * @return /
     */
    private CommonShipCreateReqDTO buildCreateShipmentReq(OtcBuildByWarehouseContextBO context) {
        // 获取包裹
        OtcPackage pkg = context.getPkg();
        // 获取工单信息
        OtcWorkorder workOrder = context.getWorkOrder();
        // 获取请求单refNum
        OtcRequest otcRequest = otcRequestService.getById(workOrder.getOtcRequestId());
        // 创建ship包裹发货请求体
        CommonShipCreateReqDTO createReqDTO = new CommonShipCreateReqDTO();
        createReqDTO.setAppId(String.valueOf(Users.tenantId()));
        createReqDTO.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        createReqDTO.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getEncodeFormat());
        // 根据shipCarrier填充profileRefNum
        createReqDTO.setProfileRefNum(ProfileRefNumUtil.getProfileRefNum(
                pkg.getShipCarrier(),
                workOrder.getWarehouseId(),
                pkg.getRefNum())
        );
        createReqDTO.setLabelSize(OtcPaperTypeEnum.LABEL_4X6.getType());
        // 填充base
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setShipDate(TimeUtils.now());
        baseInfoDTO.setRequestRefNum(otcRequest.getRequestRefNum());
        baseInfoDTO.setShipMethod(pkg.getShipMethod());
        baseInfoDTO.setShipCarrier(pkg.getShipCarrier());
        // 地址填充
        fillAddress(baseInfoDTO, pkg);
        createReqDTO.setBase(baseInfoDTO);
        // 填充包裹信息
        BasePackageDTO basePackageDTO = new BasePackageDTO();
        basePackageDTO.setPackageRefNum(pkg.getRefNum());
        basePackageDTO.setSignatureType(pkg.getSignatureType());
        fillShipSize(basePackageDTO, pkg);
        // 填充基础保险
        BaseInsuranceDTO baseInsuranceDTO = new BaseInsuranceDTO();
        baseInsuranceDTO.setAmount(pkg.getInsuranceAmountAmount());
        baseInsuranceDTO.setCurrency(pkg.getInsuranceAmountCurrency());
        boolean b = ObjectUtil.isNotEmpty(pkg.getInsuranceAmountAmount()) &&
                ObjectUtil.isNotEmpty(pkg.getInsuranceAmountCurrency());
        if (b) {
            basePackageDTO.setInsuranceAmount(baseInsuranceDTO);
        }

        createReqDTO.setPackages(List.of(basePackageDTO));
        return createReqDTO;
    }

    private void fillShipSize(BasePackageDTO basePackageDTO, OtcPackage pkg) {
        BaseShipSizeDTO baseShipSizeDTO = new BaseShipSizeDTO();
        baseShipSizeDTO.setLength(pkg.getShipSizeLength());
        baseShipSizeDTO.setWidth(pkg.getShipSizeWidth());
        baseShipSizeDTO.setHeight(pkg.getShipSizeHeight());
        baseShipSizeDTO.setWeight(pkg.getShipSizeWeight());
        baseShipSizeDTO.setWeightUnit(pkg.getShipSizeWeightUnit());
        baseShipSizeDTO.setDimensionUnit(pkg.getShipSizeDimensionUnit());
        basePackageDTO.setShipSize(baseShipSizeDTO);
    }

    private void fillAddress(BaseInfoDTO baseInfoDTO, OtcPackage pkg) {
        // 发送到地址
        BaseAddressDTO shipToAddress = new BaseAddressDTO();
        shipToAddress.setAddr1(pkg.getShipToAddressAddr1());
        shipToAddress.setAddr2(pkg.getShipToAddressAddr2());
        shipToAddress.setAddr3(pkg.getShipToAddressAddr3());
        shipToAddress.setCity(pkg.getShipToAddressCity());
        shipToAddress.setCountry(pkg.getShipToAddressCountry());
        shipToAddress.setEmail(pkg.getShipToAddressEmail());
        shipToAddress.setPhone(pkg.getShipToAddressPhone());
        shipToAddress.setState(pkg.getShipToAddressState());
        shipToAddress.setZipCode(pkg.getShipToAddressZipCode());
        shipToAddress.setName(pkg.getShipToAddressName());
        shipToAddress.setCompany(pkg.getShipToAddressCompany());
        shipToAddress.setIsResidential(pkg.getShipToAddressIsResidential());
        baseInfoDTO.setShipToAddress(shipToAddress);
        // 从哪发送
        BaseAddressDTO shipFromAddress = new BaseAddressDTO();
        shipFromAddress.setAddr1(pkg.getShipFromAddressAddr1());
        shipFromAddress.setAddr2(pkg.getShipFromAddressAddr2());
        shipFromAddress.setAddr3(pkg.getShipFromAddressAddr3());
        shipFromAddress.setCity(pkg.getShipFromAddressCity());
        shipFromAddress.setCountry(pkg.getShipFromAddressCountry());
        shipFromAddress.setEmail(pkg.getShipFromAddressEmail());
        shipFromAddress.setPhone(pkg.getShipFromAddressPhone());
        shipFromAddress.setState(pkg.getShipFromAddressState());
        shipFromAddress.setZipCode(pkg.getShipFromAddressZipCode());
        shipFromAddress.setName(pkg.getShipFromAddressName());
        shipFromAddress.setCompany(pkg.getShipFromAddressCompany());
        shipFromAddress.setIsResidential(pkg.getShipFromAddressIsResidential());
        baseInfoDTO.setShipFromAddress(shipFromAddress);
        baseInfoDTO.setShipFromAddress(shipFromAddress);
    }

    /**
     * 检查包裹状态并标记为已发货
     * <p>
     * 该方法对指定的包裹列表进行状态检查，并将符合条件的包裹标记为已发货状态。
     * 主要流程包括：
     * 1. 验证包裹列表非空
     * 2. 检查包裹状态是否为“准备发货”，非准备发货状态的包裹不能标记为已发货
     * 3. 检查包裹的处理类型是否允许发货
     * 4. 更新包裹状态为已发货，并记录发货时间
     * 5. 批量更新包裹状态
     * 6. 记录发货操作日志
     * 7. 释放已发货包裹的库存锁定
     * 8. 更新相关工单的状态
     * </p>
     * <p>
     * 该方法在包裹发货过程中起到关键作用，确保只有符合条件的包裹才能被标记为已发货，
     * 并处理发货后的库存释放和状态更新等后续操作。
     * </p>
     *
     * @param packageList 要标记为已发货的包裹列表
     * @return 操作是否成功
     * @throws BusinessException 如果包裹列表为空或包裹状态不符合要求，则抛出业务异常
     */
    private boolean shipCheckAndUpdate(List<OtcPackage> packageList) {
        Validate.notEmpty(packageList, "No packages can be shipped");
        List<OtcPackage> canNotShippedList = packageList.stream()
                .filter(obj -> !OtcPackageStatusEnum.READY_TO_SHIP.getStatus().equals(obj.getPackageStatus()))
                .toList();
        // 非ReadyToShip的包裹不能被标记为已发货
        if (ObjectUtil.isNotEmpty(canNotShippedList)) {
            String waring = canNotShippedList.stream()
                    .map(obj -> String.format("{ TrackingNum = %s, PackageStatus = %s }", obj.getTrackingNum(), obj.getPackageStatus()))
                    .collect(Collectors.joining(StringPool.COMMA));
            // throw new BusinessException(waring + " PackageStatus is not ReadyToShip cannot be Mark Shipped");
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED, "Package " + waring, "READY_TO_SHIP", "current status"));
        }

        // 更新包裹状态为已发货
        packageList.forEach(obj -> {
            obj.setPackageStatus(OtcPackageStatusEnum.SHIPPED.getStatus());
            obj.setShippedTime(TimeUtils.now());
        });

        // 更新包裹
        Validate.isTrue(super.updateBatch(packageList) == packageList.size(), "Failed to update package detail");

        for (OtcPackage pkg : packageList) {
            // 包裹: Ship 操作日志
            OtcPackageAuditLogHelper.recordLog(pkg, PackageLogConstant.SHIP_STATUS, null, null, BaseTypeLogEnum.OPERATION.getType());
        }

        // 包裹: Shipped
        OtcPackageAuditLogHelper.recordLog(packageList);

        // 释放ReadyToGo
        this.packageReleaseLockedAndReduceInStock(packageList);

        // 工单标记Shipped
        List<Long> workOrderIdList = StreamUtils.distinctMap(packageList, OtcPackage::getOtcWorkorderId);
        otcWorkorderService.shipped(workOrderIdList);

        return true;
    }

    /**
     * 释放ReadyToGo
     *
     * @param packageList 包裹列表
     */
    private void packageReleaseLockedAndReduceInStock(List<OtcPackage> packageList) {
        // MultiBox包裹，校验是否全部释放
        packageList = this.dealWithMultiBoxPackage(packageList);

        // 没有释放的包裹直接返回
        if (ObjectUtil.isEmpty(packageList)) {
            return;
        }

        // 包裹分配仓储信息
        List<Long> packageIdList = StreamUtils.distinctMap(packageList, IdModel::getId);
        List<OtcPackageBinLocation> packageBinLocationList = otcPackageBinLocationService.listByOtcPackageIdList(packageIdList);

        // 工单分配仓储信息
        List<Long> workorderIds = StreamUtils.distinctMap(packageList, OtcPackage::getOtcWorkorderId);
        List<OtcWorkorderBinLocation> workorderBinLocationList = otcWorkorderBinLocationService.listByOtcWorkorderIdList(workorderIds);

        List<Long> lockedIds = StreamUtils.distinctMap(workorderBinLocationList, OtcWorkorderBinLocation::getBinLocationDetailLockedId);
        List<BinLocationDetailLocked> lockedList = binLocationDetailLockedService.listByLocked(lockedIds);

        // 备份原始锁的信息
        Map<Long, BinLocationDetailLocked> rawLockedMap = StreamUtils.toMap(BeanUtil.copyNew(lockedList, BinLocationDetailLocked.class), IdModel::getId);

        // 工单分组
        List<BinLocationDetailLockedChangeBO> changeList = StreamUtils.groupBy(packageList, OtcPackage::getOtcWorkorderId)
                .entrySet()
                .stream()
                .flatMap(wkEntry -> {
                    Map<Long, OtcPackage> packageInCurrentWkMap = StreamUtils.toMap(wkEntry.getValue(), IdModel::getId);
                    // 产品释放数量
                    Map<Long, List<OtcPackageBinLocation>> productReleaseQtyMap = packageBinLocationList.stream()
                            .filter(pkgBin -> packageInCurrentWkMap.containsKey(pkgBin.getOtcPackageId()))
                            .collect(Collectors.groupingBy(OtcPackageBinLocation::getProductId));
                    // 当前工单的Locked
                    Map<Long, List<OtcWorkorderBinLocation>> lockedByWkBinMap = workorderBinLocationList.stream()
                            .filter(wk -> wk.getOtcWorkorderId().equals(wkEntry.getKey()))
                            .collect(Collectors.groupingBy(OtcWorkorderBinLocation::getBinLocationDetailLockedId));
                    // ReadyToGo Locked
                    return getReadyToGoLockedChanges(lockedList, lockedByWkBinMap, productReleaseQtyMap, packageInCurrentWkMap);
                })
                .toList();

        // 设置原始的锁信息
        changeList.forEach(change -> change.setSourceLock(rawLockedMap.get(change.getSourceLock().getId())));

        // 释放锁并减少库存
        pickingSlipService.releaseLockAndReduceInStock(changeList);
    }

    /**
     * 获取ReadyToGoLockedChangeBO
     *
     * @param lockedList            锁
     * @param lockedByWkBinMap      锁住的工单仓储
     * @param productReleaseQtyMap  产品释放数量
     * @param packageInCurrentWkMap 当前工单中的包裹
     * @return /
     */
    private Stream<BinLocationDetailLockedChangeBO> getReadyToGoLockedChanges(List<BinLocationDetailLocked> lockedList,
                                                                              Map<Long, List<OtcWorkorderBinLocation>> lockedByWkBinMap,
                                                                              Map<Long, List<OtcPackageBinLocation>> productReleaseQtyMap,
                                                                              Map<Long, OtcPackage> packageInCurrentWkMap) {
        return lockedList.stream()
                .filter(locked -> lockedByWkBinMap.containsKey(locked.getId()))
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getProductId))
                .entrySet()
                .stream()
                .flatMap(entry -> productReleaseQtyMap.getOrDefault(entry.getKey(), Collections.emptyList())
                        .stream()
                        .flatMap(allocation -> {
                            List<BinLocationLockedReleaseVO> releaseList = entry.getValue().stream()
                                    .map(lockedItem -> {
                                        BinLocationLockedReleaseVO releaseVO = BeanUtil.copyNew(lockedItem, BinLocationLockedReleaseVO.class);
                                        releaseVO.setBeforeFinishQty(releaseVO.getFinishQty());
                                        return releaseVO;
                                    })
                                    .toList();
                            AllocationUtil.checkAndAllocationQty(releaseList, allocation.getQty());

                            Map<Long, BinLocationDetailLocked> lockedMap = StreamUtils.toMap(entry.getValue(), IdModel::getId);
                            return releaseList.stream()
                                    .filter(obj -> obj.getChangeReleaseQty() > 0)
                                    .map(obj -> createLockedChange(obj, lockedMap, allocation, packageInCurrentWkMap));
                        }));
    }

    /**
     * 创建包裹变更信息
     *
     * @param release               释放锁
     * @param lockedMap             锁映射
     * @param allocation            包裹分配仓储
     * @param packageInCurrentWkMap 当前工单包裹
     * @return /
     */
    private BinLocationDetailLockedChangeBO createLockedChange(BinLocationLockedReleaseVO release,
                                                               Map<Long, BinLocationDetailLocked> lockedMap,
                                                               OtcPackageBinLocation allocation,
                                                               Map<Long, OtcPackage> packageInCurrentWkMap) {
        BinLocationDetailLockedChangeBO change = new BinLocationDetailLockedChangeBO();
        // 发货
        change.setChangeType(BinLocationLogEnum.OTC_SHIP.getStatus());
        change.setChangeQty(release.getChangeReleaseQty());
        change.setSourceLock(lockedMap.get(release.getId()));

        // 包裹 Ship 库位日志
        RefTableBO refTable = new RefTableBO();
        refTable.setRefTableId(allocation.getOtcPackageDetailId());
        refTable.setRefTableRefNum(packageInCurrentWkMap.get(allocation.getOtcPackageId()).getRefNum());
        refTable.setRefTableName(OtcPackageDetail.class.getSimpleName());
        refTable.setRefTableShowName(OtcPackage.class.getSimpleName());
        refTable.setRefTableShowRefNum(refTable.getRefTableRefNum());
        change.setRefTable(refTable);
        return change;
    }


    /**
     * 处理多箱包裹的发货逻辑
     * <p>
     * 该方法用于处理多箱包裹（MultiBox）的发货逻辑，确保多箱包裹的完整性。
     * 多箱包裹是指一个产品由多个包裹组成，只有当所有包裹都准备好发货时，才能一起发货。
     * </p>
     * <p>
     * 主要流程包括：
     * 1. 过滤出多箱包裹
     * 2. 获取多箱产品的信息
     * 3. 根据工单分组包裹
     * 4. 检查每个工单下的多箱包裹是否全部准备好发货
     * 5. 返回可以释放库存锁定的包裹列表
     * </p>
     * <p>
     * 该方法的复杂性在于需要处理多箱包裹之间的关联关系，确保只有当一个产品的所有相关包裹
     * 都已经准备好发货时，才能释放其库存锁定。
     * </p>
     *
     * @param packageList 要处理的包裹列表
     * @return 可以释放库存锁定的包裹列表
     * <p>
     * TODO: 该方法的逻辑复杂度高，可读性低，应该拆分为多个小方法
     * 优化建议：将多箱包裹的过滤、分组和检查逻辑分离为独立的方法，提高可读性
     */
    private List<OtcPackage> dealWithMultiBoxPackage(List<OtcPackage> packageList) {
        List<OtcPackage> multiBoxPackageList = packageList.stream()
                .filter(pkg -> Objects.nonNull(pkg.getPackageMultiboxProductId()))
                .toList();
        if (ObjectUtil.isEmpty(multiBoxPackageList)) {
            return packageList;
        }

        // 产品id、版本
        List<Long> multiBoxProductIdList = multiBoxPackageList.stream()
                .map(OtcPackage::getPackageMultiboxProductId)
                .distinct()
                .toList();
        List<Integer> multiBoxVersionIntList = multiBoxPackageList.stream()
                .map(OtcPackage::getPackageMultiboxVersionInt)
                .distinct()
                .toList();
        // 尽可能一次查询
        List<ProductMultibox> boxList = productMultiboxService.listByProductIdAndVersionIntList(multiBoxProductIdList, multiBoxVersionIntList);

        // 工单下的MultiBox包裹
        return this.listByWorkOrderIdList(StreamUtils.distinctMap(multiBoxPackageList, OtcPackage::getOtcWorkorderId)).stream()
                // 根据工单分组
                .collect(Collectors.groupingBy(OtcPackage::getOtcWorkorderId))
                .values()
                .stream()
                // 返回可Release的包裹
                .map(multiBoxList -> this.dealWithGroupWorkorderMultiBoxPackage(packageList, multiBoxList, boxList))
                .flatMap(Collection::stream)
                // 根据包裹id去重
                .collect(Collectors.toMap(OtcPackage::getId,
                        // 使用整个对象作为值
                        Function.identity(),
                        // 如果有重复，保留最后一个
                        (existing, replacement) -> replacement
                ))
                .values()
                .stream()
                .toList();
    }

    /**
     * 相同工单下的MultiBox包裹，只保留可Release的包裹
     *
     * @param packageList        总共需要释放锁的包裹
     * @param multiBoxWithWkList 该工单下MultiBox的包裹
     * @param boxList            已经查出来的盒子数量
     * @return /
     */
    @NotNull
    private List<OtcPackage> dealWithGroupWorkorderMultiBoxPackage(List<OtcPackage> packageList, List<OtcPackage> multiBoxWithWkList, List<ProductMultibox> boxList) {
        final String udxMultiBoxProductFormat = "%s:%s";
        OtcPackage firstPkg = multiBoxWithWkList.get(0);
        String currentBoxUdx = String.format(udxMultiBoxProductFormat, firstPkg.getPackageMultiboxProductId(), firstPkg.getPackageMultiboxVersionInt());

        // MultiBox
        List<ProductMultibox> currentProductBoxList = boxList.stream()
                .collect(Collectors.groupingBy(box -> String.format(udxMultiBoxProductFormat, box.getProductId(), box.getMultiboxVersionInt())))
                .entrySet()
                .stream()
                // 过滤掉没有MultiBox包裹的
                .filter(entry -> entry.getKey().equals(currentBoxUdx))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Not Found MultiBox Product"));

        // 当前点击Ship的包裹
        Map<Long, OtcPackage> currentShipPkgMap = StreamUtils.toMap(packageList, OtcPackage::getId);
        int boxSize = currentProductBoxList.size();

        // 可释放锁的
        Map<Long, OtcPackage> multiBoxCanReleaseMap = this.singleProductOwnerPackageList(multiBoxWithWkList)
                .stream()
                // 只过滤当前点击Ship所在行的包裹
                .filter(single -> single.stream().anyMatch(pkg -> currentShipPkgMap.containsKey(pkg.getId())))
                // 所有包裹已经创建
                .filter(single -> single.size() == boxSize)
                // 所有包裹已经Shipped
                .filter(single -> single.stream().allMatch(pkg -> Objects.equals(pkg.getPackageStatus(), OtcPackageStatusEnum.SHIPPED.getStatus())))
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        // 需要删除MultiBox的包裹，不参与释放锁
        Map<Long, OtcPackage> multiBoxNotReleaseMap = multiBoxWithWkList.stream()
                .filter(pkg -> !multiBoxCanReleaseMap.containsKey(pkg.getId()))
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));

        // 过滤当前点击Ship的包裹不能释放锁的
        List<OtcPackage> canReleaseList = packageList.stream()
                .filter(pkg -> !multiBoxNotReleaseMap.containsKey(pkg.getId()))
                .toList();

        // 增加其他需要释放锁的MultiBox包裹
        List<OtcPackage> otherCanReleaseList = multiBoxCanReleaseMap.values()
                .stream()
                .filter(pkg -> !currentShipPkgMap.containsKey(pkg.getId()))
                .toList();

        // 返回需要释放锁的包裹
        List<OtcPackage> needShipList = new ArrayList<>(canReleaseList);
        needShipList.addAll(otherCanReleaseList);
        return needShipList;
    }

    /**
     * 获取单个产品的所有多箱包裹分组
     * <p>
     * 该方法用于将多箱包裹按照产品和创建顺序进行分组，确保同一产品的不同箱子可以被正确关联。
     * 该方法的复杂性在于需要处理多箱产品的各个箱子之间的关联关系。
     * </p>
     * <p>
     * 主要流程包括：
     * 1. 按UPC将包裹分组
     * 2. 对每组包裹按创建时间排序，并记录下标
     * 3. 根据下标将不同UPC的同一下标包裹分到一组
     * 4. 将结果转换为列表形式返回
     * </p>
     * <p>
     * 该方法的返回结果是一个嵌套列表，其中每个内部列表包含同一产品的不同箱子包裹。
     * 这种分组方式可以确保在发货时对多箱产品进行正确的处理。
     * </p>
     *
     * @param packages 要分组的包裹列表
     * @return 分组后的包裹列表，每个内部列表包含同一产品的不同箱子
     * <p>
     * TODO: 该方法的逻辑复杂度高，可读性低，应该增加更多注释和中间变量
     * 优化建议：增加更多的中间变量和注释来解释复杂的分组逻辑，或考虑拆分为多个小方法
     */
    private List<List<OtcPackage>> singleProductOwnerPackageList(List<OtcPackage> packages) {
        // 1. 按 UPC 分组
        Map<String, List<OtcPackage>> upcGroups = packages.stream()
                .collect(Collectors.groupingBy(OtcPackage::getPackageMultiboxUpc));

        // 2. 对每组按创建时间排序，并记录下标
        Map<String, List<Pair<Integer, OtcPackage>>> indexedGroups = new HashMap<>();
        for (Map.Entry<String, List<OtcPackage>> entry : upcGroups.entrySet()) {
            List<OtcPackage> sortedPackages = entry.getValue()
                    .stream()
                    .sorted(Comparator.comparing(OtcPackage::getRefNum))
                    .toList();

            // 为每个箱子分配下标
            List<Pair<Integer, OtcPackage>> indexedPackages = new ArrayList<>();
            for (int i = 0; i < sortedPackages.size(); i++) {
                indexedPackages.add(Pair.of(i, sortedPackages.get(i)));
            }
            indexedGroups.put(entry.getKey(), indexedPackages);
        }

        // 3. 按相同下标分组
        Map<Integer, List<OtcPackage>> indexGroups = new HashMap<>();
        for (List<Pair<Integer, OtcPackage>> indexedPackages : indexedGroups.values()) {
            for (Pair<Integer, OtcPackage> indexedPackage : indexedPackages) {
                indexGroups.computeIfAbsent(indexedPackage.getKey(), k -> new ArrayList<>()).add(indexedPackage.getValue());
            }
        }
        // 排序
        indexGroups.forEach((key, value) -> value.sort(Comparator.comparing(OtcPackage::getRefNum)));

        // 4. 将结果转换为 List<List<OtcPackage>>
        List<List<OtcPackage>> result = new ArrayList<>();
        for (int i = 0; i < indexGroups.size(); i++) {
            result.add(indexGroups.get(i));
        }
        return result;

    }

    /**
     * 构建OTC包裹VO对象
     *
     * @param entity OTC包裹对象
     * @return 返回包含详细信息的OTC包裹VO对象
     */
    private OtcPackageVO buildOtcPackageVO(OtcPackage entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC包裹VO对象
        OtcPackageVO vo = Converters.get(OtcPackageConverter.class).toVO(entity);
        // details
        vo.setDetailList(otcPackageDetailService.detailsByPackageId(entity.getId()));
        vo.setOtcPickingSlip(otcPickingSlipService.refNumById(entity.getOtcPickingSlipId()));
        // labelList
        vo.setLabelList(otcPackageLabelService.listByPackageId(entity.getId()));
        return vo;
    }

    /**
     * 构建包裹
     * <p>
     * 该方法是包裹创建的核心方法，通过函数式接口实现不同类型包裹的创建逻辑。
     * 主要流程包括：
     * 1. 验证工单信息和状态
     * 2. 创建上下文对象，包含工单、工单详情等信息
     * 3. 调用提供的包裹构建函数创建包裹实体
     * 4. 调用提供的包裹详情构建函数创建包裹详情
     * 5. 生成包裹行号
     * 6. 创建包裹标签
     * 7. 保存包裹、详情和标签信息
     * 8. 执行后置处理逻辑
     * 9. 执行工单打包和拣货逻辑
     * 10. 记录操作日志
     * 11. 构建返回结果
     * </p>
     * <p>
     * 该方法通过函数式接口实现了策略模式，可以根据不同的包裹类型（普通包裹、多箱包裹、SOMP包裹等）
     * 使用不同的构建逻辑，提高了代码的灵活性和可扩展性。
     * </p>
     *
     * @param warehouseCreateParam       仓库人员构建参数，包含创建包裹所需的基本信息
     * @param buildPackageProvider       构建包裹方法，根据上下文创建包裹实体的函数
     * @param buildPackageDetailProvider 构建包裹详情方法，根据上下文创建包裹详情的函数
     * @return 包含创建结果的上下文对象
     * <p>
     * TODO: 该方法职责过多，建议拆分为更小的方法以提高可维护性
     * 优化建议：将验证、创建、保存、后处理等逻辑拆分为独立的方法，使主方法更简洁清晰
     */
    private OtcBuildByWarehouseContextBO buildByWarehouse(OtcPackageWarehouseCreateParam warehouseCreateParam,
                                                          Function<OtcBuildByWarehouseContextBO, OtcPackage> buildPackageProvider,
                                                          Function<OtcBuildByWarehouseContextBO, List<OtcPackageDetail>> buildPackageDetailProvider) {
        OtcWorkorder workOrder = otcWorkorderService.getById(warehouseCreateParam.getOtcWorkOrderId());
        Validate.notNull(workOrder, " {} not found in WorkOrder", warehouseCreateParam.getOtcWorkOrderId());
        Validate.notNull(workOrder.getOtcPickingSlipId(), "{} not allot PickingSlip", workOrder.getRefNum());
        boolean hasShippingLabel = workOrder.getRequestSnapshotProvideShippingLabelFlag();
        Validate.isTrue(!hasShippingLabel, "Workorder {} is already a shipping label available", workOrder.getRefNum());

        // 流程类型校验
        ProcessType.checkNormalAvailability(workOrder.getProcessType(), workOrder.refNumLog(), "buildByWarehouse");

        OtcBuildByWarehouseContextBO context = new OtcBuildByWarehouseContextBO();
        context.setWorkOrder(workOrder);
        // 工单详情
        context.setWorkorderDetailList(otcWorkorderDetailService.listByWorkOrderId(workOrder.getId()));
        // 其他包裹
        context.setInWorkorderPackageList(listByWorkOrderIdList(Collections.singletonList(workOrder.getId())));

        // 获取包裹
        OtcPackage pkg = buildPackageProvider.apply(context);
        context.setPkg(pkg);

        // 不支持仓库创建
        List<String> canProcessStatus = Arrays.asList(OtcPackageStatusEnum.NEW.getStatus(), OtcPackageStatusEnum.IN_PICKING.getStatus());
        Validate.isTrue(canProcessStatus.contains(pkg.getPackageStatus()),
                "Package {} status {} does not allow warehouse creation",
                pkg.getRefNum(), pkg.getPackageStatus()
        );
        // 构建详情数据
        List<OtcPackageDetail> packageDetailList = buildPackageDetailProvider.apply(context);
        context.setPackageDetailList(packageDetailList);
        // 检查包裹数量
        generateLineNum(pkg, workOrder);
        // 构建标签数据
        OtcPackageLabel label = buildPackageLabel(context);
        context.setLabel(label);

        // 包裹
        super.insert(pkg);
        // 详情
        otcPackageDetailService.insertBatch(packageDetailList);
        // 标签
        otcPackageLabelService.insert(label);

        // 后置处理
        context.getAfterBuildConsumer().accept(context);

        // 执行工单打包 以及 拣货逻辑
        doWorkorderPackAndPick(context);

        OtcPickingSlip pickingSlip = otcPickingSlipService.getById(workOrder.getOtcPickingSlipId());

        // 记录拣货单 Package 操作日志
        OtcPickingSlipAuditLogHelper.recordLog(pickingSlip, PickingSlipLogConstant.PACKAGE_STATUS,
                pkg.refNumLog(), null, BaseTypeLogEnum.OPERATION.getType()
        );

        // 构建返回对象
        OtcPackageFullOutputVO vo = new OtcPackageFullOutputVO();
        BeanUtil.copy(pkg, vo);
        vo.setDetailList(BeanUtil.copyNew(packageDetailList, OtcPackageDetailVO.class));
        vo.setOtcWorkOrder(BeanUtil.copyNew(workOrder, OtcPackageFullWorkorderVO.class));
        vo.setOtcPickingSlip(BeanUtil.copyNew(pickingSlip, RefNumVO.class));
        vo.setLabelList(BeanUtil.copyNew(Collections.singletonList(label), OtcPackageLabelVO.class));
        context.setResult(vo);
        return context;
    }


    /**
     * 填充字段
     *
     * @param dataList 页面vo列表
     */
    private void fillField(List<OtcPackagePageVO> dataList) {
        List<Long> psIdList = StreamUtils.distinctMap(dataList, OtcPackagePageVO::getOtcPickingSlipId);
        // 填充拣货单
        Map<Long, RefNumVO> psList = otcPickingSlipService.refNumMapByIds(psIdList);
        dataList.forEach(obj -> obj.setPickingSlip(psList.get(obj.getOtcPickingSlipId())));
        List<Long> workOrderIdList = StreamUtils.distinctMap(dataList, OtcPackagePageVO::getOtcWorkorderId);
        // 填充工单
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(workOrderIdList);
        Map<Long, OtcWorkorder> workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);
        dataList.stream()
                .filter(obj -> workorderMap.containsKey(obj.getOtcWorkorderId()))
                .forEach(obj -> {
                    // 工单赋值
                    obj.setOtcWorkorder(BeanUtil.copyNew(workorderMap.get(obj.getOtcWorkorderId()), OtcPackagePageWorkorderVO.class));
                    OtcPackagePageWorkorderVO otcWorkorder = obj.getOtcWorkorder();
                    // 供应商赋值
                    otcWorkorder.setBasePartnerVO(BeanUtil.copyNew(tenantCacheService.getById(otcWorkorder.getRequestSnapshotTransactionPartnerId()), BasePartnerVO.class));
                });
    }

    private String getRefNum() {
        return FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PACKAGE);
    }


    /**
     * 获取可以Pick的包裹
     *
     * @param workOrderIdList 工单集合
     * @return /
     */
    @NotNull
    private Map<Long, OtcPackage> findCanPickMap(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyMap();
        }
        // 可以拣货的包裹状态
        List<String> canPickStatusList = Arrays.asList(
                OtcPackageStatusEnum.NEW.getStatus(),
                OtcPackageStatusEnum.IN_PICKING.getStatus()
        );
        List<OtcPackage> packageList = lambdaQuery()
                .in(OtcPackage::getPackageStatus, canPickStatusList)
                .in(OtcPackage::getOtcWorkorderId, workOrderIdList)
                .list();

        packageList.forEach(obj -> {
            ProcessType.checkNormalAvailability(obj.getProcessType(), obj.refNumLog(), "findCanPickMap");
        });

        return packageList
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }


    /**
     * 包裹拣货信息处理获取
     *
     * @param pickList                  工单拣货信息
     * @param detailGroupByProductIdMap detailGroupByProductIdMap
     * @return /
     */
    @NotNull
    private List<OtcPackageDetailPickVO> processPackageDetailPickList(List<OtcWorkorderDetailPickVO> pickList, Map<String, List<OtcPackageDetail>> detailGroupByProductIdMap) {
        // 包裹拣货信息
        return pickList.stream()
                // 发生拣货的拣货信息
                .filter(obj -> obj.getChangePickQty() > 0)
                .filter(pick -> detailGroupByProductIdMap.containsKey(pick.getProductId() + StringPool.COLON + pick.getOtcWorkorderId()))
                .map(pick -> {
                    // 工单+产品
                    String key = pick.getProductId() + StringPool.COLON + pick.getOtcWorkorderId();
                    List<OtcPackageDetailPickVO> packagePickList = BeanUtil.copyNew(detailGroupByProductIdMap.get(key), OtcPackageDetailPickVO.class)
                            .stream()
                            // 绑定上工单拣货信息
                            .peek(obj -> {
                                obj.setProductId(pick.getProductId());
                                obj.setProductVersionId(pick.getProductVersionId());
                                obj.setBinLocationId(pick.getBinLocationId());
                                obj.setBinLocationDetailId(pick.getBinLocationDetailId());
                                obj.setOtcPickingSlipDetailId(pick.getOtcPickingSlipDetailId());
                                obj.setOtcPickingSlipId(pick.getOtcPickingSlipId());
                                obj.setOtcWorkorderDetailId(pick.getId());
                                obj.setOtcWorkorderId(pick.getOtcWorkorderId());
                                obj.setOtcWorkorderBinLocationId(pick.getOtcWorkorderBinLocationId());
                            })
                            .toList();
                    // 检查并拣货
                    AllocationUtil.checkAndAllocationPickQty(packagePickList, pick.getChangePickQty());
                    Map<Long, OtcPackageDetail> detailMap = StreamUtils.toMap(detailGroupByProductIdMap.get(key), IdModel::getId);
                    // 设置 Detail 更新后的拣货数量
                    packagePickList
                            .stream()
                            .filter(obj -> obj.getChangePickQty() > 0)
                            .forEach(obj -> detailMap.get(obj.getId()).setPickedQty(obj.getPickedQty()));

                    return packagePickList;
                })
                .flatMap(Collection::stream)
                // 过滤发生拣货的数据
                .filter(obj -> obj.getChangePickQty() > 0)
                .toList();
    }

    /**
     * 处理MultiBox逻辑
     *
     * @param workOrderIdList 工单id
     * @param packageMap      包裹
     * @return /
     */
    private List<OtcPackageDetail> processPickMultiBoxDetails(List<Long> workOrderIdList, Map<Long, OtcPackage> packageMap) {
        if (ObjectUtil.isEmpty(packageMap)) {
            return Collections.emptyList();
        }
        // 包裹拣货
        List<Long> packageIdList = packageMap.keySet().stream().toList();
        List<OtcPackageDetail> packageDetailList = otcPackageDetailService.groupByPackageId(packageIdList).values()
                .stream()
                .flatMap(Collection::stream)
                .toList();
        // 当前工单
        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(workOrderIdList);
        List<OtcWorkorder> mulitBoxWorkorderList = workorderList.stream()
                .filter(obj -> Objects.equals(obj.getOrderType(), OtcOrderTypeEnum.MULTI_BOX.getStatus()))
                .toList();

        // 其他类型正常拣货
        if (ObjectUtil.isEmpty(mulitBoxWorkorderList)) {
            return packageDetailList;
        }

        // 更新包裹详情拣货数量
        Map<Object, OtcWorkorder> multiBoxWkMap = StreamUtils.toMap(mulitBoxWorkorderList, IdModel::getId);
        Map<Long, OtcPackage> multiBoxPkgMap = packageMap.values().stream()
                .filter(obj -> multiBoxWkMap.containsKey(obj.getOtcWorkorderId()))
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        List<OtcPackageDetail> packageDetailUpdateList = packageDetailList.stream()
                .filter(obj -> multiBoxPkgMap.containsKey(obj.getOtcPackageId()))
                .peek(obj -> obj.setPickedQty(obj.getQty()))
                .toList();

        Validate.isTrue(otcPackageDetailService.updateBatch(packageDetailUpdateList) == packageDetailUpdateList.size(),
                "Update Package PickedQty fail"
        );

        // MultiBox工单处理
        return mulitBoxWorkorderList.stream()
                .map(workorder -> this.processUnderWorkorderPickMultiBoxPackageDetails(workorder, multiBoxPkgMap, packageDetailList))
                .flatMap(Collection::stream)
                .toList();
    }

    /**
     * 处理MultiBox类型的工单Pick返回的包裹详情
     *
     * @param workorder            工单
     * @param packageMap           所有工单下的包裹
     * @param allPackageDetailList 所有工单下包裹详情
     * @return /
     */
    private List<OtcPackageDetail> processUnderWorkorderPickMultiBoxPackageDetails(OtcWorkorder workorder,
                                                                                   Map<Long, OtcPackage> packageMap,
                                                                                   List<OtcPackageDetail> allPackageDetailList) {
        // 当前工单下的包裹
        Map<Long, OtcPackage> ownerPakcageMap = packageMap.values()
                .stream()
                .filter(obj -> Objects.equals(obj.getOtcWorkorderId(), workorder.getId()))
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        // 当前工单下包裹详情
        List<OtcPackageDetail> packageDetailList = allPackageDetailList.stream()
                .filter(obj -> ownerPakcageMap.containsKey(obj.getOtcPackageId()))
                .toList();

        List<OtcPackage> packageList = ownerPakcageMap.values()
                .stream()
                .sorted(Comparator.comparing(OtcPackage::getRefNum))
                .toList();
        Map<Long, List<OtcPackageDetail>> detailGroupPkgMap = StreamUtils.groupBy(packageDetailList, OtcPackageDetail::getOtcPackageId);
        OtcPackage ownerWorkorderPkg = packageList.get(0);

        // MultiBox产品盒子
        List<ProductMultibox> boxList = productMultiboxService.getListByProductAndVersionInt(
                ownerWorkorderPkg.getPackageMultiboxProductId(), ownerWorkorderPkg.getPackageMultiboxVersionInt()
        );
        int segmentSize = boxList.size();

        // 用户指定包裹
        boolean hasShippingLabel = ObjectUtil.nullToDefault(workorder.getRequestSnapshotProvideShippingLabelFlag(), false);
        List<List<OtcPackage>> singleProductOwnerPackageList = hasShippingLabel
                // 用户指定包裹
                ? this.singleProductOwnerPackageList(packageList)
                // buildByWarehouse走这里
                : this.findIsAllBuildOwnerLineWorkorderPkgList(packageList, segmentSize);

        // 未装满一个产品
        List<List<OtcPackage>> notFullBoxSingleProduct = singleProductOwnerPackageList.stream()
                .filter(single -> single.size() != segmentSize)
                .toList();

        Validate.isTrue(ObjectUtil.isEmpty(notFullBoxSingleProduct),
                "Product: {} number of packages and boxes created for this product does not match, {} box upc be not enough",
                Optional.ofNullable(ProductVersionCacheUtil.getById(ownerWorkorderPkg.getPackageMultiboxProductId()))
                        .map(ProductVersionCache::getSupplierSku)
                        .orElse(String.valueOf(ownerWorkorderPkg.getPackageMultiboxProductId())),
                Optional.of(notFullBoxSingleProduct)
                        .stream()
                        .flatMap(Collection::stream)
                        .map(single -> single.get(0).getPackageMultiboxUpc())
                        .collect(Collectors.joining(StringPool.PIPE))
        );

        // 构建虚拟包裹
        List<OtcPackageDetail> multiBoxVirtualPackageDetailList = singleProductOwnerPackageList
                .stream()
                // 每完成一个产品构建一个
                .map(single -> {
                    OtcPackage maxPkg = single.get(single.size() - 1);
                    // 返回一个虚拟的包裹详情
                    OtcPackageDetail detail = new OtcPackageDetail();
                    // 赋值每个MultiBox的包裹第一个Detail的id
                    detail.setId(-detailGroupPkgMap.get(maxPkg.getId()).get(0).getId());
                    detail.setQty(1);
                    detail.setPickedQty(0);
                    detail.setLineNum(1);
                    detail.setOtcPackageId(maxPkg.getId());
                    detail.setProductId(maxPkg.getPackageMultiboxProductId());
                    return detail;
                })
                .toList();
        return Stream.concat(packageDetailList.stream(), multiBoxVirtualPackageDetailList.stream()).toList();
    }

    /**
     * 获取当前包裹是否全部构建完成
     *
     * @param lineSinglePackageList 需要构建的包裹
     * @return /
     */
    public List<List<OtcPackage>> findIsAllBuildOwnerLineWorkorderPkgList(List<OtcPackage> lineSinglePackageList, int boxCount) {
        if (ObjectUtil.isEmpty(lineSinglePackageList)) {
            return Collections.emptyList();
        }
        OtcPackage first = lineSinglePackageList.get(0);

        // 该工单下的所有包裹
        List<OtcPackage> allPkgInWorkorderList = lambdaQuery()
                .eq(OtcPackage::getOtcWorkorderId, first.getOtcWorkorderId())
                .list();

        Map<Long, OtcPackage> packageMap = StreamUtils.toMap(lineSinglePackageList, IdModel::getId);

        return this.singleProductOwnerPackageList(allPkgInWorkorderList).stream()
                // 仅返回所有包裹创建
                .filter(lines -> lines.size() == boxCount)
                // 需要构建的包裹，正好能构建所有箱子，返回该所有箱子包裹
                .filter(lines -> lines.stream().anyMatch(line -> packageMap.containsKey(line.getId())))
                .toList();
    }

    /**
     * 更新并分配包裹仓储信息
     *
     * @param detailGroupByProductIdMap   包裹详情
     * @param packageDetailUpdatePickList 包裹拣货信息
     */
    private void updateAndAllocatePackageDetail(Map<String, List<OtcPackageDetail>> detailGroupByProductIdMap, List<OtcPackageDetailPickVO> packageDetailUpdatePickList) {
        // 增加otc_package_bin_location
        List<OtcPackageBinLocation> packageBinLocationList = packageDetailUpdatePickList.stream()
                .map(obj -> {
                    OtcPackageBinLocation packageBinLocation = BeanUtil.copyNew(obj, OtcPackageBinLocation.class);
                    packageBinLocation.setId(IdWorker.getId());
                    packageBinLocation.setOtcPackageDetailId(obj.getId());
                    // 设置拣货数量
                    packageBinLocation.setQty(obj.getChangePickQty());
                    return packageBinLocation;
                })
                .toList();
        otcPackageBinLocationService.insertBatch(packageBinLocationList);

        // 工单详情映射
        Map<Long, Integer> detailPickChangeMap = packageDetailUpdatePickList.stream()
                .collect(Collectors.groupingBy(OtcPackageDetailPickVO::getId,
                        Collectors.mapping(BasePickVO::getChangePickQty, Collectors.summingInt(Integer::intValue)))
                );
        List<OtcPackageDetail> pickDetailUpdateList = detailGroupByProductIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> detailPickChangeMap.containsKey(obj.getId()))
                .toList();

        // 构建更新对象
        int packageUpdateCount = otcPackageDetailService.updateBatch(pickDetailUpdateList);
        Validate.isTrue(packageUpdateCount == pickDetailUpdateList.size(), "Failed to update package detail");
    }

}
