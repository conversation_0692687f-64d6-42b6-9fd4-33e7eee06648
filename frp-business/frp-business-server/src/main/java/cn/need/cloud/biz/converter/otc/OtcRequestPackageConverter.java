package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcRequestPackageDTO;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackage;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC请求包裹 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcRequestPackageConverter extends AbstractModelConverter<OtcRequestPackage, OtcRequestPackageVO, OtcRequestPackageDTO> {

}
