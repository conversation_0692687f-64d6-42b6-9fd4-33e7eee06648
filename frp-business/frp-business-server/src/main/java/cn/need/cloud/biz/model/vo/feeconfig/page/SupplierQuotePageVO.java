package cn.need.cloud.biz.model.vo.feeconfig.page;

import cn.need.cloud.biz.model.entity.base.FeeConfigAble;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * 供应商-仓库报价 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商-仓库报价 分页列表VO对象")
public class SupplierQuotePageVO extends SuperVO implements FeeConfigAble {

    @Serial
    private static final long serialVersionUID = 6284750651390611327L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private LocalDateTime endTime;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价")
    private Long quoteId;


    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private LocalDateTime startTime;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;


    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 仓库报价
     */
    @Schema(description = "仓库报价")
    private RefNumWithNameVO quote;

    /**
     * 供应商
     */
    @Schema(description = "供应商")
    private BasePartnerVO supplier;


}