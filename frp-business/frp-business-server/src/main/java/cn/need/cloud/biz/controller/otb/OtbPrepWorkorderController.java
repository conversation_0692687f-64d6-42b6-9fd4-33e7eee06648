package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPrepWorkorderConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.query.auto.OtbPrepWorkorderAutoQuery;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbPrepWorkorderBinLocationPageVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Set;

/**
 * <p>
 * OTB预提工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-prep-workorder")
@Tag(name = "OTB预提工单")
public class OtbPrepWorkorderController extends AbstractRestController<OtbPrepWorkorderService, OtbPrepWorkorder, OtbPrepWorkorderConverter, OtbPrepWorkorderVO> {

    @Resource
    private OtbPrepWorkorderBinLocationService otbPrepWorkorderBinLocationService;

    @Operation(summary = "根据id获取OTB预提工单详情", description = "根据数据主键id，从数据库中获取其对应的OTB预提工单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPrepWorkorderVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB预提工单详情
        OtbPrepWorkorderVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB预提工单详情", description = "根据数据RefNum，从数据库中获取其对应的OTB预提工单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPrepWorkorderVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTB预提工单详情
        OtbPrepWorkorderVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取OTB预提工单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB预提工单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPrepWorkorderPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPrepWorkOrderListQuery> search) {

        // 获取OTB预提工单分页
        PageData<OtbPrepWorkorderPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "获取OTB预提工单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB预提工单列表")
    @PostMapping(value = "/list-v1")
    public Result<PageData<OtbPrepWorkorderPageVO>> listV1(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPrepWorkorderAutoQuery> search) {

        // 获取OTB预提工单分页
        PageData<OtbPrepWorkorderPageVO> resultPage = service.pageV1ByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "开始处理工单", description = "开始处理工单")
    @PostMapping(value = "/begin")
    public Result<Boolean> begin(@RequestBody @Parameter(description = "Prep工单id集合", required = true) Set<Long> ids) {

        // 开始处理工单
        service.begin(ids);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "count 数量", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC预提工单数量")
    @PostMapping(value = "/count")
    public Result<Long> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) OtbPrepWorkOrderListQuery search) {

        // 返回结果
        return success(service.filterBuildPickingSlipCount(search));
    }

    @Operation(summary = "获取OTB Prep工单仓储位置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB Prep工单仓储位置列表")
    @PostMapping(value = "/bin-location/list")
    public Result<PageData<OtbPrepWorkorderBinLocationPageVO>> binLocationList(
            @Valid @RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPrepWorkOrderBinLocationQuery> search) {

        // 获取OTC工单仓储位置分页
        PageData<OtbPrepWorkorderBinLocationPageVO> resultPage = otbPrepWorkorderBinLocationService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
