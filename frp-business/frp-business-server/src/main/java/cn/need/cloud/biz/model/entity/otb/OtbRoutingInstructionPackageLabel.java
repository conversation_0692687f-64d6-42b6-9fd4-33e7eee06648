package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.service.base.UploadStringAble;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * otb发货指南包裹标签
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("otb_routing_instruction_package_label")
public class OtbRoutingInstructionPackageLabel extends SuperModel implements UploadStringAble {


    /**
     * 路由指令ID
     */
    @TableField("otb_routing_instruction_id")
    private Long otbRoutingInstructionId;

    /**
     * 托盘SSCC编号
     */
    @TableField("package_ssccnum")
    private String packageSsccNum;

    /**
     * 面单类型
     */
    @TableField("label_type")
    private String labelType;

    /**
     * label RefNum
     */
    @TableField("label_ref_num")
    private String labelRefNum;

    /**
     * 纸张类型
     */
    @TableField("paper_type")
    private String paperType;

    /**
     * 数据类型
     */
    @TableField("raw_data_type")
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    @TableField("file_id_raw_data_type")
    private String fileIdRawDataType;

    /**
     * label数据类型
     */
    @TableField("label_raw_data")
    private String labelRawData;

    /**
     * 行序号
     */
    @TableField("line_num")
    private Integer lineNum;


    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

}
