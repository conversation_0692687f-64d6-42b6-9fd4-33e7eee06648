package cn.need.cloud.biz.service.otc.workorder;

import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderBinLocation;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderBinLocationQuery;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderBinLocationPageVO;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OTC预提工单仓储位置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPrepWorkorderBinLocationService extends SuperService<OtcPrepWorkorderBinLocation> {

    /**
     * 根据查询条件获取OTC预提工单仓储位置列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC预提工单仓储位置对象的列表(分页)
     */
    PageData<OtcPrepWorkorderBinLocationPageVO> pageByQuery(PageSearch<OtcPrepWorkorderBinLocationQuery> search);

    /**
     * 根据OTB预提工单详情ID列表获取OTC预提工单仓储位置列表
     *
     * @param workorderDetailIdList 工单详情id集合
     * @return /
     */
    List<OtcPrepWorkorderBinLocation> listByOtcWorkorderDetailIdList(List<Long> workorderDetailIdList);

    /**
     * 根据OTB预提工单详情ID列表获取OTC预提工单仓储位置列表
     *
     * @param workorderDetailIdList 工单详情id集合
     * @return /
     */
    default Map<Long, List<OtcPrepWorkorderBinLocation>> groupByPrepWorkorderDetailId(List<Long> workorderDetailIdList) {
        return StreamUtils.groupBy(this.listByOtcWorkorderDetailIdList(workorderDetailIdList), OtcPrepWorkorderBinLocation::getOtcPrepPickingSlipDetailId);
    }

    /**
     * 根据Prep工单获取
     *
     * @param workorderId workorderId
     * @return /
     */
    default List<OtcPrepWorkorderBinLocation> listByPrepWorkorderId(Long workorderId) {
        return listByPrepWorkorderIds(Collections.singletonList(workorderId));
    }

    /**
     * 根据Prep工单获取
     *
     * @param workorderIds workorderId
     * @return /
     */
    List<OtcPrepWorkorderBinLocation> listByPrepWorkorderIds(Collection<Long> workorderIds);
}