package cn.need.cloud.biz.service.helper.auditshowlog.inbound;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;

import java.util.List;

/**
 * 入库请求单日志工具
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public class InboundRequestAuditLogHelper {

    private InboundRequestAuditLogHelper() {
    }

    public static void recordLog(List<InboundRequest> inboundRequestList, String type, String note, String description) {
        inboundRequestList.forEach(item -> recordLog(item, type, note, description));
    }

    public static void recordLog(List<InboundRequest> inboundRequestList) {
        inboundRequestList.forEach(InboundRequestAuditLogHelper::recordLog);
    }

    public static void recordLog(InboundRequest inboundRequest) {
        recordLog(inboundRequest, BaseTypeLogEnum.STATUS.getType(), null, null);
    }

    public static void recordLog(InboundRequest inboundRequest, String type, String note, String description) {
        recordLog(inboundRequest, inboundRequest.getInboundRequestStatus(), type, note, description);
    }

    public static void recordLog(InboundRequest inboundRequest, String status, String type, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(inboundRequest)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setNote, note)
                .with(AuditShowLog::setType, type)
                .build();
        AuditLogHolder.record(auditShowLog);
    }

    public static void recordLog(InboundRequest inboundRequest, Long warehouseId, String status, String note, String description) {
        AuditShowLog auditShowLog = AuditLogUtil.commonLog(inboundRequest)
                .with(AuditShowLog::setEvent, status)
                .with(AuditShowLog::setType, BaseTypeLogEnum.STATUS.getType())
                .with(AuditShowLog::setDescription, description)
                .with(AuditShowLog::setWarehouseId, warehouseId)
                .with(AuditShowLog::setNote, note)
                .build();
        AuditLogHolder.record(auditShowLog);
    }


    public static void recordWithModified(InboundRequest inboundRequest, String status, String description) {
        recordLog(inboundRequest, status, BaseTypeLogEnum.OPERATION.getType(), null, description);
    }
}
