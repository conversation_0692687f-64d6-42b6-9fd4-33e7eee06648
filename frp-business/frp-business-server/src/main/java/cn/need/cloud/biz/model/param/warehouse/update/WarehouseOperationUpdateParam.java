package cn.need.cloud.biz.model.param.warehouse.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 仓库分配 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "仓库分配 vo对象")
public class WarehouseOperationUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id")
    private Long operatorId;

}