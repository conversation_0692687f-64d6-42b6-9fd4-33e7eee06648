package cn.need.cloud.biz.converter.product;


import cn.need.cloud.biz.client.dto.product.ProductMultiboxDetailDTO;
import cn.need.cloud.biz.model.entity.product.ProductMultiboxDetail;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品多箱详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductMultiboxDetailConverter extends AbstractModelConverter<ProductMultiboxDetail, ProductMultiboxDetailVO, ProductMultiboxDetailDTO> {

}
