package cn.need.cloud.biz.provider.transfer;

import cn.need.cloud.biz.client.api.path.TransferOwnerShipRequestPath;
import cn.need.cloud.biz.client.api.transfer.TransferOwnerShipRequestClient;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.transfer.TransferOwnerShipRequestDetailReqDTO;
import cn.need.cloud.biz.client.dto.req.transfer.TransferOwnerShipRequestReqDTO;
import cn.need.cloud.biz.model.param.TransferOwnerShipRequestCreateParam;
import cn.need.cloud.biz.model.param.TransferOwnerShipRequestDetailCreateParam;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.transfer.TransferOwnerShipRequestService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(TransferOwnerShipRequestPath.PREFIX)
public class TransferOwnerShipRequestProvider implements TransferOwnerShipRequestClient {
    @Resource
    private TransferOwnerShipRequestService transferOwnerShipRequestService;

    @Override
    @PostMapping(value = TransferOwnerShipRequestPath.CREATE_WITH_UPDATE)
    public Result<Boolean> createWithAudit(@RequestBody TransferOwnerShipRequestReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(
                reqDTO.getLogisticPartner(),
                reqDTO.getToPartner(),
                reqDTO.getFromPartner()
        );
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充产品信息
        List<ProductReqDTO> fromProduct = reqDTO.getDetailList().stream().map(TransferOwnerShipRequestDetailReqDTO::getFromProduct).toList();
        ProductUtil.fillProduct(reqDTO.getFromPartner(), fromProduct);

        List<ProductReqDTO> toProduct = reqDTO.getDetailList().stream().map(TransferOwnerShipRequestDetailReqDTO::getToProduct).toList();
        ProductUtil.fillProduct(reqDTO.getToPartner(), toProduct);

        //构建传参数
        TransferOwnerShipRequestCreateParam param = BeanUtil.copyNew(reqDTO, TransferOwnerShipRequestCreateParam.class);
        List<TransferOwnerShipRequestDetailCreateParam> paramDetailList = BeanUtil.copyNew(reqDTO.getDetailList(), TransferOwnerShipRequestDetailCreateParam.class);

        param.setDetailList(paramDetailList);

        transferOwnerShipRequestService.createWithAudit(param, reqDTO.getAuditResultType());

        //判断是否通过
        return Result.ok(Boolean.TRUE);
    }
}
