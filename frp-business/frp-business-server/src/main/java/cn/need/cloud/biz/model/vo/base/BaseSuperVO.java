package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.model.vo.base.aware.BaseCreateAndUpdateByAware;
import cn.need.cloud.upms.cache.util.UserCacheUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.mybatis.model.IdModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * VO对象承载公共字段的超类，该类用来定义数据的创建人、创建时间等数据。
 *
 * <AUTHOR>
 * @since 2022/7/14

 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BaseSuperVO extends IdModel implements BaseCreateAndUpdateByAware {

    @Serial
    private static final long serialVersionUID = 8628352349152808902L;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 最后更新人id
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 缓存创建人姓名
     */
    private String createByName;

    /**
     * 缓存更新人姓名
     */
    private String updateByName;

    // 手动实现接口方法
    @Override
    public String getCreateByName() {
        if (createByName != null) {
            return createByName;
        }
        // Retrieve from cache once and store the result
        createByName = Optional.ofNullable(this.getCreateBy())
                .map(UserCacheUtil::getName)
                .orElse(StringPool.EMPTY);
        return createByName;
    }

    @Override
    public String getUpdateByName() {
        if (updateByName != null) {
            return updateByName;
        }
        // Retrieve from cache once and store the result
        updateByName = Optional.ofNullable(this.getUpdateBy())
                .map(UserCacheUtil::getName)
                .orElse(StringPool.EMPTY);
        return updateByName;
    }

}
