package cn.need.cloud.biz.converter;

import cn.need.cloud.biz.model.entity.TransferOwnerShipRequest;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;
import cn.need.cloud.biz.client.dto.TransferOwnerShipRequestDTO;

/**
 * <p>
 * 货权转移 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public class TransferOwnerShipRequestConverter extends AbstractModelConverter<TransferOwnerShipRequest, TransferOwnerShipRequestVO, TransferOwnerShipRequestDTO> {

}
