package cn.need.cloud.biz.model.param.base.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量Rollback对象
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@Schema(description = "批量Rollback对象")
public class BatchRollbackUpdateParam implements Serializable {

    @Schema(description = "包裹主键")
    @NotNull(message = "id is not null")
    @NotEmpty(message = "id is not empty")
    private List<Long> idList;


    @Schema(description = "备注")
    @NotNull(message = "note is not null")
    @NotBlank(message = "note is not blank")
    private String note;

}
