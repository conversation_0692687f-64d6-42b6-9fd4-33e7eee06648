package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundRequestDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库请求 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundRequestConverter extends AbstractModelConverter<InboundRequest, InboundRequestVO, InboundRequestDTO> {

}
