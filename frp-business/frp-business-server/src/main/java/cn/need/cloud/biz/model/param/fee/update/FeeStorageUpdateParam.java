package cn.need.cloud.biz.model.param.fee.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 费用storage UpdateParam对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@Schema(description = "费用storage UpdateParam对象")
public class FeeStorageUpdateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 2452773358104451573L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 货币代码（如USD、CNY）
     */
    @Schema(description = "货币代码（如USD、CNY）")
    @NotEmpty(message = "currency cannot be empty")
    @Size(max = 3, message = "currency cannot exceed 3 characters")
    private String currency;
    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    @Size(max = 512, message = "deletedNote cannot exceed 512 characters")
    private String deletedNote;
    /**
     * 费用原始数据表id
     */
    @Schema(description = "费用原始数据表id")
    @NotNull(message = "feeOriginalDataId cannot be null")
    private Long feeOriginalDataId;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 512, message = "note cannot exceed 512 characters")
    private String note;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    @NotEmpty(message = "refNum cannot be empty")
    @Size(max = 50, message = "refNum cannot exceed 50 characters")
    private String refNum;
    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    @NotEmpty(message = "snapshotRefNum cannot be empty")
    @Size(max = 50, message = "snapshotRefNum cannot exceed 50 characters")
    private String snapshotRefNum;
    /**
     * 请求id
     */
    @Schema(description = "请求id")
    @NotNull(message = "snapshotRequestId cannot be null")
    private Long snapshotRequestId;
    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    @NotEmpty(message = "snapshotRequestRefNum cannot be empty")
    @Size(max = 200, message = "snapshotRequestRefNum cannot exceed 200 characters")
    private String snapshotRequestRefNum;
    /**
     * 总费用
     */
    @Schema(description = "总费用")
    @NotEmpty(message = "totalFee cannot be empty")
    private BigDecimal totalFee;
    /**
     * 交易伙伴id
     */
    @Schema(description = "交易伙伴id")
    @NotNull(message = "transactionPartnerId cannot be null")
    private Long transactionPartnerId;
    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    @NotNull(message = "warehouseId cannot be null")
    private Long warehouseId;

}