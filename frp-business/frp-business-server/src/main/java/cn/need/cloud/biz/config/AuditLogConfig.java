package cn.need.cloud.biz.config;


import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.DispatcherType;
import javax.servlet.Filter;

/***
 * 日志配置
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Configuration
@Slf4j
public class AuditLogConfig {

    @Bean
    public FilterRegistrationBean<Filter> auditLogFilter(AuditShowLogService auditShowLogService,
                                                         BinLocationLogService binLocationLogService) {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter((req, resp, chain) -> {
            // 初始化
            AuditLogHolder.init();

            // 正常业务逻辑
            chain.doFilter(req, resp);

            // 存在异常即返回
            Object currentException = req.getAttribute("org.springframework.web.servlet.DispatcherServlet.EXCEPTION");
            if (currentException != null) {
                return;
            }
            // 日志入库逻辑
            try {
                // 插入日志
                auditShowLogService.insertBatch(AuditLogHolder.getAndClearShowLog());
                binLocationLogService.insertBatch(AuditLogHolder.getAndClearBinLocationLog());
            } catch (Exception exception) {
                log.error("AuditShowLog 异常", exception);
            }
        });
        registration.addUrlPatterns("/*");
        registration.setName("AuditShowLogFilter");
        registration.setOrder(-50);
        return registration;
    }
}
