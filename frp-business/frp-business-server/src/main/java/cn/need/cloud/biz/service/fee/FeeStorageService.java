package cn.need.cloud.biz.service.fee;


import cn.need.cloud.biz.model.entity.fee.FeeStorage;
import cn.need.cloud.biz.model.param.fee.create.FeeStorageCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeStorageUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeStorageQuery;
import cn.need.cloud.biz.model.vo.fee.FeeStorageVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeStoragePageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 费用storage service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface FeeStorageService extends SuperService<FeeStorage> {

    /**
     * 根据参数新增费用storage
     *
     * @param createParam 请求创建参数，包含需要插入的费用storage的相关信息
     * @return 费用storageID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeStorageCreateParam createParam);


    /**
     * 根据参数更新费用storage
     *
     * @param updateParam 请求创建参数，包含需要更新的费用storage的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeStorageUpdateParam updateParam);

    /**
     * 根据查询条件获取费用storage列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用storage对象的列表(分页)
     */
    List<FeeStoragePageVO> listByQuery(FeeStorageQuery query);

    /**
     * 根据查询条件获取费用storage列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个费用storage对象的列表(分页)
     */
    PageData<FeeStoragePageVO> pageByQuery(PageSearch<FeeStorageQuery> search);

    /**
     * 根据ID获取费用storage
     *
     * @param id 费用storageID
     * @return 返回费用storageVO对象
     */
    FeeStorageVO detailById(Long id);

    /**
     * 根据费用storage唯一编码获取费用storage
     *
     * @param refNum 费用storage唯一编码
     * @return 返回费用storageVO对象
     */
    FeeStorageVO detailByRefNum(String refNum);


}