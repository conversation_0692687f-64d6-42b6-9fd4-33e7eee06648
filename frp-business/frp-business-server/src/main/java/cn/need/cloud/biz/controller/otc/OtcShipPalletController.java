package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcShipPalletConverter;
import cn.need.cloud.biz.model.entity.otc.OtcShipPallet;
import cn.need.cloud.biz.model.param.otc.create.shippallet.OtcShipPalletWithReturnCreateParam;
import cn.need.cloud.biz.model.param.otc.update.shippallet.OtcShipPalletLabelUpdateParam;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletCheckTrackingNumQuery;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletDetailQuery;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipPalletQuery;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletLabelVO;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletVO;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletDetailPageVO;
import cn.need.cloud.biz.model.vo.page.OtcShipPalletPageVO;
import cn.need.cloud.biz.service.otc.ship.OtcShipPalletDetailService;
import cn.need.cloud.biz.service.otc.ship.OtcShipPalletService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * OTC运输托盘 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-ship-pallet")
@Tag(name = "OTC运输托盘")
@Validated
public class OtcShipPalletController extends AbstractRestController<OtcShipPalletService, OtcShipPallet, OtcShipPalletConverter, OtcShipPalletVO> {


    @Resource
    private OtcShipPalletDetailService otcShipPalletDetailService;

    @Operation(summary = "根据id删除OTC运输托盘", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OTC运输托盘详情", description = "根据数据主键id，从数据库中获取其对应的OTC运输托盘详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcShipPalletVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC运输托盘详情
        OtcShipPalletVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC运输托盘详情", description = "根据数据RefNum，从数据库中获取其对应的OTC运输托盘详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcShipPalletVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC运输托盘详情
        OtcShipPalletVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC运输托盘分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC运输托盘列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcShipPalletPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcShipPalletQuery> search) {

        // 获取OTC运输托盘分页
        PageData<OtcShipPalletPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "Build Pallet", description = "根据传入的搜索条件参数，构建ShipPallet")
    @PostMapping(value = "/with-return")
    public Result<OtcShipPalletVO> withReturn(@RequestBody
                                              @Valid @Parameter(description = "构建条件参数", required = true) OtcShipPalletWithReturnCreateParam search) {

        OtcShipPalletVO result = service.withReturn(search);
        // 返回结果
        return success(result);
    }

    @Operation(summary = "Build Pallet Label", description = "根据传入的搜索条件参数，构建标签")
    @PostMapping(value = "/build-label-by-ref-num")
    public Result<OtcShipPalletLabelVO> withReturn(@RequestBody
                                                   @Valid @Parameter(description = "构建条件参数", required = true) OtcShipPalletLabelUpdateParam updateParam) {

        // 返回结果
        return success(service.buildShipPalletLabelByRefNum(updateParam.getRefNum()));
    }

    @Operation(summary = "详情列表分页", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC运输托盘详情列表")
    @PostMapping(value = "/detail/list")
    public Result<PageData<OtcShipPalletDetailPageVO>> detailList(@RequestBody @Parameter(description = "详情搜索条件参数", required = true) PageSearch<OtcShipPalletDetailQuery> search) {

        // 获取OTC运输托盘分页
        PageData<OtcShipPalletDetailPageVO> resultPage = otcShipPalletDetailService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "校验TrackingNum", description = "校验TrackingNum")
    @PostMapping(value = "/check-tracking-num")
    public Result<Boolean> checkTrackingNum(@RequestBody @Parameter(description = "数据主键id", required = true)
                                            OtcShipPalletCheckTrackingNumQuery query) {

        return success(service.checkTrackingNum(query));
    }

}
