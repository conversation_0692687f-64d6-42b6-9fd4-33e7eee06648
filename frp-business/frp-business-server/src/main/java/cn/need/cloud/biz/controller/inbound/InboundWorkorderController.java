package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundWorkorderConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureByProductVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderVO;
import cn.need.cloud.biz.model.vo.page.InboundWorkorderPageVO;
import cn.need.cloud.biz.service.inbound.InboundWorkorderService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 入库工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-workorder")
@Tag(name = "入库工单")
public class InboundWorkorderController extends AbstractRestController<InboundWorkorderService, InboundWorkorder, InboundWorkorderConverter, InboundWorkorderVO> {

    @Operation(summary = "根据id获取入库工单详情", description = "根据数据主键id，从数据库中获取其对应的入库工单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundWorkorderVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取入库工单详情
        InboundWorkorderVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取入库工单详情", description = "根据数据RefNum，从数据库中获取其对应的入库工单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InboundWorkorderVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取入库工单详情
        InboundWorkorderVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取入库工单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库工单列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundWorkorderPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundWorkorderQuery> search) {

        // 获取入库工单分页
        PageData<InboundWorkorderPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "下拉列表", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "数据主键id", required = true) InboundWorkorderQuery query) {

        return success(service.distinctValue(query));
    }

    @Operation(summary = "确认货物到达", description = "确认货物到达")
    @PostMapping(value = "/arrived")
    public Result<Object> productArrived(@RequestBody @Parameter(description = "数据主键id", required = true) IdCondition id) {

        Validate.notNull(id.getId(), "The id value cannot be null.");
        service.productArrived(id.getId());
        return success();
    }


    @Operation(summary = "测量并改产品规格", description = "测量并改产品规格")
    @PostMapping(value = "/confirm")
    public Result<Integer> measureConfirm(@Valid @RequestBody @Parameter(description = "测量信息", required = true) InboundMeasureVO measureVO) {

        service.confirm(measureVO);
        return success();
    }

    @Operation(summary = "工单产品维度下测量并改产品规格", description = "工单产品维度下测量并改产品规格")
    @PostMapping(value = "/confirm-by-product")
    public Result<Integer> measureConfirmByProduct(@Valid @RequestBody @Parameter(description = "测量信息", required = true) InboundMeasureByProductVO measureVO) {

        service.confirmByProduct(measureVO);
        return success();
    }

    @Operation(summary = "打印入库工单更新状态", description = "打印入库工单更新状态")
    @PostMapping(value = "/mark-printed")
    public Result<Integer> markPrinted(@RequestBody PrintQuery printQuery) {

        service.markPrinted(printQuery);
        return success();
    }

    @Operation(summary = "完成卸货", description = "完成卸货")
    @GetMapping(value = "/finish-unload/{id}")
    public Result<Boolean> finishUnload(@PathVariable("id") @Parameter(description = "入库工单id", required = true) Long id) {

        Validate.notNull(id, "The id value cannot be null.");
        service.finishUnload(id);
        return success(Boolean.TRUE);
    }

}
