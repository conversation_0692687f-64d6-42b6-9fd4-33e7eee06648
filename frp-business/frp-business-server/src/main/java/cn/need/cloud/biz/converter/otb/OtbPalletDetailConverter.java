package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPalletDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPalletDetail;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB托盘详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPalletDetailConverter extends AbstractModelConverter<OtbPalletDetail, OtbPalletDetailVO, OtbPalletDetailDTO> {

}
