package cn.need.cloud.biz.model.entity.fee;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * <p>
 * 费用原始数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fee_original_data")
public class FeeOriginalData extends RefNumModel {


    @Serial
    private static final long serialVersionUID = 2916577189534375521L;
    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 额外数据(Json)
     */
    @TableField("extra_data")
    private String extraData;

    /**
     * 计费类型
     */
    @TableField("fee_model_type")
    private String feeModelType;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 开始处理时间
     */
    @TableField("process_start_time")
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    @TableField("process_end_time")
    private LocalDateTime processEndTime;

    /**
     * 唯一标识码
     */
    @TableField("ref_num")
    private String refNum;

    /**
     * 唯一标识码
     */
    @TableField("snapshot_ref_num")
    private String snapshotRefNum;

    /**
     * 请求id
     */
    @TableField("snapshot_request_id")
    private Long snapshotRequestId;

    /**
     * 外部唯一标识码
     */
    @TableField("snapshot_request_ref_num")
    private String snapshotRequestRefNum;

    /**
     * 交易伙伴id
     */
    @TableField("transaction_partner_id")
    private Long transactionPartnerId;

    /**
     * 仓库id
     */
    @TableField("warehouse_id")
    private Long warehouseId;

}
