package cn.need.cloud.biz.model.vo.feeconfig.page;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 仓库报价 分页列表VO对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价 分页列表VO对象")
public class QuotePageVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = 3506005517033953250L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;


}