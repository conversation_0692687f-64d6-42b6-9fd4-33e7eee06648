package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.model.entity.otb.OtbPickingSlip;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailPickVO;
import lombok.Data;

import java.util.List;

/***
 * 拣货上下文信息
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class OtbPickingSlipPickContextVO {

    /**
     * 拣货产品条件列表
     */
    private List<OtbPickingSlipProductPickQuery> pickList;

    /**
     * 当前拣货单
     */
    private OtbPickingSlip pickingSlip;

    /**
     * 拣货单详情 拣货后信息
     */
    private List<OtbPickingSlipDetailPickVO> pickAfterDetailList;

    /**
     * 工单详情 拣货后信息
     */
    private List<OtbWorkorderDetailPickVO> workOrderPickAfterDetailList;
}
