package cn.need.cloud.biz.model.param.otc.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 快递公司配置 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "快递公司配置 vo对象")
public class OtcShipStationConfigCreateParam implements Serializable {


    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * shipMethodCategory
     */
    @Schema(description = "shipMethodCategory")
    private String shipMethodCategory;

    /**
     * shipStation
     */
    @Schema(description = "shipStation")
    private String shipStation;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

}