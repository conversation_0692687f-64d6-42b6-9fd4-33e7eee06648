package cn.need.cloud.biz.model.vo.otc;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 快递公司配置 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "快递公司配置 vo对象")
public class OtcShipStationConfigVO extends BaseSuperVO {


    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * shipMethodCategory
     */
    @Schema(description = "shipMethodCategory")
    private String shipMethodCategory;

    /**
     * shipStation
     */
    @Schema(description = "shipStation")
    private String shipStation;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

}