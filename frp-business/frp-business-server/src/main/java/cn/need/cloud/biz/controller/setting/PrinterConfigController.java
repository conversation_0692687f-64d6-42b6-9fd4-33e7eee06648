package cn.need.cloud.biz.controller.setting;

import cn.need.cloud.biz.converter.setting.PrinterConfigConverter;
import cn.need.cloud.biz.model.entity.setting.PrinterConfig;
import cn.need.cloud.biz.model.param.setting.create.PrinterConfigCreateParam;
import cn.need.cloud.biz.model.param.setting.update.PrinterConfigUpdateParam;
import cn.need.cloud.biz.model.query.setting.PrinterConfigQuery;
import cn.need.cloud.biz.model.vo.page.PrinterConfigPageVO;
import cn.need.cloud.biz.model.vo.setting.PrinterConfigVO;
import cn.need.cloud.biz.service.setting.PrinterConfigService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 打印配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/printer-config")
@Tag(name = "打印配置")
public class PrinterConfigController extends AbstractRestController<PrinterConfigService, PrinterConfig, PrinterConfigConverter, PrinterConfigVO> {

    @Operation(summary = "新增打印配置", description = "接收打印配置的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrinterConfigCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改打印配置", description = "接收打印配置的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrinterConfigUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除打印配置", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取打印配置详情", description = "根据数据主键id，从数据库中获取其对应的打印配置详情")
    @GetMapping(value = "/detail/{id}")
    public Result<PrinterConfigVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取打印配置详情
        PrinterConfigVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取打印配置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的打印配置列表")
    @PostMapping(value = "/list")
    public Result<PageData<PrinterConfigPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<PrinterConfigQuery> search) {

        // 获取打印配置分页
        PageData<PrinterConfigPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
