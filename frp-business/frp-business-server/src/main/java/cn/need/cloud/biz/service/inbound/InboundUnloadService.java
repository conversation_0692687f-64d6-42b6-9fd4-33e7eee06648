package cn.need.cloud.biz.service.inbound;

import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.query.inbound.InboundUnloadQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadPrintVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipVO;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundAggregatedUploadVO;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundUnloadVO;
import cn.need.cloud.biz.model.vo.page.InboundUnloadPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 入库工单卸货表 根据这个来生成上架单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InboundUnloadService extends SuperService<InboundUnload> {

    /**
     * 根据查询条件获取入库工单卸货表 根据这个来生成上架单列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库工单卸货表 根据这个来生成上架单对象的列表(分页)
     */
    List<InboundUnloadPageVO> listByQuery(InboundUnloadQuery query);

    /**
     * 根据查询条件获取入库工单卸货表 根据这个来生成上架单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个入库工单卸货表 根据这个来生成上架单对象的列表(分页)
     */
    PageData<InboundUnloadPageVO> pageByQuery(PageSearch<InboundUnloadQuery> search);

    /**
     * 根据ID获取入库工单卸货表 根据这个来生成上架单
     *
     * @param id 入库工单卸货表 根据这个来生成上架单ID
     * @return 返回入库工单卸货表 根据这个来生成上架单VO对象
     */
    InboundUnloadVO detailById(Long id);

    /**
     * 根据入库工单卸货表 根据这个来生成上架单唯一编码获取入库工单卸货表 根据这个来生成上架单
     *
     * @param refNum 入库工单卸货表 根据这个来生成上架单唯一编码
     * @return 返回入库工单卸货表 根据这个来生成上架单VO对象
     */
    InboundUnloadVO detailByRefNum(String refNum);

    /**
     * 卸货
     *
     * @param inBoundUnloadsVO 卸货信息
     * @return 卸货信息
     */
    InboundPutawaySlipVO regularUnloadByProduct(InboundAggregatedUploadVO inBoundUnloadsVO);

    /**
     * 根据入库工单id，产品id获取卸货单列表
     *
     * @param workOrderId 入库工单id
     * @param productId   产品id
     * @return 卸货单信息
     */
    List<InboundUnload> listByProIdAndInboundId(Long workOrderId, Long productId);

    /**
     * 根据上架单id获取卸货单
     *
     * @param id 上架单id
     * @return 卸货单集合
     */
    List<InboundUnload> listByPutAwaySlipId(Long id);

    /**
     * 填充卸货单
     *
     * @param inboundPutawaySlipVO 上架单vo对象
     * @param inboundUnloadList    上架单列表
     */
    void fillUnload(InboundPutawaySlipVO inboundPutawaySlipVO, List<InboundUnload> inboundUnloadList);

    /**
     * 根据入库工单详情id获取对应卸货单
     *
     * @param inboundWorkorderDetailIdList 入库工单详情id集合
     * @return 入库卸货单
     */
    List<InboundUnload> listByInboundWorkOrderDetailIds(Set<Long> inboundWorkorderDetailIdList);

    /**
     * 根据打托单id删除卸货单详情
     *
     * @param palletId 打托id
     */
    void removeByPalletId(Long palletId);

    /**
     * 按产品维度打托卸货
     *
     * @param inboundPalletUnloadVO 打托卸货对象
     * @return 打托单打印信息
     */
    InboundPalletUnloadPrintVO palletUnloadByProduct(InboundPalletUnloadVO inboundPalletUnloadVO);

    /**
     * 更新卸货单状态
     *
     * @param inboundUnloadList 卸货单集合
     */
    void updateUnloadQty(List<InboundUnload> inboundUnloadList);

    /**
     * 判断入库卸货单单是否都达到某个状态
     *
     * @param id     入库工单id
     * @param status 状态
     * @return true or  false
     */
    boolean exist(Long id, String status);

    /**
     * 获取上架单id
     *
     * @param inboundWorkOrderId 入库工单id
     * @return 上架单id
     */
    Set<Long> getPutAwaySlipId(Long inboundWorkOrderId);

    /**
     * 调整数量校验
     *
     * @param inBoundWorkOrderDetailId 入库工单详情
     */
    void needQtyValid(Long inBoundWorkOrderDetailId, Integer needReceiveQty);

    /**
     * 根据入库工单详情获取卸货单
     *
     * @param inBoundWorkOrderDetailId 入库工单详情
     * @return 卸货单
     */
    List<InboundUnload> getByInboundDetailId(Long inBoundWorkOrderDetailId);

    /**
     * 回滚卸货单打托数量
     *
     * @param palletId 打托单id
     * @param note     回滚原因
     */
    void rollBackByPalletId(Long palletId, String note);

    /**
     * 根据入库工单id删除卸货单
     *
     * @param inboundWorkorderId 入库工单id
     */
    List<InboundUnload> listByInboundWorkOrderIds(Long inboundWorkorderId);

    List<InboundUnload> listByRequestId(Long requestId);

    /**
     * 根据上架单id集合获取卸货单
     *
     * @param putAwaySlipIdSet 上架单id集合
     * @return 上架单id集合
     */
    Map<Long, List<InboundUnload>> getMap(Set<Long> putAwaySlipIdSet);
}