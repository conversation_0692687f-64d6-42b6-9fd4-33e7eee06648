<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.PrepRequestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.PrepRequest">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time"  property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="prep_request_status" property="prepRequestStatus" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="request_ref_num" property="requestRefNum" />
        <result column="note" property="note" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.prep_request_status,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.transaction_partner_id,
        t.request_ref_num,
        t.note
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.PrepRequestPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            prep_request t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.prepRequestStatus != null and qo.prepRequestStatus != ''">
            AND t.prep_request_status = #{qo.prepRequestStatus}
        </if>
        <if test="qo.prepRequestStatusList != null and qo.prepRequestStatusList.size > 0 ">
            AND t.prep_request_status in
            <foreach collection="qo.prepRequestStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.transactionPartnerId != null">
            AND t.transaction_partner_id = #{qo.transactionPartnerId}
        </if>
        <if test="qo.transactionPartnerIdList != null and qo.transactionPartnerIdList.size > 0 ">
            AND t.transaction_partner_id in
            <foreach collection="qo.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestRefNum != null and qo.requestRefNum != ''">
            AND t.request_ref_num = #{qo.requestRefNum}
        </if>
        <if test="qo.requestRefNumList != null and qo.requestRefNumList.size > 0 ">
            AND t.request_ref_num in
            <foreach collection="qo.requestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
    </sql>

</mapper>