<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPutawaySlipDetailMapper">

    <select id="listAvailableByWorkorderIds"
            resultType="cn.need.cloud.biz.model.entity.otb.OtbPutawaySlipDetail">
        SELECT
        *
        FROM
        otb_putaway_slip_detail opsd
        WHERE
        opsd.remove_flag = 0
        AND (EXISTS(select 1 from otb_putaway_slip ops where ops.id = opsd.putaway_slip_id and  ops.putaway_slip_status in ('New', 'Processing')))
        AND opsd.qty > opsd.putaway_qty
        AND opsd.workorder_id IN
        <foreach collection="workorderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>