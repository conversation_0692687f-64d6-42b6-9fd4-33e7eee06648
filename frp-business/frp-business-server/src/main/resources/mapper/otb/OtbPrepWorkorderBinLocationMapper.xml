<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPrepWorkorderBinLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderBinLocation">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="otb_workorder_id" property="otbWorkorderId" />
        <result column="otb_workorder_detail_id" property="otbWorkorderDetailId" />
        <result column="otb_prep_workorder_id" property="otbPrepWorkorderId" />
        <result column="otb_prep_workorder_detail_id" property="otbPrepWorkorderDetailId" />
        <result column="otb_prep_picking_slip_id" property="otbPrepPickingSlipId" />
        <result column="otb_prep_picking_slip_detail_id" property="otbPrepPickingSlipDetailId" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_detail_locked_id" property="binLocationDetailLockedId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="qty" property="qty" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="product_id" property="productId" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="product_version_id" property="productVersionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.otb_workorder_id,
        t.otb_workorder_detail_id,
        t.otb_prep_workorder_id,
        t.otb_prep_workorder_detail_id,
        t.otb_prep_picking_slip_id,
        t.otb_prep_picking_slip_detail_id,
        t.bin_location_detail_id,
        t.bin_location_id,
        t.qty,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.bin_location_detail_locked_id,
        t.product_version_id,
        t.product_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otb.page.OtbPrepWorkorderBinLocationPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_prep_workorder_bin_location t
        WHERE
            t.remove_flag = 0
            <include refid="List_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用查询条件 -->
    <sql id="List_Where_List">
        <if test="qo.otbWorkorderId != null">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
        <if test="qo.otbPrepWorkorderId != null">
            AND t.otb_prep_workorder_id = #{qo.otbPrepWorkorderId}
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.otbWorkorderId != null">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
        <if test="qo.otbPrepWorkorderId != null">
            AND t.otb_prep_workorder_id = #{qo.otbPrepWorkorderId}
        </if>
    </sql>

</mapper>