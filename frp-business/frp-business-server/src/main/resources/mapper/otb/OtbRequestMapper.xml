<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbRequestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbRequest">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="request_ref_num" property="requestRefNum" />
        <result column="channel" property="channel" />
        <result column="ship_window_start" property="shipWindowStart" />
        <result column="otb_request_status" property="otbRequestStatus" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="note" property="note" />
        <result column="request_shipment_status" property="requestShipmentStatus" />
        <result column="order_num" property="orderNum" />
        <result column="ship_to_address_addr1" property="shipToAddressAddr1" />
        <result column="ship_to_address_addr2" property="shipToAddressAddr2" />
        <result column="ship_to_address_addr3" property="shipToAddressAddr3" />
        <result column="ship_to_address_city" property="shipToAddressCity" />
        <result column="ship_to_address_company" property="shipToAddressCompany" />
        <result column="ship_to_address_country" property="shipToAddressCountry" />
        <result column="ship_to_address_email" property="shipToAddressEmail" />
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential" />
        <result column="ship_to_address_name" property="shipToAddressName" />
        <result column="ship_to_address_note" property="shipToAddressNote" />
        <result column="ship_to_address_phone" property="shipToAddressPhone" />
        <result column="ship_to_address_state" property="shipToAddressState" />
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode" />
        <result column="ship_window_end" property="shipWindowEnd" />
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1" />
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2" />
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3" />
        <result column="ship_from_address_city" property="shipFromAddressCity" />
        <result column="ship_from_address_company" property="shipFromAddressCompany" />
        <result column="ship_from_address_country" property="shipFromAddressCountry" />
        <result column="ship_from_address_email" property="shipFromAddressEmail" />
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential" />
        <result column="ship_from_address_name" property="shipFromAddressName" />
        <result column="ship_from_address_note" property="shipFromAddressNote" />
        <result column="ship_from_address_phone" property="shipFromAddressPhone" />
        <result column="ship_from_address_state" property="shipFromAddressState" />
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode" />
        <result column="process_start_time" property="processStartTime" />
        <result column="process_end_time" property="processEndTime" />
        <result column="fee_status" property="feeStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.request_ref_num,
        t.channel,
        t.ship_window_start,
        t.otb_request_status,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.transaction_partner_id,
        t.note,
        t.request_shipment_status,
        t.order_num,
        t.ship_to_address_addr1,
        t.ship_to_address_addr2,
        t.ship_to_address_addr3,
        t.ship_to_address_city,
        t.ship_to_address_company,
        t.ship_to_address_country,
        t.ship_to_address_email,
        t.ship_to_address_is_residential,
        t.ship_to_address_name,
        t.ship_to_address_note,
        t.ship_to_address_phone,
        t.ship_to_address_state,
        t.ship_to_address_zip_code,
        t.ship_window_end,
        t.ship_from_address_addr1,
        t.ship_from_address_addr2,
        t.ship_from_address_addr3,
        t.ship_from_address_city,
        t.ship_from_address_company,
        t.ship_from_address_country,
        t.ship_from_address_email,
        t.ship_from_address_is_residential,
        t.ship_from_address_name,
        t.ship_from_address_note,
        t.ship_from_address_phone,
        t.ship_from_address_state,
        t.process_start_time,
        t.process_end_time,
        t.fee_status,
        t.ship_from_address_zip_code,
        t.ship_type,
        t.routing_instruction_file_file_extension,
        t.routing_instruction_file_file_data,
        t.routing_instruction_file_file_type,
        t.routing_instruction_file_paper_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_request t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>
    <select id="detailById" resultMap="GetDetail"
            parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>,
            d.line_num as dLineNum,
            d.product_id,
            d.qty,
            d.finish_qty as finishQty,
            d.product_barcode,
            d.product_channel_sku
        from otb_request t
        left join otb_request_detail d on t.id = d.otb_request_id
        where t.id = #{id}
        and t.remove_flag = 0
        and COALESCE(d.remove_flag, 0) = 0
    </select>

    <select id="dropProList" resultType="java.util.Map" >
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otb_request t
        WHERE
        t.remove_flag=0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <resultMap id="GetDetail" type="cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO" autoMapping="true">
        <id property="id" column="id"/>
        <collection property="detailList" ofType="cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO" autoMapping="true">
            <id property="lineNum" column="dLineNum"/>
            <id property="finishQty" column="finishQty"/>
        </collection>
    </resultMap>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestRefNum != null and qo.requestRefNum != ''">
            AND t.request_ref_num = #{qo.requestRefNum}
        </if>
        <if test="qo.requestRefNumList != null and qo.requestRefNumList.size > 0 ">
            AND t.request_ref_num in
            <foreach collection="qo.requestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.channel != null and qo.channel != ''">
            AND t.channel = #{qo.channel}
        </if>
        <if test="qo.channelList != null and qo.channelList.size > 0 ">
            AND t.channel in
            <foreach collection="qo.channelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipWindowStartStart != null">
            AND t.ship_window_start  &gt;= #{qo.shipWindowStartStart}
        </if>
        <if test="qo.shipWindowStartEnd != null">
            AND t.ship_window_start  &lt; #{qo.shipWindowStartEnd}
        </if>
        <if test="qo.otbRequestStatus != null and qo.otbRequestStatus != ''">
            AND t.otb_request_status = #{qo.otbRequestStatus}
        </if>
        <if test="qo.otbRequestStatusList != null and qo.otbRequestStatusList.size > 0 ">
            AND t.otb_request_status in
            <foreach collection="qo.otbRequestStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.transactionPartnerId != null">
            AND t.transaction_partner_id = #{qo.transactionPartnerId}
        </if>
        <if test="qo.transactionPartnerIdList != null and qo.transactionPartnerIdList.size > 0 ">
            AND t.transaction_partner_id in
            <foreach collection="qo.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.requestShipmentStatus != null and qo.requestShipmentStatus != ''">
            AND t.request_shipment_status = #{qo.requestShipmentStatus}
        </if>
        <if test="qo.requestShipmentStatusList != null and qo.requestShipmentStatusList.size > 0 ">
            AND t.request_shipment_status in
            <foreach collection="qo.requestShipmentStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.orderNum != null and qo.orderNum != ''">
            AND t.order_num = #{qo.orderNum}
        </if>
        <if test="qo.shipToAddressAddr1 != null and qo.shipToAddressAddr1 != ''">
            AND t.ship_to_address_addr1 = #{qo.shipToAddressAddr1}
        </if>
        <if test="qo.shipToAddressAddr2 != null and qo.shipToAddressAddr2 != ''">
            AND t.ship_to_address_addr2 = #{qo.shipToAddressAddr2}
        </if>
        <if test="qo.shipToAddressAddr3 != null and qo.shipToAddressAddr3 != ''">
            AND t.ship_to_address_addr3 = #{qo.shipToAddressAddr3}
        </if>
        <if test="qo.shipToAddressCity != null and qo.shipToAddressCity != ''">
            AND t.ship_to_address_city = #{qo.shipToAddressCity}
        </if>
        <if test="qo.shipToAddressCompany != null and qo.shipToAddressCompany != ''">
            AND t.ship_to_address_company = #{qo.shipToAddressCompany}
        </if>
        <if test="qo.shipToAddressCountry != null and qo.shipToAddressCountry != ''">
            AND t.ship_to_address_country = #{qo.shipToAddressCountry}
        </if>
        <if test="qo.shipToAddressEmail != null and qo.shipToAddressEmail != ''">
            AND t.ship_to_address_email = #{qo.shipToAddressEmail}
        </if>
        <if test="qo.shipToAddressIsResidential != null">
            AND t.ship_to_address_is_residential = #{qo.shipToAddressIsResidential}
        </if>
        <if test="qo.shipToAddressName != null and qo.shipToAddressName != ''">
            AND t.ship_to_address_name = #{qo.shipToAddressName}
        </if>
        <if test="qo.shipToAddressNote != null and qo.shipToAddressNote != ''">
            AND t.ship_to_address_note = #{qo.shipToAddressNote}
        </if>
        <if test="qo.shipToAddressPhone != null and qo.shipToAddressPhone != ''">
            AND t.ship_to_address_phone = #{qo.shipToAddressPhone}
        </if>
        <if test="qo.shipToAddressState != null and qo.shipToAddressState != ''">
            AND t.ship_to_address_state = #{qo.shipToAddressState}
        </if>
        <if test="qo.shipToAddressZipCode != null and qo.shipToAddressZipCode != ''">
            AND t.ship_to_address_zip_code = #{qo.shipToAddressZipCode}
        </if>
        <if test="qo.shipWindowEndStart != null">
            AND t.ship_window_end  &gt;= #{qo.shipWindowEndStart}
        </if>
        <if test="qo.shipWindowEndEnd != null">
            AND t.ship_window_end  &lt; #{qo.shipWindowEndEnd}
        </if>
        <if test="qo.shipFromAddressAddr1 != null and qo.shipFromAddressAddr1 != ''">
            AND t.ship_from_address_addr1 = #{qo.shipFromAddressAddr1}
        </if>
        <if test="qo.shipFromAddressAddr2 != null and qo.shipFromAddressAddr2 != ''">
            AND t.ship_from_address_addr2 = #{qo.shipFromAddressAddr2}
        </if>
        <if test="qo.shipFromAddressAddr3 != null and qo.shipFromAddressAddr3 != ''">
            AND t.ship_from_address_addr3 = #{qo.shipFromAddressAddr3}
        </if>
        <if test="qo.shipFromAddressCity != null and qo.shipFromAddressCity != ''">
            AND t.ship_from_address_city = #{qo.shipFromAddressCity}
        </if>
        <if test="qo.shipFromAddressCompany != null and qo.shipFromAddressCompany != ''">
            AND t.ship_from_address_company = #{qo.shipFromAddressCompany}
        </if>
        <if test="qo.shipFromAddressCountry != null and qo.shipFromAddressCountry != ''">
            AND t.ship_from_address_country = #{qo.shipFromAddressCountry}
        </if>
        <if test="qo.shipFromAddressEmail != null and qo.shipFromAddressEmail != ''">
            AND t.ship_from_address_email = #{qo.shipFromAddressEmail}
        </if>
        <if test="qo.shipFromAddressIsResidential != null">
            AND t.ship_from_address_is_residential = #{qo.shipFromAddressIsResidential}
        </if>
        <if test="qo.shipFromAddressName != null and qo.shipFromAddressName != ''">
            AND t.ship_from_address_name = #{qo.shipFromAddressName}
        </if>
        <if test="qo.shipFromAddressNote != null and qo.shipFromAddressNote != ''">
            AND t.ship_from_address_note = #{qo.shipFromAddressNote}
        </if>
        <if test="qo.shipFromAddressPhone != null and qo.shipFromAddressPhone != ''">
            AND t.ship_from_address_phone = #{qo.shipFromAddressPhone}
        </if>
        <if test="qo.shipFromAddressState != null and qo.shipFromAddressState != ''">
            AND t.ship_from_address_state = #{qo.shipFromAddressState}
        </if>
        <if test="qo.shipFromAddressZipCode != null and qo.shipFromAddressZipCode != ''">
            AND t.ship_from_address_zip_code = #{qo.shipFromAddressZipCode}
        </if>

        <if test="qo.feeStatus != null and qo.feeStatus != ''">
            AND t.fee_status = #{qo.feeStatus}
        </if>
        <if test="qo.feeStatusList != null and qo.feeStatusList.size > 0 ">
            AND t.fee_status in
            <foreach collection="qo.feeStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>