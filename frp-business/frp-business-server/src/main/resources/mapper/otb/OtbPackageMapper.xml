<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPackage">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="sscc_num" property="ssccNum" />
        <result column="carton_size_length" property="cartonSizeLength" />
        <result column="carton_size_width" property="cartonSizeWidth" />
        <result column="carton_size_height" property="cartonSizeHeight" />
        <result column="carton_size_weight" property="cartonSizeWeight" />
        <result column="carton_size_weight_unit" property="cartonSizeWeightUnit" />
        <result column="carton_size_dimension_unit" property="cartonSizeDimensionUnit" />
        <result column="line_num" property="lineNum" />
        <result column="otb_request_id" property="otbRequestId" />
        <result column="otb_workorder_id" property="otbWorkorderId" />
        <result column="otb_shipment_id" property="otbShipmentId" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="otb_pallet_id" property="otbPalletId" />
        <result column="order_num" property="orderNum" />
        <result column="otb_package_status" property="otbPackageStatus" />
        <result column="otb_package_type" property="otbPackageType" />
        <result column="otb_pallet_type" property="otbPalletType" />
        <result column="otb_picking_slip_id" property="otbPickingSlipId" />
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1" />
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2" />
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3" />
        <result column="ship_from_address_city" property="shipFromAddressCity" />
        <result column="ship_from_address_company" property="shipFromAddressCompany" />
        <result column="ship_from_address_country" property="shipFromAddressCountry" />
        <result column="ship_from_address_email" property="shipFromAddressEmail" />
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential" />
        <result column="ship_from_address_name" property="shipFromAddressName" />
        <result column="ship_from_address_note" property="shipFromAddressNote" />
        <result column="ship_from_address_phone" property="shipFromAddressPhone" />
        <result column="ship_from_address_state" property="shipFromAddressState" />
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode" />
        <result column="ship_to_address_addr1" property="shipToAddressAddr1" />
        <result column="ship_to_address_addr2" property="shipToAddressAddr2" />
        <result column="ship_to_address_addr3" property="shipToAddressAddr3" />
        <result column="ship_to_address_city" property="shipToAddressCity" />
        <result column="ship_to_address_company" property="shipToAddressCompany" />
        <result column="ship_to_address_country" property="shipToAddressCountry" />
        <result column="ship_to_address_email" property="shipToAddressEmail" />
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential" />
        <result column="ship_to_address_name" property="shipToAddressName" />
        <result column="ship_to_address_note" property="shipToAddressNote" />
        <result column="ship_to_address_phone" property="shipToAddressPhone" />
        <result column="ship_to_address_state" property="shipToAddressState" />
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode" />
        <result column="short_ssccnum" property="shortSsccNum" />
        <result column="station" property="station" />
        <result column="ship_carrier" property="shipCarrier" />
        <result column="ship_method" property="shipMethod" />
        <result column="ship_api_profile_ref_num" property="shipApiProfileRefNum" />
        <result column="tracking_num" property="trackingNum" />
        <result column="process_type" property="processType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.sscc_num,
        t.carton_size_length,
        t.carton_size_width,
        t.carton_size_height,
        t.carton_size_weight,
        t.carton_size_weight_unit,
        t.carton_size_dimension_unit,
        t.line_num,
        t.otb_request_id,
        t.otb_workorder_id,
        t.otb_shipment_id,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.otb_pallet_id,
        t.order_num,
        t.otb_package_status,
        t.otb_package_type,
        t.otb_pallet_type,
        t.otb_picking_slip_id,
        t.ship_from_address_addr1,
        t.ship_from_address_addr2,
        t.ship_from_address_addr3,
        t.ship_from_address_city,
        t.ship_from_address_company,
        t.ship_from_address_country,
        t.ship_from_address_email,
        t.ship_from_address_is_residential,
        t.ship_from_address_name,
        t.ship_from_address_note,
        t.ship_from_address_phone,
        t.ship_from_address_state,
        t.ship_from_address_zip_code,
        t.ship_to_address_addr1,
        t.ship_to_address_addr2,
        t.ship_to_address_addr3,
        t.ship_to_address_city,
        t.ship_to_address_company,
        t.ship_to_address_country,
        t.ship_to_address_email,
        t.ship_to_address_is_residential,
        t.ship_to_address_name,
        t.ship_to_address_note,
        t.ship_to_address_phone,
        t.ship_to_address_state,
        t.ship_to_address_zip_code,
        t.short_ssccnum as shortSsccNum,
        t.station,
        t.ship_carrier,
        t.ship_method,
        t.process_type,
        t.ship_api_profile_ref_num,
        t.tracking_num
    </sql>
    <update id="updateBatchWithNull" parameterType="cn.need.cloud.biz.model.entity.otb.OtbPackage">
            <foreach collection="otbPackageList" item="item" separator=";">
                UPDATE otb_package
                SET otb_pallet_id=#{item.otbPalletId},
                    otb_pallet_type=#{item.otbPalletType},
                    otb_package_status=#{item.otbPackageStatus},
                    otb_shipment_id=#{item.otbShipmentId},
                    ship_carrier=#{item.shipCarrier},
                    ship_method=#{item.shipMethod},
                    ship_api_profile_ref_num=#{item.shipApiProfileRefNum},
                    update_time = #{item.updateTime},
                    update_by = #{item.updateBy},
                    version = version+1
                <where>
                    id = #{item.id}
                </where>
            </foreach>
     </update>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPackagePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_package t
        WHERE
            t.remove_flag = 0
            <if test="qo.otbWorkorderQuery!=null">
                and exists(  select ow.id
                             from otb_workorder ow
                             ow.id=t.otb_workorder_id and iw.remove_flag=0
                             <include refid="Base_otb_workorder_Where_List"/>
                           )
            </if>
            <if test="qo.otbPickingSlipQuery!=null">
                and exists(  select ops.id
                             from otb_picking_slip ops
                             ops.id=t.otb_picking_slip_id and ops.remove_flag=0
                             <include refid="Base_otb_picking_slip_Where_List"/>
                           )
            </if>
            <if test="qo.otbRequestQuery!= null">
                and exists(  select orq.id
                             from otb_request orq
                             orq.id=t.otb_request_id and orq.remove_flag=0
                             <include refid="Base_otb_request_Where_List"/>
                           )
            </if>
            <if test="qo.otbPackageLabelQuery!= null">
                and exists(  select opl.id
                             from otb_package_label opl
                             opl.otb_package_id=t.id and opl.remove_flag=0
                             <include refid="Base_otb_package_label_Where_List"/>
                           )
            </if>
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <sql id="Base_otb_workorder_Where_List">
        <if test="qo.otbWorkorderQuery.refNumList!= null and qo.otbWorkorderQuery.refNumList.size > 0 ">
            and ow.ref_num in <foreach collection="qo.otbWorkorderQuery.refNumList" item="refNum" open="(" separator="," close=")">
                #{refNum}
            </foreach>
        </if>
    </sql>

    <sql id="Base_otb_picking_slip_Where_List">
        <if test="qo.otbPickingSlipQuery.refNumList!= null and qo.otbPickingSlipQuery.refNumList.size > 0 ">
            and ops.ref_num in <foreach collection="qo.otbPickingSlipQuery.refNumList" item="refNum" open="(" separator="," close=")">
                #{refNum}
            </foreach>
        </if>
    </sql>

    <sql id="Base_otb_request_Where_List">
        <if test="qo.otbRequestQuery.refNumList!= null and qo.otbRequestQuery.refNumList.size > 0 ">
            and orq.ref_num in <foreach collection="qo.otbRequestQuery.refNumList" item="refNum" open="(" separator="," close=")">
                #{refNum}
            </foreach>

            and orq.request_num in <foreach collection="qo.otbRequestQuery.requestNumList" item="requestNum" open="(" separator="," close=")">
                #{requestNum}
            </foreach>
        </if>
    </sql>

    <sql id="Base_otb_package_label_Where_List">
        <if test="qo.otbPackageLabelQuery.printStatusList!= null and qo.otbPackageLabelQuery.printStatusList.size > 0 ">
            and opl.print_status in <foreach collection="qo.otbPackageLabelQuery.printStatusList" item="printStatus" open="(" separator="," close=")">
                #{printStatus}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>


    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.otbPalletSsccNum!=null and qo.otbPalletSsccNum!= ''">
            and exists(  select id
                         from otb_pallet op
                         where op.remove_flag = 0 and op.sscc_num = #{qo.otbPalletSsccNum}
                       )
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--包裹id-->
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.ssccNum != null and qo.ssccNum != ''">
            AND t.sscc_num = #{qo.ssccNum}
        </if>
        <if test="qo.ssccNumList != null and qo.ssccNumList.size > 0 ">
            AND t.sscc_num in
            <foreach collection="qo.ssccNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lineNum != null">
            AND t.line_num = #{qo.lineNum}
        </if>
        <if test="qo.otbRequestIdList != null and qo.otbRequestIdList.size()>0">
            AND t.otb_request_id in <foreach collection="qo.otbRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
        <if test="qo.otbWorkorderIdList != null and qo.otbWorkorderIdList.size()>0">
            AND t.otb_workorder_id in <foreach collection="qo.otbWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
        <if test="qo.otbPickingSlipIdList != null and qo.otbPickingSlipIdList.size()>0">
            AND t.otb_picking_slip_id in <foreach collection="qo.otbPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        </if>
        <if test="qo.otbShipmentId != null">
            AND t.otb_shipment_id = #{qo.otbShipmentId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPalletId != null">
            AND t.otb_pallet_id = #{qo.otbPalletId}
        </if>
        <if test="qo.orderNum != null and qo.orderNum != ''">
            AND t.order_num = #{qo.orderNum}
        </if>
        <if test="qo.otbPackageStatus != null and qo.otbPackageStatus != ''">
            AND t.otb_package_status = #{qo.otbPackageStatus}
        </if>
        <if test="qo.otbPackageStatusList != null and qo.otbPackageStatusList.size > 0 ">
            AND t.otb_package_status in
            <foreach collection="qo.otbPackageStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPackageType != null and qo.otbPackageType != ''">
            AND t.otb_package_type = #{qo.otbPackageType}
        </if>
        <if test="qo.otbPackageTypeList != null and qo.otbPackageTypeList.size > 0 ">
            AND t.otb_package_type in
            <foreach collection="qo.otbPackageTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPalletType != null and qo.otbPalletType != ''">
            AND t.otb_pallet_type = #{qo.otbPalletType}
        </if>
        <if test="qo.otbPalletTypeList != null and qo.otbPalletTypeList.size > 0 ">
            AND t.otb_pallet_type in
            <foreach collection="qo.otbPalletTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPickingSlipId != null">
            AND t.otb_picking_slip_id = #{qo.otbPickingSlipId}
        </if>
        <if test="qo.shortSsccNum != null and qo.shortSsccNum != ''">
            AND t.short_ssccnum = #{qo.shortSsccNum}
        </if>
        <if test="qo.shortSsccNumList != null and qo.shortSsccNumList.size > 0 ">
            AND t.short_ssccnum in
            <foreach collection="qo.shortSsccNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.station != null and qo.station != ''">
            AND t.station = #{qo.station}
        </if>
        <if test="qo.stationList != null and qo.stationList.size > 0 ">
            AND t.station in
            <foreach collection="qo.stationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipMethod != null and qo.shipMethod != ''">
            AND t.ship_method = #{qo.shipMethod}
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipApiProfileRefNum != null and qo.shipApiProfileRefNum != ''">
            AND t.ship_api_profile_ref_num = #{qo.shipApiProfileRefNum}
        </if>
        <if test="qo.shipApiProfileRefNumList != null and qo.shipApiProfileRefNumList.size > 0 ">
            AND t.ship_api_profile_ref_num in
            <foreach collection="qo.shipApiProfileRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.trackingNum != null and qo.trackingNum != ''">
            AND t.tracking_num = #{qo.trackingNum}
        </if>
        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0 ">
            AND t.tracking_num in
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.otbWorkorderId != null and qo.otbWorkorderId != ''">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
    </sql>

</mapper>