<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPickingSlipDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="line_num" property="lineNum" />
        <result column="qty" property="qty" />
        <result column="picked_qty" property="pickedQty" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="note" property="note" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="otb_picking_slip_id" property="otbPickingSlipId" />
        <result column="product_id" property="productId" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="product_version_id" property="productVersionId" />
        <result column="bin_location_detail_locked_id" property="binLocationDetailLockedId" />
        <result column="packed_qty" property="packedQty" />
        <result column="product_barcode" property="productBarcode" />
        <result column="product_channel_sku" property="productChannelSku" />
        <result column="relabel_status" property="relabelStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.line_num,
        t.qty,
        t.picked_qty,
        t.bin_location_detail_id,
        t.bin_location_id,
        t.note,
        t.version,
        t.tenant_id,
        t.warehouse_id,
        t.otb_picking_slip_id,
        t.product_id,
        t.hazmat_version_ref_num,
        t.product_version_id,
        t.bin_location_detail_locked_id,
        t.packed_qty,
        t.product_barcode,
        t.product_channel_sku,
        t.relabel_status
    </sql>
    <select id="summary" resultType="cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipSummaryVO">
        SELECT SUM(t.qty) AS qty, t.product_id AS productId, t.bin_location_id AS binLocationId
        FROM otb_picking_slip_detail t
        WHERE
            t.remove_flag = 0
            AND t.otb_picking_slip_id IN
            <foreach collection="psIds" item="psId" separator="," open="(" close=")">
                #{psId}
            </foreach>
        GROUP BY product_id, bin_location_id
    </select>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPickingSlipDetailPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_picking_slip_detail t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lineNum != null">
            AND t.line_num = #{qo.lineNum}
        </if>
        <if test="qo.qty != null">
            AND t.qty = #{qo.qty}
        </if>
        <if test="qo.pickedQty != null">
            AND t.picked_qty = #{qo.pickedQty}
        </if>
        <if test="qo.binLocationDetailId != null">
            AND t.bin_location_detail_id = #{qo.binLocationDetailId}
        </if>
        <if test="qo.binLocationId != null">
            AND t.bin_location_id = #{qo.binLocationId}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.otbPickingSlipId != null">
            AND t.otb_picking_slip_id = #{qo.otbPickingSlipId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
        <if test="qo.binLocationDetailLockedId != null">
            AND t.bin_location_detail_locked_id = #{qo.binLocationDetailLockedId}
        </if>
        <if test="qo.packedQty != null">
            AND t.packed_qty = #{qo.packedQty}
        </if>
        <if test="qo.productBarcode != null and qo.productBarcode != ''">
            AND t.product_barcode = #{qo.productBarcode}
        </if>
        <if test="qo.productBarcodeList != null and qo.productBarcodeList.size > 0 ">
            AND t.product_barcode in
            <foreach collection="qo.productBarcodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.relabelStatus != null and qo.relabelStatus != ''">
            AND t.relabel_status = #{qo.relabelStatus}
        </if>
        <if test="qo.relabelStatusList != null and qo.relabelStatusList.size > 0 ">
            AND t.relabel_status in
            <foreach collection="qo.relabelStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>