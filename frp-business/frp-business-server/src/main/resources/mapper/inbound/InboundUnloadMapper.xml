<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.inbound.InboundUnloadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.inbound.InboundUnload">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="inbound_workorder_id" property="inboundWorkorderId" />
        <result column="inbound_workorder_detail_id" property="inboundWorkorderDetailId" />
        <result column="putaway_slip_id" property="putawaySlipId" />
        <result column="qty" property="qty" />
        <result column="note" property="note" />
        <result column="pallet_qty" property="palletQty" />
        <result column="pallet_putaway_qty" property="palletPutawayQty" />
        <result column="regular_putaway_qty" property="regularPutawayQty" />
        <result column="inbound_unload_status" property="inboundUnloadStatus" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
        <result column="request_id" property="requestId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.inbound_workorder_id,
        t.inbound_workorder_detail_id,
        t.putaway_slip_id,
        t.qty,
        t.note,
        t.pallet_qty,
        t.pallet_putaway_qty,
        t.regular_putaway_qty,
        t.inbound_unload_status,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.product_id,
        t.product_version_id,
        t.request_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.InboundUnloadPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            inbound_unload t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundWorkorderId != null">
            AND t.inbound_workorder_id = #{qo.inboundWorkorderId}
        </if>
        <if test="qo.inboundWorkorderDetailId != null">
            AND t.inbound_workorder_detail_id = #{qo.inboundWorkorderDetailId}
        </if>
        <if test="qo.putawaySlipId != null">
            AND t.putaway_slip_id = #{qo.putawaySlipId}
        </if>
        <if test="qo.qty != null">
            AND t.qty = #{qo.qty}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.palletQty != null">
            AND t.pallet_qty = #{qo.palletQty}
        </if>
        <if test="qo.palletPutawayQty != null">
            AND t.pallet_putaway_qty = #{qo.palletPutawayQty}
        </if>
        <if test="qo.regularPutawayQty != null">
            AND t.regular_putaway_qty = #{qo.regularPutawayQty}
        </if>
        <if test="qo.inboundUnloadStatus != null and qo.inboundUnloadStatus != ''">
            AND t.inbound_unload_status = #{qo.inboundUnloadStatus}
        </if>
        <if test="qo.inboundUnloadStatusList != null and qo.inboundUnloadStatusList.size > 0 ">
            AND t.inbound_unload_status in
            <foreach collection="qo.inboundUnloadStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
        <if test="qo.requestId != null">
            AND t.request_id = #{qo.requestId}
        </if>
    </sql>

</mapper>