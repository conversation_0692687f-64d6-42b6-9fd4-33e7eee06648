<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.inbound.InboundPalletDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.inbound.InboundPalletDetail">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="carton_per_layer" property="cartonPerLayer" />
        <result column="layers_count" property="layersCount" />
        <result column="ext_carton" property="extCarton" />
        <result column="pcs_per_carton" property="pcsPerCarton" />
        <result column="note" property="note" />
        <result column="inbound_workorder_id" property="inboundWorkorderId" />
        <result column="inbound_unload_id" property="inboundUnloadId" />
        <result column="putaway_slip_id" property="putawaySlipId" />
        <result column="putaway_slip_detail_id" property="putawaySlipDetailId" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
        <result column="inbound_pallet_id" property="inboundPalletId" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_id" property="binLocationId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.carton_per_layer,
        t.layers_count,
        t.ext_carton,
        t.pcs_per_carton,
        t.note,
        t.inbound_workorder_id,
        t.inbound_unload_id,
        t.putaway_slip_id,
        t.putaway_slip_detail_id,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.product_id,
        t.product_version_id,
        t.inbound_pallet_id,
        t.bin_location_detail_id,
        t.bin_location_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.InboundPalletDetailPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            inbound_pallet_detail t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.cartonPerLayer != null">
            AND t.carton_per_layer = #{qo.cartonPerLayer}
        </if>
        <if test="qo.layersCount != null">
            AND t.layers_count = #{qo.layersCount}
        </if>
        <if test="qo.extCarton != null">
            AND t.ext_carton = #{qo.extCarton}
        </if>
        <if test="qo.pcsPerCarton != null">
            AND t.pcs_per_carton = #{qo.pcsPerCarton}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.inboundWorkorderId != null">
            AND t.inbound_workorder_id = #{qo.inboundWorkorderId}
        </if>
        <if test="qo.inboundUnloadId != null">
            AND t.inbound_unload_id = #{qo.inboundUnloadId}
        </if>
        <if test="qo.putawaySlipId != null">
            AND t.putaway_slip_id = #{qo.putawaySlipId}
        </if>
        <if test="qo.putawaySlipDetailId != null">
            AND t.putaway_slip_detail_id = #{qo.putawaySlipDetailId}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
        <if test="qo.inboundPalletId != null">
            AND t.inbound_pallet_id = #{qo.inboundPalletId}
        </if>
        <if test="qo.binLocationDetailId != null">
            AND t.bin_location_detail_id = #{qo.binLocationDetailId}
        </if>
        <if test="qo.binLocationId != null">
            AND t.bin_location_id = #{qo.binLocationId}
        </if>
    </sql>

</mapper>