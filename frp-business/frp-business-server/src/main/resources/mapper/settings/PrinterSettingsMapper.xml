<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.setting.PrinterSettingsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.setting.PrinterSettings">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="label_printer" property="labelPrinter" />
        <result column="one_by_three_label_printer" property="oneByThreeLabelPrinter" />
        <result column="regular_printer" property="regularPrinter" />
        <result column="mobile_label_printer" property="mobileLabelPrinter" />
        <result column="mobile_c_lodop_ip" property="mobileCLodopIp" />
        <result column="scanner_plugin_ip" property="scannerPluginIp" />
        <result column="scanner" property="scanner" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.version,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.label_printer,
        t.one_by_three_label_printer,
        t.regular_printer,
        t.mobile_label_printer,
        t.mobile_c_lodop_ip,
        t.scanner_plugin_ip,
        t.scanner
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.labelPrinter != null and qo.labelPrinter != ''">
            AND t.label_printer = #{qo.labelPrinter}
        </if>
        <if test="qo.oneByThreeLabelPrinter != null and qo.oneByThreeLabelPrinter != ''">
            AND t.one_by_three_label_printer = #{qo.oneByThreeLabelPrinter}
        </if>
        <if test="qo.regularPrinter != null and qo.regularPrinter != ''">
            AND t.regular_printer = #{qo.regularPrinter}
        </if>
        <if test="qo.mobileLabelPrinter != null and qo.mobileLabelPrinter != ''">
            AND t.mobile_label_printer = #{qo.mobileLabelPrinter}
        </if>
        <if test="qo.mobileCLodopIp != null and qo.mobileCLodopIp != ''">
            AND t.mobile_c_lodop_ip = #{qo.mobileCLodopIp}
        </if>
        <if test="qo.scannerPluginIp != null and qo.scannerPluginIp != ''">
            AND t.scanner_plugin_ip = #{qo.scannerPluginIp}
        </if>
        <if test="qo.scanner != null and qo.scanner != ''">
            AND t.scanner = #{qo.scanner}
        </if>
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.PrinterSettingsPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            printer_settings t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="selectByUserId" resultType="cn.need.cloud.biz.model.vo.setting.PrinterSettingsVO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            printer_settings t
        WHERE
            t.remove_flag = 0
            AND t.create_by = #{userId}
        ORDER BY
            t.create_time
        DESC LIMIT 1;
    </select>

</mapper>
