<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.log.BinLocationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.log.BinLocationLog">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="ref_table_id" property="refTableId" />
        <result column="ref_table_name" property="refTableName" />
        <result column="ref_table_ref_num" property="refTableRefNum" />
        <result column="ref_table_show_name" property="refTableShowName" />
        <result column="ref_table_show_ref_num" property="refTableShowRefNum" />
        <result column="source_bin_location_id" property="sourceBinLocationId" />
        <result column="source_bin_location_detail_id" property="sourceBinLocationDetailId" />
        <result column="source_change_in_stock_qty" property="sourceChangeInStockQty" />
        <result column="source_before_in_stock_qty" property="sourceBeforeInStockQty" />
        <result column="source_after_in_stock_qty" property="sourceAfterInStockQty" />
        <result column="dest_bin_location_id" property="destBinLocationId" />
        <result column="dest_bin_location_detail_id" property="destBinLocationDetailId" />
        <result column="dest_change_in_stock_qty" property="destChangeInStockQty" />
        <result column="dest_before_in_stock_qty" property="destBeforeInStockQty" />
        <result column="dest_after_in_stock_qty" property="destAfterInStockQty" />
        <result column="note" property="note" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.version,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.ref_table_id,
        t.ref_table_name,
        t.ref_table_ref_num,
        t.ref_table_show_name,
        t.ref_table_show_ref_num,
        t.source_bin_location_id,
        t.source_bin_location_detail_id,
        t.source_change_in_stock_qty,
        t.source_before_in_stock_qty,
        t.source_after_in_stock_qty,
        t.dest_bin_location_id,
        t.dest_bin_location_detail_id,
        t.dest_change_in_stock_qty,
        t.dest_before_in_stock_qty,
        t.dest_after_in_stock_qty,
        t.note,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.product_id,
        t.product_version_id,
        t.change_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            bin_location_log t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableId != null">
            AND t.ref_table_id = #{qo.refTableId}
        </if>
        <if test="qo.refTableName != null and qo.refTableName != ''">
            AND t.ref_table_name = #{qo.refTableName}
        </if>
        <if test="qo.refTableRefNum != null and qo.refTableRefNum != ''">
            AND t.ref_table_ref_num = #{qo.refTableRefNum}
        </if>
        <if test="qo.refTableRefNumList != null and qo.refTableRefNumList.size > 0 ">
            AND t.ref_table_ref_num in
            <foreach collection="qo.refTableRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableShowNameList != null and qo.refTableShowNameList.size > 0 ">
            AND t.ref_table_show_name in
            <foreach collection="qo.refTableShowNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableShowName != null and qo.refTableShowName != ''">
            AND t.ref_table_show_name = #{qo.refTableShowName}
        </if>
        <if test="qo.refTableShowRefNum != null and qo.refTableShowRefNum != ''">
            AND t.ref_table_show_ref_num = #{qo.refTableShowRefNum}
        </if>
        <if test="qo.refTableShowRefNumList != null and qo.refTableShowRefNumList.size > 0 ">
            AND t.ref_table_show_ref_num in
            <foreach collection="qo.refTableShowRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.sourceBinLocationId != null and qo.destBinLocationId != null">
            and (t.source_bin_location_id = #{qo.sourceBinLocationId} or t.dest_bin_location_id = #{qo.destBinLocationId})
        </if>
        <if test="qo.sourceBinLocationDetailId != null">
            AND t.source_bin_location_detail_id = #{qo.sourceBinLocationDetailId}
        </if>
        <if test="qo.sourceChangeInStockQty != null">
            AND t.source_change_in_stock_qty = #{qo.sourceChangeInStockQty}
        </if>
        <if test="qo.sourceBeforeInStockQty != null">
            AND t.source_before_in_stock_qty = #{qo.sourceBeforeInStockQty}
        </if>
        <if test="qo.sourceAfterInStockQty != null">
            AND t.source_after_in_stock_qty = #{qo.sourceAfterInStockQty}
        </if>
        <if test="qo.destBinLocationDetailId != null">
            AND t.dest_bin_location_detail_id = #{qo.destBinLocationDetailId}
        </if>
        <if test="qo.destChangeInStockQty != null">
            AND t.dest_change_in_stock_qty = #{qo.destChangeInStockQty}
        </if>
        <if test="qo.destBeforeInStockQty != null">
            AND t.dest_before_in_stock_qty = #{qo.destBeforeInStockQty}
        </if>
        <if test="qo.destAfterInStockQty != null">
            AND t.dest_after_in_stock_qty = #{qo.destAfterInStockQty}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
    </sql>

</mapper>