<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcRequestPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcRequestPackage">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="line_num" property="lineNum" />
        <result column="tracking_num" property="trackingNum" />
        <result column="ship_express_flag" property="shipExpressFlag" />
        <result column="ship_method" property="shipMethod" />
        <result column="ship_carrier" property="shipCarrier" />
        <result column="ship_size_length" property="shipSizeLength" />
        <result column="ship_size_width" property="shipSizeWidth" />
        <result column="ship_size_height" property="shipSizeHeight" />
        <result column="ship_size_weight" property="shipSizeWeight" />
        <result column="note" property="note" />
        <result column="otc_request_id" property="otcRequestId" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ship_size_dimension_unit" property="shipSizeDimensionUnit" />
        <result column="ship_size_weight_unit" property="shipSizeWeightUnit" />
        <result column="package_multibox_upc" property="packageMultiboxUpc" />
        <result column="package_multibox_line_num" property="packageMultiboxLineNum" />
        <result column="package_multibox_product_id" property="packageMultiboxProductId" />
        <result column="package_multibox_version_int" property="packageMultiboxVersionInt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.line_num,
        t.tracking_num,
        t.ship_express_flag,
        t.ship_method,
        t.ship_carrier,
        t.ship_size_length,
        t.ship_size_width,
        t.ship_size_height,
        t.ship_size_weight,
        t.note,
        t.otc_request_id,
        t.version,
        t.tenant_id,
        t.warehouse_id,
        t.ship_size_dimension_unit,
        t.ship_size_weight_unit,
        t.package_multibox_upc,
        t.package_multibox_line_num,
        t.package_multibox_product_id,
        t.package_multibox_version_int
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackagePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otc_request_package t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lineNum != null">
            AND t.line_num = #{qo.lineNum}
        </if>
        <if test="qo.trackingNum != null and qo.trackingNum != ''">
            AND t.tracking_num = #{qo.trackingNum}
        </if>
        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0 ">
            AND t.tracking_num in
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipExpressFlag != null">
            AND t.ship_express_flag = #{qo.shipExpressFlag}
        </if>
        <if test="qo.shipMethod != null and qo.shipMethod != ''">
            AND t.ship_method = #{qo.shipMethod}
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipSizeLength != null">
            AND t.ship_size_length = #{qo.shipSizeLength}
        </if>
        <if test="qo.shipSizeWidth != null">
            AND t.ship_size_width = #{qo.shipSizeWidth}
        </if>
        <if test="qo.shipSizeHeight != null">
            AND t.ship_size_height = #{qo.shipSizeHeight}
        </if>
        <if test="qo.shipSizeWeight != null">
            AND t.ship_size_weight = #{qo.shipSizeWeight}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.otcRequestId != null">
            AND t.otc_request_id = #{qo.otcRequestId}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.shipSizeDimensionUnit != null and qo.shipSizeDimensionUnit != ''">
            AND t.ship_size_dimension_unit = #{qo.shipSizeDimensionUnit}
        </if>
        <if test="qo.shipSizeWeightUnit != null and qo.shipSizeWeightUnit != ''">
            AND t.ship_size_weight_unit = #{qo.shipSizeWeightUnit}
        </if>
        <if test="qo.packageMultiboxUpc != null and qo.packageMultiboxUpc != ''">
            AND t.package_multibox_upc = #{qo.packageMultiboxUpc}
        </if>
        <if test="qo.packageMultiboxUpcList != null and qo.packageMultiboxUpcList.size > 0 ">
            AND t.package_multibox_upc in
            <foreach collection="qo.packageMultiboxUpcList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.packageMultiboxLineNum != null">
            AND t.package_multibox_line_num = #{qo.packageMultiboxLineNum}
        </if>
        <if test="qo.packageMultiboxProductId != null">
            AND t.package_multibox_product_id = #{qo.packageMultiboxProductId}
        </if>
        <if test="qo.packageMultiboxVersionInt != null">
            AND t.package_multibox_version_int = #{qo.packageMultiboxVersionInt}
        </if>
    </sql>

</mapper>