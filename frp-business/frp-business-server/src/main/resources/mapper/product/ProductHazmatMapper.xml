<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductHazmatMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.ProductHazmat">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="package_instruction" property="packageInstruction" />
        <result column="product_id" property="productId" />
        <result column="transportation_regulatory_class" property="transportationRegulatoryClass" />
        <result column="un_regulatory_id" property="unRegulatoryId" />
        <result column="version_ref_num" property="versionRefNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.package_instruction,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.transportation_regulatory_class,
        ${dbTableAlias}.un_regulatory_id,
        ${dbTableAlias}.version_ref_num
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.page.ProductHazmatPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ph"/>
            </include>
        FROM
        product_hazmat ph
        WHERE
            ph.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ph"/>
                    <property name="qoTableAlias" value="qoph"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ph"/>
                <property name="qoTableAlias" value="qoph"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoph.timeZone != null and qoph.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoph.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            product_hazmat ph
        WHERE
            ph.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ph"/>
                    <property name="qoTableAlias" value="qoph"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoph.timeZone != null and qoph.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoph.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
    <if test="${qoTableAlias} != null">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.id"/>
            <property name="qoColumnName" value="${qoTableAlias}.id"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.tenant_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.tenantId"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version"/>
            <property name="qoColumnName" value="${qoTableAlias}.version"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.createBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.createTime"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.package_instruction"/>
            <property name="qoColumnName" value="${qoTableAlias}.packageInstruction"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.product_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.productId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.transportation_regulatory_class"/>
            <property name="qoColumnName" value="${qoTableAlias}.transportationRegulatoryClass"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.un_regulatory_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.unRegulatoryId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version_ref_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.versionRefNum"/>
        </include>

    </if>
    </sql>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM product_hazmat ${InnerTableAlias}
                WHERE ${JoinCondition}
                WHERE ${InnerTableAlias}.remove_flag = 0
                    <include refid="Base_Where_List">
                        <property name="dbTableAlias" value="${InnerTableAlias}"/>
                        <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
                    </include>
            )
        </if>
    </sql>
</mapper>