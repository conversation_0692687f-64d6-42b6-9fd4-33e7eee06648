<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.ProductGroup">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="parent_product_id" property="parentProductId" />
        <result column="child_product_id" property="childProductId" />
        <result column="instruction_note" property="instructionNote" />
        <result column="revert_instruction_note" property="revertInstructionNote" />
        <result column="convert_group_type" property="convertGroupType" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="group_version_int" property="groupVersionInt" />
        <result column="deleted_note" property="deletedNote" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.parent_product_id,
        t.child_product_id,
        t.instruction_note,
        t.revert_instruction_note,
        t.convert_group_type,
        t.version,
        t.tenant_id,
        t.group_version_int,
        t.deleted_note
    </sql>
    <sql id="Group_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.parent_product_id,
        t.child_product_id,
        t.instruction_note,
        t.revert_instruction_note,
        t.convert_group_type,
        t.group_version_int,
        t.deleted_note
    </sql>
    <sql id="Parent_Product_Column_List">
        p.ref_num parentProductRefNum,
        p.supplier_sku parentProductSupplierSku,
        p.upc  parentProductUpc,
        p.title  parentProductTitle
    </sql>
    <sql id="Child_Product_Column_List">
        c.ref_num childProductRefNum,
        c.supplier_sku childProductSupplierSku,
        c.upc  childProductUpc,
        c.title childProductTitle
    </sql>

    <select id="getParentList" resultType="cn.need.cloud.biz.model.vo.product.ProductGroupListVO"
            parameterType="java.lang.Long">
        SELECT
        <include refid="Group_Column_List" />,
        <include refid="Parent_Product_Column_List" />,
        <include refid="Child_Product_Column_List" />
        FROM
        product_group t
        inner join product p on t.parent_product_id = p.id
        inner join product c on t.child_product_id = c.id
        WHERE
        t.remove_flag = 0
        and p.remove_flag = 0
        and c.remove_flag = 0
        AND t.parent_product_id = #{productId}
    </select>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.page.ProductGroupPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            product_group t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="getOldVersionInt" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT MAX(t.group_version_int)
        FROM product_group t
        WHERE t.parent_product_id = #{productId}
    </select>

    <select id="findByParentProductIdAndVersionInt"
            resultType="cn.need.cloud.biz.model.entity.product.ProductGroup">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        product_group t
        WHERE
        t.parent_product_id = #{productId}
        AND t.group_version_int = #{versionInt}
    </select>

    <select id="findByParentIdAndVersionInts"
            resultType="cn.need.cloud.biz.model.entity.product.ProductGroup">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_group t
        WHERE
            1 = 1
        <if test="parentIdList != null and parentIdList.size() > 0">
            AND t.parent_product_id IN
            <foreach collection="parentIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="childIdList != null and childIdList.size() > 0">
            AND t.child_product_id IN
            <foreach collection="childIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupVersionIntList != null and groupVersionIntList.size() > 0">
            AND t.group_version_int IN
            <foreach collection="groupVersionIntList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findParentIdByChildProductIdAndVersionInt" resultType="java.lang.Long">
        SELECT t.parent_product_id
        FROM product_group t
        WHERE 1 = 1
          AND t.child_product_id = #{childId}
          AND t.group_version_int = #{versionInt}
    </select>
    <select id="findParentIdsByChildProductIdAndVersionInt" resultType="java.lang.Long">
        SELECT t.parent_product_id
        FROM product_group t
        WHERE 1 = 1
          AND t.child_product_id IN
          <foreach collection="childIdList" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
          AND t.group_version_int IN
          <foreach collection="versionIntList" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
    </select>


    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.parentProductId != null">
            AND t.parent_product_id = #{qo.parentProductId}
        </if>
        <if test="qo.childProductId != null">
            AND t.child_product_id = #{qo.childProductId}
        </if>
        <if test="qo.instructionNote != null and qo.instructionNote != ''">
            AND t.instruction_note = #{qo.instructionNote}
        </if>
        <if test="qo.revertInstructionNote != null and qo.revertInstructionNote != ''">
            AND t.revert_instruction_note = #{qo.revertInstructionNote}
        </if>
        <if test="qo.convertGroupType != null and qo.convertGroupType != ''">
            AND t.convert_group_type = #{qo.convertGroupType}
        </if>
        <if test="qo.convertGroupTypeList != null and qo.convertGroupTypeList.size > 0 ">
            AND t.convert_group_type in
            <foreach collection="qo.convertGroupTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.groupVersionInt != null">
            AND t.group_version_int = #{qo.groupVersionInt}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
    </sql>

</mapper>