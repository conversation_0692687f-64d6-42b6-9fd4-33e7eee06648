<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductMultiboxMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.ProductMultibox">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="ship_length" property="shipLength" />
        <result column="ship_width" property="shipWidth" />
        <result column="ship_height" property="shipHeight" />
        <result column="ship_weight" property="shipWeight" />
        <result column="ship_weight_unit" property="shipWeightUnit" />
        <result column="ship_dimension_unit" property="shipDimensionUnit" />
        <result column="upc" property="upc" />
        <result column="line_num" property="lineNum" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="product_id" property="productId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="multibox_version_int" property="multiboxVersionInt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.ship_length,
        t.ship_width,
        t.ship_height,
        t.ship_weight,
        t.ship_weight_unit,
        t.ship_dimension_unit,
        t.upc,
        t.line_num,
        t.version,
        t.tenant_id,
        t.product_id,
        t.deleted_note,
        t.multibox_version_int
    </sql>

    <sql id="Multibox_Column_List">
        t.id,
        t.create_time,
        t.ship_length,
        t.ship_width,
        t.ship_height,
        t.ship_weight,
        t.ship_weight_unit,
        t.ship_dimension_unit,
        t.upc,
        t.line_num,
        t.product_id,
        t.multibox_version_int
    </sql>
    <sql id="Product_Column_List">
        p.transaction_partner_id,
        p.ref_num,
        p.supplier_sku,
        p.upc as productDetailUpc,
        p.assembly_product_flag,
        p.title,
        p.multibox_flag,
        p.group_type
    </sql>
    <sql id="Multibox_Detail_Column_List">
        d.id as detailId,
        d.line_num as detailLineNum,
        d.qty,
        d.product_id as detailProductId,
        d.create_time as detailCreateTime
    </sql>
    <select id="listByProductId" resultMap="GetMultiboxList"
            parameterType="java.lang.Long">
        select
        <include refid="Multibox_Column_List"/>,
        <include refid="Product_Column_List"/>,
        <include refid="Multibox_Detail_Column_List"/>
        from product_multibox t
        inner join product_multibox_detail d on t.id = d.product_multibox_id
        inner join product p on p.id = d.product_id
        where t.product_id = #{productId}
        and t.remove_flag = 0
        and d.remove_flag = 0
        and p.remove_flag = 0
    </select>
    <resultMap id="GetMultiboxList" type="cn.need.cloud.biz.model.vo.product.ProductMultiboxListVO" autoMapping="true">
        <id property="id" column="id"/>
        <collection property="detailList"  ofType="cn.need.cloud.biz.model.vo.product.MultiboxDetailVO" autoMapping="true">
            <id property="multiboxDetailId" column="detailId" />
            <result property="productId" column="detailProductId" />
            <result property="lineNum" column="detailLineNum" />
            <result property="upc" column="productDetailUpc" />
            <result property="createTime" column="detailCreateTime" />
        </collection>
    </resultMap>


    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.page.ProductMultiboxPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            product_multibox t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="getOldVersionInt" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT max(multibox_version_int)
        FROM product_multibox
        WHERE product_id = #{productId}
    </select>
    <select id="findListByProductAndVersionInt"
            resultType="cn.need.cloud.biz.model.entity.product.ProductMultibox">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        product_multibox t
        WHERE
        t.product_id = #{productId}
        AND t.multibox_version_int = #{versionInt}
    </select>

    <select id="findListByProductAndVersionIntList"
            resultType="cn.need.cloud.biz.model.entity.product.ProductMultibox">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        product_multibox t
        WHERE
        t.product_id IN
        <foreach collection="productIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND t.multibox_version_int IN
        <foreach collection="versionInts" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findOneByProductIdAndVersionIntAndUpcAndLineNum"
            resultType="cn.need.cloud.biz.model.entity.product.ProductMultibox">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        product_multibox t
        WHERE
        t.product_id = #{productId}
        AND t.multibox_version_int = #{multiboxVersionInt}
        AND t.upc = #{upc}
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipLength != null">
            AND t.ship_length = #{qo.shipLength}
        </if>
        <if test="qo.shipWidth != null">
            AND t.ship_width = #{qo.shipWidth}
        </if>
        <if test="qo.shipHeight != null">
            AND t.ship_height = #{qo.shipHeight}
        </if>
        <if test="qo.shipWeight != null">
            AND t.ship_weight = #{qo.shipWeight}
        </if>
        <if test="qo.shipWeightUnit != null and qo.shipWeightUnit != ''">
            AND t.ship_weight_unit = #{qo.shipWeightUnit}
        </if>
        <if test="qo.shipDimensionUnit != null and qo.shipDimensionUnit != ''">
            AND t.ship_dimension_unit = #{qo.shipDimensionUnit}
        </if>
        <if test="qo.upc != null and qo.upc != ''">
            AND t.upc = #{qo.upc}
        </if>
        <if test="qo.upcList != null and qo.upcList.size > 0 ">
            AND t.upc in
            <foreach collection="qo.upcList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lineNum != null">
            AND t.line_num = #{qo.lineNum}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.multiboxVersionInt != null">
            AND t.multibox_version_int = #{qo.multiboxVersionInt}
        </if>
    </sql>

</mapper>