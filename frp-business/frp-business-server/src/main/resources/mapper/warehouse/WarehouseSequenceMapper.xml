<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.warehouse.WarehouseSequenceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.warehouse.WarehouseSequence">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="warehouse_ref_num" property="warehouseRefNum" />
        <result column="sequence_type" property="sequenceType" />
        <result column="to_day" property="toDay" />
        <result column="code" property="code" />
        <result column="sequence_id" property="sequenceId" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.warehouse_ref_num,
        t.sequence_type,
        t.to_day,
        t.code,
        t.sequence_id,
        t.version
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.WarehouseSequencePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            warehouse_sequence t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.warehouseRefNum != null and qo.warehouseRefNum != ''">
            AND t.warehouse_ref_num = #{qo.warehouseRefNum}
        </if>
        <if test="qo.warehouseRefNumList != null and qo.warehouseRefNumList.size > 0 ">
            AND t.warehouse_ref_num in
            <foreach collection="qo.warehouseRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.sequenceType != null and qo.sequenceType != ''">
            AND t.sequence_type = #{qo.sequenceType}
        </if>
        <if test="qo.sequenceTypeList != null and qo.sequenceTypeList.size > 0 ">
            AND t.sequence_type in
            <foreach collection="qo.sequenceTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.toDay != null and qo.toDay != ''">
            AND t.to_day = #{qo.toDay}
        </if>
        <if test="qo.code != null and qo.code != ''">
            AND t.code = #{qo.code}
        </if>
        <if test="qo.sequenceId != null">
            AND t.sequence_id = #{qo.sequenceId}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
    </sql>

</mapper>