<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.warehouse.PalletTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.warehouse.PalletTemplate">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="carton_per_layer" property="cartonPerLayer" />
        <result column="layers_count" property="layersCount" />
        <result column="ext_carton" property="extCarton" />
        <result column="pcs_per_carton" property="pcsPerCarton" />
        <result column="product_id" property="productId" />
        <result column="note" property="note" />
        <result column="ref_num" property="refNum" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="set_default_flag" property="setDefaultFlag" />
        <result column="product_version_id" property="productVersionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.carton_per_layer,
        t.layers_count,
        t.ext_carton,
        t.pcs_per_carton,
        t.product_id,
        t.note,
        t.ref_num,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.set_default_flag,
        t.product_version_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.PalletTemplatePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            pallet_template t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="getLatestTemplate" resultType="cn.need.cloud.biz.model.entity.warehouse.PalletTemplate">
        select id,
               version,
               set_default_flag
        from pallet_template
        where product_version_id = #{productVersionId}
        order by create_time DESC
        LIMIT 1
    </select>


    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <!--版本产品id-->
        <if test="qo.productVersionIdList != null and qo.productVersionIdList.size > 0 ">
            AND t.product_version_id in
            <foreach collection="qo.productVersionIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.cartonPerLayer != null">
            AND t.carton_per_layer = #{qo.cartonPerLayer}
        </if>
        <if test="qo.layersCount != null">
            AND t.layers_count = #{qo.layersCount}
        </if>
        <if test="qo.extCarton != null">
            AND t.ext_carton = #{qo.extCarton}
        </if>
        <if test="qo.pcsPerCarton != null">
            AND t.pcs_per_carton = #{qo.pcsPerCarton}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.setDefaultFlag != null">
            AND t.set_default_flag = #{qo.setDefaultFlag}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
    </sql>

</mapper>