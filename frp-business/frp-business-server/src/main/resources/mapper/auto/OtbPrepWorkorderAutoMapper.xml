<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.auto.OtbPrepWorkorderAutoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="inventory_reserve_id" property="inventoryReserveId" />
        <result column="otb_prep_picking_slip_id" property="otbPrepPickingSlipId" />
        <result column="otb_prep_workorder_status" property="otbPrepWorkorderStatus" />
        <result column="otb_request_id" property="otbRequestId" />
        <result column="otb_workorder_detail_id" property="otbWorkorderDetailId" />
        <result column="otb_workorder_id" property="otbWorkorderId" />
        <result column="prep_workorder_product_type" property="prepWorkorderProductType" />
        <result column="prep_workorder_type" property="prepWorkorderType" />
        <result column="prep_workorder_version_int" property="prepWorkorderVersionInt" />
        <result column="process_type" property="processType" />
        <result column="product_barcode" property="productBarcode" />
        <result column="product_channel_sku" property="productChannelSku" />
        <result column="product_id" property="productId" />
        <result column="putaway_qty" property="putawayQty" />
        <result column="qty" property="qty" />
        <result column="ref_num" property="refNum" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.bin_location_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.detail_product_type,
        ${dbTableAlias}.hazmat_version_ref_num,
        ${dbTableAlias}.inventory_reserve_id,
        ${dbTableAlias}.otb_prep_picking_slip_id,
        ${dbTableAlias}.otb_prep_workorder_status,
        ${dbTableAlias}.otb_request_id,
        ${dbTableAlias}.otb_workorder_detail_id,
        ${dbTableAlias}.otb_workorder_id,
        ${dbTableAlias}.prep_workorder_product_type,
        ${dbTableAlias}.prep_workorder_type,
        ${dbTableAlias}.prep_workorder_version_int,
        ${dbTableAlias}.process_type,
        ${dbTableAlias}.product_barcode,
        ${dbTableAlias}.product_channel_sku,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.putaway_qty,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.transaction_partner_id,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opw"/>
            </include>
        FROM
        otb_prep_workorder opw
        WHERE
            opw.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opw"/>
                    <property name="qoTableAlias" value="qoopw"/>
                </include>
        <include refid="Custom_Where_List">
            <property name="dbTableAlias" value="opw"/>
            <property name="qoTableAlias" value="qoopw"/>
        </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opw"/>
                <property name="qoTableAlias" value="qoopw"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopw.timeZone != null and qoopw.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopw.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            otb_prep_workorder opw
        WHERE
            opw.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opw"/>
                    <property name="qoTableAlias" value="qoopw"/>
                </include>
        <include refid="Custom_Where_List">
            <property name="dbTableAlias" value="opw"/>
            <property name="qoTableAlias" value="qoopw"/>
        </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopw.timeZone != null and qoopw.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopw.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.binLocationId != null">
            AND ${dbTableAlias}.bin_location_id = #{${qoTableAlias}.binLocationId}
        </if>
    <if test="${qoTableAlias}.binLocationIdList != null and ${qoTableAlias}.binLocationIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_id in
        <foreach collection="${qoTableAlias}.binLocationIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.detailProductType != null and ${qoTableAlias}.detailProductType != ''">
            AND ${dbTableAlias}.detail_product_type = #{${qoTableAlias}.detailProductType}
        </if>
    <if test="${qoTableAlias}.detailProductTypeList != null and ${qoTableAlias}.detailProductTypeList.size > 0 ">
        AND ${dbTableAlias}.detail_product_type in
        <foreach collection="${qoTableAlias}.detailProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.hazmatVersionRefNum != null and ${qoTableAlias}.hazmatVersionRefNum != ''">
            AND ${dbTableAlias}.hazmat_version_ref_num = #{${qoTableAlias}.hazmatVersionRefNum}
        </if>
    <if test="${qoTableAlias}.hazmatVersionRefNumList != null and ${qoTableAlias}.hazmatVersionRefNumList.size > 0 ">
        AND ${dbTableAlias}.hazmat_version_ref_num in
        <foreach collection="${qoTableAlias}.hazmatVersionRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.inventoryReserveId != null">
            AND ${dbTableAlias}.inventory_reserve_id = #{${qoTableAlias}.inventoryReserveId}
        </if>
    <if test="${qoTableAlias}.inventoryReserveIdList != null and ${qoTableAlias}.inventoryReserveIdList.size > 0 ">
        AND ${dbTableAlias}.inventory_reserve_id in
        <foreach collection="${qoTableAlias}.inventoryReserveIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbPrepPickingSlipId != null">
            AND ${dbTableAlias}.otb_prep_picking_slip_id = #{${qoTableAlias}.otbPrepPickingSlipId}
        </if>
    <if test="${qoTableAlias}.otbPrepPickingSlipIdList != null and ${qoTableAlias}.otbPrepPickingSlipIdList.size > 0 ">
        AND ${dbTableAlias}.otb_prep_picking_slip_id in
        <foreach collection="${qoTableAlias}.otbPrepPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbPrepWorkorderStatus != null and ${qoTableAlias}.otbPrepWorkorderStatus != ''">
            AND ${dbTableAlias}.otb_prep_workorder_status = #{${qoTableAlias}.otbPrepWorkorderStatus}
        </if>
    <if test="${qoTableAlias}.otbPrepWorkorderStatusList != null and ${qoTableAlias}.otbPrepWorkorderStatusList.size > 0 ">
        AND ${dbTableAlias}.otb_prep_workorder_status in
        <foreach collection="${qoTableAlias}.otbPrepWorkorderStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbRequestId != null">
            AND ${dbTableAlias}.otb_request_id = #{${qoTableAlias}.otbRequestId}
        </if>
    <if test="${qoTableAlias}.otbRequestIdList != null and ${qoTableAlias}.otbRequestIdList.size > 0 ">
        AND ${dbTableAlias}.otb_request_id in
        <foreach collection="${qoTableAlias}.otbRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbWorkorderDetailId != null">
            AND ${dbTableAlias}.otb_workorder_detail_id = #{${qoTableAlias}.otbWorkorderDetailId}
        </if>
    <if test="${qoTableAlias}.otbWorkorderDetailIdList != null and ${qoTableAlias}.otbWorkorderDetailIdList.size > 0 ">
        AND ${dbTableAlias}.otb_workorder_detail_id in
        <foreach collection="${qoTableAlias}.otbWorkorderDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbWorkorderId != null">
            AND ${dbTableAlias}.otb_workorder_id = #{${qoTableAlias}.otbWorkorderId}
        </if>
    <if test="${qoTableAlias}.otbWorkorderIdList != null and ${qoTableAlias}.otbWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.otb_workorder_id in
        <foreach collection="${qoTableAlias}.otbWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepWorkorderProductType != null and ${qoTableAlias}.prepWorkorderProductType != ''">
            AND ${dbTableAlias}.prep_workorder_product_type = #{${qoTableAlias}.prepWorkorderProductType}
        </if>
    <if test="${qoTableAlias}.prepWorkorderProductTypeList != null and ${qoTableAlias}.prepWorkorderProductTypeList.size > 0 ">
        AND ${dbTableAlias}.prep_workorder_product_type in
        <foreach collection="${qoTableAlias}.prepWorkorderProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepWorkorderType != null and ${qoTableAlias}.prepWorkorderType != ''">
            AND ${dbTableAlias}.prep_workorder_type = #{${qoTableAlias}.prepWorkorderType}
        </if>
    <if test="${qoTableAlias}.prepWorkorderTypeList != null and ${qoTableAlias}.prepWorkorderTypeList.size > 0 ">
        AND ${dbTableAlias}.prep_workorder_type in
        <foreach collection="${qoTableAlias}.prepWorkorderTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepWorkorderVersionInt != null">
            AND ${dbTableAlias}.prep_workorder_version_int = #{${qoTableAlias}.prepWorkorderVersionInt}
        </if>
        <if test="${qoTableAlias}.processType != null and ${qoTableAlias}.processType != ''">
            AND ${dbTableAlias}.process_type = #{${qoTableAlias}.processType}
        </if>
    <if test="${qoTableAlias}.processTypeList != null and ${qoTableAlias}.processTypeList.size > 0 ">
        AND ${dbTableAlias}.process_type in
        <foreach collection="${qoTableAlias}.processTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productBarcode != null and ${qoTableAlias}.productBarcode != ''">
            AND ${dbTableAlias}.product_barcode = #{${qoTableAlias}.productBarcode}
        </if>
    <if test="${qoTableAlias}.productBarcodeList != null and ${qoTableAlias}.productBarcodeList.size > 0 ">
        AND ${dbTableAlias}.product_barcode in
        <foreach collection="${qoTableAlias}.productBarcodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productChannelSku != null and ${qoTableAlias}.productChannelSku != ''">
            AND ${dbTableAlias}.product_channel_sku = #{${qoTableAlias}.productChannelSku}
        </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.putawayQty != null">
            AND ${dbTableAlias}.putaway_qty = #{${qoTableAlias}.putawayQty}
        </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.transactionPartnerId != null">
            AND ${dbTableAlias}.transaction_partner_id = #{${qoTableAlias}.transactionPartnerId}
        </if>
    <if test="${qoTableAlias}.transactionPartnerIdList != null and ${qoTableAlias}.transactionPartnerIdList.size > 0 ">
        AND ${dbTableAlias}.transaction_partner_id in
        <foreach collection="${qoTableAlias}.transactionPartnerIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opw"/>
           </include>
        FROM
            otb_prep_workorder opw
        WHERE
            opw.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="opw" />
                <property name="qoTableAlias" value="qoopw" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="opw" />
                <property name="qoTableAlias" value="qoopw" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opw"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM otb_prep_workorder ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.auto.OtbPrepWorkorderAutoMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>

    <!-- 自定义 -->
    <sql id="Custom_Where_List">
        <include refid="cn.need.cloud.biz.mapper.auto.OtbWorkorderAutoMapper.Exists_InnerTable">
            <property name="InnerQoTableAlias" value="qoopw.workorderQuery"/>
            <property name="InnerTableAlias" value="ow"/>
            <property name="JoinCondition" value="${dbTableAlias}.otb_workorder_id = ow.id"/>
        </include>

    </sql>
</mapper>