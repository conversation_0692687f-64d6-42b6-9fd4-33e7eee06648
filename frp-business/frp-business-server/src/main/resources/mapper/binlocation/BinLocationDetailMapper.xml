<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.binlocation.BinLocationDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO">
        <id column="id" property="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="inStockQty" column="in_stock_qty"/>
        <result property="productId" column="bpid"/>
        <result property="productVersionId" column="bpvid"/>
        <result property="binLocationId" column="bin_location_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createBy" column="create_by"/>
        <result property="createBy" column="create_by"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.in_stock_qty,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.product_id as bpid,
        t.product_version_id as bpvid,
        t.bin_location_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.BinLocationDetailPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            bin_location_detail t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="listByBinLocationAndProductId"
            resultType="cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail">
        SELECT
        *
        FROM
        bin_location_detail t
        WHERE
        t.remove_flag = 0
        <if test="bl != null">
            AND EXISTS (
                SELECT id FROM bin_location bl
                WHERE bl.id = t.bin_location_id
                AND bl.remove_flag = 0
                <include refid="Bin_Location_Where_List"/>
            )
        </if>
        <if test="productIdList != null and productIdList.size > 0 ">
            AND t.product_id IN
            <foreach collection="productIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="Bin_Location_Where_List">
        <if test="bl.warehouseZoneTypeList != null and bl.warehouseZoneTypeList.size > 0 ">
            AND bl.warehouse_zone_type in
            <foreach collection="bl.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.binTypeList != null and bl.binTypeList.size > 0 ">
            AND bl.bin_type in
            <foreach collection="bl.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.typeList != null and bl.typeList.size > 0 ">
            AND bl.type in
            <foreach collection="bl.typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lrowList != null and bl.lrowList.size > 0 ">
            AND bl.lrow in
            <foreach collection="bl.lrowList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.ldepthList != null and bl.ldepthList.size > 0 ">
            AND bl.ldepth in
            <foreach collection="bl.ldepthList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.llevelList != null and bl.llevelList.size > 0 ">
            AND bl.llevel in
            <foreach collection="bl.llevelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lsplitsList != null and bl.lsplitsList.size > 0 ">
            AND bl.lsplit in
            <foreach collection="bl.lsplitsList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inStockQty != null">
            AND t.in_stock_qty = #{qo.inStockQty}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
        <if test="qo.binLocationId != null">
            AND t.bin_location_id = #{qo.binLocationId}
        </if>
    </sql>

</mapper>