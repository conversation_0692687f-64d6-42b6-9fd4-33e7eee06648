package cn.need.cloud.biz.service.feeconfig.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigInboundConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigInboundMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInbound;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInboundDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigInboundCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigInboundDetailCreateParam;
import cn.need.cloud.biz.service.feeconfig.FeeConfigInboundDetailService;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.helper.SectionHelper;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 仓库报价费用配置inbound service 实现类测试
 */
@ExtendWith(MockitoExtension.class)
public class FeeConfigInboundServiceImplTest {

    @InjectMocks
    private FeeConfigInboundServiceImpl feeConfigInboundService;

    @Mock
    private FeeConfigInboundMapper feeConfigInboundMapper;

    @Mock
    private FeeConfigInboundDetailService feeConfigInboundDetailService;

    @Mock
    private QuoteService quoteService;

    @Mock
    private FeeConfigInboundConverter feeConfigInboundConverter;

    private FeeConfigInboundCreateParam createParam;
    private FeeConfigInbound feeConfigInbound;
    private List<FeeConfigInboundDetailCreateParam> detailList;
    private List<FeeConfigInboundDetail> detailEntities;

    @BeforeEach
    public void setUp() {
        // 准备测试数据
        createParam = new FeeConfigInboundCreateParam();
        createParam.setActiveFlag(true);
        //createParam.setConditionType("WEIGHT");
        createParam.setCurrency("USD");
        createParam.setFeeCalculationType("MULTIPLY");
        createParam.setName("Test Fee Config");
        createParam.setNote("Test note");

        // 准备详情列表
        detailList = new ArrayList<>();
        FeeConfigInboundDetailCreateParam detail1 = new FeeConfigInboundDetailCreateParam();
        detail1.setBaseFee(new BigDecimal("10.00"));
        detail1.setLineNum(1);
        detail1.setSectionStart(0L);
        detail1.setSectionEnd(100L);
        detail1.setUnitFee(new BigDecimal("1.00"));

        FeeConfigInboundDetailCreateParam detail2 = new FeeConfigInboundDetailCreateParam();
        detail2.setBaseFee(new BigDecimal("20.00"));
        detail2.setLineNum(2);
        detail2.setSectionStart(100L);
        detail2.setSectionEnd(999999999L);
        detail2.setUnitFee(new BigDecimal("2.00"));

        detailList.add(detail1);
        detailList.add(detail2);
        createParam.setDetailList(detailList);

        // 准备实体对象
        feeConfigInbound = new FeeConfigInbound();
        feeConfigInbound.setActiveFlag(true);
        //feeConfigInbound.setConditionType("WEIGHT");
        feeConfigInbound.setCurrency("USD");
        //feeConfigInbound.setFeeCalculationType("MULTIPLY");
        feeConfigInbound.setName("Test Fee Config");
        feeConfigInbound.setNote("Test note");

        // 准备详情实体列表
        detailEntities = new ArrayList<>();
        FeeConfigInboundDetail detailEntity1 = new FeeConfigInboundDetail();
        detailEntity1.setBaseFee(new BigDecimal("10.00"));
        detailEntity1.setLineNum(1);
        detailEntity1.setSectionStart(0L);
        detailEntity1.setSectionEnd(100L);
        detailEntity1.setUnitFee(new BigDecimal("1.00"));

        FeeConfigInboundDetail detailEntity2 = new FeeConfigInboundDetail();
        detailEntity2.setBaseFee(new BigDecimal("20.00"));
        detailEntity2.setLineNum(2);
        detailEntity2.setSectionStart(100L);
        detailEntity2.setSectionEnd(999999999L);
        detailEntity2.setUnitFee(new BigDecimal("2.00"));

        detailEntities.add(detailEntity1);
        detailEntities.add(detailEntity2);

        // 设置mapper字段
        ReflectionTestUtils.setField(feeConfigInboundService, "mapper", feeConfigInboundMapper);
    }

    /**
     * 测试正常情况下的insertByParam方法
     */
    @Test
    public void testInsertByParam_Success() {
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class);
             MockedStatic<SectionHelper> sectionHelperMockedStatic = Mockito.mockStatic(SectionHelper.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(FeeConfigInboundConverter.class)).thenReturn(feeConfigInboundConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_INBOUND)).thenReturn("FEE-CONFIG-INBOUND-123456");

            // 不做任何操作，只是验证调用
            sectionHelperMockedStatic.when(() -> SectionHelper.checkDetailList(createParam.getDetailList())).then(invocation -> null);

            // 设置转换器的行为
            when(feeConfigInboundConverter.toEntity(createParam)).thenReturn(feeConfigInbound);

            // 设置mapper的行为
            when(feeConfigInboundMapper.insert(any(FeeConfigInbound.class))).thenReturn(1);

            // 设置详情服务的行为
            when(feeConfigInboundDetailService.initFeeConfigInboundDetail(eq(createParam.getDetailList()), any(Consumer.class))).thenReturn(detailEntities);
            when(feeConfigInboundDetailService.insertBatch(detailEntities)).thenReturn(detailEntities.size());

            // 执行测试方法
            Long result = feeConfigInboundService.insertByParam(createParam);

            // 验证结果
            assertEquals(123456L, result);
            assertEquals(123456L, feeConfigInbound.getId());
            assertEquals("FEE-CONFIG-INBOUND-123456", feeConfigInbound.getRefNum());

            // 验证方法调用
            verify(feeConfigInboundConverter, times(1)).toEntity(createParam);
            verify(feeConfigInboundMapper, times(1)).insert(feeConfigInbound);
            verify(feeConfigInboundDetailService, times(1)).initFeeConfigInboundDetail(eq(createParam.getDetailList()), any(Consumer.class));
            verify(feeConfigInboundDetailService, times(1)).insertBatch(detailEntities);

            // 验证静态方法调用
            sectionHelperMockedStatic.verify(() -> SectionHelper.checkDetailList(createParam.getDetailList()), times(1));
        }
    }

    /**
     * 测试参数为空的情况
     */
    @Test
    public void testInsertByParam_NullParam() {
        // 执行测试方法并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            feeConfigInboundService.insertByParam(null);
        });

        // 验证异常消息
        assertEquals(ErrorMessages.PARAMETER_EMPTY, exception.getMessage());

        // 验证方法未被调用
        verify(feeConfigInboundConverter, never()).toEntity(any(FeeConfigInboundCreateParam.class));
        verify(feeConfigInboundMapper, never()).insert(any());
        verify(feeConfigInboundDetailService, never()).initFeeConfigInboundDetail(any(List.class), any(Consumer.class));
        verify(feeConfigInboundDetailService, never()).insertBatch(any());
    }

    /**
     * 测试详情列表验证失败的情况
     */
    @Test
    public void testInsertByParam_InvalidDetailList() {
        // 模拟静态方法
        try (MockedStatic<SectionHelper> sectionHelperMockedStatic = Mockito.mockStatic(SectionHelper.class)) {
            // 设置静态方法抛出异常
            sectionHelperMockedStatic.when(() -> SectionHelper.checkDetailList(createParam.getDetailList()))
                    .thenThrow(new BusinessException("detailList validation failed"));

            // 执行测试方法并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                feeConfigInboundService.insertByParam(createParam);
            });

            // 验证异常消息
            assertEquals("detailList validation failed", exception.getMessage());

            // 验证方法未被调用
            verify(feeConfigInboundConverter, never()).toEntity(any(FeeConfigInboundCreateParam.class));

            verify(feeConfigInboundMapper, never()).insert(any());

            verify(feeConfigInboundDetailService, never()).initFeeConfigInboundDetail(any(List.class), any(Consumer.class));

            verify(feeConfigInboundDetailService, never()).insertBatch(any());
        }
    }

    /**
     * 测试转换器返回空实体的情况
     */
    @Test
    public void testInsertByParam_NullEntity() {
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<SectionHelper> sectionHelperMockedStatic = Mockito.mockStatic(SectionHelper.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(FeeConfigInboundConverter.class)).thenReturn(feeConfigInboundConverter);
            sectionHelperMockedStatic.when(() -> SectionHelper.checkDetailList(createParam.getDetailList())).then(invocation -> null);

            // 设置转换器返回null
            when(feeConfigInboundConverter.toEntity(createParam)).thenReturn(null);

            // 执行测试方法并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                feeConfigInboundService.insertByParam(createParam);
            });

            // 验证异常消息
            assertEquals("FeeConfigInbound cannot be empty", exception.getMessage());

            // 验证方法调用
            verify(feeConfigInboundConverter, times(1)).toEntity(createParam);
            verify(feeConfigInboundMapper, never()).insert(any());
            verify(feeConfigInboundDetailService, never()).initFeeConfigInboundDetail(any(List.class), any(Consumer.class));
            verify(feeConfigInboundDetailService, never()).insertBatch(any());
        }
    }

    /**
     * 测试详情服务初始化失败的情况
     */
    @Test
    public void testInsertByParam_DetailInitFailed() {
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class);
             MockedStatic<SectionHelper> sectionHelperMockedStatic = Mockito.mockStatic(SectionHelper.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(FeeConfigInboundConverter.class)).thenReturn(feeConfigInboundConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_INBOUND)).thenReturn("FEE-CONFIG-INBOUND-123456");
            sectionHelperMockedStatic.when(() -> SectionHelper.checkDetailList(createParam.getDetailList())).then(invocation -> null);

            // 设置转换器的行为
            when(feeConfigInboundConverter.toEntity(createParam)).thenReturn(feeConfigInbound);

            // 设置mapper的行为
            when(feeConfigInboundMapper.insert(any(FeeConfigInbound.class))).thenReturn(1);

            // 设置详情服务抛出异常
            when(feeConfigInboundDetailService.initFeeConfigInboundDetail(eq(createParam.getDetailList()), any(Consumer.class)))
                    .thenThrow(new BusinessException("Detail initialization failed"));

            // 执行测试方法并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                feeConfigInboundService.insertByParam(createParam);
            });

            // 验证异常消息
            assertEquals("Detail initialization failed", exception.getMessage());

            // 验证方法调用
            verify(feeConfigInboundConverter, times(1)).toEntity(createParam);
            verify(feeConfigInboundMapper, times(1)).insert(feeConfigInbound);
            verify(feeConfigInboundDetailService, times(1)).initFeeConfigInboundDetail(eq(createParam.getDetailList()), any(Consumer.class));
            verify(feeConfigInboundDetailService, never()).insertBatch(any());
        }
    }
}
