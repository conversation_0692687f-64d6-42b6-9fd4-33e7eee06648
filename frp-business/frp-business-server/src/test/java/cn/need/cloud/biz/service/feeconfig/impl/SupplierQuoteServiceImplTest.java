package cn.need.cloud.biz.service.feeconfig.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.SupplierQuoteConverter;
import cn.need.cloud.biz.mapper.feeconfig.SupplierQuoteMapper;
import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierQuoteCreateParam;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.feeconfig.SupplierService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 供应商-仓库报价 service 实现类测试
 */
@ExtendWith(MockitoExtension.class)
public class SupplierQuoteServiceImplTest {

    @InjectMocks
    private SupplierQuoteServiceImpl supplierQuoteService;

    @Mock
    private SupplierQuoteMapper supplierQuoteMapper;

    @Mock
    private QuoteService quoteService;

    @Mock
    private SupplierService supplierService;

    @Mock
    private SupplierQuoteConverter supplierQuoteConverter;

    private SupplierQuoteCreateParam createParam;
    private SupplierQuote supplierQuote;
    private List<SupplierQuote> existingSupplierQuotes;

    @BeforeEach
    public void setUp() {
        // 准备测试数据
        createParam = new SupplierQuoteCreateParam();
        createParam.setActiveFlag(true);
        createParam.setName("Test Supplier Quote");
        createParam.setNote("Test note");
        createParam.setQuoteId(1L);
        createParam.setSupplierId(2L);

        // 设置时间范围
        LocalDateTime now = LocalDateTime.now();
        createParam.setStartTime(now);
        createParam.setEndTime(now.plusDays(30));

        // 准备实体对象
        supplierQuote = new SupplierQuote();
        supplierQuote.setActiveFlag(true);
        supplierQuote.setName("Test Supplier Quote");
        supplierQuote.setNote("Test note");
        supplierQuote.setQuoteId(1L);
        supplierQuote.setSupplierId(2L);
        supplierQuote.setStartTime(now);
        supplierQuote.setEndTime(now.plusDays(30));

        // 准备现有的供应商报价列表
        existingSupplierQuotes = new ArrayList<>();

        // 设置mapper字段
        ReflectionTestUtils.setField(supplierQuoteService, "mapper", supplierQuoteMapper);

        // 使用spy来模拟方法
        supplierQuoteService = Mockito.spy(supplierQuoteService);
    }

    /**
     * 测试正常情况下的insertByParam方法
     */
    @Test
    public void testInsertByParam_Success() {
        // 模拟查询现有供应商报价的行为
        doReturn(existingSupplierQuotes).when(supplierQuoteService).listSupplierQuotesBySupplierIdQuoteId(any(), any());
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(SupplierQuoteConverter.class)).thenReturn(supplierQuoteConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_SUPPLIER_QUOTE)).thenReturn("FEE-SUPPLIER-QUOTE-123456");

            // 设置转换器的行为
            when(supplierQuoteConverter.toEntity(createParam)).thenReturn(supplierQuote);

            // 设置mapper的行为
            when(supplierQuoteMapper.insert(any(SupplierQuote.class))).thenReturn(1);

            // 模拟查询现有供应商报价的行为已在测试方法开始设置

            // 执行测试方法
            Long result = supplierQuoteService.insertByParam(createParam);

            // 验证结果
            assertEquals(123456L, result);
            assertEquals(123456L, supplierQuote.getId());
            assertEquals("FEE-SUPPLIER-QUOTE-123456", supplierQuote.getRefNum());

            // 验证方法调用
            verify(supplierQuoteConverter, times(1)).toEntity(createParam);
            verify(supplierQuoteMapper, times(1)).insert(supplierQuote);
            verify(supplierQuoteService, times(1)).listSupplierQuotesBySupplierIdQuoteId(any(), any());
        }
    }

    /**
     * 测试参数为空的情况
     */
    @Test
    public void testInsertByParam_NullParam() {
        // 执行测试方法并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            supplierQuoteService.insertByParam(null);
        });

        // 验证异常消息
        assertEquals(ErrorMessages.PARAMETER_EMPTY, exception.getMessage());

        // 验证方法未被调用
        verify(supplierQuoteConverter, never()).toEntity(any(SupplierQuoteCreateParam.class));
        verify(supplierQuoteMapper, never()).insert(any());
        verify(supplierQuoteService, never()).listSupplierQuotesBySupplierIdQuoteId(any(), any());
    }

    /**
     * 测试结束时间早于开始时间的情况
     */
    @Test
    public void testInsertByParam_EndTimeBeforeStartTime() {
        // 设置结束时间早于开始时间
        createParam.setEndTime(createParam.getStartTime().minusDays(1));

        // 执行测试方法并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            supplierQuoteService.insertByParam(createParam);
        });

        // 验证异常消息
        assertEquals("End time cannot be less than the start time", exception.getMessage());

        // 验证方法未被调用
        verify(supplierQuoteConverter, never()).toEntity(any(SupplierQuoteCreateParam.class));
        verify(supplierQuoteMapper, never()).insert(any());
        verify(supplierQuoteService, never()).listSupplierQuotesBySupplierIdQuoteId(any(), any());
    }

    /**
     * 测试转换器返回空实体的情况
     */
    @Test
    public void testInsertByParam_NullEntity() {
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class)) {
            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(SupplierQuoteConverter.class)).thenReturn(supplierQuoteConverter);

            // 设置转换器返回null
            when(supplierQuoteConverter.toEntity(createParam)).thenReturn(null);

            // 执行测试方法并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                supplierQuoteService.insertByParam(createParam);
            });

            // 验证异常消息
            assertEquals("SupplierQuote cannot be empty", exception.getMessage());

            // 验证方法调用
            verify(supplierQuoteConverter, times(1)).toEntity(createParam);
            verify(supplierQuoteMapper, never()).insert(any());
            verify(supplierQuoteService, never()).listSupplierQuotesBySupplierIdQuoteId(any(), any());
        }
    }

    /**
     * 测试时间重叠检查失败的情况
     */
    @Test
    public void testInsertByParam_OverlappingTimes() {
        // 创建一个现有的供应商报价，其时间范围与新报价重叠
        SupplierQuote existingQuote = new SupplierQuote();
        existingQuote.setId(999L);
        existingQuote.setActiveFlag(true);
        existingQuote.setStartTime(createParam.getStartTime().minusDays(10));
        existingQuote.setEndTime(createParam.getStartTime().plusDays(10));
        existingQuote.setRefNum("EXISTING-QUOTE-999");
        existingSupplierQuotes.add(existingQuote);

        // 模拟查询现有供应商报价的行为
        doReturn(existingSupplierQuotes).when(supplierQuoteService).listSupplierQuotesBySupplierIdQuoteId(any(), any());
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(SupplierQuoteConverter.class)).thenReturn(supplierQuoteConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_SUPPLIER_QUOTE)).thenReturn("FEE-SUPPLIER-QUOTE-123456");

            // 设置转换器的行为
            when(supplierQuoteConverter.toEntity(createParam)).thenReturn(supplierQuote);

            // 模拟查询现有供应商报价的行为已在测试方法开始设置

            // 执行测试方法并验证异常
            BusinessException exception = assertThrows(BusinessException.class, () -> {
                supplierQuoteService.insertByParam(createParam);
            });

            // 验证方法调用
            verify(supplierQuoteConverter, times(1)).toEntity(createParam);
            verify(supplierQuoteService, times(1)).listSupplierQuotesBySupplierIdQuoteId(any(), any());
            verify(supplierQuoteMapper, never()).insert(any());
        }
    }

    /**
     * 测试插入失败的情况
     */
    @Test
    public void testInsertByParam_InsertFailed() {
        // 模拟查询现有供应商报价的行为
        doReturn(existingSupplierQuotes).when(supplierQuoteService).listSupplierQuotesBySupplierIdQuoteId(any(), any());
        // 模拟静态方法
        try (MockedStatic<Converters> convertersMockedStatic = Mockito.mockStatic(Converters.class);
             MockedStatic<IdWorker> idWorkerMockedStatic = Mockito.mockStatic(IdWorker.class);
             MockedStatic<FormatUtil> formatUtilMockedStatic = Mockito.mockStatic(FormatUtil.class)) {

            // 设置静态方法的行为
            convertersMockedStatic.when(() -> Converters.get(SupplierQuoteConverter.class)).thenReturn(supplierQuoteConverter);
            idWorkerMockedStatic.when(IdWorker::getId).thenReturn(123456L);
            formatUtilMockedStatic.when(() -> FormatUtil.generateRefNum(RefNumTypeEnum.FEE_SUPPLIER_QUOTE)).thenReturn("FEE-SUPPLIER-QUOTE-123456");

            // 设置转换器的行为
            when(supplierQuoteConverter.toEntity(createParam)).thenReturn(supplierQuote);

            // 模拟查询现有供应商报价的行为已在测试方法开始设置

            // 设置mapper的行为 - 插入失败
            when(supplierQuoteMapper.insert(any(SupplierQuote.class))).thenReturn(0);

            // 执行测试方法
            Long result = supplierQuoteService.insertByParam(createParam);

            // 验证结果
            assertEquals(123456L, result);
            assertEquals(123456L, supplierQuote.getId());
            assertEquals("FEE-SUPPLIER-QUOTE-123456", supplierQuote.getRefNum());

            // 验证方法调用
            verify(supplierQuoteConverter, times(1)).toEntity(createParam);
            verify(supplierQuoteMapper, times(1)).insert(supplierQuote);
            verify(supplierQuoteService, times(1)).listSupplierQuotesBySupplierIdQuoteId(any(), any());
        }
    }
}
