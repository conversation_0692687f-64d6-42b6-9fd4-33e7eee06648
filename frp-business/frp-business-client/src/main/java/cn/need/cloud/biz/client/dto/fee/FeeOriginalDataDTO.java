package cn.need.cloud.biz.client.dto.fee;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * 费用原始数据表 dto对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeOriginalDataDTO extends SuperDTO {


    @Serial
    private static final long serialVersionUID = -8183734190565128865L;
    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 额外数据(Json)
     */
    private String extraData;
    
    /**
     * 计费类型
     */
    private String feeModelType;

    /**
     * 备注
     */
    private String note;

    /**
     * 开始处理时间
     */
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    private LocalDateTime processEndTime;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 唯一标识码
     */
    private String snapshotRefNum;

    /**
     * 请求id
     */
    private Long snapshotRequestId;

    /**
     * 外部唯一标识码
     */
    private String snapshotRequestRefNum;

    /**
     * 交易伙伴id
     */
    private Long transactionPartnerId;

    /**
     * 仓库id
     */
    private Long warehouseId;

}