package cn.need.cloud.biz.client.constant.enums.feeconfig;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计费条件类型（决定区间判断的依据）
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FeeModelTypeEnum {

    /**
     * inbound
     */
    INBOUND("Inbound"),

    /**
     * outbound
     */
    OTC("Otc"),

    /**
     * outbound
     */
    OTB("Otb"),

    /**
     * STORAGE
     */
    STORAGE("Storage");

    @EnumValue
    @JsonValue
    private final String code;


}

