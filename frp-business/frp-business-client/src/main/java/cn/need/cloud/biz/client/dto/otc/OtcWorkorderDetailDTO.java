package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * OTC工单详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcWorkorderDetailDTO extends SuperDTO {


    @Serial
    private static final long serialVersionUID = -3940592710945651735L;
    /**
     * 库存锁定id
     */
    private Long inventoryLockedId;

    /**
     * 打包为Package 数量
     */
    private Integer packedQty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 发货到c端工单id
     */
    private Long otcWorkorderId;

    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 备注
     */
    private String note;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * finishQty
     */
    private Integer finishQty;

    /**
     * 拣货数量
     */
    private Integer pickedQty;

    /**
     * readyToShipQty
     */
    private Integer readyToShipQty;

    /**
     * 库存预定id
     */
    private Long inventoryReserveId;

    /**
     * 预定数量
     */
    private Integer reserveQty;

}