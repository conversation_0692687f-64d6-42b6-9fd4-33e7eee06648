package cn.need.cloud.biz.client.dto.otb;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTB托盘详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtbPalletDetailDTO extends SuperDTO {


    /**
     * 数量
     */
    private Integer qty;

    /**
     * 渠道要求的需要贴的产品标识码
     */
    private String productBarcode;

    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * otb托盘id
     */
    private Long otbPalletId;

}