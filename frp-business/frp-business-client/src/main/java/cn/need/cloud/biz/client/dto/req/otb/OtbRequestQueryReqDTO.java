package cn.need.cloud.biz.client.dto.req.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseWithTransactionPartnerReqDTO;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * OTB请求 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTB请求 query对象")
public class OtbRequestQueryReqDTO extends BaseWarehouseWithTransactionPartnerReqDTO {

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"requestRefNum"})
    private Set<String> requestRefNumList;

    /**
     * 渠道
     */
    @Schema(description = "渠道集合")
    @Condition(value = Keyword.IN, fields = {"channel"})
    private List<String> channelList;

    /**
     * 发货窗口开始时间
     */
    @Schema(description = "发货窗口开始时间")
    private LocalDateTime shipWindowStart;
    /**
     * 发货窗口开始时间开始
     */
    @Schema(description = "发货窗口开始时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime shipWindowStartStart;
    /**
     * 发货窗口开始时间结束
     */
    @Schema(description = "发货窗口开始时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime shipWindowStartEnd;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态集合")
    @Condition(value = Keyword.IN, fields = {"otbRequestStatus"})
    private List<String> otbRequestStatusList;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    @Condition(value = Keyword.IN, fields = {"refNum"})
    private Set<String> refNumList;

    /**
     * 请求发货状态
     */
    @Schema(description = "请求发货状态集合")
    @Condition(value = Keyword.IN, fields = {"requestShipmentStatus"})
    private List<String> requestShipmentStatusList;

    /**
     * 发货窗口结束时间
     */
    @Schema(description = "发货窗口结束时间")
    private LocalDateTime shipWindowEnd;
    /**
     * 发货窗口结束时间开始
     */
    @Schema(description = "发货窗口结束时间开始")
    @Condition(Keyword.GE)
    private LocalDateTime shipWindowEndStart;
    /**
     * 发货窗口结束时间结束
     */
    @Schema(description = "发货窗口结束时间结束")
    @Condition(Keyword.LE)
    private LocalDateTime shipWindowEndEnd;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    @Condition(value = Keyword.IN, fields = {"feeStatus"})
    private List<String> feeStatusList;
}