package cn.need.cloud.biz.client.dto.req.base.info;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Schema(description = "id请求vo对象")
@Data
public class IdReqDTO implements Serializable {

    @Schema(description = "行序号")
    @NotNull(message = "lineNum cannot be null")
    private Integer lineNum;

    @Schema(description = "参考编码")
    @NotNull(message = "refNum cannot be null")
    private String refNum;

    @JsonIgnore
    private Long id;
}
