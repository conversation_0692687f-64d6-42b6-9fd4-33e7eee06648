package cn.need.cloud.biz.client.dto.resp.otb;

import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OTB请求 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTB请求 vo对象")
public class OtbRequestRespDTO implements Serializable {


    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;

    /**
     * 发货窗口开始时间
     */
    @Schema(description = "发货窗口开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipWindowStart;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String otbRequestStatus;

    @Schema(description = "基础仓库信息")
    private BaseWarehouseDTO baseWarehouseDTO;


    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    @Schema(description = "交易伙伴信息")
    private BasePartnerDTO transactionPartnerDTO;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 请求发货状态
     */
    @Schema(description = "请求发货状态")
    private String requestShipmentStatus;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNum;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;


    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 发货窗口结束时间
     */
    @Schema(description = "发货窗口结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipWindowEnd;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;


    /**
     * 发货人姓名
     */
    @Schema(description = "发货人姓名")
    private String shipFromAddressName;

    /**
     * 发货备注
     */
    @Schema(description = "发货备注")
    private String shipFromAddressNote;

    /**
     * 发货电话
     */
    @Schema(description = "发货电话")
    private String shipFromAddressPhone;

    /**
     * 发货州
     */
    @Schema(description = "发货州")
    private String shipFromAddressState;

    /**
     * 发货邮编
     */
    @Schema(description = "发货邮编")
    private String shipFromAddressZipCode;

    @Schema(description = "订单详情")
    private List<OtbRequestDetailRespDTO> detailList;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    @Schema(description = "处理完成时间")
    private LocalDateTime processEndTime;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;
}