package cn.need.cloud.biz.client.dto.resp.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseFullProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = " Otb请求detail参数")
public class OtbRequestDetailRespDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    @Schema(description = "产品条码")
    private String productBarcode;

    @Schema(description = "产品渠道SKU")
    private String productChannelSku;

    @Schema(description = "数量")
    private Integer qty;

    @Schema(description = "行号")
    @NotNull(message = "lineNum must not be null.")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

    @Schema(description = "产品对象")
    private BaseFullProductDTO baseFullProductDTO;

}
