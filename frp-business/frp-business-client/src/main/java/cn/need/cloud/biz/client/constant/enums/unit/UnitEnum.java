package cn.need.cloud.biz.client.constant.enums.unit;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/***
 *
 * 单位
 * <AUTHOR>
 * @since 2024-11-15
 */
@Getter
@AllArgsConstructor
public enum UnitEnum {
    /**
     * 长度单位
     */
    IN("IN"),
    /**
     * 重量单位
     */
    LB("LB"),
    ;

    @EnumValue
    @JsonValue
    private final String type;
}
