package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * Otc预拣货单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtcPrepPickingSlipStatusEnum {

    /**
     * 新建
     */
    NEW("New"),
    /**
     * 拣货中
     */
    IN_PICKING("InPicking"),
    /**
     * 拣货完成
     */
    PICKED("Picked"),
    /**
     * 上架
     */
    PUT_AWAY("PutAway"),
    /**
     * 锁定
     */
    LOCKED("Locked"),
    /**
     * 取消
     */
    CANCELLED("Cancelled"),
    /**
     * 挂起
     */
    ON_HOLD("OnHold");
    @EnumValue
    @JsonValue
    private final String status;

    /**
     * 是否可以拣货的状态
     *
     * @param status 状态
     * @return /
     */
    public static boolean canPickProcessStatus(String status) {
        final List<String> canProcessStatus = Arrays.asList(
                OtcPrepPickingSlipStatusEnum.NEW.getStatus(),
                OtcPrepPickingSlipStatusEnum.IN_PICKING.getStatus()
        );
        return canProcessStatus.contains(status);
    }
}

