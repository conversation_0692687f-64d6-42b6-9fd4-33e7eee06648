package cn.need.cloud.biz.client.dto.req.otc;

import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC请求详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "OTC请求产品信息 dto对象")
public class OtcRequestProductRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    @Schema(description = "数量")
    private Integer qty;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "行号")
    private Integer lineNum;

    @Schema(description = "产品信息")
    private BaseProductDTO baseProductDTO;

}