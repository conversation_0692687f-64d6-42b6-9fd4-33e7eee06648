package cn.need.cloud.biz.client.dto.req.base;


import cn.need.cloud.biz.client.dto.req.base.info.RequestReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "Request审批 dto对象")
@EqualsAndHashCode(callSuper = true)
public class BaseRequestAuditReqDTO extends BaseLogisticAndTransactionPartnerAndWarehouseReqDTO {

    @Serial
    private static final long serialVersionUID = 9035405377524141358L;

    @Schema(description = "Request请求dto对象")
    private RequestReqDTO requestReqDTO;

    @Schema(description = "原因")
    private String note;

    @Schema(description = "审核类型")
    private String type;

    @JsonIgnore
    public Long getRequestId() {
        if (ObjectUtil.isNotEmpty(requestReqDTO)) {
            return requestReqDTO.getRequestId();
        }
        return null;
    }
}
