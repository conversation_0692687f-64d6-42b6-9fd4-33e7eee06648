package cn.need.cloud.biz.client.dto.otb;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * otb发货指南托盘标签 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtbRoutingInstructionPackageLabelDTO extends SuperDTO {


    /**
     * 路由指令ID
     */
    private Long otbRoutingInstructionId;

    /**
     * 包裹SSCC编号
     */
    private String packageSsccNum;

    /**
     * 面单类型
     */
    private String labelType;

    /**
     * label RefNum
     */
    private String labelRefNum;

    /**
     * 纸张类型
     */
    private String paperType;

    /**
     * 数据类型
     */
    private String rawDataType;

    /**
     * 文件系统数据类型
     */
    private String fileIdRawDataType;

    /**
     * label数据类型
     */
    private String labelRawData;

    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

}