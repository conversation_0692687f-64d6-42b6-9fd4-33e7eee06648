package cn.need.cloud.biz.client.dto.req.base;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.req.base.info.ProductVersionReqDTO;
import cn.need.cloud.biz.client.dto.req.binlocation.BinLocationInfoDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "库位 产品 基础信息")
public class BaseProductVersionAndBinLocationRespDTO implements Serializable {

    @Schema(description = "产品版本")
    private ProductVersionReqDTO productVersion;

    @NotNull(message = "binLocation can not null")
    @Schema(description = "库位信息")
    @NotEmptyOfAllField(message = "binLocation can not null", columns = {"refNum", "locationName"})
    private BinLocationInfoDTO binLocationInfoDTO;

    @JsonIgnore
    public Long getProductVersionId() {
        if (ObjectUtil.isNotEmpty(productVersion)) {
            return productVersion.getProductVersionId();
        }
        return null;
    }

    @JsonIgnore
    public Long getBinLocationId() {
        if (binLocationInfoDTO != null) {
            return binLocationInfoDTO.getBinLocationId();
        }
        return null;
    }
}
