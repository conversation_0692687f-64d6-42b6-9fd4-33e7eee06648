package cn.need.cloud.biz.client.dto.otb;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * otb拣货单 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtbPickingSlipDTO extends SuperDTO {


    /**
     * 分配人
     */
    private Long assignedUserId;

    /**
     * 拣货到那里
     */
    private String pickToStation;

    /**
     * 备注
     */
    private String note;

    /**
     * otb 拣货单状态
     */
    private String otbPickingSlipStatus;

    /**
     * 打印状态
     */
    private String printStatus;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * otb工单id
     */
    private Long otbWorkorderId;

}