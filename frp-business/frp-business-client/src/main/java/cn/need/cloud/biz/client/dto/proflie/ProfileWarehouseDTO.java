package cn.need.cloud.biz.client.dto.proflie;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 仓库档案 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProfileWarehouseDTO extends SuperDTO {


    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 分类代码
     */
    private String categoryCode;

    /**
     * 分类描述
     */
    private String categoryDesc;

    /**
     * 值类型
     */
    private String valueType;

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 备注
     */
    private String note;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 激活标志
     */
    private Boolean activeFlag;

    /**
     * 代码
     */
    private String code;

}