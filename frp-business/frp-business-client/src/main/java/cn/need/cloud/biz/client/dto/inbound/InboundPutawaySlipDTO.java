package cn.need.cloud.biz.client.dto.inbound;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 上架 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InboundPutawaySlipDTO extends SuperDTO {


    /**
     * 备注
     */
    private String note;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 打印状态
     */
    private String printStatus;

    /**
     * 上架状态
     */
    private String inboundPutawaySlipStatus;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

}