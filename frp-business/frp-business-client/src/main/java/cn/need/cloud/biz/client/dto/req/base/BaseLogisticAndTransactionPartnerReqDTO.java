package cn.need.cloud.biz.client.dto.req.base;

import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * logisticPartner对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
public class BaseLogisticAndTransactionPartnerReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * 物流伙伴
     */
    @NotNull(message = "logisticPartner can not null")
    private TenantReqDTO logisticPartner;

    /**
     * 交易伙伴
     */
    @NotNull(message = "transactionPartner can not null")
    private TenantReqDTO transactionPartner;

    @JsonIgnore
    public Long getLogisticPartnerId() {
        if (logisticPartner != null) {
            return logisticPartner.getTenantId();
        }
        return null;
    }

    @JsonIgnore
    public Long getTransactionPartnerId() {
        if (transactionPartner != null) {
            return transactionPartner.getTenantId();
        }
        return null;
    }
}
