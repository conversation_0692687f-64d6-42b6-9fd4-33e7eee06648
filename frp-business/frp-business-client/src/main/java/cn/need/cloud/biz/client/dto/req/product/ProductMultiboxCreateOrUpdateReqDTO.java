package cn.need.cloud.biz.client.dto.req.product;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseLogisticAndTransactionPartnerReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductWithLogisticAndTransactionPartnerReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.util.List;


/**
 * 产品多箱 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品多箱 新建参数")
public class ProductMultiboxCreateOrUpdateReqDTO extends BaseProductWithLogisticAndTransactionPartnerReqDTO {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    /**
     * 多箱详情
     */
    @Schema(description = "多箱详情")
    @Valid
    @NotNull(message = "multiboxReqList can not null")
    private List<ProductMultiboxReqDTO> multiboxList;
}