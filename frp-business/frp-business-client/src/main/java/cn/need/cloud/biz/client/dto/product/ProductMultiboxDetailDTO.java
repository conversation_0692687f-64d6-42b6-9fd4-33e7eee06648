package cn.need.cloud.biz.client.dto.product;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 产品多箱详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductMultiboxDetailDTO extends SuperDTO {


    @Serial
    private static final long serialVersionUID = 5207737356027609789L;
    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 产品多箱ID
     */
    private Long productMultiboxId;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 产品id
     */
    private Long productId;

}