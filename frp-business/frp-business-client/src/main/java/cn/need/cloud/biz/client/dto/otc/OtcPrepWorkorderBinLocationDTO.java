package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC预提工单仓储位置 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcPrepWorkorderBinLocationDTO extends SuperDTO {


    /**
     * 工单id
     */
    private Long otcWorkorderId;

    /**
     * 工单详情id
     */
    private Long otcWorkorderDetailId;

    /**
     * 预工单id
     */
    private Long otcPrepWorkorderId;

    /**
     * 预工单详情id
     */
    private Long otcPrepWorkorderDetailId;

    /**
     * 预拣货id
     */
    private Long otcPrepPickingSlipId;

    /**
     * 预拣货详情id
     */
    private Long otcPrepPickingSlipDetailId;

    /**
     * 库位详情id
     */
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    private Long binLocationId;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

}