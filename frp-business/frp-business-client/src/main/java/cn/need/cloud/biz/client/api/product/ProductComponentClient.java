package cn.need.cloud.biz.client.api.product;

import cn.need.cloud.biz.client.api.path.ProductComponentPath;
import cn.need.cloud.biz.client.api.path.ProductPath;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductComponentCreateOrUpdateReqDTO;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 产品组合管理 Feign 客户端
 * <p>
 * 该接口提供产品组合相关的远程调用功能，包括产品组合的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@FeignClient(value = "${serviceId.frp-business}", path = ProductComponentPath.PREFIX,contextId = "ProductComponentClient")
public interface ProductComponentClient {
    /**
     * 新增产品
     * <p>
     * 创建一个新的产品记录
     * </p>
     *
     * @param createOrUpdateReqDTO 产品数据传输对象，包含产品的详细信息
     * @return @IgnoreAuth
     * Result<Long> 包含新创建产品ID的结果对象
     */
    @PostMapping(value = ProductComponentPath.CREATE_UPDATE)
    @IgnoreAuth
    Result<Integer> createOrUpdate(@RequestBody ProductComponentCreateOrUpdateReqDTO createOrUpdateReqDTO);

    /**
     * 删除产品
     * <p>
     * 根据提供的产品信息删除产品记录
     * </p>
     *
     * @param baseDeleteReqDTO 包含要删除产品信息的数据传输对象
     * @return @IgnoreAuth
     * Result<Integer> 包含删除影响行数的结果对象
     */
    @PostMapping(value = ProductComponentPath.DELETE)
    @IgnoreAuth
    Result<Integer> remove(@RequestBody BaseDeleteReqDTO baseDeleteReqDTO);

}
