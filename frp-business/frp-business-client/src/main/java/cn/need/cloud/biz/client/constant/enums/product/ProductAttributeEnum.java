package cn.need.cloud.biz.client.constant.enums.product;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品组类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProductAttributeEnum {
    /**
     * 没有
     */
    PRODUCT("Product"),

    /**
     * 从
     */
    MULTIBOX("Multibox");
    @EnumValue
    @JsonValue
    private final String type;
}
