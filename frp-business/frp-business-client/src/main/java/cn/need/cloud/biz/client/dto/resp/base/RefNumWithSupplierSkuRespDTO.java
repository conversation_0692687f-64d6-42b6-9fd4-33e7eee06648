package cn.need.cloud.biz.client.dto.resp.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * <p>
 * 产品参考编号与供应商SKU响应DTO
 * </p>
 *
 * <p>
 * 该类用于返回产品创建或更新操作的结果，包含系统内部生成的唯一标识码和供应商SKU编号。
 * 主要用于外部系统与内部系统之间的产品数据映射和跟踪。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
public class RefNumWithSupplierSkuRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5999118679518902314L;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * supplierSku
     */
    @Schema(description = "supplierSku")
    private String supplierSku;
}