package cn.need.cloud.biz.client.api.otb;

import cn.need.cloud.biz.client.api.path.OtbShipmentPath;
import cn.need.cloud.biz.client.api.path.ProductPath;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbShipmentPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbShipmentRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.RequestProductRespDTO;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * otb入库请求管理 Feign 客户端
 * <p>
 * 该接口提供otbRequest相关的远程调用功能，包括入库请求的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@FeignClient(value = "${serviceId.frp-business}", path = OtbShipmentPath.PREFIX,contextId = "OtbShipmentClient")
public interface OtbShipmentClient {

    @PostMapping(value = OtbShipmentPath.LIST)
    @IgnoreAuth
    Result<PageData<OtbShipmentPageRespDTO>> list(@RequestBody PageSearch<OtbShipmentQueryReqDTO> search);

    @PostMapping(value = OtbShipmentPath.DETAIL)
    @IgnoreAuth
    Result<OtbShipmentRespDTO> detail(@RequestBody OtbShipmentDetailQueryReqDTO query);

    @PostMapping(value = OtbShipmentPath.REQUEST_ACTUAL_PRODUCT)
    @IgnoreAuth
    Result<List<RequestProductRespDTO>> requestActualProduct(@RequestBody BaseDetailQueryReqDTO query);
}
