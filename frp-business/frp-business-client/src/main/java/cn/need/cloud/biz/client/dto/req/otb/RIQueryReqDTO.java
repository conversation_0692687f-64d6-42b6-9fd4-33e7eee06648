package cn.need.cloud.biz.client.dto.req.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseWithTransactionPartnerReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "otb发货指南 vo对象")
public class RIQueryReqDTO extends BaseWarehouseWithTransactionPartnerReqDTO {

    @NotNull(message = "reqDTO cannot be empty")
    @Schema(description = "otb发货指南 dto对象")
    private RIReqDTO reqDTO;

    @JsonIgnore
    public Long getRiId() {
        if (ObjectUtil.isNotEmpty(reqDTO)) {
            return reqDTO.getRiId();
        }
        return null;
    }
}
