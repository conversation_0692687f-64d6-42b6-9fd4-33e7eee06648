package cn.need.cloud.biz.client.dto.req.binlocation;

import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;


/**
 * 库位 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库位 query对象")
public class BinLocationQueryReqDTO extends BaseWarehouseReqDTO {

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private String locationName;

    /**
     * 库位名称
     */
    @Schema(description = "库位名称")
    private Set<String> locationNameList;

    /**
     * 库位名称模糊查
     */
    @Schema(description = "库位名称模糊查")
    private Set<String> locationNameLikeList;

    /**
     * 是否有效 1：有效，0：无效
     */
    @Schema(description = "是否有效 1：有效，0：无效")
    private Boolean activeFlag;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码集合")
    private Set<String> refNumList;

    /**
     * 库位类型（用户可修改）
     */
    @Schema(description = "库位类型（用户可修改）集合")
    private Set<String> binTypeList;

    /**
     * 仓库分区
     */
    @Schema(description = "仓库分区集合")
    private Set<String> warehouseZoneTypeList;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    private Boolean defaultFlag;

    /**
     * upcList
     */
    @Schema(description = "upcList")
    private Set<String> upcList;

    /**
     * 供应商sku
     */
    @Schema(description = "供应商sku集合")
    private Set<String> supplierSkuList;

    /**
     * 产品refNum
     */
    @Schema(description = "产品refNum集合")
    private Set<String> productRefNumList;

    /**
     * 库存数量
     */
    @Schema(description = "库存数量")
    private Integer inStockQty;

}