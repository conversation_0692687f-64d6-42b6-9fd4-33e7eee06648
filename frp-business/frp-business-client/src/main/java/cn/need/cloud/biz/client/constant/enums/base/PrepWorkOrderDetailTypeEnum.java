package cn.need.cloud.biz.client.constant.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * Otc预拣货单详情类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrepWorkOrderDetailTypeEnum {

    /**
     * None,代表不需要做什么
     */
    NONE("None"),

    /**
     * PrepConvert
     */
    PREPCONVERT("PrepConvert");


    @EnumValue
    @JsonValue
    private final String status;

}

