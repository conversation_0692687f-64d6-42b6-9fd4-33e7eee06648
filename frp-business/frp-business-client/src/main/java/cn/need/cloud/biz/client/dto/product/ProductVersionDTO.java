package cn.need.cloud.biz.client.dto.product;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 产品版本详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductVersionDTO extends SuperDTO {


    /**
     * 产品id
     */
    private Long productId;

    /**
     * 供应商SKU
     */
    private String supplierSku;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * UPC码
     */
    private String upc;

    /**
     * 产品标题
     */
    private String title;

    /**
     * 净长度
     */
    private BigDecimal netLength;

    /**
     * 净宽度
     */
    private BigDecimal netWidth;

    /**
     * 净高度
     */
    private BigDecimal netHeight;

    /**
     * 净重量
     */
    private BigDecimal netWeight;

    /**
     * 发货长度
     */
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    private BigDecimal shipWeight;

    /**
     * 纸箱长度
     */
    private BigDecimal cartonLength;

    /**
     * 纸箱宽度
     */
    private BigDecimal cartonWidth;

    /**
     * 纸箱高度
     */
    private BigDecimal cartonHeight;

    /**
     * 纸箱重量
     */
    private BigDecimal cartonWeight;

    /**
     * 每箱数量
     */
    private Integer pcsPerCarton;

    /**
     * 产品版本号
     */
    private Integer productVersionInt;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 纸箱尺寸单位
     */
    private String cartonDimensionUnit;

    /**
     * 纸箱重量单位
     */
    private String cartonWeightUnit;

    /**
     * 净尺寸单位
     */
    private String netDimensionUnit;

    /**
     * 净重量单位
     */
    private String netWeightUnit;

    /**
     * 发货尺寸单位
     */
    private String shipDimensionUnit;

    /**
     * 发货重量单位
     */
    private String shipWeightUnit;

    /**
     * 交易伙伴ID
     */
    private Long transactionPartnerId;

    /**
     * 产品重新测量类型
     */
    private String productRemeasureType;

}