package cn.need.cloud.biz.client.constant;

/**
 * Redis Key常量
 */
public class RedisConstant {

    //region 基础信息
    /**
     * 仓库信息key
     */
    public static final String WAREHOUSE = "WAREHOUSE";

    /**
     * Token
     */
    public static final String OPEN_TOKEN = "OPEN:TOKEN";
    //endregion

    //region 入库相关
    /**
     * 入库工单
     */
    public static final String INBOUND_WORK_ORDER = "INBOUND:WORKORDER";

    /**
     * 入库请求审核锁前缀
     */
    public static final String INBOUND_REQUEST_AUDIT_LOCK_PREFIX = "INBOUND:REQUEST:AUDIT:";
    //endregion

    //region 库位相关
    /**
     * 库位详情新增锁前缀
     */
    public static final String BIN_LOCATION_DETAIL_CREATE_LOCK_PREFIX = "BINLOCATION:DETAIL:CREATE:";

    /**
     * 库位详情锁新增锁前缀
     */
    public static final String BIN_LOCATION_DETAIL_LOCKED_CREATE_LOCK_PREFIX = "BINLOCATION:DETAIL:LOCKED:CREATE:";
    //endregion

    //region 包裹相关
    /**
     * 包裹触发ReadyToShip分布式锁
     */
    public static final String PACKAGE_READY_TO_SHIP_LOCKED = "PACKAGE:READYTOSHIP:LOCKED:";
    //endregion

    //region OTC相关
    /**
     * OTC请求拒绝锁前缀
     */
    public static final String OTC_REQUEST_REJECT_LOCK_PREFIX = "OTC:REQUEST:REJECT:";

    /**
     * OTC请求审核通过锁前缀
     */
    public static final String OTC_REQUEST_APPROVE_LOCK_PREFIX = "OTC:REQUEST:APPROVE:";

    /**
     * OTC工单创建锁前缀
     */
    public static final String OTC_WORK_ORDER_CREATE_LOCK_PREFIX = "OTC:WORKORDER:CREATE:";

    /**
     * OTC上架创建锁前缀
     */
    public static final String OTC_PUTAWAY_SLIP_CREATE_LOCK_PREFIX = "OTC:PUTAWAY:SLIP:CREATE:";
    //endregion

    //region OTB相关
    /**
     * OTB工单审核锁前缀
     */
    public static final String OTB_REQUEST_AUDIT_LOCK_PREFIX = "OTB:REQUEST:AUDIT:";
    //endregion

    //region 费用原始数据相关
    /**
     * 费用原始数据InBound创建前缀
     */
    public static final String FEE_ORIGINAL_DATA_INBOUND_CREATE_PREFIX = "FEE:ORIGINAL:DATA:INBOUND:CREATE:";

    /**
     * 费用原始数据OTB创建前缀
     */
    public static final String FEE_ORIGINAL_DATA_OTB_CREATE_PREFIX = "FEE:ORIGINAL:DATA:OTB:CREATE:";

    /**
     * 费用原始数据OTC创建前缀
     */
    public static final String FEE_ORIGINAL_DATA_OTC_CREATE_PREFIX = "FEE:ORIGINAL:DATA:OTC:CREATE:";

    /**
     * 费用原始数据Storage创建前缀
     */
    public static final String FEE_ORIGINAL_DATA_STORAGE_CREATE_PREFIX = "FEE:ORIGINAL:DATA:STORAGE:CREATE:";
    //endregion
}
