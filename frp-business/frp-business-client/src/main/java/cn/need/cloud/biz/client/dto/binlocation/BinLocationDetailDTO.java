package cn.need.cloud.biz.client.dto.binlocation;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 库位详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BinLocationDetailDTO extends SuperDTO {


    /**
     * inStockQty
     */
    private Integer inStockQty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品版本id
     */
    private Long productVersionId;

    /**
     * 库位id
     */
    private Long binLocationId;

}