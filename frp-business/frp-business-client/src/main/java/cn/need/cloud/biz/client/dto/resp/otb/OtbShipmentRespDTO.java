package cn.need.cloud.biz.client.dto.resp.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * OTB装运 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTB装运 vo对象")
public class OtbShipmentRespDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * otb请求refNum
     */
    @Schema(description = "otb请求refNum")
    private String otbRequestOfRefNum;

    /**
     * otb请求单请求refNum
     */
    @Schema(description = "otb请求单请求refNum")
    private String otbRequestOfRequestRefNum;

    /**
     * otb工单RefNum
     */
    @Schema(description = "otb工单RefNum")
    private String otbWorkorderRefNum;

    /**
     * otb 装运状态
     */
    @Schema(description = "otb 装运状态")
    private String otbShipmentStatus;

    /**
     * otb 装运类型
     */
    @Schema(description = "otb 装运类型")
    private String otbShipmentType;

    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Long otbPickingSlipId;

    /**
     * otb拣货单refNum
     */
    @Schema(description = "otb拣货单refNum")
    private String otbPickingSlipRefNum;

    /**
     * 仓库信息
     */
    @Schema(description = "仓库信息")
    private BaseWarehouseDTO baseWarehouseDTO;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 提货单编号
     */
    @Schema(description = "提货单编号")
    private String bolNum;

    /**
     * 打印状态
     */
    @Schema(description = "打印状态")
    private String printStatus;

    /**
     * 签名的Bol文件数据
     */
    @Schema(description = "签名的Bol文件数据")
    private String otbSignedBolFileFileData;

    /**
     * 签名的Bol文件拓展名
     */
    @Schema(description = "签名的Bol文件拓展名")
    private String otbSignedBolFileFileExtension;

    /**
     * 签名的Bol文件类型
     */
    @Schema(description = "签名的Bol文件类型")
    private String otbSignedBolFileFileType;

    /**
     * 包裹总数
     */
    @Schema(description = "包裹总数")
    private Integer packageCount;

    /**
     * 托盘总数
     */
    @Schema(description = "托盘总数")
    private Integer palletCount;

    /**
     * 重量单位
     */
    @Schema(description = "重量单位")
    private String totalWeightUnit;

    /**
     * 重量
     */
    @Schema(description = "重量")
    private BigDecimal totalWeightValue;

    /**
     * 体积单位
     */
    @Schema(description = "体积单位")
    private String volumeUnit;

    /**
     * 体积
     */
    @Schema(description = "体积")
    private BigDecimal volumeValue;

    /**
     * 签名的Bol文件纸张类型
     */
    @Schema(description = "签名的Bol文件纸张类型")
    private String otbSignedBolFilePaperType;

    /**
     * 托盘文件状态
     */
    @Schema(description = "托盘文件状态")
    private String palletFileStatus;

    /**
     * 发货单明细
     */
    @Schema(description = "发货单明细")
    private List<OtbShipmentDetailRespDTO> otbShipmentDetailList;

    /**
     * 包裹集合
     */
    @Schema(description = "包裹集合")
    private List<OtbPackageRespDTO> otbPackageList;

    /**
     * 打托单集合
     */
    @Schema(description = "打托单集合")
    private List<OtbPalletRespDTO> otbPalletList;

    @Schema(description = "RI")
    private List<OtbRoutingInstructionRespDTO> routingInstructionList;

}