package cn.need.cloud.biz.client.dto.product;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.req.base.BaseLogisticAndTransactionPartnerReqDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serial;


/**
 * 产品 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductUpdateV1DTO extends BaseLogisticAndTransactionPartnerReqDTO {

    @Serial
    private static final long serialVersionUID = -5049118626542518606L;
    /**
     * 产品信息
     */
    @NotNull(message = "product can not null")
    @NotEmptyOfAllField(message = "refNum and supplierSku cannot both be null.", columns = {"refNum", "supplierSku"})
    private ProductReqDTO product;

    /**
     * 备注
     */
    private String note;

    /**
     * 描述
     */
    private String description;

    /**
     * 标题
     */
    private String title;


}