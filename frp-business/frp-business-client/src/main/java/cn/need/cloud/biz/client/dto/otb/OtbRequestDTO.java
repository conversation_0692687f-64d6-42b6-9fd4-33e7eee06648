package cn.need.cloud.biz.client.dto.otb;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * OTB请求 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtbRequestDTO extends SuperDTO {


    /**
     * 外部唯一标识码
     */
    private String requestRefNum;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 发货窗口开始时间
     */
    private LocalDateTime shipWindowStart;

    /**
     * 请求状态
     */
    private String otbRequestStatus;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 交易伙伴Id
     */
    private Long transactionPartnerId;

    /**
     * 备注
     */
    private String note;

    /**
     * 请求发货状态
     */
    private String requestShipmentStatus;

    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 收货地址1
     */
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    private String shipToAddressEmail;

    /**
     * 收货地址是否为住宅
     */
    private Boolean shipToAddressIsResidential;

    /**
     * 收货地址名称
     */
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    private String shipToAddressZipCode;

    /**
     * 发货窗口结束时间
     */
    private LocalDateTime shipWindowEnd;

    /**
     * 发货地址1
     */
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    private String shipFromAddressEmail;

    /**
     * 是否住宅地址
     */
    private Boolean shipFromAddressIsResidential;

    /**
     * 发货人姓名
     */
    private String shipFromAddressName;

    /**
     * 发货备注
     */
    private String shipFromAddressNote;

    /**
     * 发货电话
     */
    private String shipFromAddressPhone;

    /**
     * 发货州
     */
    private String shipFromAddressState;

    /**
     * 发货邮编
     */
    private String shipFromAddressZipCode;

}