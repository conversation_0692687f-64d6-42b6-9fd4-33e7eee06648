package cn.need.cloud.biz.client.constant.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/***
 * 谁拣货拣货单枚举
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@AllArgsConstructor
@Getter
public enum PickFromType {

    /**
     * 未拣货
     */
    NONE("None"),

    /**
     * 正常库存上拣货
     */
    NORMAL("Normal"),

    /**
     * 都是从 Prep库位上拣货
     */
    PREP("Prep"),

    /**
     * 都有
     */
    BOTH("Both"),
    ;

    @EnumValue
    @JsonValue
    private final String type;
}
