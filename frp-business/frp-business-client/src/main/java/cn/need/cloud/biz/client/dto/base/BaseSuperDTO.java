package cn.need.cloud.biz.client.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class BaseSuperDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -8939858807419302139L;


    /**
     * 创建人
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 最后更新人
     */
    @Schema()
    private String updateByName;

    /**
     * 创建时间
     */
    @Schema
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    @Schema
    private LocalDateTime updateTime;
}
