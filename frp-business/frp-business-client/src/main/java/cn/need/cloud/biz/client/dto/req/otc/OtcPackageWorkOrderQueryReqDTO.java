package cn.need.cloud.biz.client.dto.req.otc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * OTC包裹 query对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "OTC包裹 工单query对象")
public class OtcPackageWorkOrderQueryReqDTO {

    /**
     * 请求RequestRefNum
     */
    @Schema(description = "请求RefNum集合")
    private List<String> requestRefNumList;

    /**
     * 请求渠道
     */
    @Schema(description = "请求渠道集合")
    private List<String> requestChannelList;

    /**
     * 请求RequestRefNum
     */
    @Schema(description = "请求RequestRefNum集合")
    private List<String> requestRequestRefNumList;


}