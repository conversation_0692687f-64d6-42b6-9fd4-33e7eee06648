package cn.need.cloud.biz.client.dto.feeconfig;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 仓库报价费用配置inbound dto对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeConfigInboundDTO extends SuperDTO {


    /**
     * 是否有效(0-无效，1-有效)
     */
    private Boolean activeFlag;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    private String conditionType;

    /**
     * 货币代码（如USD、CNY）
     */
    private String currency;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 额外计费判断字段
     */
    private String extraFeeJudgeFields;

    /**
     * 费用计费类型(detail得出的结果，是否要乘condition_type得出的值)
     */
    private String feeCalculationType;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String note;

    /**
     * 仓库报价id
     */
    private Long quoteId;

    /**
     * 仓库id
     */
    private Long warehouseId;

}