package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * Otc预拣货单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtcWorkOrderPrepStatusEnum {

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 无
     */
    NONE("None"),

    /**
     * 正在处理
     */
    PROCESSING("Processing"),

    /**
     * 处理完成
     */
    PROCESSED("Processed");
    @EnumValue
    @JsonValue
    private final String status;

}

