package cn.need.cloud.biz.client.dto.inventory;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryLockedDTO extends SuperDTO {


    /**
     * 关联表Id
     */
    private Long refTableId;

    /**
     * refName
     */
    private String refTableName;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * finishQty
     */
    private Integer finishQty;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 唯一标识码
     */
    private String refTableRefNum;

    /**
     * 库存锁定状态 不能直接使用该字段做Where etc
     */
    private String lockedStatus;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * showRefName
     */
    private String refTableShowName;

    /**
     * showRefNum
     */
    private String refTableShowRefNum;

    /**
     * 备注
     */
    private String note;

}