package cn.need.cloud.biz.client.dto.resp.inbound;

import cn.need.cloud.biz.client.dto.req.binlocation.BinLocationInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "入库上架单库位信息vo对象")
public class InboundPutAwaySlipBinLocationRespDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "上架数量")
    private Integer qty;

    @NotNull(message = "binLocation can not null")
    @Schema(description = "库位信息")
    private BinLocationInfoDTO binLocationInfoDTO;
}
