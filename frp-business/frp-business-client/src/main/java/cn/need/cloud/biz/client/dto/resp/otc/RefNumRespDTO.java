package cn.need.cloud.biz.client.dto.resp.otc;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC预提货单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
public class RefNumRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5999118679518902314L;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

}