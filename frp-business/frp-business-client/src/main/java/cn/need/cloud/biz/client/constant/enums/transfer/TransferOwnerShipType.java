package cn.need.cloud.biz.client.constant.enums.transfer;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/***
 * 货权转移
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Getter
@AllArgsConstructor
public enum TransferOwnerShipType {

    /**
     * 货权转移
     */
    TRANSFER_OWNERSHIP("TransferOwnership"),

    /**
     * 重新打标签
     */
    RE_LABEL("ReLabel"),
    ;

    @EnumValue
    @JsonValue
    private final String type;

    public static TransferOwnerShipType typeOf(String type) {
        return Arrays.stream(TransferOwnerShipType.values())
                .filter(obj -> Objects.equals(obj.getType(), type))
                .findAny()
                .orElse(null);
    }
}
