package cn.need.cloud.biz.client.constant.enums.transfer;

import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AuditResultType
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@AllArgsConstructor
@Getter
public enum AuditResultType {

    /**
     * 拒绝
     */
    REJECT("Reject"),

    /**
     * 同意
     */
    AGREE("Agree"),
    ;

    @EnumValue
    @JsonValue
    private final String type;

    public static AuditResultType getEnum(String type) {
        switch (type) {
            case "Reject":
                return REJECT;
            case "Agree":
                return AGREE;
            default:
                throw new BusinessException(StringUtil.format("current type {} is not supported", type));
        }
    }

}
