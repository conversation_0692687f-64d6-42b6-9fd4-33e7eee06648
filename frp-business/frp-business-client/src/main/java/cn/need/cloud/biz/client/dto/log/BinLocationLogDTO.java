package cn.need.cloud.biz.client.dto.log;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * query对象
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Data
public class BinLocationLogDTO implements Serializable {

    /**
     * refTableId
     */
    private Long refTableId;

    /**
     * refTableName
     */
    private String refTableName;

    /**
     * refTableRefNum
     */
    private String refTableRefNum;

    /**
     * refTableRefNum
     */
    private List<String> refTableRefNumList;

    /**
     * refTableShowName
     */
    private String refTableShowName;

    /**
     * refTableShowRefNum
     */
    private String refTableShowRefNum;

    /**
     * refTableShowRefNum
     */
    private List<String> refTableShowRefNumList;

    /**
     * sourceBinLocationId
     */
    private Long sourceBinLocationId;

    /**
     * sourceBinLocationDetailId
     */
    private Long sourceBinLocationDetailId;

    /**
     * sourceChangeInStockQty
     */
    private Integer sourceChangeInStockQty;

    /**
     * sourceBeforeInStockQty
     */
    private Integer sourceBeforeInStockQty;

    /**
     * sourceAfterInStockQty
     */
    private Integer sourceAfterInStockQty;

    /**
     * destBinLocationId
     */
    private Long destBinLocationId;

    /**
     * destBinLocationDetailId
     */
    private Long destBinLocationDetailId;

    /**
     * destChangeInStockQty
     */
    private Integer destChangeInStockQty;

    /**
     * destBeforeInStockQty
     */
    private Integer destBeforeInStockQty;

    /**
     * destAfterInStockQty
     */
    private Integer destAfterInStockQty;

    /**
     * note
     */
    private String note;

    /**
     * deletedNote
     */
    private String deletedNote;

    /**
     * tenantId
     */
    private Long tenantId;

    /**
     * warehouseId
     */
    private Long warehouseId;

    /**
     * productId
     */
    private Long productId;

    /**
     * 版本产品id
     */
    private Long productVersionId;

}