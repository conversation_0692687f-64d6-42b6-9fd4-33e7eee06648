package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单Filter Build拣货单策略
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@AllArgsConstructor
public enum OtcBuildPickingSlipStrategyEnum {

    /**
     * 默认选项
     * 当前所选订单
     * 一票多件的订单 会单独生成一个 拣货单，出货的时候，需要根据 Request 来出货
     * 前提：一票一件的 只有相同的 Ship Station 才会有生成到一个拣货单上的机会
     * 一票一件的 没有特殊要求，HasCusShipRequire = false 相同产品的订单，会合并生成到一个拣货单上，（一个拣货单上可以有多个产品）（这种可以闭眼出的意思）
     * 一票一件的 特殊情况， HasCusShipRequire = false, 相同产品的订单，也会合并生成到一个拣货单上，（一个拣货单上可以有多个产品）
     */
    Default("Default");
    @EnumValue
    @JsonValue
    private final String status;

}
