package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Otb包裹label纸张类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PackageLabelTypeEnum {

    SSCC("SSCC"),

    PALLET_SSCC("PalletSSCC"),

    SHIPPING_LABEL("ShippingLabel"),
    ;

    @EnumValue
    @JsonValue
    private final String type;
}
