package cn.need.cloud.biz.client.constant.enums.inbound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库打托
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InboundPalletEnum {

    /**
     * 没有
     */
    NEW("New"),

    /**
     * 上架
     */
    PUT_AWAY("PutAway"),

    /**
     * 取消
     */
    CANCELLED("Cancelled"),

    ;

    @EnumValue
    @JsonValue
    private final String status;
}
