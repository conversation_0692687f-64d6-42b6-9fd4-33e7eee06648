package cn.need.cloud.biz.client.dto.req.base;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.req.binlocation.BinLocationInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库位信息")
public class BaseBinLocationDTO extends BaseWarehouseReqDTO {
    @Serial
    private static final long serialVersionUID = 8628352349152808902L;

    @NotNull(message = "binLocation can not null")
    @Schema(description = "库位信息")
    @NotEmptyOfAllField(message = "binLocation can not null", columns = {"refNum", "locationName"})
    private BinLocationInfoDTO binLocationInfoDTO;

    @JsonIgnore
    public Long getBinLocationId() {
        if (binLocationInfoDTO != null) {
            return binLocationInfoDTO.getBinLocationId();
        }
        return null;
    }
}
