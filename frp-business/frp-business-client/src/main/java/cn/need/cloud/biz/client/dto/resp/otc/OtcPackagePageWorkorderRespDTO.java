package cn.need.cloud.biz.client.dto.resp.otc;

import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * OTC工单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "包裹OTC工单 vo对象")
public class OtcPackagePageWorkorderRespDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = 9073097547297445695L;
    /**
     * request快照RefNum
     */
    @Schema(description = "request快照RefNum")
    private String requestSnapshotRefNum;

    /**
     * 请求快照RequestRefnum
     */
    @Schema(description = "请求快照RequestRefnum")
    private String requestSnapshotRequestRefNum;

    /**
     * 发货到c端工单状态
     */
    @Schema(description = "发货到c端工单状态, workorderStatus")
    private String otcWorkorderStatus;

    // TODO detailProductType

    /**
     * 请求快照订单类型
     */
    @Schema(description = "请求快照订单类型, orderType")
    private String requestSnapshotOrderType;

    /**
     * request快照渠道
     */
    @Schema(description = "request快照渠道, channel")
    private String requestSnapshotChannel;

    /**
     * 拣货到那里
     */
    @Schema(description = "拣货到那里")
    private String pickToStation;


    @Schema(description = "供应商")
    private BasePartnerDTO transactionPartner;

}