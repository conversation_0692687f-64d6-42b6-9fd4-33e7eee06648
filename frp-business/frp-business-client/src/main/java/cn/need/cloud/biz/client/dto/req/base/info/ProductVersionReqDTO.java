package cn.need.cloud.biz.client.dto.req.base.info;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseTransactionPartnerReqDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductVersionReqDTO extends BaseTransactionPartnerReqDTO {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    @NotNull(message = "product can not null")
    @Schema(description = "产品信息")
    @NotEmptyOfAllField(message = "refNum and supplierSku cannot both be null.", columns = {"refNum", "supplierSku"})
    private ProductReqDTO product;

    @Schema(description = "版本参考编码")
    @NotNull(message = "versionRefNum can not null")
    private String versionRefNum;

    @JsonIgnore
    private Long productVersionId;

    @JsonIgnore
    private Long productId;

    @JsonIgnore
    public String getRefNum() {
        if (product != null) {
            return product.getRefNum();
        }
        return null;
    }

    @JsonIgnore
    public String getSupplierSku() {
        if (product != null) {
            return product.getSupplierSku();
        }
        return null;
    }

}
