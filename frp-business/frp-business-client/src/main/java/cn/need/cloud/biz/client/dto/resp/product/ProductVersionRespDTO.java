package cn.need.cloud.biz.client.dto.resp.product;

import cn.need.cloud.biz.client.dto.base.BaseTransactionPartnerDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 产品 vo对象
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品尺寸对象")
public class ProductVersionRespDTO extends BaseTransactionPartnerDTO {


    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 产品标题
     */
    @Schema(description = "产品标题")
    private String title;

    /**
     * 净长度
     */
    @Schema(description = "净长度")
    private BigDecimal netLength;

    /**
     * 净宽度
     */
    @Schema(description = "净宽度")
    private BigDecimal netWidth;

    /**
     * 净高度
     */
    @Schema(description = "净高度")
    private BigDecimal netHeight;

    /**
     * 净重量
     */
    @Schema(description = "净重量")
    private BigDecimal netWeight;

    /**
     * 发货长度
     */
    @Schema(description = "发货长度")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @Schema(description = "发货宽度")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @Schema(description = "发货高度")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @Schema(description = "发货重量")
    private BigDecimal shipWeight;

    /**
     * 纸箱长度
     */
    @Schema(description = "纸箱长度")
    private BigDecimal cartonLength;

    /**
     * 纸箱宽度
     */
    @Schema(description = "纸箱宽度")
    private BigDecimal cartonWidth;

    /**
     * 纸箱高度
     */
    @Schema(description = "纸箱高度")
    private BigDecimal cartonHeight;

    /**
     * 纸箱重量
     */
    @Schema(description = "纸箱重量")
    private BigDecimal cartonWeight;

    /**
     * 每箱数量
     */
    @Schema(description = "每箱数量")
    private Integer pcsPerCarton;

    /**
     * 产品版本号
     */
    @Schema(description = "产品版本号")
    private Integer productVersionInt;

    /**
     * 纸箱尺寸单位
     */
    @Schema(description = "纸箱尺寸单位")
    private String cartonDimensionUnit;

    /**
     * 纸箱重量单位
     */
    @Schema(description = "纸箱重量单位")
    private String cartonWeightUnit;

    /**
     * 净尺寸单位
     */
    @Schema(description = "净尺寸单位")
    private String netDimensionUnit;

    /**
     * 净重量单位
     */
    @Schema(description = "净重量单位")
    private String netWeightUnit;

    /**
     * 发货尺寸单位
     */
    @Schema(description = "发货尺寸单位")
    private String shipDimensionUnit;

    /**
     * 发货重量单位
     */
    @Schema(description = "发货重量单位")
    private String shipWeightUnit;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 产品重新测量类型
     */
    @Schema(description = "产品重新测量类型")
    private String productRemeasureType;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;


}