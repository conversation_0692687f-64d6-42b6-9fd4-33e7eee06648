package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC包裹详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcPackageDetailDTO extends SuperDTO {


    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * c端出货打包id
     */
    private Long otcPackageId;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 拣货数量
     */
    private Integer pickedQty;

}