package cn.need.cloud.biz.client.dto.resp.inbound;

import cn.need.cloud.biz.client.dto.base.BaseProductVersionDTO;
import cn.need.cloud.biz.client.dto.base.BaseSuperDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "入库卸货查询vo对象")
public class InboundUnloadRespDTO extends BaseSuperDTO {

    @Schema(description = "入库上架详情id")
    private Long id;

    @Schema(description = "上架数量")
    private Integer qty;

    @Schema(description = "产品版本信息")
    private BaseProductVersionDTO baseProductVersionDTO;

    @Schema(description = "仓库信息")
    private BaseWarehouseDTO baseWarehouseDTO;

}
