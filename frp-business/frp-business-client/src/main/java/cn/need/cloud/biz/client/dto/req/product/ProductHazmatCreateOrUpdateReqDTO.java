package cn.need.cloud.biz.client.dto.req.product;

import cn.need.cloud.biz.client.dto.req.base.BaseProductWithLogisticAndTransactionPartnerReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serial;


/**
 * CreateOrUpdateReq对象
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = " CreateOrUpdateReq对象")
public class ProductHazmatCreateOrUpdateReqDTO extends BaseProductWithLogisticAndTransactionPartnerReqDTO {

    @Serial
    private static final long serialVersionUID = 6130969844449607262L;
    /**
     * packageInstruction
     */
    @Schema(description = "packageInstruction")
    @NotEmpty(message = "packageInstruction cannot be empty")
    @Size(max = 255, message = "packageInstruction cannot exceed 255 characters")
    private String packageInstruction;
    /**
     * transportationRegulatoryClass
     */
    @Schema(description = "transportationRegulatoryClass")
    @NotEmpty(message = "transportationRegulatoryClass cannot be empty")
    @Size(max = 255, message = "transportationRegulatoryClass cannot exceed 255 characters")
    private String transportationRegulatoryClass;
    /**
     * unRegulatoryId
     */
    @Schema(description = "unRegulatoryId")
    @NotEmpty(message = "unRegulatoryId cannot be empty")
    @Size(max = 255, message = "unRegulatoryId cannot exceed 255 characters")
    private String unRegulatoryId;

}