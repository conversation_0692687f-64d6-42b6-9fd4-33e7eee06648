package cn.need.cloud.biz.client.dto.resp.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * OTB包裹 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "OTB包裹 vo对象")
public class OtbPackageRespDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2L;

    /**
     * Serial Shipping Container Code，序列化货运容器代码
     */
    @Schema(description = "Serial Shipping Container Code，序列化货运容器代码")
    private String ssccNum;

    /**
     * 入库箱子-长
     */
    @Schema(description = "入库箱子-长")
    private BigDecimal cartonSizeLength;

    /**
     * 入库箱子-宽
     */
    @Schema(description = "入库箱子-宽")
    private BigDecimal cartonSizeWidth;

    /**
     * 入库箱子-高
     */
    @Schema(description = "入库箱子-高")
    private BigDecimal cartonSizeHeight;

    /**
     * 入库箱子-重量
     */
    @Schema(description = "入库箱子-重量")
    private BigDecimal cartonSizeWeight;

    /**
     * 入库箱子-重量单位
     */
    @Schema(description = "入库箱子-重量单位")
    private String cartonSizeWeightUnit;

    /**
     * 入库箱子-长度单位
     */
    @Schema(description = "入库箱子-长度单位")
    private String cartonSizeDimensionUnit;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * otb请求refNum
     */
    @Schema(description = "otb请求refNum")
    private String otbRequestOfRefNum;

    /**
     * otb请求requestRefNum
     */
    @Schema(description = "otb请求requestRefNum")
    private String otbRequestOfRequestRefNum;

    /**
     * otb工单refNum
     */
    @Schema(description = "otb工单refNum")
    private String otbWorkorderRefNum;

    /**
     * 仓库基础信息
     */
    @Schema(description = "仓库基础信息")
    private BaseWarehouseDTO baseWarehouseDTO;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * otb托盘id
     */
    @Schema(description = "otb托盘id")
    private String otbPalletRefNum;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNum;

    /**
     * otb包裹状态
     */
    @Schema(description = "otb包裹状态")
    private String otbPackageStatus;

    /**
     * otb包裹类型
     */
    @Schema(description = "otb包裹类型")
    private String otbPackageType;

    /**
     * otb托盘类型
     */
    @Schema(description = "otb托盘类型")
    private String otbPalletType;

    /**
     * otb拣货单id
     */
    @Schema(description = "otb拣货单id")
    private Long otbPickingSlipId;

    /**
     * otb拣货单refNum
     */
    @Schema(description = "otb拣货单refNum")
    private String otbPickingSlipRefNum;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    private String shipFromAddressAddr1;

    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    private String shipFromAddressAddr2;

    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    private String shipFromAddressAddr3;

    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    private String shipFromAddressCity;

    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    private String shipFromAddressCompany;

    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    private String shipFromAddressCountry;

    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    private String shipFromAddressEmail;

    /**
     * 发货地址是否为住宅
     */
    @Schema(description = "发货地址是否为住宅")
    private Boolean shipFromAddressIsResidential;

    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    private String shipFromAddressName;

    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    private String shipFromAddressNote;

    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    private String shipFromAddressPhone;

    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    private String shipFromAddressState;

    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    private String shipFromAddressZipCode;

    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    private String shipToAddressAddr1;

    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    private String shipToAddressAddr2;

    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    private String shipToAddressAddr3;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String shipToAddressCity;

    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    private String shipToAddressCompany;

    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    private String shipToAddressCountry;

    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    private String shipToAddressEmail;

    /**
     * 收货地址是否为住宅
     */
    @Schema(description = "收货地址是否为住宅")
    private Boolean shipToAddressIsResidential;

    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    private String shipToAddressName;

    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    private String shipToAddressNote;

    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    private String shipToAddressPhone;

    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    private String shipToAddressState;

    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    private String shipToAddressZipCode;

    /**
     * 短ssccnum
     */
    @Schema(description = "短ssccnum")
    private String shortSsccNum;

    /**
     * 发货站
     */
    @Schema(description = "发货站")
    private String station;

    /**
     * 运输公司
     */
    @Schema(description = "运输公司")
    private String shipCarrier;

    /**
     * 运输方式
     */
    @Schema(description = "运输方式")
    private String shipMethod;

    /**
     * 快递Api配置RefNum
     */
    @Schema(description = "快递Api配置RefNum")
    private String shipApiProfileRefNum;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

}