package cn.need.cloud.biz.client.dto.req.base;


import cn.need.cloud.biz.client.dto.req.base.info.RequestReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "Request审批 dto对象")
@EqualsAndHashCode(callSuper = true)
public class BaseRequestBatchAuditReqDTO extends BaseLogisticAndTransactionPartnerAndWarehouseReqDTO {

    @Serial
    private static final long serialVersionUID = 5894453671417247318L;

    @Schema(description = "Request请求dto对象")
    private List<RequestReqDTO> requestReqList;

    @Schema(description = "删除原因")
    private String note;

    @Schema(description = "审核类型")
    private String type;

    @JsonIgnore
    public List<Long> getRequestIdList() {
        if (ObjectUtil.isNotEmpty(requestReqList)) {
            return requestReqList.stream()
                    .map(RequestReqDTO::getRequestId)
                    .distinct()
                    .toList();
        }
        return null;
    }
}
