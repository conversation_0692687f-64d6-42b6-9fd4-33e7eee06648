package cn.need.cloud.biz.client.constant.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
@AllArgsConstructor
public enum ReLabelStatus {

    /**
     * 不需要多贴标签
     */
    NONE("None"),

    /**
     * 需要盖住UPC标签
     */
    NEW("New"),

    /**
     * 已经 Relabel
     */
    PRINTED("Printed");

    @EnumValue
    @JsonValue
    private final String status;
}
