package cn.need.cloud.biz.client.dto.req.base;

import cn.need.cloud.biz.client.dto.req.base.info.ProductVersionReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class BaseProductVersionReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    @Schema(description = "产品版本")
    @NotNull(message = "productVersion cannot be null")
    private ProductVersionReqDTO productVersion;

    @JsonIgnore
    public Long getProductVersionId() {
        if (ObjectUtil.isNotEmpty(productVersion)) {
            return productVersion.getProductVersionId();
        }
        return null;
    }

    public Long getProductId() {
        if (ObjectUtil.isNotEmpty(productVersion)) {
            return productVersion.getProductId();
        }
        return null;
    }
}
