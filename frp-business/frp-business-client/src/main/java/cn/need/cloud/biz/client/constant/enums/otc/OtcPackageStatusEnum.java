package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Otc包裹状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtcPackageStatusEnum {

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 拣货中
     */
    IN_PICKING("InPicking"),

    /**
     * 已拣货
     */
    PICKED("Picked"),

    /**
     * 准备发货
     */
    READY_TO_SHIP("ReadyToShip"),

    /**
     * 已发货
     */
    SHIPPED("Shipped"),

    /**
     * 已取消
     */
    CANCELLED("Cancelled"),

    /**
     * 暂停
     */
    ON_HOLD("OnHold");
    @EnumValue
    @JsonValue
    private final String status;


    public static List<String> canCancelStatus() {
        return List.of(NEW.getStatus(), IN_PICKING.getStatus(), PICKED.getStatus());
    }
}

