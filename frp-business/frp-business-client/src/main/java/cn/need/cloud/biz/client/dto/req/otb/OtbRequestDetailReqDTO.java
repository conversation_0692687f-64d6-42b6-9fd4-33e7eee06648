package cn.need.cloud.biz.client.dto.req.otb;

import cn.need.cloud.biz.client.dto.req.base.BaseProductReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = " Otb请求detail参数")
public class OtbRequestDetailReqDTO extends BaseProductReqDTO {
    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    @Schema(description = "产品条码")
    @NotNull(message = "productBarcode must not be null.")
    private String productBarcode;

    @Schema(description = "产品渠道SKU")
    @NotNull(message = "productChannelSku must not be null.")
    private String productChannelSku;

    @Schema(description = "数量")
    @NotNull(message = "qty must not be null.")
    @Min(value = 1, message = "qty can not be less than 1")
    private Integer qty;

    @Schema(description = "行号")
    @NotNull(message = "lineNum must not be null.")
    @Min(value = 1, message = "lineNum can not be less than 1")
    private Integer lineNum;

}
