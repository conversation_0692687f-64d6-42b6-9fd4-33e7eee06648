package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * Otb拣货单枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtbPickingSlipStatusEnum {
    /**
     * 新建
     */
    NEW("New"),

    /**
     * 拣货中
     */
    IN_PICKING("InPicking"),

    /**
     * 拣货完成
     */
    PICKED("Picked"),

    /**
     * 打包完成
     */
    PACKED("Packed"),

    /**
     * 取消
     */
    CANCELLED("Cancelled"),

    /**
     * OnHold
     */
    ON_HOLD("OnHold"),

    /**
     * 锁定
     */
    LOCKED("Locked"),
    ;

    @EnumValue
    @JsonValue
    private final String status;

    /**
     * 是否可以拣货的状态
     *
     * @param status 状态
     * @return /
     */
    public static boolean canPickProcessStatus(String status) {
        final List<String> canProcessStatus = Arrays.asList(
                OtbPickingSlipStatusEnum.NEW.getStatus(),
                OtbPickingSlipStatusEnum.IN_PICKING.getStatus()
        );
        return canProcessStatus.contains(status);
    }
}
