package cn.need.cloud.biz.client.api.otc;

import cn.need.cloud.biz.client.api.path.BinLocationPath;
import cn.need.cloud.biz.client.api.path.OtcRequestPath;
import cn.need.cloud.biz.client.api.path.ProductPath;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestAuditReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestBatchAuditReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otc.OtcRequestCreateOrUpdateReqDTO;
import cn.need.cloud.biz.client.dto.req.otc.OtcRequestQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.OtcRequestPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.OtcRequestRespDTO;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 入库请求管理 Feign 客户端
 * <p>
 * 该接口提供库位库存相关的远程调用功能，包括库位库存的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@FeignClient(value = "${serviceId.frp-business}", path = OtcRequestPath.PREFIX,contextId = "OtcRequestClient")
public interface OtcRequestClient {

    /**
     * 创建或更新otc请求
     * <p>
     * 创建或更新otc请求
     * </p>
     *
     * @param reqDTO otc请求数据传输对象，包含otc请求的详细信息
     * @return @IgnoreAuth
     * Result<Boolean> 包含创建或更新otc请求的结果
     */
    @PostMapping(value = OtcRequestPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(@RequestBody OtcRequestCreateOrUpdateReqDTO reqDTO);

    /**
     * 获取出库请求详情
     * <p>
     * 获取出库请求详情
     * </p>
     *
     * @param query 出库请求数据传输对象，包含出库请求的详细信息
     * @return @IgnoreAuth
     * Result<BinLocationRespDTO> 包含出库请求的结果
     */
    @PostMapping(value = BinLocationPath.DETAIL)
    @IgnoreAuth
    Result<OtcRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query);

    /**
     * 出库请求列表列表
     * <p>
     * 出库请求列表列表
     * </p>
     *
     * @param search 出库请求数据传输对象，包含出库请求的详细信息
     * @return @IgnoreAuth
     * Result<PageData<OtcRequestPageRespDTO>> 包含出库请求列表的结果
     */
    @PostMapping(value = OtcRequestPath.LIST)
    @IgnoreAuth
    Result<PageData<OtcRequestPageRespDTO>> list(@RequestBody PageSearch<OtcRequestQueryReqDTO> search);

    /**
     * 取消请求
     * <p>
     * 根据提供的请求信息取消请求记录
     * </p>
     *
     * @param baseDeleteReqDTO 包含要取消请求信息的数据传输对象
     * @return @IgnoreAuth
     * Result<Integer> 包含取消影响行数的结果对象
     */
    @PostMapping(value = OtcRequestPath.CANCEL)
    @IgnoreAuth
    Result<Integer> cancel(@RequestBody BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO);

    /**
     * 审批请求
     * <p>
     * 根据提供的请求信息审批请求记录
     * </p>
     *
     * @param baseAuditReqDTO 包含要审批请求信息的数据传输对象
     * @return @IgnoreAuth
     * Result<Integer> 包含审批影响行数的结果对象
     */
    @PostMapping(value = OtcRequestPath.AUDIT)
    @IgnoreAuth
    Result<Boolean> audit(@RequestBody BaseRequestAuditReqDTO baseAuditReqDTO);

    /**
     * 审批请求
     * <p>
     * 根据提供的请求信息审批请求记录
     * </p>
     *
     * @param baseRequestBatchAuditReqDTO 包含要审批请求信息的数据传输对象
     * @return @IgnoreAuth
     * Result<Integer> 包含审批影响行数的结果对象
     */
    @PostMapping(value = OtcRequestPath.BATCH_AUDIT)
    @IgnoreAuth
    Result<Boolean> batchAudit(@RequestBody BaseRequestBatchAuditReqDTO baseRequestBatchAuditReqDTO);
}
