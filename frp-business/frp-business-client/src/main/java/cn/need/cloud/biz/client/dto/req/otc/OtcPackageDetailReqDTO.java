package cn.need.cloud.biz.client.dto.req.otc;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.req.base.BaseLogisticPartnerAndWarehouseReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "OTC包裹详情 req对象")
public class OtcPackageDetailReqDTO extends BaseLogisticPartnerAndWarehouseReqDTO {


    @Schema(description = "包裹信息")
    @NotNull(message = "otcPackageDetailDTO cannot be null")
    @NotEmptyOfAllField(message = "refNum and trackingNum cannot be empty",columns = {"refNum", "trackingNum"})
    private OtcPackageDetailDTO otcPackageDetailDTO;

    @JsonIgnore
    public Long getPackageId(){
        if (ObjectUtil.isNotEmpty(otcPackageDetailDTO)) {
            return otcPackageDetailDTO.getPackageId();
        }
        return null;
    }
}
