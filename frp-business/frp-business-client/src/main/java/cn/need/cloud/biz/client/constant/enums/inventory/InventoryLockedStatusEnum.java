package cn.need.cloud.biz.client.constant.enums.inventory;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 库存锁定状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InventoryLockedStatusEnum {

    /**
     * 锁定
     */
    LOCKED("Locked"),

    /**
     * 部分释放
     */
    PART_RELEASE("PartRelease"),

    /**
     * 释放
     */
    RELEASE("Release");
    @EnumValue
    @JsonValue
    private final String status;
}

