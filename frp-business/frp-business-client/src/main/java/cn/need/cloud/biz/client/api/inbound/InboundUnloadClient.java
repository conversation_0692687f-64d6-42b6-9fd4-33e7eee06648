package cn.need.cloud.biz.client.api.inbound;

import cn.need.cloud.biz.client.api.path.InboundUnloadPath;
import cn.need.cloud.biz.client.api.path.ProductPath;
import cn.need.cloud.biz.client.dto.req.inbound.InboundUnloadQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundUnloadRespDTO;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 入库卸货管理 Feign 客户端
 * <p>
 * 该接口提供入库卸货相关的远程调用功能，包括入库卸货的增删改查等操作。
 * 通过OpenFeign实现微服务间的通信。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since frp-dev.25
 */
@FeignClient(value = "${serviceId.frp-business}", path = InboundUnloadPath.PREFIX,contextId = "InboundUnloadClient")
public interface InboundUnloadClient {

    /**
     * 获取指定请求单下上架信息
     * <p>
     * 获取指定请求单下上架信息
     * </p>
     *
     * @param reqDTO 卸货单数据传输对象，包含卸货单的详细信息
     * @return @IgnoreAuth
     * Result<List<InboundUnloadRespDTO>>包含卸货单的结果
     */
    @PostMapping(value = InboundUnloadPath.LIST_BY_REQUEST)
    @IgnoreAuth
    Result<List<InboundUnloadRespDTO>> listByRequest(@RequestBody InboundUnloadQueryReqDTO reqDTO);
}
