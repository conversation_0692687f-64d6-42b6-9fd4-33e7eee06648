package cn.need.cloud.biz.client.constant.enums.inbound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库测量类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InboundMeasureTypeEnum {

    /**
     * 没有
     */
    NONE("None"),

    /**
     * 重新测量
     */
    Remeasured("Remeasured");

    @EnumValue
    @JsonValue
    private final String status;
}
