package cn.need.cloud.biz.client.constant.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 上架单状态
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Getter
@AllArgsConstructor
public enum PutAwaySlipStatus {

    /**
     * 新建
     */
    NEW("New"),

    /**
     * 正在处理
     */
    PROCESSING("Processing"),

    /**
     * 处理完成
     */
    PROCESSED("Processed"),

    /**
     * 取消
     */
    CANCELLED("Cancelled"),
    ;

    @EnumValue
    @JsonValue
    private final String status;


    public static boolean canPutAway(String status) {
        return canPutAwayStatuses().contains(status);
    }

    public static List<String> canPutAwayStatuses() {
        return List.of(NEW.getStatus(), PROCESSING.getStatus());
    }
}
