package cn.need.cloud.biz.client.dto.inbound;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 入库请求 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InboundRequestDTO extends SuperDTO {


    /**
     * 运输方式类型
     */
    private String transportMethodType;

    /**
     * 外部唯一标识码
     */
    private String requestRefNum;

    /**
     * 预计到达日期
     */
    private LocalDateTime estimateArrivalDate;

    /**
     * 实际到达日期
     */
    private LocalDateTime actualArrivalDate;

    /**
     * 快递号
     */
    private String trackingNum;

    /**
     * 发件人姓名
     */
    private String fromAddressName;

    /**
     * 发件人公司
     */
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    private String fromAddressState;

    /**
     * 发件人城市
     */
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    private String fromAddressPhone;

    /**
     * 发件人备注
     */
    private String fromAddressNote;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 交易伙伴ID
     */
    private Long transactionPartnerId;

    /**
     * 请求状态
     */
    private String inboundRequestStatus;

    /**
     * 备注
     */
    private String note;

    /**
     * 容器类型
     */
    private String containerType;

    /**
     * 发件人地址是否为住宅
     */
    private Boolean fromAddressIsResidential;

}