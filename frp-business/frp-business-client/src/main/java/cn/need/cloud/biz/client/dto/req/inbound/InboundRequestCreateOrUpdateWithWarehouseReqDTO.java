package cn.need.cloud.biz.client.dto.req.inbound;

import cn.need.cloud.biz.client.constant.RegexConstant;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestWithWarehouseReqDTO;
import cn.need.framework.common.core.lang.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 入库请求 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库请求 dto对象")
public class InboundRequestCreateOrUpdateWithWarehouseReqDTO extends BaseRequestWithWarehouseReqDTO {

    @Schema(description = "请求参考号")
    @NotNull(message = "requestRefNum cannot be empty")
    @Pattern(regexp = RegexConstant.CHARACTER_REGEX, message = RegexConstant.CHARACTER_REGEX_MESSAGE)
    private String requestRefNum;
    /**
     * 运输方式类型
     */
    @Schema(description = "运输方式类型")
    @NotNull(message = "transportMethodType cannot be null")
    private String transportMethodType;
    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    @NotNull(message = "estimateArrivalDate cannot be null")
    private LocalDateTime estimateArrivalDate;
    /**
     * 快递号
     */
    @Schema(description = "快递号")
    @NotNull(message = "trackingNum cannot be null")
    private String trackingNum;
    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    @NotNull(message = "fromAddressName cannot be null")
    private String fromAddressName;
    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    @NotNull(message = "fromAddressCompany cannot be null")
    private String fromAddressCompany;
    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    @NotNull(message = "fromAddressCountry cannot be null")
    private String fromAddressCountry;
    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    @NotNull(message = "fromAddressState cannot be null")
    private String fromAddressState;
    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    @NotNull(message = "fromAddressCity cannot be null")
    private String fromAddressCity;
    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    @NotNull(message = "fromAddressZipCode cannot be null")
    private String fromAddressZipCode;
    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    @NotNull(message = "fromAddressAddr1 cannot be null")
    private String fromAddressAddr1;
    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String fromAddressAddr2;
    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String fromAddressAddr3;
    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String fromAddressEmail;
    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String fromAddressPhone;
    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String fromAddressNote;
    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean fromAddressIsResidential;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;
    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String containerType;
    /**
     * 入库请求单详情
     */
    @Schema(description = "入库请求单详情")
    private List<InboundRequestDetailReqDTO> details;

    @Override
    public String getRequestOfRequestRefNum() {
        if (ObjectUtil.isNotEmpty(super.getRequestOfRequestRefNum())) {
            return super.getRequestOfRequestRefNum();
        }
        return null;
    }

}