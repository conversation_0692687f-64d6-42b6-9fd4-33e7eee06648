package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * OTC工单 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcWorkorderDTO extends SuperDTO {


    /**
     * 发货到c端请求id
     */
    private Long otcRequestId;

    /**
     * 发货到c端拣货id
     */
    private Long otcPickingSlipId;

    /**
     * 请求快照快递运输标志
     */
    private Boolean requestSnapshotShipExpressFlag;

    /**
     * 运输方式
     */
    private String shipMethod;

    /**
     * 运输公司
     */
    private String shipCarrier;

    /**
     * 请求快照最后运输时间
     */
    private LocalDateTime requestSnapshotLastShipDate;

    /**
     * 请求快照保险金额
     */
    private BigDecimal requestSnapshotInsuranceAmountAmount;

    /**
     * 请求快照签名类型
     */
    private String requestSnapshotSignatureType;

    /**
     * 请求快照订单类型
     */
    private String requestSnapshotOrderType;

    /**
     * 请求快照是否有特定运输要求
     */
    private Boolean requestSnapshotHasCusShipRequire;

    /**
     * 请求快照运输方式
     */
    private String requestSnapshotShipMethod;

    /**
     * 请求快照提供运输标签标志
     */
    private Boolean requestSnapshotProvideShippingLabelFlag;

    /**
     * 发货到c端工单状态
     */
    private String otcWorkorderStatus;

    /**
     * 构建运输打包类型
     */
    private String buildShipPackageType;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 备注
     */
    private String note;

    /**
     * request快照备注
     */
    private String requestSnapshotNote;

    /**
     * request快照RefNum
     */
    private String requestSnapshotRefNum;

    /**
     * 请求快照RequestRefnum
     */
    private String requestSnapshotRequestRefNum;

    /**
     * 请求快照运输公司
     */
    private String requestSnapshotShipCarrier;

    /**
     * request快照transactionPartnerId
     */
    private Long requestSnapshotTransactionPartnerId;

    /**
     * request快照渠道
     */
    private String requestSnapshotChannel;

    /**
     * 预处理工单状态
     */
    private String workorderPrepStatus;

    /**
     * 请求快照保险金货币
     */
    private String requestSnapshotInsuranceAmountCurrency;

    /**
     * 拣货到那里
     */
    private String pickToStation;

    /**
     * 锁定前
     */
    private String lockedBefore;

}