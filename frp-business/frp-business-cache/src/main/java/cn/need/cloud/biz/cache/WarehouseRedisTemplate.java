package cn.need.cloud.biz.cache;


import cn.need.cloud.biz.cache.bean.WarehouseCache;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class WarehouseRedisTemplate {

    public static final String REDIS_WAREHOUSE_KEY = "WAREHOUSE:CACHE";
    private final BoundHashOperations<String, String, WarehouseCache> operations;

    public WarehouseRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.operations = hash(redisTemplate);
    }

    public BoundHashOperations<String, String, WarehouseCache> hash() {
        return operations;
    }

    public BoundHashOperations<String, String, WarehouseCache> hash(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.boundHashOps(REDIS_WAREHOUSE_KEY);
    }
}
