package cn.need.cloud.biz.cache;


import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class ProductVersionRedisTemplate {

    public static final String REDIS_PRODUCT_VERSION_KEY = "PRODUCT_VERSION:CACHE";
    private final BoundHashOperations<String, String, ProductVersionCache> operations;

    public ProductVersionRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.operations = hash(redisTemplate);
    }

    public BoundHashOperations<String, String, ProductVersionCache> hash() {
        return operations;
    }

    public BoundHashOperations<String, String, ProductVersionCache> hash(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.boundHashOps(REDIS_PRODUCT_VERSION_KEY);
    }
}
