package cn.need.cloud.biz.cache.bean;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

/**
 * 仓库缓存
 *
 * <AUTHOR>
 */
@Data
public class WarehouseCache implements Serializable {

    @Serial
    private static final long serialVersionUID = 194211791495435145L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private String code;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 名字
     */
    private String addressName;

    /**
     * 公司
     */
    private String addressCompany;

    /**
     * 国家
     */
    private String addressCountry;

    /**
     * 洲
     */
    private String addressState;

    /**
     * 城市
     */
    private String addressCity;

    /**
     * 邮编
     */
    private String addressZipCode;

    /**
     * 地址1
     */
    private String addressAddr1;

    /**
     * 地址2
     */
    private String addressAddr2;

    /**
     * 地址3
     */
    private String addressAddr3;

    /**
     * 邮箱
     */
    private String addressEmail;

    /**
     * 电话
     */
    private String addressPhone;

    /**
     * 备注
     */
    private String addressNote;

    /**
     * 唯一标识码
     */
    private String refNum;

    /**
     * 操作人id
     */
    private Set<Long> operationIds;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 删除原因
     */
    private String deletedNote;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 是否是住宅地址
     */
    private Integer addressIsResidential;

    /**
     * amazonShipProfileShipApiRefNum
     */
    private String amazonShipProfileShipApiRefNum;

    /**
     * fedexShipProfileShipApiRefNum
     */
    private String fedexShipProfileShipApiRefNum;

    /**
     * uspsShipProfileShipApiRefNum
     */
    private String uspsShipProfileShipApiRefNum;

    /**
     * ontracShipProfileShipApiRefNum
     */
    private String ontracShipProfileShipApiRefNum;


    /**
     * upsshipProfileShipApiRefNum
     */
    private String upsshipProfileShipApiRefNum;

    /**
     * 是否有效
     */
    private Boolean activeFlag;

    /**
     * amazonShipPalletProfileShipApiRefNum
     */
    private String amazonShipPalletProfileShipApiRefNum;

    /**
     * 亚马逊仓库编码
     */
    private String amazonWarehouseCode;

    /**
     * ssccprefix
     */
    private String ssccprefix;

    private Long defaultOtbAssemblyBinLocationId;

    private Long defaultOtbConvertBinLocationId;

    private Long defaultOtbMultiboxBinLocationId;

    private Long defaultOtcReadytogoBinLocationId;

    private Long defaultOtcOutsideBinLocationId;

    private Long defaultOtbReadytogoBinLocationId;

    private Long defaultOtbOutsideBinLocationId;

    private Long defaultOtcConvertBinLocationId;

    private Long defaultOtcMultiboxBinLocationId;

    private Long defaultOtcAssemblyBinLocationId;

    private String amazonSellingPartyCode;

    //todo: 这个是错误字段，不要,没有任何意义
    private String amazonVendorContainerCode;

}
