package cn.need.cloud.biz.cache;


import cn.need.cloud.biz.cache.bean.WarehouseCache;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public interface WarehouseCacheService {


    /**
     * 根据id，获取仓库缓存数据
     *
     * @param warehouseId 仓库id
     * @return WarehouseCache 仓库缓存数据
     */
    WarehouseCache getById(Long warehouseId);

    /**
     * 根据规则值集合，获取仓库缓存数据集合
     *
     * @param warehouseIds id集合
     * @return List<WarehouseCache> 租户缓存数据集合
     */
    List<WarehouseCache> listByIds(Collection<Long> warehouseIds);

    /**
     * 获取所有仓库缓存数据
     *
     * @return List<WarehouseCache> 租户缓存数据集合
     */
    Set<Long> list();

    /**
     * 获取所有仓库缓存数据
     *
     * @return List<WarehouseCache> 租户缓存数据集合
     */
    List<WarehouseCache> listAll();
}
