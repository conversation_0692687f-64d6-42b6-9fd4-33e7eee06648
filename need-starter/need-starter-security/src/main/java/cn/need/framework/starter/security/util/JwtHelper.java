package cn.need.framework.starter.security.util;

import cn.need.framework.common.core.jwt.JwtUtil;
import cn.need.framework.common.core.session.JwtUser;
import cn.need.framework.starter.security.config.JwtConfig;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JwtHelper {

    @Autowired
    private JwtConfig jwtConfig;

    public String generateToken(JwtUser jwtUser) {
        return JwtUtil.generateToken(jwtUser, jwtConfig.getSecret());
    }

    public Claims parseToken(String jwt) {
        return JwtUtil.parseToken(jwt, jwtConfig.getSecret());
    }
}
