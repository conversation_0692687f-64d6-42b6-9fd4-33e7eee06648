package cn.need.framework.starter.security.config;

import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.starter.security.interceptor.PermissionsInterceptor;
import cn.need.framework.starter.security.interceptor.UserInterceptor;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;
import java.util.function.Consumer;

@Configuration
@AllArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final RedisTemplate<String, Object> redisTemplate;

    private final JwtConfig jwtConfig;

    private final IgnoreConfig ignoreConfig;

    @Bean
    public UserInterceptor userInterceptor() {
        return new UserInterceptor(jwtConfig);
    }

    @Bean
    public PermissionsInterceptor permissionInterceptor() {
        return new PermissionsInterceptor(redisTemplate, ignoreConfig);
    }

    @Override
    public void addInterceptors(@NotNull InterceptorRegistry registry) {
        // 提取公共方法以减少重复代码
        Consumer<InterceptorRegistration> configurePaths = registration -> {
            registration.addPathPatterns(listIncludePathPatterns());
            registration.excludePathPatterns(listExcludePathPatterns());
        };

        // 添加用户拦截器并配置路径
        configurePaths.accept(registry.addInterceptor(userInterceptor()));

        // 添加权限拦截器并配置路径
        configurePaths.accept(registry.addInterceptor(permissionInterceptor()));
    }


    private List<String> listIncludePathPatterns() {
        return Lists.arrayList("/api/**", "/client/**");
    }

    private List<String> listExcludePathPatterns() {
        return Lists.arrayList();
    }
}
