package cn.need.framework.starter.security.interceptor;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.starter.security.config.IgnoreConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.need.framework.starter.security.constant.SecurityConstants.CACHE_ROLE_PERMISSIONS_PREFIX;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Component
public class PermissionsInterceptor implements HandlerInterceptor {

    private final RedisTemplate<String, Object> redisTemplate;

    private final IgnoreConfig ignoreConfig;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        try {

            // 判断是否忽略权限验证 默认开启权限验证
            if (ObjectUtil.isNotEmpty(ignoreConfig) && ignoreConfig.getPermission()) {
                return true;
            }

            // 获取请求路径并进行规范化处理
            String requestPath = request.getRequestURI();
            if (requestPath.contains("?")) {
                requestPath = requestPath.substring(0, requestPath.indexOf('?'));
            }

            // 确保忽略的URL列表已初始化且非空
            Set<String> ignoreUrls = new HashSet<>(List.of(ignoreConfig.getUrls()));
            String finalRequestPath = requestPath;
            if (!ignoreUrls.isEmpty() && ignoreUrls.stream().anyMatch(ignoreUrl -> pathMatcher.match(ignoreUrl, finalRequestPath))) {
                return true;
            }

            // 检查用户是否已登录，若未登录则返回401 Unauthorized
            if (ObjectUtil.isEmpty(Users.id())) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "User not logged in");
                return false;
            }

            // 确保忽略的验证权限URL列表已初始化且非空
            Set<String> ignorePermissionUrls = new HashSet<>(List.of(ignoreConfig.getPermissionUrls()));
            if (!ignorePermissionUrls.isEmpty() && ignorePermissionUrls.stream().anyMatch(ignoreUrl -> pathMatcher.match(ignoreUrl, finalRequestPath))) {
                return true;
            }

            // 检查用户是否有角色，若没有角色则返回403 Forbidden
            List<Long> roleIds = Users.roleIds();
            if (ObjectUtil.isEmpty(roleIds)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Access denied");
                return false;
            }

            // 根据当前登录用户角色获取权限
            Set<String> permissions = getRolePermissions(roleIds);

            // 检查权限
            if (ObjectUtil.isEmpty(permissions) || permissions.stream().noneMatch(permission -> pathMatcher.match(permission, finalRequestPath))) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Access denied");
                return false;
            }

            return true;

        } catch (Exception e) {
            // 记录异常日志
            log.error("Error in preHandle: ", e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Internal server error");
            return false;
        }
    }

    /**
     * 根据角色ID集合获取角色权限集合
     *
     * @param roleIds 角色ID集合，用于标识特定的角色
     * @return Set<String> 角色权限集合，每个权限作为一个字符串元素
     */
    public Set<String> getRolePermissions(List<Long> roleIds) {

        // 初始化一个集合用于存储所有角色的权限
        Set<String> allPermissions = new HashSet<>();

        try {
            // 构造缓存键列表
            List<String> cacheKeys = roleIds.stream()
                    .filter(Objects::nonNull)
                    .map(roleId -> CACHE_ROLE_PERMISSIONS_PREFIX + roleId)
                    .collect(Collectors.toList());

            // 批量查询Redis中的权限集合
            List<Object> permissionsList = redisTemplate.opsForValue().multiGet(cacheKeys);

            if (ObjectUtil.isEmpty(permissionsList)) {
                return Sets.newHashSet();
            }

            // 合并所有权限集合
            for (Object permissionsObj : permissionsList) {
                if (permissionsObj instanceof Set) {
                    @SuppressWarnings("unchecked")
                    Set<String> permissionList = (Set<String>) permissionsObj;
                    allPermissions.addAll(permissionList);
                }
            }

        } catch (Exception e) {
            // 记录日志并抛出自定义异常
            log.error("获取角色权限失败，roleIds: {}", roleIds, e);
            return Sets.newHashSet();
        }

        // 返回总的权限集合
        return allPermissions;
    }

}
