package cn.need.framework.starter.security.interceptor;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.session.Users;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Configuration
@Slf4j
public class FeignInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ObjectUtil.isNull(attributes)) {
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        //1. 优先从request对象中获取token
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        // 如果数据为空，从当前线程中获取
        if (ObjectUtil.isEmpty(token)) {
            token = Users.token();
        }
        // 打印token
        if (log.isDebugEnabled()) {
            log.debug("----------------->>> FeignInterceptor token={}", token);
        }
        // token不为空的情况下，设置到请求头中，需要添加前缀
        if (ObjectUtil.isNotEmpty(token)) {
            requestTemplate.header(HttpHeaders.AUTHORIZATION, token);
        }
    }
}
