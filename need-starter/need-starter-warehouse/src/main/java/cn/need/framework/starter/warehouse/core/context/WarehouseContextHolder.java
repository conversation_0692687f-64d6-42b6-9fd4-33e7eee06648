package cn.need.framework.starter.warehouse.core.context;

import cn.need.framework.common.core.lang.ObjectUtil;
import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * 多仓库上下文 Holder
 *
 * <AUTHOR>
 */
public class WarehouseContextHolder {

    /**
     * 当前仓库编号
     */
    private static final ThreadLocal<Long> WAREHOUSE_ID = new TransmittableThreadLocal<>();

    /**
     * 是否忽略仓库
     */
    private static final ThreadLocal<Boolean> IGNORE = new TransmittableThreadLocal<>();

    /**
     * 获得仓库编号
     *
     * @return 仓库编号
     */
    public static Long getWarehouseId() {
        return WAREHOUSE_ID.get();
    }

    public static void setWarehouseId(Long warehouseId) {
        WAREHOUSE_ID.set(warehouseId);
    }

    /**
     * 获得仓库编号。如果不存在返回默认值
     *
     * @return 仓库编号
     */
    public static Long getRequiredWarehouseId() {
        return ObjectUtil.emptyToDefault(getWarehouseId(), 0L);
    }

    /**
     * 当前是否忽略仓库
     *
     * @return 是否忽略
     */
    public static boolean isIgnore() {
        return Boolean.TRUE.equals(IGNORE.get());
    }

    public static void setIgnore(Boolean ignore) {
        IGNORE.set(ignore);
    }

    public static void clear() {
        WAREHOUSE_ID.remove();
        IGNORE.remove();
    }

}
