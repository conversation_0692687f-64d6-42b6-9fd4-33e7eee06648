package cn.need.framework.starter.warehouse.util;

import cn.need.framework.common.core.lang.NumberUtil;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 专属于 web 包的工具类
 *
 * <AUTHOR>
 */
public class WebFrameworkUtils {

    public static final String HEADER_WAREHOUSE_ID = "warehouse-id";

    /**
     * 获得仓库编号，从 header 中
     * 考虑到其它 framework 组件也会使用到仓库编号，所以不得不放在 WebFrameworkUtils 统一提供
     *
     * @param request 请求
     * @return 仓库编号
     */
    public static Long getWarehouseId(HttpServletRequest request) {
        String warehouseId = request.getHeader(HEADER_WAREHOUSE_ID);
        return NumberUtil.isNumber(warehouseId) ? Long.valueOf(warehouseId) : null;
    }


    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes servletRequestAttributes)) {
            return null;
        }
        return servletRequestAttributes.getRequest();
    }

}
