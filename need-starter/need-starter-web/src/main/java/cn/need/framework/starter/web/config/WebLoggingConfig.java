package cn.need.framework.starter.web.config;

import cn.need.framework.starter.web.log.RequestLoggingFilter;
import cn.need.framework.starter.web.log.TraceLogFilter;
import cn.need.framework.starter.web.log.WebLogProperties;
import cn.need.framework.starter.web.repeat.RepeatableWrapperRequestFilter;
import cn.need.framework.starter.web.repeat.WebRepeatProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import javax.servlet.DispatcherType;
import javax.servlet.Filter;

/**
 * Web可重复读配置
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({WebLogProperties.class, WebRepeatProperties.class})
public class WebLoggingConfig {


    @Bean
    public FilterRegistrationBean<RepeatableWrapperRequestFilter> repeatableRequestFilter(WebRepeatProperties repeatProperties) {
        FilterRegistrationBean<RepeatableWrapperRequestFilter> filterBean = new FilterRegistrationBean<>();
        filterBean.setFilter(new RepeatableWrapperRequestFilter(repeatProperties));
        filterBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 20);
        return filterBean;
    }

    @Bean
    public FilterRegistrationBean<RequestLoggingFilter> requestLoggingFilter(WebLogProperties logProperties) {
        FilterRegistrationBean<RequestLoggingFilter> registration = new FilterRegistrationBean<>();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        RequestLoggingFilter loggingFilter = new RequestLoggingFilter(logProperties);
        registration.setFilter(loggingFilter);
        registration.addUrlPatterns("/*");
        registration.setName(RequestLoggingFilter.class.getSimpleName());
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 200);
        return registration;
    }

    @Bean
    public FilterRegistrationBean<Filter> traceLogFilter() {
        FilterRegistrationBean<Filter> filterBean = new FilterRegistrationBean<>();
        filterBean.setFilter(new TraceLogFilter());
        filterBean.addUrlPatterns("/*");
        filterBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 190);
        return filterBean;
    }


}
