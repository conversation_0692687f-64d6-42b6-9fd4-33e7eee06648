package cn.need.framework.starter.web.repeat;

import cn.need.framework.starter.web.log.RepeatableRequestWrapper;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 可重复读Filter
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@AllArgsConstructor
public class RepeatableWrapperRequestFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(RepeatableWrapperRequestFilter.class);

    private final WebRepeatProperties properties;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws IOException, ServletException {
        if (!properties.isEnable()) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // 使用 ContentCachingResponseWrapper 缓存响应体
            ContentCachingResponseWrapper resp = new ContentCachingResponseWrapper(response);
            // 使用 RepeatableRequestWrapper 缓存请求体
            RepeatableRequestWrapper req = new RepeatableRequestWrapper(request);

            // 继续过滤链
            filterChain.doFilter(req, resp);

            // 将缓存的响应体复制回原始响应
            resp.copyBodyToResponse();
        } catch (IOException | ServletException e) {
            log.error("Error in RepeatableWrapperRequestFilter", e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in RepeatableWrapperRequestFilter", e);
            throw new ServletException("Unexpected error in RepeatableWrapperRequestFilter", e);
        }
    }
}
