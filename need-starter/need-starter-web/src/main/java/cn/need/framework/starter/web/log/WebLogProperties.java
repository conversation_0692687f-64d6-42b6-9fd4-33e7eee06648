package cn.need.framework.starter.web.log;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * WebLogProperties
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
@ConfigurationProperties(prefix = WebLogProperties.PREFIX)
@Getter
@Setter
public class WebLogProperties {

    public static final String PREFIX = "servlet.request.log";

    /**
     * 是否开启
     */
    private boolean enable = true;

    /**
     * 是否包含请求参数
     */
    private boolean includeQueryString = true;

    /**
     * 是否包含请求头
     */
    private boolean includeHeaders = false;

    /**
     * 是否包含请求体
     */
    private boolean includePayload = true;

    /**
     * 是否包含客户端信息
     */
    private boolean includeClientInfo = false;

    /**
     * 截断长度
     */
    private int truncationLength = -1;
}
