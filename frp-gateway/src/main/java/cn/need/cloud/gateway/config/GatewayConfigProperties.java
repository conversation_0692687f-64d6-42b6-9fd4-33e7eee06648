package cn.need.cloud.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 网关自定义配置信息.
 * 注解RefreshScope标记该配置具备热加载能力
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties("gateway")
public class GatewayConfigProperties {

    /**
     * 用来解密前端登录密码的秘钥
     */
    public String aesSecret;

}