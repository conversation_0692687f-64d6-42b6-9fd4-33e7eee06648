package cn.need.cloud.gateway.config;

import cn.need.cloud.log.cache.LoggerPatternCacheService;
import cn.need.cloud.log.cache.impl.RedisLoggerPatternCacheServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 日志配置
 *
 * <AUTHOR>
 */
@Configuration
public class LogCacheConfig {

    /**
     * 注入日志配置缓存操作工具
     */
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public LoggerPatternCacheService loggerPatternCacheService(RedisTemplate redisTemplate) {
        return new RedisLoggerPatternCacheServiceImpl(redisTemplate);
    }
}
