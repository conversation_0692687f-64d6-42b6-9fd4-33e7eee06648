package cn.need.cloud.gateway.decorator;

import cn.need.cloud.gateway.util.DataBufferFix;
import cn.need.cloud.gateway.util.DataBufferWrapper;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.lang.NonNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * gateway request 装饰对象
 *
 * <AUTHOR>
 */
public class LoggerServerHttpRequestDecorator extends ServerHttpRequestDecorator {

    /**
     * 装饰对象中缓存wrapper对象
     */
    private DataBufferWrapper wrapper = null;

    public LoggerServerHttpRequestDecorator(ServerHttpRequest delegate) {
        super(delegate);
    }

    /**
     * 重写父类的获取请求body方法
     *
     * @return Flux<DataBuffer>
     */
    @NonNull
    @Override
    public Flux<DataBuffer> getBody() {
        //加同步锁
        synchronized (this) {
            //缓存wrapper对象不为空的情况下
            if (isNotNull(wrapper)) {
                return Flux.from(Mono.justOrEmpty(wrapper.newDataBuffer()));
            }
            //构建Mono对象
            Mono<DataBuffer> mono = DataBufferFix.join(super.getBody()).doOnNext(d -> wrapper = d).filter(d -> isNotNull(d.getFactory()))
                    .map(DataBufferWrapper::newDataBuffer);
            return Flux.from(mono);
        }
    }
}
