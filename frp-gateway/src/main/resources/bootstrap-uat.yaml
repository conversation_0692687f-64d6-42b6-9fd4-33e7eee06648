spring:
    # 服务名称
    application:
        name: frp-gateway-@NACOS_GROUP@
    # 配置spring cloud的配置中心，采用alibaba的nacos做为配置中心
    cloud:
        nacos:
            config:
                server-addr: @NACOS_HOST@:@NACOS_PORT@
                file-extension: yaml
                # nacos命名空间
                namespace: uat
                username: @NACOS_USERNAME@
                password: @NACOS_PASSWORD@
                # 分组
                group: @NACOS_GROUP@
                # nacos共享配置
                shared-configs:
                    -   data-id: frp-redis.yaml
                        group: @SHARE_GROUP@
                    -   data-id: gateway-feign.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-log.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-other.yaml
                        group: @SHARE_GROUP@
            discovery:
                server-addr: @NACOS_HOST@:@NACOS_PORT@
                # 命名空间
                namespace: uat
                username: @NACOS_USERNAME@
                password: @NACOS_PASSWORD@
