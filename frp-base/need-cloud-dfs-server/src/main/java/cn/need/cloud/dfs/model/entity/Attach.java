package cn.need.cloud.dfs.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 附件表实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("dfs_attach")
@EqualsAndHashCode(callSuper = true)
public class Attach extends SuperModel {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 附件地址
     */
    @TableField("link")
    private String link;

    /**
     * 附件域名
     */
    @TableField("domain")
    private String domain;

    /**
     * 附件名称
     */
    @TableField("name")
    private String name;

    /**
     * 附件原名
     */
    @TableField("original_name")
    private String originalName;

    /**
     * 附件拓展名
     */
    @TableField("extension")
    private String extension;

    /**
     * 附件大小
     */
    @TableField("attach_size")
    private Long attachSize;

    /**
     * 模块
     */
    @TableField("module")
    private String module;

    /**
     * 表单名称
     */
    @TableField("form_name")
    private String formName;

    /**
     * 单据号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 附件名称
     */
    @TableField("file_name")
    private String fileName;
}
