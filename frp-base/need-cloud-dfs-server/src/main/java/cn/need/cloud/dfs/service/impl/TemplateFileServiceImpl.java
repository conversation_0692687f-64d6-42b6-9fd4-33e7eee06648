package cn.need.cloud.dfs.service.impl;

import cn.need.cloud.dfs.mapper.TemplateFileMapper;
import cn.need.cloud.dfs.model.entity.TemplateFile;
import cn.need.cloud.dfs.model.vo.TemplateFileVO;
import cn.need.cloud.dfs.model.vo.resp.TemplateFileRespVO;
import cn.need.cloud.dfs.service.TemplateFileService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.support.util.DictUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模版文件 服务实现
 *
 * <AUTHOR>
 */
@Service
public class TemplateFileServiceImpl extends SuperServiceImpl<TemplateFileMapper, TemplateFile> implements TemplateFileService {

    @Override
    public List<TemplateFileRespVO> pageByCondition(Page<TemplateFileRespVO> page, TemplateFileVO condition) {
        return mapper.pageByCondition(page, condition);
    }

    @Override
    public List<TemplateFileRespVO> listByCondition(TemplateFileVO condition) {
        return mapper.listByCondition(condition);
    }

    @Override
    public TemplateFileVO getByTemplateCode(String templateCode) {
        TemplateFile templateFile = lambdaQuery().eq(TemplateFile::getTemplateCode, templateCode).one();
        if (ObjectUtil.isNull(templateFile)) {
            return null;
        }
        TemplateFileVO vo = new TemplateFileVO();
        BeanUtils.copyProperties(templateFile, vo);
        DictUtil.set(vo);
        return vo;
    }
}
