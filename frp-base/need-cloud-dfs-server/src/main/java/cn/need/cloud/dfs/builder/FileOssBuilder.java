package cn.need.cloud.dfs.builder;

import cn.need.cloud.dfs.props.Oss;
import cn.need.framework.common.oss.props.OssProperties;
import cn.need.framework.common.oss.rule.OssRule;
import cn.need.framework.common.oss.template.FileTemplate;
import com.aliyun.oss.ClientConfiguration;
import lombok.SneakyThrows;

/**
 * 本地存储构建类
 *
 * <AUTHOR>
 */
public class FileOssBuilder {

    @SneakyThrows
    public static FileTemplate template(Oss oss, OssRule ossRule) {
        // 创建ClientConfiguration。ClientConfiguration是OSSClient的配置类，可配置代理、连接超时、最大连接数等参数。
        ClientConfiguration conf = new ClientConfiguration();
        // 设置OSSClient允许打开的最大HTTP连接数，默认为1024个。
        conf.setMaxConnections(1024);
        // 设置Socket层传输数据的超时时间，默认为50000毫秒。
        conf.setSocketTimeout(50000);
        // 设置建立连接的超时时间，默认为50000毫秒。
        conf.setConnectionTimeout(50000);
        // 设置从连接池中获取连接的超时时间（单位：毫秒），默认不超时。
        conf.setConnectionRequestTimeout(1000);
        // 设置连接空闲超时时间。超时则关闭连接，默认为60000毫秒。
        conf.setIdleConnectionTime(60000);
        // 设置失败请求重试次数，默认为3次。
        conf.setMaxErrorRetry(5);
        OssProperties ossProperties = new OssProperties();
        //eg:http://134.175.84.80:10000
        ossProperties.setEndpoint(oss.getEndpoint());
        ossProperties.setAccessKey(oss.getAccessKey());
        ossProperties.setSecretKey(oss.getSecretKey());
        //eg:/opt/zserp/basic-dfs/product/
        ossProperties.setBucketName(oss.getBucketName());
        //product
        ossProperties.setRegion(oss.getRegion());
        return new FileTemplate(ossRule, ossProperties);
    }

}
