package cn.need.cloud.dfs.builder;

import cn.need.cloud.dfs.props.Oss;
import cn.need.framework.common.oss.props.OssProperties;
import cn.need.framework.common.oss.rule.OssRule;
import cn.need.framework.common.oss.template.AmazonS3Template;
import cn.need.framework.common.oss.template.OssTemplate;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.time.Duration;

/**
 * Description: 
 *
 * <AUTHOR>
 * @since 2025/5/15
 *
 * <p>
 * </p>
 *
 */
public class AmazonS3OssBuilder {

    public static OssTemplate template(Oss oss, OssRule ossRule) {
        StaticCredentialsProvider credentialsProvider
                = StaticCredentialsProvider.create(AwsBasicCredentials.create(oss.getAccess<PERSON>ey(), oss.getSecretKey()));
        S3Client client = S3Client.builder()
                .credentialsProvider(credentialsProvider)
                .region(Region.of(oss.getRegion()))
                .httpClient(ApacheHttpClient.builder()
                        .maxConnections(1024)
                        .connectionTimeout(Duration.ofSeconds(50000))
                        .socketTimeout(Duration.ofSeconds(50000))
                        .connectionAcquisitionTimeout(Duration.ofSeconds(1000))
                        .connectionMaxIdleTime(Duration.ofSeconds(60000))
                        .build())
                .build();
        OssProperties ossProperties = new OssProperties();
        ossProperties.setEndpoint(oss.getEndpoint());
        ossProperties.setAccessKey(oss.getAccessKey());
        ossProperties.setSecretKey(oss.getSecretKey());
        ossProperties.setBucketName(oss.getBucketName());
        return new AmazonS3Template(client, ossProperties, ossRule);
    }
}
