package cn.need.cloud.dfs.props;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Oss配置
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "OSS配置")
public class Oss {

    /**
     * 所属分类 取值数据字典[OSS_CATEGORY]
     * minio:minio 七牛云:qiniu 阿里云:ali 腾讯云:tencent
     */
    @Schema(description = "所属分类")
    private String category;


    /**
     * oss地址
     */
    @Schema(description = "资源地址")
    private String endpoint;

    /**
     * accessKey
     */
    @Schema(description = "accessKey")
    private String accessKey;

    /**
     * secretKey
     */
    @Schema(description = "secretKey")
    private String secretKey;

    /**
     * 空间名
     */
    @Schema(description = "空间名")
    private String bucketName;

    /**
     * 应用ID TencentCOS需要
     */
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 地域简称 TencentCOS需要
     */
    @Schema(description = "地域简称")
    private String region;
}
