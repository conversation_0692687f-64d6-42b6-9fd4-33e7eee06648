<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.dfs.mapper.TemplateFileMapper">


    <select id="pageByCondition" resultType="cn.need.cloud.dfs.model.vo.resp.TemplateFileRespVO">
        SELECT * FROM dfs_template_file t where t.remove_flag = 0
        <if test="condition.templateCode!= null and condition.templateCode != ''">
            AND t.template_code like concat('%',#{condition.templateCode},'%')
        </if>
        <if test="condition.templateName!= null and condition.templateName != ''">
            AND t.template_name like concat('%',#{condition.templateName},'%')
        </if>
        <if test="condition.fileUrl!= null and condition.fileUrl != ''">
            AND t.file_url like concat('%',#{condition.fileUrl},'%')
        </if>
        <if test="condition.state!= null">
            AND t.state = #{condition.state}
        </if>
        order by t.id desc
    </select>
    <select id="listByCondition" resultType="cn.need.cloud.dfs.model.vo.resp.TemplateFileRespVO">
        SELECT * FROM dfs_template_file t where t.remove_flag = 0
        <if test="condition.templateCode!= null and condition.templateCode != ''">
            AND t.template_code like concat('%',#{condition.templateCode},'%')
        </if>
        <if test="condition.templateName!= null and condition.templateName != ''">
            AND t.template_name like concat('%',#{condition.templateName},'%')
        </if>
        <if test="condition.fileUrl!= null and condition.fileUrl != ''">
            AND t.file_url like concat('%',#{condition.fileUrl},'%')
        </if>
        <if test="condition.state!= null">
            AND t.state = #{condition.state}
        </if>
        order by t.id desc
    </select>
</mapper>
