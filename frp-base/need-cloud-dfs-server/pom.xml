<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.need.cloud</groupId>
        <artifactId>frp-base</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>

    <artifactId>need-cloud-dfs-server</artifactId>
    <packaging>jar</packaging>
    <name>need-cloud-dfs-server</name>
    <description>the dfs server Center for uneed need-cloud</description>

    <!-- 参数配置 -->
    <properties>

    </properties>

    <dependencies>
        <!-- 文件服务client依赖，版本与当前项目保持一致 -->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dfs-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dict-client</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- uneed common -->
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-security</artifactId>
        </dependency>

        <!--tika-core-->
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
