package cn.need.cloud.dict.client.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegionReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3581463316306452538L;

    /**
     * 区域名称集合
     */
    private List<String> regionNameList;

    /**
     * 上级ids
     */
    private List<Long> parentIds;

    /**
     * 深度
     */
    private Integer depth;

    /**
     * 父级编码
     */
    private String parentCode;

}
