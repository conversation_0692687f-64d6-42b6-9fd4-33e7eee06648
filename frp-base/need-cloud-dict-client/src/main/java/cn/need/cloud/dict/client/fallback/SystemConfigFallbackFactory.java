package cn.need.cloud.dict.client.fallback;

import cn.need.cloud.dict.client.api.SystemConfigClient;
import cn.need.cloud.dict.client.api.path.DictClientPath;
import cn.need.cloud.dict.client.api.path.SystemConfigPath;
import cn.need.framework.common.core.http.HttpCode;
import cn.need.framework.common.support.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 系统配置client 的FallbackFactory类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SystemConfigFallbackFactory implements FallbackFactory<SystemConfigClient> {

    @Override
    public SystemConfigClient create(Throwable cause) {

        log.error("远程调用[{}{}{}", getClass().getName(), "]接口异常：", cause.getMessage(), cause);

        return dto -> {
            log.error("调用[{}]执行熔断，参数[{}]", "POST ".concat(DictClientPath.PREFIX)
                    .concat(SystemConfigPath.SAVE_CONFIG), dto);
            return Result.fail(HttpCode.FEIGN_CLIENT_FALLBACK, cause.getMessage());
        };
    }
}
