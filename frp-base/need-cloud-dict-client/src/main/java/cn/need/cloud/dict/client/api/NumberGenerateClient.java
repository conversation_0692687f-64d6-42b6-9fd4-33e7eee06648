package cn.need.cloud.dict.client.api;

import cn.need.cloud.dict.client.api.path.NumberGeneratePath;
import cn.need.cloud.dict.client.fallback.NumberGenerateFallbackFactory;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 编码生成器  Feign 客户端
 *
 * <AUTHOR>
 */
@FeignClient(value = "${serviceId.frp-base}", path = NumberGeneratePath.PREFIX, contextId = "NumberGenerateClient",
        fallbackFactory = NumberGenerateFallbackFactory.class)
public interface NumberGenerateClient {

    /**
     * 初始化配置
     *
     * @return 成功或失败
     */
    @GetMapping(NumberGeneratePath.INIT)
    Result<String> initNumberConfig();

    @GetMapping(NumberGeneratePath.GENERATE_NUMBER)
    Result<String> generateNumber(@RequestParam(name = "code") String code);

    @GetMapping(NumberGeneratePath.GENERATE_NUMBER_ZONE)
    Result<String> generateNumber(@RequestParam(name = "code") String code,
                                  @RequestParam(name = "zoneId", required = false) String zoneId);

    @GetMapping(NumberGeneratePath.GENERATE_NUMBER_PRODUCT_CODE)
    Result<String> generateNumberWithProduct(
            @RequestParam(name = "code") String code,
            @RequestParam(name = "abbrName", required = false) String abbrName
    );

    @GetMapping(NumberGeneratePath.GENERATE_NUMBER_WAREHOUSE_CODE)
    Result<String> generateNumber(
            @RequestParam(name = "code") String code,
            @RequestParam(name = "zoneId", required = false) String zoneId,
            @RequestParam(name = "warehouseCode", required = false) String warehouseCode
    );


    @GetMapping(NumberGeneratePath.GENERATE_NUMBER_WAREHOUSE_CODE_DELIMITER)
    Result<String> generateNumber(
            @RequestParam(name = "code") String code,
            @RequestParam(name = "zoneId", required = false) String zoneId,
            @RequestParam(name = "warehouseCode", required = false) String warehouseCode,
            @RequestParam(name = "delimiter") String delimiter
    );
}
