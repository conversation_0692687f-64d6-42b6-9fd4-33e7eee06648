package cn.need.cloud.dict.client.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统配置 dto对象
 *
 * <AUTHOR>
 */
@Data
public class SystemConfigDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 配置编号
     */
    private String confCode;

    /**
     * 配置名称
     */
    private String confName;

    /**
     * 配置值
     */
    private String confValue;

    /**
     * 分组信息，同一分组下的配置编号唯一
     */
    private String confGroup;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    private Integer state;

    /**
     * 描述
     */
    private String description;

}