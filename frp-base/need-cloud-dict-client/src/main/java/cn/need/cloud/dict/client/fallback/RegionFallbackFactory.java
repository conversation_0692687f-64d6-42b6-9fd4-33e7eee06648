package cn.need.cloud.dict.client.fallback;

import cn.need.cloud.dict.client.api.RegionClient;
import cn.need.cloud.dict.client.api.path.DictClientPath;
import cn.need.cloud.dict.client.api.path.RegionClientPath;
import cn.need.cloud.dict.client.dto.RegionDTO;
import cn.need.cloud.dict.client.dto.req.RegionReqDTO;
import cn.need.framework.common.core.http.HttpCode;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 行政区域功能客户端回退策略.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RegionFallbackFactory implements FallbackFactory<RegionClient> {

    @Override
    public RegionClient create(Throwable cause) {

        log.error("远程调用[{}{}{}", getClass().getName(), "]接口异常：", cause.getMessage(), cause);

        return new RegionClient() {
            @Override
            public Result<Integer> save(RegionDTO dto) {
                log.error("调用[{}]执行熔断，参数[{}]", "POST ".concat(RegionClientPath.PREFIX)
                        .concat(RegionClientPath.SAVE), dto);
                return Result.fail(HttpCode.FEIGN_CLIENT_FALLBACK, cause.getMessage());
            }

            @Override
            public Result<Integer> saveBatch(List<RegionDTO> dtoList) {
                log.error("调用[{}]执行熔断，参数[{}]", "POST ".concat(RegionClientPath.PREFIX)
                        .concat(RegionClientPath.SAVE_BATCH), dtoList);
                return Result.fail(HttpCode.FEIGN_CLIENT_FALLBACK, cause.getMessage());
            }

            @Override
            public Result<Integer> deleteByCode(String regionCode) {
                log.error("调用[{}]执行熔断，参数[{}]", "GET ".concat(RegionClientPath.PREFIX)
                        .concat(RegionClientPath.DELETE_BY_CODE), "regionCode=" + regionCode);
                return Result.fail(HttpCode.FEIGN_CLIENT_FALLBACK, cause.getMessage());
            }

            @Override
            public Result<PageData<RegionDTO>> getRegion(PageSearch<RegionReqDTO> search) {
                log.error("调用[{}]执行熔断，参数[{}]", "POST ".concat(DictClientPath.PREFIX)
                        .concat(RegionClientPath.GET_REGION), search);
                return Result.fail(HttpCode.FEIGN_CLIENT_FALLBACK, cause.getMessage());
            }

            @Override
            public Result<List<RegionDTO>> findByCondition(RegionReqDTO condition) {
                log.error("调用[{}]执行熔断，参数[{}]", "POST ".concat(DictClientPath.PREFIX)
                        .concat(RegionClientPath.FIND_BY_CONDITION), condition);
                return Result.fail(HttpCode.FEIGN_CLIENT_FALLBACK, cause.getMessage());
            }
        };
    }
}
