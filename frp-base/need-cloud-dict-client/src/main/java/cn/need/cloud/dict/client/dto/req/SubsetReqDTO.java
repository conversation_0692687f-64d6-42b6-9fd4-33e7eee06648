package cn.need.cloud.dict.client.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 数据字典子集接口查询条件
 *
 * <AUTHOR>
 */
@Data
public class SubsetReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4881107627862744032L;

    /**
     * 字典编号，必填项
     */
    @NotBlank(message = "字典编号不能为空")
    private String code;

    /**
     * 根级编号，可选项，默认会是与字典编号保持一致
     */
    private String root;

    /**
     * 需要返回的扩展字段，可选项，取值范围：value(字典值[String])、description(字典描述[String])、sorting(排序值[Integer]、enable(有效性[Boolean]))、parent(父级字典编号[String])、root(根级编号[String])、subset(直系子集编号集合[List<String>])
     */
    private List<String> extendFields;
}
