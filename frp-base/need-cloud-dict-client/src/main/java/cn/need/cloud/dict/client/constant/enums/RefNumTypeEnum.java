package cn.need.cloud.dict.client.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 * refNum 类型
 * 需要在 数据库中配置
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RefNumTypeEnum {


    //region FeeConfig

    /**
     * FeeSupplierQuote
     */
    FEE_SUPPLIER_QUOTE("FEE_SUPPLIER_QUOTE"),

    /**
     * QUOTE
     */
    FEE_QUOTE("FEE_QUOTE"),

    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_INBOUND("FEE_CONFIG_INBOUND"),
    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_INBOUND_DETAIL("FEE_CONFIG_INBOUND_DETAIL"),

    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_OTC("FEE_CONFIG_OTC"),
    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_OTC_DETAIL("FEE_CONFIG_OTC_DETAIL"),

    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_OTB("FEE_CONFIG_OTB"),
    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_OTB_DETAIL("FEE_CONFIG_OTB_DETAIL"),

    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_STORAGE("FEE_CONFIG_STORAGE"),
    
    /**
     * FEE_CONFIG_INBOUND
     */
    FEE_CONFIG_STORAGE_DETAIL("FEE_CONFIG_STORAGE_DETAIL"),

    //endregion FeeConfig

    //region Fee

    /**
     * FEE_ORIGINAL_DATA_INBOUND
     */
    FEE_ORIGINAL_DATA_INBOUND("FEE_ORIGINAL_DATA_INBOUND"),

    /**
     * FEE_ORIGINAL_DATA_OTC
     */
    FEE_ORIGINAL_DATA_OTC("FEE_ORIGINAL_DATA_OTC"),

    /**
     * FEE_ORIGINAL_DATA_OTB
     */
    FEE_ORIGINAL_DATA_OTB("FEE_ORIGINAL_DATA_OTB"),

    /**
     * FEE_ORIGINAL_DATA_STORAGE
     */
    FEE_ORIGINAL_DATA_STORAGE("FEE_ORIGINAL_DATA_STORAGE"),

    /**
     * FEE_INBOUND
     */
    FEE_INBOUND("FEE_INBOUND"),

    /**
     * FEE_INBOUND
     */
    FEE_OTC("FEE_OTC"),

    /**
     * FEE_INBOUND
     */
    FEE_OTB("FEE_OTB"),

    /**
     * FEE_STORAGE
     */
    FEE_STORAGE("FEE_STORAGE"),

    /**
     * FEE_INBOUND
     */
    FEE_INBOUND_DETAIL("FEE_INBOUND_DETAIL"),

    /**
     * FEE_OTC
     */
    FEE_OTC_DETAIL("FEE_OTC_DETAIL"),

    /**
     * FEE_OTB
     */
    FEE_OTB_DETAIL("FEE_OTB_DETAIL"),

    /**
     * FEE_STORAGE
     */
    FEE_STORAGE_DETAIL("FEE_STORAGE_DETAIL"),

    //endregion Fee

    //region Inventory

    /**
     * 库存审计
     */
    INVENTORY_AUDIT("INVENTORY_AUDIT"),

    //endregion Inventory

    //region inbound

    /**
     * 入库请求单
     */
    INBOUND_REQUEST("INBOUND_REQUEST"),

    /**
     * 卸货单
     */
    INBOUND_UNLOAD("INBOUND_UNLOAD"),

    /**
     * 打托单
     */
    INBOUND_PALLET("INBOUND_PALLET"),

    /**
     * 打托单
     */
    INBOUND_PUT_AWAY_SLIP("INBOUND_PUT_AWAY_SLIP"),

    /**
     * 入库工单
     */
    INBOUND_WORKORDER("INBOUND_WORKORDER"),

    //endregion inbound

    //region Pallet
    /**
     * 空托盘
     */
    PALLET_EMPTY("PALLET_EMPTY"),

    /**
     *
     */
    PALLET_TEMPLATE("PALLET_TEMPLATE"),

    //endregion Pallet

    //region OTC

    /**
     * OTC请求
     */
    OTC_REQUEST("OTC_REQUEST"),

    /**
     * OTC工单
     */
    OTC_WORK_ORDER("OTC_WORK_ORDER"),

    /**
     * Prep工单
     */
    OTC_PREP_WORK_ORDER("OTC_PREP_WORK_ORDER"),

    /**
     * OTC拣货单
     */
    OTC_PICKING_SLIP("OTC_PICKING_SLIP"),

    /**
     * Prep拣货单
     */
    OTC_PREP_PICKING_SLIP("OTC_PREP_PICKING_SLIP"),

    /**
     * OTC包裹
     */
    OTC_PACKAGE("OTC_PACKAGE"),

    /**
     * 运输托盘
     */
    OTC_SHIP_PALLET("OTC_SHIP_PALLET"),


    //endregion OTC

    //region OTB

    /**
     * OTB请求
     */
    OTB_REQUEST("OTB_REQUEST"),


    /**
     * OTB路由
     */
    OTB_ROUTING_INSTRUCTION("OTB_ROUTING_INSTRUCTION"),

    /**
     * Otb包裹
     */
    OTB_PACKAGE("OTB_PACKAGE"),

    /**
     * OTC工单
     */
    OTB_WORK_ORDER("OTB_WORK_ORDER"),

    /**
     * OTB拣货单
     */
    OTB_PICKING_SLIP("OTB_PICKING_SLIP"),

    /**
     * Prep工单
     */
    OTB_PREP_WORK_ORDER("OTB_PREP_WORK_ORDER"),

    /**
     * OTB Prep拣货单
     */
    OTB_PREP_PICKING_SLIP("OTB_PREP_PICKING_SLIP"),

    /**
     * Otb打托单
     */
    OTB_PALLET("OTB_PALLET"),

    /**
     * Otb打托单
     */
    OTB_SHIPMENT("OTB_SHIPMENT"),

    //endregion OTB

    /**
     * 库位
     */
    BIN_LOCATION("BIN_LOCATION"),

    /**
     * 仓库
     */
    WAREHOUSE("WAREHOUSE"),

    /**
     * 产品
     */
    PRODUCT("PRODUCT"),

    /**
     * 危险品
     */
    HAZMAT_HAZ("HAZMAT"),

    /**
     * OTC 上架单
     */
    OTC_PUT_AWAY_SLIP("OTC_PUT_AWAY_SLIP"),

    /**
     * OTC PREP上架单
     */
    OTC_PREP_PUT_AWAY_SLIP("OTC_PREP_PUT_AWAY_SLIP"),

    /**
     * OTB 上架单
     */
    OTB_PUT_AWAY_SLIP("OTB_PUT_AWAY_SLIP"),

    /**
     * OTB PREP上架单
     */
    OTB_PREP_PUT_AWAY_SLIP("OTB_PREP_PUT_AWAY_SLIP"),

    /**
     * 货权转移
     */
    TRANSFER_OWNER_SHIP_REQUEST("TRANSFER_OWNER_SHIP_REQUEST"),

    /**
     * 租户
     */
    TENANT("TENANT");


    public final String code;

    // public final String prefix;
    // public final String date_format;
    // public final String stream_length;

}