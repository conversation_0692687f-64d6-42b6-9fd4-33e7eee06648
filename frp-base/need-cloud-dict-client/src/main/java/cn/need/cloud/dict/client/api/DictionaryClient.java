package cn.need.cloud.dict.client.api;

import cn.need.cloud.dict.client.api.path.DictClientPath;
import cn.need.cloud.dict.client.dto.DictionaryDTO;
import cn.need.cloud.dict.client.dto.req.DictionaryDeleteReqDTO;
import cn.need.cloud.dict.client.dto.req.DictionaryReqDTO;
import cn.need.cloud.dict.client.dto.req.SubsetReqDTO;
import cn.need.cloud.dict.client.fallback.DictionaryFallbackFactory;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@FeignClient(value = "${serviceId.frp-base}", contextId = "DictionaryClient",
        fallbackFactory = DictionaryFallbackFactory.class)
public interface DictionaryClient {

    /**
     * 获取数据字典
     *
     * @param search 分页条件
     * @return PageData<DictionaryDTO>
     */
    @PostMapping(DictClientPath.GET_DICTIONARY)
    Result<PageData<DictionaryDTO>> getDictionary(@RequestBody PageSearch<DictionaryReqDTO> search);

    /**
     * 保存字典数据，会根据数据的id是否为null，对数据做新增或是修改处理，并将数据写入到缓存中
     *
     * @param dto 数据字典dto对象
     * @return int 受影响行数
     */
    @PostMapping(DictClientPath.SAVE_DICTIONARY)
    Result<Integer> save(@RequestBody DictionaryDTO dto);

    /**
     * 保存字典数据集合，会根据数据的id是否为null，对数据做新增或是修改处理，并将数据写入到缓存中
     *
     * @param dtoList 数据字典dto对象集合
     * @return int 受影响行数
     */
    @PostMapping(DictClientPath.SAVE_DICTIONARY_BATCH)
    Result<Integer> saveBatch(@RequestBody List<DictionaryDTO> dtoList);

    /**
     * 根据数据字典删除条件集合，批量删除字典数据
     *
     * @param deleteParams 数据字典删除条件集合
     * @return int 受影响行数
     */
    @PostMapping(DictClientPath.DELETE_DICTIONARY_BATCH)
    Result<Integer> deleteBatch(@RequestBody List<DictionaryDeleteReqDTO> deleteParams);

    /**
     * 获取数据字典子集接口，只返回有效的字典数据
     *
     * @param param 数据字典子集接口查询条件
     * @return 有效的字典子集
     */
    @PostMapping(value = DictClientPath.SUBSET)
    Result<List<Map<String, Object>>> subset(@RequestBody SubsetReqDTO param);

    /**
     * 根据字典名称模糊查获取字典编码
     *
     * @param dto 数据字典子集接口查询条件
     * @return 有效的字典子集
     */
    @PostMapping(value = DictClientPath.BY_NAME)
    Result<List<DictionaryDTO>> getCodeByName(@RequestBody DictionaryDTO dto);

    /**
     * 获取所有字典行
     *
     * @return 有效的字典子集
     */
    @GetMapping(value = DictClientPath.LIST_ALL)
    Result<List<DictionaryDTO>> getList(@RequestBody List<String> codes);
}
