/*
 Navicat Premium Dump SQL

 Source Server         : ipower-dev
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : ***************:30201
 Source Schema         : frp_base_dev

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 22/02/2025 16:42:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `app_access_config`;
CREATE TABLE `app_access_config`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键ID',
    `app_id`              varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `name`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `service_type`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`         tinyint(1)                                             NOT NULL DEFAULT '1' COMMENT '是否激活',
    `note`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '备注',
    `version`             bigint                                                 NOT NULL DEFAULT '0' COMMENT '版本',
    `deleted_note`        varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '删除备注',
    `default_operator_id` bigint                                                          DEFAULT NULL COMMENT '默认操作员ID',
    `default_tenant_id`   bigint                                                          DEFAULT NULL COMMENT '默认租户ID',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                          DEFAULT NULL COMMENT '更新者ID',
    `update_time`         datetime                                               NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='app权限配置';

-- ----------------------------
-- Table structure for dfs_attach
-- ----------------------------
DROP TABLE IF EXISTS `dfs_attach`;
CREATE TABLE `dfs_attach`
(
    `id`            bigint     NOT NULL COMMENT '主键',
    `link`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '附件地址',
    `domain`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '附件域名',
    `name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '附件名称',
    `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '附件原名',
    `extension`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '附件拓展名',
    `attach_size`   bigint                                                 DEFAULT NULL COMMENT '附件大小',
    `create_by`     bigint                                                 DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime   NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 DEFAULT NULL COMMENT '最后更新人',
    `update_time`   datetime   NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1) NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '模块',
    `form_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表单名称',
    `order_no`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '单据号',
    `file_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '原始名称',
    `version`       bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='附件';

-- ----------------------------
-- Table structure for dfs_template_file
-- ----------------------------
DROP TABLE IF EXISTS `dfs_template_file`;
CREATE TABLE `dfs_template_file`
(
    `id`            bigint NOT NULL COMMENT '主键',
    `template_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '文件模板编号',
    `function_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '功能名称',
    `template_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '导出文件名称',
    `path`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '导入模板路径',
    `api`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '导入API',
    `state`         tinyint(1)                                             DEFAULT '1' COMMENT '状态 1:有效 0:无效 ',
    `create_by`     bigint                                                 DEFAULT NULL COMMENT '创建人',
    `update_by`     bigint                                                 DEFAULT NULL COMMENT '最后修改人',
    `create_time`   datetime                                               DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime                                               DEFAULT NULL COMMENT '最后修改时间',
    `remove_flag`   tinyint(1)                                             DEFAULT NULL COMMENT '逻辑删除标记',
    `file_url`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模板下载地址',
    `attach_id`     bigint                                                 DEFAULT NULL COMMENT '附件表主键id',
    `version`       bigint NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='导入导出模板配置';

-- ----------------------------
-- Table structure for dict_config
-- ----------------------------
DROP TABLE IF EXISTS `dict_config`;
CREATE TABLE `dict_config`
(
    `id`          bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `conf_code`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置编号',
    `conf_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称',
    `conf_value`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '配置值',
    `state`       tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
    `conf_group`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '分组信息，同一分组下的配置编号唯一',
    `create_by`   bigint                                                 NOT NULL COMMENT '创建人',
    `create_time` datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`     bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `C_INX_CONF_CODE` (`conf_code`, `conf_group`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1820756045492916227
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统配置';

-- ----------------------------
-- Table structure for dict_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `dict_dictionary`;
CREATE TABLE `dict_dictionary`
(
    `id`          bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `dict_code`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段编码',
    `dict_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典名称',
    `dict_value`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字典值',
    `dict_type`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '字典类型',
    `state`       tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
    `parent_id`   bigint                                                 NOT NULL COMMENT '父级id',
    `depth`       int                                                    NOT NULL COMMENT '深度，用来记录树结构的层级关系',
    `path`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路径，用来记录树结构数据id的路径，用''.''分隔',
    `sorting`     int                                                    NOT NULL COMMENT '排序字段',
    `create_by`   bigint                                                 DEFAULT NULL COMMENT '创建人',
    `create_time` datetime                                               DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 DEFAULT NULL COMMENT '最后更新人',
    `update_time` datetime                                               DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             DEFAULT '0' COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`     bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INX_PARENT_ID` (`parent_id`) USING BTREE,
    KEY `INX_PATH` (`path`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1863851819267854339
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='数据字典';

-- ----------------------------
-- Table structure for dict_lang
-- ----------------------------
DROP TABLE IF EXISTS `dict_lang`;
CREATE TABLE `dict_lang`
(
    `id`          bigint                                                NOT NULL COMMENT '主键',
    `tenant_id`   bigint                                                 DEFAULT NULL COMMENT '租户ID',
    `module_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块名',
    `lang_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '语言代码(代码中用到的key)',
    `lang_zh_CN`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '简体中文信息',
    `lang_en_US`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '英文信息',
    `params`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '参数信息',
    `create_by`   bigint                                                NOT NULL COMMENT '创建人',
    `create_time` datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`   bigint                                                NOT NULL COMMENT '最后更新人',
    `update_time` datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INX_REGION_CODE` (`module_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='平台多语言配置';

-- ----------------------------
-- Table structure for dict_lang_field
-- ----------------------------
DROP TABLE IF EXISTS `dict_lang_field`;
CREATE TABLE `dict_lang_field`
(
    `id`                 bigint                                                  NOT NULL COMMENT '主键',
    `lang_field`         varchar(399) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言字段属性',
    `lang_field_desc`    varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '语言字段描述',
    `resource_id`        bigint                                                 DEFAULT NULL COMMENT '资源id',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `create_by`          bigint                                                  NOT NULL COMMENT '创建人',
    `create_time`        datetime                                                NOT NULL COMMENT '创建时间',
    `update_by`          bigint                                                  NOT NULL COMMENT '最后更新人',
    `update_time`        datetime                                                NOT NULL COMMENT '最后更新时间',
    `remove_flag`        tinyint(1)                                              NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `lang_type`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '类型：FIELD-字段,EXCEPTION-异常信息',
    `show_type`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '显示类型：TIPS：提示类型，FIELD：字段类型',
    `lang_field_tips_id` bigint                                                 DEFAULT NULL COMMENT '提示id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_LANG_FIELD_RESOURCE_ID` (`resource_id`) USING BTREE,
    KEY `INX_LANG_FIELD_FIELD` (`lang_field`) USING BTREE,
    KEY `INX_LANG_FIELD_LANG_TYPE` (`lang_type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='平台多语言字段定义';

-- ----------------------------
-- Table structure for dict_lang_field_translation
-- ----------------------------
DROP TABLE IF EXISTS `dict_lang_field_translation`;
CREATE TABLE `dict_lang_field_translation`
(
    `id`                     bigint                                                  NOT NULL COMMENT '主键',
    `lang_code`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言编码',
    `lang_field_translation` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '语言翻译',
    `lang_field_tips`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '语言提示',
    `lang_field_id`          bigint                                                 DEFAULT NULL COMMENT '语言字段id',
    `lang_field_tips_id`     bigint                                                 DEFAULT NULL COMMENT '语言提示id',
    `create_by`              bigint                                                  NOT NULL COMMENT '创建人',
    `create_time`            datetime                                                NOT NULL COMMENT '创建时间',
    `update_by`              bigint                                                  NOT NULL COMMENT '最后更新人',
    `update_time`            datetime                                                NOT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                              NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INX_LANG_FIELD_LANG_CODE` (`lang_code`) USING BTREE,
    KEY `IDX_LANG_FIELD_LANG_FIELD_ID` (`lang_field_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='平台多语言字段翻译';

-- ----------------------------
-- Table structure for dict_language
-- ----------------------------
DROP TABLE IF EXISTS `dict_language`;
CREATE TABLE `dict_language`
(
    `id`          bigint                                                 NOT NULL COMMENT '主键',
    `lang_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '语言编码',
    `module_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '模块编码',
    `title_code`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题编码',
    `title_name`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '标题名称',
    `create_by`   bigint                                                 NOT NULL COMMENT '创建人',
    `create_time` datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`   bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time` datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `INX_LANG_AND_TITLE_AND_MODULE` (`module_code`, `lang_code`, `title_code`) USING BTREE,
    KEY `INX_LANG_CODE` (`lang_code`) USING BTREE,
    KEY `INX_LANG_MODULE_CODE_AND_LANG_CODE` (`module_code`, `lang_code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='平台多语言配置';

-- ----------------------------
-- Table structure for dict_region
-- ----------------------------
DROP TABLE IF EXISTS `dict_region`;
CREATE TABLE `dict_region`
(
    `id`                 bigint                                                 NOT NULL,
    `region_code`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '区域编号',
    `region_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '区域名称',
    `parent_region_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '父级区域编码',
    `full_region_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域编码全',
    `full_region_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域编码全名称',
    `region_name_py`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域名称拼音',
    `short_name`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '简称',
    `short_name_py`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '短拼音',
    `longitude`          decimal(10, 6)                                         DEFAULT NULL COMMENT '经度',
    `latitude`           decimal(10, 6)                                         DEFAULT NULL COMMENT '纬度',
    `parent_id`          bigint                                                 DEFAULT NULL COMMENT '父级ID',
    `depth`              int                                                    DEFAULT NULL COMMENT '区域深度，记录字典的层级关系，0/国家,1/省，2/市，3/区县，4/乡镇',
    `path`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域路径，用来记录当前类别的id路径，用“.”分隔',
    `sorting`            double(18, 2)                                          DEFAULT NULL COMMENT '区域排序',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`        int                                                    NOT NULL COMMENT '逻辑删除标记',
    `code_path`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域路径，用来记录当前类别的编码路径，用“.”分隔',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注·',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for dict_region_cn
-- ----------------------------
DROP TABLE IF EXISTS `dict_region_cn`;
CREATE TABLE `dict_region_cn`
(
    `id`                 bigint                                                 NOT NULL COMMENT '主键',
    `region_code`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '区域编号',
    `region_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '区域名称',
    `parent_region_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '父级区域编码',
    `full_region_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域编码全',
    `full_region_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域编码全名称',
    `region_name_py`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域名称拼音',
    `short_name`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '简称',
    `short_name_py`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '短拼音',
    `longitude`          decimal(10, 6)                                         DEFAULT NULL COMMENT '经度',
    `latitude`           decimal(10, 6)                                         DEFAULT NULL COMMENT '纬度',
    `parent_id`          bigint                                                 DEFAULT NULL COMMENT '父级ID',
    `depth`              int                                                    DEFAULT NULL COMMENT '区域深度，记录字典的层级关系，0/国家,1/省，2/市，3/区县，4/乡镇',
    `path`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域路径，用来记录当前类别的id路径，用“.”分隔',
    `sorting`            double(18, 2)                                          DEFAULT NULL COMMENT '区域排序',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`        int                                                    NOT NULL COMMENT '逻辑删除标记',
    `code_path`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '区域路径，用来记录当前类别的编码路径，用“.”分隔',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `IDX_REGON_CODE` (`region_code`) USING BTREE,
    KEY `IDX_PARENT_ID` (`parent_id`) USING BTREE,
    KEY `IDX_REGION_NAME` (`region_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='行政区域';

-- ----------------------------
-- Table structure for dict_site
-- ----------------------------
DROP TABLE IF EXISTS `dict_site`;
CREATE TABLE `dict_site`
(
    `id`                      bigint     NOT NULL COMMENT '主键',
    `title`                   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '配置名称',
    `skin_name`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '皮肤值',
    `platform_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '系统名称',
    `login_logo`              varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '登录页logo',
    `login_background`        varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '登录页背景图',
    `main_logo`               varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '首页左上角logo',
    `main_name`               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '首页左上角文字',
    `login_remember_flag`     tinyint(1)                                              DEFAULT NULL COMMENT ' 是否需要显示“记住账号/密码” (1-是, 0-否)',
    `login_switch_lang_flag`  tinyint(1)                                              DEFAULT NULL COMMENT '是否需要显示“语言切换” (1-是, 0-否)',
    `login_need_captcha_flag` tinyint(1)                                              DEFAULT NULL COMMENT '是否需要验证码验证 (1-是, 0-否)',
    `login_captcha_url`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '验证码地址',
    `black_white_enable_flag` tinyint(1)                                              DEFAULT NULL COMMENT '是否开启黑白名单 (1-是, 0-否)',
    `watermark_enable_flag`   tinyint(1)                                              DEFAULT NULL COMMENT '是否开启安全水印 (1-是, 0-否)',
    `watermark_modules`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '安全水印作用模块,半角逗号分割',
    `language_enable_flag`    tinyint(1)                                              DEFAULT NULL COMMENT '是否启用多语言  (1-是, 0-否)',
    `lock_remain_time`        int                                                     DEFAULT NULL COMMENT '锁定持续时间',
    `lock_fail_count`         int                                                     DEFAULT NULL COMMENT '登录失败多少次后(1)触发自动锁定',
    `lock_fail_times`         int                                                     DEFAULT NULL COMMENT '连续多长时间(2)登录失败触发自动锁定',
    `default_lang`            varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '默认语言(  来源数据字典)',
    `passwd_days`             int                                                     DEFAULT NULL COMMENT '密码有效天数',
    `status`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '状态',
    `create_by`               bigint     NOT NULL COMMENT '创建人',
    `create_time`             datetime   NOT NULL COMMENT '创建时间',
    `update_by`               bigint     NOT NULL COMMENT '最后更新人',
    `update_time`             datetime   NOT NULL COMMENT '最后更新时间',
    `remove_flag`             tinyint(1) NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`                 bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INX_REGION_CODE` (`skin_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='站点配置';

-- ----------------------------
-- Table structure for dict_version
-- ----------------------------
DROP TABLE IF EXISTS `dict_version`;
CREATE TABLE `dict_version`
(
    `id`                   bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `version_no`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '版本号',
    `version_title`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本标题',
    `version_content`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '版本内容描述',
    `version_status`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本状态',
    `version_publisher`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本发布人',
    `terminal`             tinyint                                                DEFAULT NULL COMMENT '终端类型：0: 移动端，1: PC 端，枚举：TerminalEnum',
    `status`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'TO_BE_SUBMITTED' COMMENT '状态：TO_BE_SUBMITTED: 待提交，SUBMITTED: 已提交，枚举：SubmitStatusEnum',
    `server_address`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '服务器地址',
    `publish_announcement` tinyint(1)                                             DEFAULT '0' COMMENT '是否发布公告',
    `announcement_id`      bigint                                                 DEFAULT NULL COMMENT '公告id',
    `popup_flag`           tinyint(1)                                             DEFAULT '0' COMMENT '是否强制弹窗,0：否，1: 是',
    `start_time`           datetime                                               DEFAULT NULL COMMENT '开始时间',
    `end_time`             datetime                                               DEFAULT NULL COMMENT '结束时间',
    `publish_time`         datetime                                               DEFAULT NULL COMMENT '发版日期',
    `publish_scope`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '发布范围',
    `create_by`            bigint                                                 NOT NULL COMMENT '创建人',
    `create_by_name`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '创建人名称',
    `create_time`          datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`            bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_by_name`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '更新人名称',
    `update_time`          datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`          tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`              bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1821842540484104195
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统版本信息';

-- ----------------------------
-- Table structure for frp_app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `frp_app_access_config`;
CREATE TABLE `frp_app_access_config`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键ID',
    `app_id`              varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `name`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `service_type`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`         tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `note`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '备注',
    `version`             datetime                                               NOT NULL COMMENT '版本',
    `deleted_note`        varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '删除备注',
    `default_operator_id` bigint                                                      DEFAULT NULL COMMENT '默认操作员ID',
    `default_tenant_id`   bigint                                                      DEFAULT NULL COMMENT '默认租户ID',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                      DEFAULT NULL COMMENT '更新者ID',
    `update_time`         datetime                                               NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='app权限配置';

-- ----------------------------
-- Table structure for frp_duo_white_list
-- ----------------------------
DROP TABLE IF EXISTS `frp_duo_white_list`;
CREATE TABLE `frp_duo_white_list`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键ID',
    `name`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `ipaddress`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'IP地址',
    `note`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '备注',
    `version`      datetime                                               NOT NULL COMMENT '版本',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '删除备注',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                      DEFAULT NULL COMMENT '更新者ID',
    `update_time`  datetime                                               NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='二次验证白名单';

-- ----------------------------
-- Table structure for frp_partner
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner`;
CREATE TABLE `frp_partner`
(
    `id`                     bigint                                                 NOT NULL COMMENT '合作伙伴ID',
    `name`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴名称',
    `abbr_name`              varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴简称',
    `contact_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '联系人姓名',
    `contact_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人邮箱',
    `address_name`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址名称',
    `address_company`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '公司地址',
    `address_country`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '国家',
    `address_state`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '州/省',
    `address_city`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
    `address_zip_code`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '邮编',
    `address_addr1`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址1',
    `address_addr2`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址2',
    `address_addr3`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址3',
    `address_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址邮箱',
    `address_phone`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '地址电话',
    `address_note`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址备注',
    `partner_type`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴类型',
    `active_flag`            tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `version`                datetime                                               NOT NULL COMMENT '版本',
    `deleted_note`           varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '删除备注',
    `address_is_residential` tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '是否住宅地址',
    `create_by`              bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`            datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`              bigint                                                          DEFAULT NULL COMMENT '更新人',
    `update_time`            datetime                                               NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`            tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='企业伙伴';

-- ----------------------------
-- Table structure for frp_partner_app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner_app_access_config`;
CREATE TABLE `frp_partner_app_access_config`
(
    `id`              bigint                                                 NOT NULL COMMENT '配置ID',
    `name`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称',
    `partner_user_id` bigint                                                 NOT NULL COMMENT '合作伙伴用户ID',
    `app_id`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`      varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `service_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`     tinyint(1)                                             NOT NULL COMMENT '是否激活',
    `note`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '备注',
    `version`         datetime                                               NOT NULL COMMENT '版本',
    `deleted_note`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '删除备注',
    `tenant_id`       bigint                                                 NOT NULL COMMENT '租户ID',
    `create_by`       bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`     datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`       bigint                                                      DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime                                               NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`     tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `fk_partner_app_access_config_partner_user_partner_user_id` (`partner_user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='企业伙伴app权限配置';

-- ----------------------------
-- Table structure for frp_partner_ship
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner_ship`;
CREATE TABLE `frp_partner_ship`
(
    `id`                  bigint                                                NOT NULL COMMENT '关系ID',
    `provider_partner_id` bigint                                                NOT NULL COMMENT '提供者合作伙伴ID',
    `client_partner_id`   bigint                                                NOT NULL COMMENT '客户合作伙伴ID',
    `provider_type`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '提供者类型',
    `active_flag`         tinyint(1)                                            NOT NULL COMMENT '是否激活',
    `note`                varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin     DEFAULT NULL COMMENT '备注',
    `version`             datetime                                              NOT NULL COMMENT '版本',
    `create_by`           bigint                                                NOT NULL COMMENT '创建人',
    `create_time`         datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                     DEFAULT NULL COMMENT '更新人',
    `update_time`         datetime                                              NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`         tinyint(1)                                            NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `fk_partner_ship_partner_client_partner_id` (`client_partner_id`) USING BTREE,
    KEY `fk_partner_ship_partner_provider_partner_id` (`provider_partner_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='企业关系';

-- ----------------------------
-- Table structure for frp_partner_user
-- ----------------------------
DROP TABLE IF EXISTS `frp_partner_user`;
CREATE TABLE `frp_partner_user`
(
    `id`           bigint                                                 NOT NULL COMMENT '用户ID',
    `user_id`      bigint                                                 NOT NULL COMMENT '用户ID',
    `first_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '名字',
    `last_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '姓氏',
    `email`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电子邮件',
    `phone`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin       DEFAULT NULL COMMENT '电话号码',
    `active_flag`  tinyint(1)                                             NOT NULL COMMENT '激活标志',
    `version`      datetime                                               NOT NULL COMMENT '版本',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '删除备注',
    `tenant_id`    bigint                                                 NOT NULL COMMENT '租户ID',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                      DEFAULT NULL COMMENT '更新者ID',
    `update_time`  datetime                                               NULL DEFAULT NULL COMMENT '更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='企业用户';

-- ----------------------------
-- Table structure for frp_profile_partner
-- ----------------------------
DROP TABLE IF EXISTS `frp_profile_partner`;
CREATE TABLE `frp_profile_partner`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键ID',
    `tenant_id`     bigint                                                 NOT NULL COMMENT '租户ID',
    `version`       datetime                                               NOT NULL COMMENT '版本时间戳',
    `deleted_note`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '删除备注',
    `service_type`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `category_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分类代码',
    `category_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '分类描述',
    `value_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '值类型',
    `name`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `value`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
    `note`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '备注',
    `sort`          int                                                    NOT NULL COMMENT '排序',
    `active_flag`   tinyint(1)                                             NOT NULL COMMENT '激活标志',
    `code`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '代码',
    `create_by`     bigint                                                 NOT NULL COMMENT '创建者ID',
    `create_time`   datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                          DEFAULT NULL COMMENT '更新者ID',
    `update_time`   datetime                                               NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='企业伙伴档案';

-- ----------------------------
-- Table structure for frp_tenant_partner
-- ----------------------------
DROP TABLE IF EXISTS `frp_tenant_partner`;
CREATE TABLE `frp_tenant_partner`
(
    `id`          bigint     NOT NULL,
    `tenant_id`   bigint          DEFAULT NULL,
    `partner_id`  bigint          DEFAULT NULL,
    `create_by`   bigint     NOT NULL,
    `create_time` datetime   NOT NULL,
    `update_by`   bigint          DEFAULT NULL,
    `update_time` datetime   NULL DEFAULT NULL,
    `remove_flag` tinyint(1) NOT NULL,
    `version`     bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='租户企业对应信息';

-- ----------------------------
-- Table structure for log_alert_record
-- ----------------------------
DROP TABLE IF EXISTS `log_alert_record`;
CREATE TABLE `log_alert_record`
(
    `id`            bigint     NOT NULL COMMENT '主键id',
    `source`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '来源',
    `keyword`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关健字',
    `module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '功能模块',
    `function_desc` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务功能说明',
    `request_url`   varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求url地址',
    `request_body`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求参数内容',
    `alert_info`    longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '预警信息',
    `create_by`     bigint     NOT NULL COMMENT '创建人',
    `update_by`     bigint     NOT NULL COMMENT '更新人',
    `create_time`   datetime   NOT NULL COMMENT '创建时间',
    `update_time`   datetime   NOT NULL COMMENT '修改时间',
    `remove_flag`   tinyint(1) NOT NULL COMMENT '逻辑删除标记',
    `version`       bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_SOURCE` (`source`) USING BTREE,
    KEY `INDEX_KEYWORD` (`keyword`) USING BTREE,
    KEY `INDEX_MODULE` (`module`) USING BTREE,
    KEY `INDEX_REQUEST_URL` (`request_url`) USING BTREE,
    KEY `INDEX_CREATE_TIME` (`create_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='预警日志数据';

-- ----------------------------
-- Table structure for log_error_record
-- ----------------------------
DROP TABLE IF EXISTS `log_error_record`;
CREATE TABLE `log_error_record`
(
    `id`                bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `request_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求用户',
    `request_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求用户角色信息',
    `request_time`      datetime                                               NOT NULL COMMENT '请求时间',
    `request_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求URL',
    `request_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求ip地址',
    `request_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '请求来源',
    `status`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应状态',
    `error_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '错误信息',
    `create_by`         bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`         bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`       datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`       datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`           bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE,
    KEY `INDEX_request_time` (`request_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='错误日志记录信息';

-- ----------------------------
-- Table structure for log_login_record
-- ----------------------------
DROP TABLE IF EXISTS `log_login_record`;
CREATE TABLE `log_login_record`
(
    `id`              bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `login_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '登录用户',
    `login_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户角色信息',
    `login_time`      datetime                                               NOT NULL COMMENT '登录时间',
    `login_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求url',
    `login_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录ip',
    `login_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '登录来源，用来标记是PC端还是小程序',
    `status`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '状态，成功或失败',
    `error_code`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '错误信息',
    `create_by`       bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`       bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`     datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`     datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`     tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`         bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE,
    KEY `INDEX_login_time` (`login_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='登录日志记录';

-- ----------------------------
-- Table structure for log_operation_record
-- ----------------------------
DROP TABLE IF EXISTS `log_operation_record`;
CREATE TABLE `log_operation_record`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `operation_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '操作用户',
    `operation_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '操作用户角色',
    `operation_time`      datetime                                               NOT NULL COMMENT '操作时间',
    `operation_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '操作URL',
    `operation_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作ip',
    `operation_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '操作来源，用来标记是PC端还是小程序',
    `operation_module`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'log' COMMENT '操作模块',
    `operation_function`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'default' COMMENT '操作功能',
    `operation_type`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'online' COMMENT '操作类型',
    `status`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '状态，成功或失败',
    `error_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '错误信息',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`           bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`         datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `operation_user_id`   bigint                                                          DEFAULT NULL COMMENT '操作用户id',
    `version`             bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE,
    KEY `INDEX_operation_time` (`operation_time`) USING BTREE,
    KEY `idx_user_id` (`operation_user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='操作日志记录表';

-- ----------------------------
-- Table structure for log_record_search
-- ----------------------------
DROP TABLE IF EXISTS `log_record_search`;
CREATE TABLE `log_record_search`
(
    `id`                 bigint                                                 NOT NULL COMMENT '主键id',
    `record_type`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '记录类型：REQUEST,RESPONSE',
    `operation_function` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作功能',
    `operation_module`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作模块',
    `request_path`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求路径',
    `keyword`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '查询关键字',
    `record_id`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`        tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_OPERATION_FUNCTION` (`operation_function`) USING BTREE,
    KEY `INDEX_KEYWORD` (`keyword`) USING BTREE,
    KEY `INDEX_REQUEST_PATH` (`request_path`) USING BTREE,
    KEY `INDEX_REQUEST_MODULE` (`operation_module`) USING BTREE,
    KEY `INDEX_RECORD_ID` (`record_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='日志记录查询信息';

-- ----------------------------
-- Table structure for log_request_data
-- ----------------------------
DROP TABLE IF EXISTS `log_request_data`;
CREATE TABLE `log_request_data`
(
    `id`                 bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `request_user`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求用户',
    `request_user_role`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求用户角色',
    `request_time`       datetime                                               NOT NULL COMMENT '请求时间',
    `request_schema`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '请求Schema',
    `request_method`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '请求方法',
    `request_media_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求媒介类型',
    `request_url`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求url地址',
    `request_path`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求的api路径',
    `request_ip`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求的真实ip地址',
    `request_source`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '请求来源',
    `request_id`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求id',
    `request_headers`    mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求头信息',
    `request_body`       longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求参数内容',
    `target_server`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目标服务',
    `create_by`          bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`          bigint                                                 NOT NULL COMMENT '更新人',
    `create_time`        datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`        datetime                                               NOT NULL COMMENT '修改时间',
    `remove_flag`        tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`            bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE,
    KEY `INDEX_request_time` (`request_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='请求明细数据';

-- ----------------------------
-- Table structure for log_request_path_pattern
-- ----------------------------
DROP TABLE IF EXISTS `log_request_path_pattern`;
CREATE TABLE `log_request_path_pattern`
(
    `id`                   bigint                                                 NOT NULL COMMENT '主键id',
    `request_path`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求路径',
    `request_description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求描述',
    `operation_function`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作功能，数据字典动态值：OPERATION_MODULE',
    `operation_module`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作模块，数据字典动态值：OPERATION_MODULE',
    `operation_type`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '操作类型，数据字典动态值：OPERATION_TYPE',
    `request_source`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '请求来源，取值数据字典：LOGGER_SOURCE',
    `record_response_data` tinyint(1)                                             NOT NULL COMMENT '用来标记是否记录响应数据，0 - 不记录，1 - 记录',
    `state`                tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `request_key`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求信息的搜索key，多个用'',''隔开',
    `response_key`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '响应信息的搜索key，多个用'',''隔开',
    `create_by`            bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`            bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`          datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`          datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`          tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`              bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='请求路径匹配规则';

-- ----------------------------
-- Table structure for log_request_used_time
-- ----------------------------
DROP TABLE IF EXISTS `log_request_used_time`;
CREATE TABLE `log_request_used_time`
(
    `id`                bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `request_user`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求用户',
    `request_user_role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求用户角色信息',
    `request_url`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NOT NULL COMMENT '请求URL',
    `request_ip`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求ip地址',
    `request_source`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '请求来源',
    `request_time`      datetime                                               NOT NULL COMMENT '请求时间',
    `response_time`     datetime                                               NOT NULL COMMENT '响应时间',
    `used_time`         bigint                                                 NOT NULL COMMENT '请求用时(毫秒)',
    `status`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应状态',
    `error_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '错误编码',
    `error_message`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '错误信息',
    `create_by`         bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`         bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`       datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`       datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`           bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE,
    KEY `INDEX_request_time` (`request_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='请求时长日志信息';

-- ----------------------------
-- Table structure for log_response_data
-- ----------------------------
DROP TABLE IF EXISTS `log_response_data`;
CREATE TABLE `log_response_data`
(
    `id`                     bigint                                                NOT NULL COMMENT '主键id',
    `record_id`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `response_time`          datetime                                              NOT NULL COMMENT '响应时间',
    `response_status`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '响应状态',
    `response_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '响应编号',
    `response_reason_phrase` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '响应描述简语',
    `response_message`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '响应信息',
    `response_headers`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '响应头信息',
    `response_body`          longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '响应数据',
    `create_by`              bigint                                                NOT NULL COMMENT '创建人',
    `update_by`              bigint                                                NOT NULL COMMENT '最后更新人',
    `create_time`            datetime                                              NOT NULL COMMENT '创建时间',
    `update_time`            datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记',
    `version`                bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='响应明细数据';

-- ----------------------------
-- Table structure for log_third_record
-- ----------------------------
DROP TABLE IF EXISTS `log_third_record`;
CREATE TABLE `log_third_record`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键id',
    `record_id`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '日志记录id，UUID，用来标记记录、请求、响应的关联关系',
    `operation_module`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '操作模块，数据字典动态值：OPERATION_MODULE',
    `request_path`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求路径',
    `request_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求描述',
    `request_user`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '请求用户',
    `request_ip`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求ip',
    `request_time`        datetime                                               NOT NULL COMMENT '请求时间',
    `used_time`           bigint                                                 NOT NULL COMMENT '用时',
    `response_status`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '状态，成功或失败',
    `response_code`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '响应编码',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建人',
    `update_by`           bigint                                                 NOT NULL COMMENT '最后更新人',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_time`         datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记',
    `version`             bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INDEX_record_id` (`record_id`) USING BTREE,
    KEY `INDEX_REQUEST_DESCRIPTION` (`request_description`) USING BTREE,
    KEY `IDX_REQUEST_PATH` (`request_path`) USING BTREE,
    KEY `IDX_REQUEST_TIME` (`request_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='第三方请求日志记录';

-- ----------------------------
-- Table structure for number_generate_config
-- ----------------------------
DROP TABLE IF EXISTS `number_generate_config`;
CREATE TABLE `number_generate_config`
(
    `id`            bigint                                                NOT NULL COMMENT '主键',
    `code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
    `prefix_code`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码前缀',
    `date_format`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '时间格式',
    `stream_length` int                                                            DEFAULT NULL COMMENT '流水号长度',
    `current_num`   bigint                                                         DEFAULT '0' COMMENT '当前数',
    `step_size`     bigint                                                NOT NULL DEFAULT '1000' COMMENT '最大可用值步长',
    `reset_flag`    tinyint                                                        DEFAULT '0' COMMENT '是否每天重置',
    `description`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注',
    `create_by`     bigint                                                NOT NULL COMMENT '创建人',
    `create_time`   datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                NOT NULL COMMENT '最后更新人',
    `update_time`   datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `tenant_id`     mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
    `version`       bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INX_REGION_CODE` (`code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='编码生成配置表';

-- ----------------------------
-- Table structure for number_generate_record
-- ----------------------------
DROP TABLE IF EXISTS `number_generate_record`;
CREATE TABLE `number_generate_record`
(
    `id`          bigint                                                NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
    `prefix_code` varchar(255) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '编码前缀',
    `max_num`     bigint                                                         DEFAULT NULL COMMENT '最大数',
    `create_by`   bigint                                                NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` datetime                                                       DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint                                                NOT NULL DEFAULT '0' COMMENT '最后更新人',
    `update_time` datetime                                                       DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint(1)                                            NOT NULL DEFAULT '0' COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`     bigint                                                NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
    `tenant_id`   bigint                                                         DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1893141822788419587
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='编码生成记录';

-- ----------------------------
-- Table structure for partner_app_access_config
-- ----------------------------
DROP TABLE IF EXISTS `partner_app_access_config`;
CREATE TABLE `partner_app_access_config`
(
    `id`              bigint                                                 NOT NULL COMMENT '配置ID',
    `name`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称',
    `partner_user_id` bigint                                                 NOT NULL COMMENT '合作伙伴用户ID',
    `app_id`          varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用ID',
    `app_secret`      varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '应用密钥',
    `service_type`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务类型',
    `active_flag`     tinyint(1)                                             NOT NULL DEFAULT '1' COMMENT '是否激活',
    `note`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '备注',
    `version`         bigint                                                 NOT NULL DEFAULT '0' COMMENT '版本',
    `deleted_note`    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '删除备注',
    `tenant_id`       bigint                                                 NOT NULL COMMENT '租户ID',
    `create_by`       bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`     datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`       bigint                                                          DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime                                               NULL     DEFAULT NULL COMMENT '更新时间',
    `remove_flag`     tinyint(1)                                             NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `fk_partner_app_access_config_partner_user_partner_user_id` (`partner_user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='企业伙伴app权限配置';

-- ----------------------------
-- Table structure for upms_business_role
-- ----------------------------
DROP TABLE IF EXISTS `upms_business_role`;
CREATE TABLE `upms_business_role`
(
    `id`            bigint                                                NOT NULL COMMENT '主键',
    `business_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务数据类型，可以是用户、组织、职位、分组',
    `business_id`   bigint                                                NOT NULL COMMENT '业务数据id',
    `role_id`       bigint                                                NOT NULL COMMENT '角色id',
    `create_by`     bigint                                                NOT NULL COMMENT '创建人',
    `create_time`   datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                NOT NULL COMMENT '最后更新人',
    `update_time`   datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`       bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_BUSINESS_ID` (`business_id`) USING BTREE,
    KEY `IDX_BUSINESS_TYPE` (`business_type`) USING BTREE,
    KEY `IDX_ROLE_ID` (`role_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='业务角色信息';

-- ----------------------------
-- Table structure for upms_icon_library
-- ----------------------------
DROP TABLE IF EXISTS `upms_icon_library`;
CREATE TABLE `upms_icon_library`
(
    `id`          bigint   NOT NULL,
    `name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图标名称',
    `icon_url`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图标url',
    `state`       tinyint                                                DEFAULT '1' COMMENT '状态.1-有效，0-无效',
    `create_by`   decimal(20, 0)                                         DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_by`   decimal(20, 0)                                         DEFAULT NULL COMMENT '最后更新人',
    `update_time` datetime NOT NULL COMMENT '最后更新时间',
    `remove_flag` tinyint  NOT NULL                                      DEFAULT '0' COMMENT '逻辑删除标记',
    `version`     bigint   NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for upms_permissions
-- ----------------------------
DROP TABLE IF EXISTS `upms_permissions`;
CREATE TABLE `upms_permissions`
(
    `id`               bigint                                                 NOT NULL COMMENT '主键',
    `permissions_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限编号',
    `permissions_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限名称',
    `permissions_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '权限类型',
    `href`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '链接地址',
    `terminal`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '终端PC 、Mobile',
    `icon`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图标',
    `icon_type`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图标类型，参见字典【ICON_TYPE】',
    `state`            tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
    `parent_id`        bigint                                                 NOT NULL COMMENT '父级id',
    `depth`            int                                                    NOT NULL COMMENT '深度，用来记录树结构的层级关系',
    `path`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路径，用来记录树结构数据id的路径，用''.''分隔',
    `route_path`       json                                                   DEFAULT NULL COMMENT '可访问的路由路径',
    `sorting`          int                                                    NOT NULL COMMENT '排序字段',
    `create_by`        bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`      datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`        bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`      datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`      tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`          bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    `deleted_note`     varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `C_INX_DICT_CODE` (`permissions_code`) USING BTREE,
    KEY `INX_PARENT_ID` (`parent_id`) USING BTREE,
    KEY `INX_PATH` (`path`) USING BTREE,
    KEY `PERMISSIONS_TYPE` (`permissions_type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统权限信息';

-- ----------------------------
-- Table structure for upms_role
-- ----------------------------
DROP TABLE IF EXISTS `upms_role`;
CREATE TABLE `upms_role`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键',
    `role_code`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '角色编号',
    `role_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色名称',
    `role_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '角色名称英文',
    `description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
    `state`        tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `role_group`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '分组信息，同一分组下的配置编号唯一',
    `sorting`      int                                                    NOT NULL COMMENT '排序字段',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`  datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`      bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `INX_REGION_CODE` (`role_code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统角色信息';

-- ----------------------------
-- Table structure for upms_role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `upms_role_permissions`;
CREATE TABLE `upms_role_permissions`
(
    `id`             bigint                                                NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id`        bigint                                                NOT NULL COMMENT '角色id',
    `permissions_id` bigint                                                NOT NULL COMMENT '权限id',
    `terminal`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '1' COMMENT '终端，PC 、Mobile',
    `create_by`      bigint                                                NOT NULL COMMENT '创建人',
    `create_time`    datetime                                              NOT NULL COMMENT '创建时间',
    `update_by`      bigint                                                NOT NULL COMMENT '最后更新人',
    `update_time`    datetime                                              NOT NULL COMMENT '最后更新时间',
    `remove_flag`    tinyint(1)                                            NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`        bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `C_INX_CONF_CODE` (`role_id`) USING BTREE,
    KEY `IDX_PERMISSIONS_ID` (`permissions_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1892122376885829651
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统角色权限信息';

-- ----------------------------
-- Table structure for upms_route
-- ----------------------------
DROP TABLE IF EXISTS `upms_route`;
CREATE TABLE `upms_route`
(
    `id`             bigint                                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `server_name`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '所属服务',
    `request_method` varchar(255) COLLATE utf8mb4_bin                       NOT NULL COMMENT '请求方式',
    `path`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '接口路径',
    `ignore_flag`    tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '是否忽略权限控制（0不需要，1需要）',
    `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '备注',
    `create_by`      bigint                                                 NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`    datetime                                                        DEFAULT NULL COMMENT '创建时间',
    `update_by`      bigint                                                 NOT NULL DEFAULT '0' COMMENT '最后更新人',
    `update_time`    datetime                                                        DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`    tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`        bigint                                                 NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 38270
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='接口权限路由';

-- ----------------------------
-- Table structure for upms_tenant
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant`;
CREATE TABLE `upms_tenant`
(
    `id`                     bigint                                                 NOT NULL COMMENT '主键',
    `tenant_code`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '编码',
    `state`                  tinyint                                                         DEFAULT NULL COMMENT '状态 1:有效 0:无效',
    `create_by`              bigint                                                          DEFAULT NULL COMMENT '创建人',
    `create_time`            datetime                                                        DEFAULT NULL COMMENT '创建时间',
    `update_by`              bigint                                                          DEFAULT NULL COMMENT '最后更新人',
    `update_time`            datetime                                                        DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                                      DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `tenant_logo`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '租户logo',
    `create_by_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '创建人名称',
    `update_by_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '更新人名称',
    `name`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴名称',
    `abbr_name`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '合作伙伴简称',
    `contact_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '联系人姓名',
    `contact_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人邮箱',
    `address_name`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址名称',
    `address_company`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '公司地址',
    `address_country`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '国家',
    `address_state`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '州/省',
    `address_city`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
    `address_zip_code`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '邮编',
    `address_addr1`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址1',
    `address_addr2`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址2',
    `address_addr3`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址3',
    `address_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址邮箱',
    `address_phone`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '地址电话',
    `address_note`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址备注',
    `partner_type`           json                                                   NOT NULL COMMENT '合作伙伴类型',
    `version`                bigint                                                 NOT NULL COMMENT '版本',
    `deleted_note`           varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '删除备注',
    `address_is_residential` tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '是否住宅地址',
    `default_flag`           tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '是否默认租户',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_CODE` (`tenant_code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='租户';

-- ----------------------------
-- Table structure for upms_tenant_company
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant_company`;
CREATE TABLE `upms_tenant_company`
(
    `id`                bigint     NOT NULL COMMENT '主键',
    `tenant_id`         bigint                                                 DEFAULT NULL COMMENT '租户id',
    `company_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户企业名称',
    `tax_num`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '税号',
    `company_address`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单位地址',
    `mobile`            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '手机号',
    `settlement_bank`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '开户行',
    `bank_account`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行账户',
    `scene_picture`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '场所照片',
    `bank_card_picture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行卡照片',
    `state`             tinyint(1) NOT NULL                                    DEFAULT '0' COMMENT '有效状态，0:无效，1:有效',
    `create_by`         bigint                                                 DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime                                               DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint                                                 DEFAULT NULL COMMENT '最后更新人',
    `update_time`       datetime                                               DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1)                                             DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`           bigint     NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_TENANT_COMPANY_TENANT_ID` (`tenant_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='租户经营信息';

-- ----------------------------
-- Table structure for upms_tenant_config
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant_config`;
CREATE TABLE `upms_tenant_config`
(
    `id`                bigint NOT NULL COMMENT '主键',
    `credit_limit_flag` tinyint    DEFAULT NULL COMMENT '白条额度控制',
    `back_invoice_flag` tinyint    DEFAULT NULL COMMENT '代反向开票',
    `tenant_id`         bigint     DEFAULT NULL COMMENT '租户id',
    `create_by`         bigint     DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime   DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint     DEFAULT NULL COMMENT '最后更新人',
    `update_time`       datetime   DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`       tinyint(1) DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`           bigint NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_TENANT_CONFIG_TENANT_ID` (`tenant_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='租户配置';

-- ----------------------------
-- Table structure for upms_tenant_copy1
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant_copy1`;
CREATE TABLE `upms_tenant_copy1`
(
    `id`                     bigint                                                 NOT NULL COMMENT '主键',
    `tenant_code`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '编码',
    `state`                  tinyint                                                         DEFAULT NULL COMMENT '状态 1:有效 0:无效',
    `create_by`              bigint                                                          DEFAULT NULL COMMENT '创建人',
    `create_time`            datetime                                                        DEFAULT NULL COMMENT '创建时间',
    `update_by`              bigint                                                          DEFAULT NULL COMMENT '最后更新人',
    `update_time`            datetime                                                        DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`            tinyint(1)                                                      DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `tenant_logo`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '租户logo',
    `create_by_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '创建人名称',
    `update_by_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '更新人名称',
    `name`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴名称',
    `abbr_name`              varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴简称',
    `contact_name`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '联系人姓名',
    `contact_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人邮箱',
    `address_name`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址名称',
    `address_company`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '公司地址',
    `address_country`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '国家',
    `address_state`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '州/省',
    `address_city`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
    `address_zip_code`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '邮编',
    `address_addr1`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址1',
    `address_addr2`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址2',
    `address_addr3`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址3',
    `address_email`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址邮箱',
    `address_phone`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '地址电话',
    `address_note`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '地址备注',
    `partner_type`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '合作伙伴类型',
    `version`                bigint                                                 NOT NULL COMMENT '版本',
    `deleted_note`           varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '删除备注',
    `address_is_residential` tinyint(1)                                             NOT NULL DEFAULT '0' COMMENT '是否住宅地址',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_CODE` (`tenant_code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='租户';

-- ----------------------------
-- Table structure for upms_tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `upms_tenant_info`;
CREATE TABLE `upms_tenant_info`
(
    `id`            bigint NOT NULL COMMENT '主键',
    `tenant_code`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '编码',
    `tenant_name`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '名称',
    `tenant_logo`   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户logo',
    `tenant_type`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户类型:数据字典(TENANT_TYPE)',
    `tenant_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '状态:数据字典(STATE)',
    `create_by`     bigint                                                 DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime                                               DEFAULT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 DEFAULT NULL COMMENT '最后更新人',
    `update_time`   datetime                                               DEFAULT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                             DEFAULT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`       bigint NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_TENANT_INFO_CODE` (`tenant_code`) USING BTREE,
    FULLTEXT KEY `IDX_TENANT_INFO_TENANT_NAME` (`tenant_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='租户';

-- ----------------------------
-- Table structure for upms_user
-- ----------------------------
DROP TABLE IF EXISTS `upms_user`;
CREATE TABLE `upms_user`
(
    `id`            bigint                                                 NOT NULL COMMENT '主键',
    `user_type`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '用户类型',
    `tenant_id`     bigint                                                          DEFAULT NULL COMMENT '租户id',
    `terminal_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '终端类型',
    `name`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户的姓名',
    `nick_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户昵称',
    `first_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '用户的名字',
    `last_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '用户的姓氏',
    `password`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户密码',
    `salt`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '加密盐值',
    `state`         tinyint(1)                                             NOT NULL DEFAULT '1' COMMENT '数据状态',
    `email`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '邮箱',
    `phone`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '手机号',
    `avatar`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '用户头像',
    `signature`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '个性签名',
    `remark`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '备注',
    `create_by`     bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`   datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`     bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`   datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`   tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`       bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `C_INX_CONF_CODE` (`user_type`) USING BTREE,
    KEY `IDX_UPMS_USER_TENANT_ID` (`tenant_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='系统用户信息';

-- ----------------------------
-- Table structure for upms_user_account
-- ----------------------------
DROP TABLE IF EXISTS `upms_user_account`;
CREATE TABLE `upms_user_account`
(
    `id`             bigint                                                 NOT NULL COMMENT '主键',
    `user_id`        bigint                                                 NOT NULL COMMENT '用户id',
    `user_account`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户账号',
    `account_type`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '帐号类型',
    `state`          tinyint(1)                                             NOT NULL COMMENT '数据状态，0 - 无效，1 - 有效',
    `description`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
    `create_by`      bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`    datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`      bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`    datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`    tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `login_type`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '登陆类型',
    `login_ip`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '登陆ip',
    `login_terminal` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '登陆终端',
    `login_date`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '登陆时间',
    `expired_date`   datetime                                               DEFAULT NULL COMMENT '账号有效期时间',
    `active_date`    datetime                                               DEFAULT NULL COMMENT '活跃时间',
    `version`        bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_USER_ACCOUNT` (`user_account`) USING BTREE,
    KEY `IDX_UPMS_USER_ACCOUNT_USER_ID` (`user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='用户账号信息';

-- ----------------------------
-- Table structure for upms_user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `upms_user_permissions`;
CREATE TABLE `upms_user_permissions`
(
    `id`                  bigint                                                 NOT NULL COMMENT '主键',
    `user_id`             bigint                                                 NOT NULL COMMENT '用户id',
    `data_scope`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'SELF' COMMENT '数据范围（可看全部 ALL ，看个人 SELF， 看组织 ORGNIZATION）',
    `data_scope_dept_ids` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT '' COMMENT '数据范围(指定部门数组)',
    `create_by`           bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`         datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`           bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`         datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`         tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`             bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='用户权限信息';

-- ----------------------------
-- Table structure for upms_user_tenant
-- ----------------------------
DROP TABLE IF EXISTS `upms_user_tenant`;
CREATE TABLE `upms_user_tenant`
(
    `id`           bigint                                                 NOT NULL COMMENT '主键',
    `user_id`      bigint                                                 DEFAULT NULL COMMENT '用户id',
    `tenant_id`    bigint                                                 DEFAULT NULL COMMENT '租户id',
    `first_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '名字',
    `last_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '姓氏',
    `email`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电子邮件',
    `phone`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '电话号码',
    `active_flag`  tinyint(1)                                             NOT NULL COMMENT '激活标志',
    `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除备注',
    `create_by`    bigint                                                 NOT NULL COMMENT '创建人',
    `create_time`  datetime                                               NOT NULL COMMENT '创建时间',
    `update_by`    bigint                                                 NOT NULL COMMENT '最后更新人',
    `update_time`  datetime                                               NOT NULL COMMENT '最后更新时间',
    `remove_flag`  tinyint(1)                                             NOT NULL COMMENT '逻辑删除标记，0 - 未删除，1 - 已删除',
    `version`      bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `IDX_USER_TENANT_USER_ID` (`user_id`) USING BTREE,
    KEY `IDX_USER_TENANT_TENANT_ID` (`tenant_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='用户租户信息';

-- ----------------------------
-- Table structure for usertemp
-- ----------------------------
DROP TABLE IF EXISTS `usertemp`;
CREATE TABLE `usertemp`
(
    `id`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `user_type`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `user_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `no`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `version`      bigint NOT NULL COMMENT '乐观锁版本号'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_group
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_group`;
CREATE TABLE `xxl_job_group`
(
    `id`           int                                                   NOT NULL AUTO_INCREMENT,
    `app_name`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '执行器AppName',
    `title`        varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '执行器名称',
    `address_type` tinyint                                               NOT NULL DEFAULT '0' COMMENT '执行器地址类型：0=自动注册、1=手动录入',
    `address_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '执行器地址列表，多地址逗号分隔',
    `update_time`  datetime                                                       DEFAULT NULL,
    `version`      bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 8
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_info
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_info`;
CREATE TABLE `xxl_job_info`
(
    `id`                        int                                                    NOT NULL AUTO_INCREMENT,
    `job_group`                 int                                                    NOT NULL COMMENT '执行器主键ID',
    `job_desc`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `add_time`                  datetime                                                        DEFAULT NULL,
    `update_time`               datetime                                                        DEFAULT NULL,
    `author`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '作者',
    `alarm_email`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '报警邮件',
    `schedule_type`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
    `schedule_conf`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
    `misfire_strategy`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
    `executor_route_strategy`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '执行器路由策略',
    `executor_handler`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '执行器任务参数',
    `executor_block_strategy`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin           DEFAULT NULL COMMENT '阻塞处理策略',
    `executor_timeout`          int                                                    NOT NULL DEFAULT '0' COMMENT '任务执行超时时间，单位秒',
    `executor_fail_retry_count` int                                                    NOT NULL DEFAULT '0' COMMENT '失败重试次数',
    `glue_type`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT 'GLUE类型',
    `glue_source`               mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT 'GLUE源代码',
    `glue_remark`               varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'GLUE备注',
    `glue_updatetime`           datetime                                                        DEFAULT NULL COMMENT 'GLUE更新时间',
    `child_jobid`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
    `trigger_status`            tinyint                                                NOT NULL DEFAULT '0' COMMENT '调度状态：0-停止，1-运行',
    `trigger_last_time`         bigint                                                 NOT NULL DEFAULT '0' COMMENT '上次调度时间',
    `trigger_next_time`         bigint                                                 NOT NULL DEFAULT '0' COMMENT '下次调度时间',
    `version`                   bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 16
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_lock
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_lock`;
CREATE TABLE `xxl_job_lock`
(
    `lock_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '锁名称',
    `version`   bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`lock_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_log
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log`;
CREATE TABLE `xxl_job_log`
(
    `id`                        bigint  NOT NULL AUTO_INCREMENT,
    `job_group`                 int     NOT NULL COMMENT '执行器主键ID',
    `job_id`                    int     NOT NULL COMMENT '任务，主键ID',
    `executor_address`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
    `executor_handler`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '执行器任务参数',
    `executor_sharding_param`   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
    `executor_fail_retry_count` int     NOT NULL                                       DEFAULT '0' COMMENT '失败重试次数',
    `trigger_time`              datetime                                               DEFAULT NULL COMMENT '调度-时间',
    `trigger_code`              int     NOT NULL COMMENT '调度-结果',
    `trigger_msg`               text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '调度-日志',
    `handle_time`               datetime                                               DEFAULT NULL COMMENT '执行-时间',
    `handle_code`               int     NOT NULL COMMENT '执行-状态',
    `handle_msg`                text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '执行-日志',
    `alarm_status`              tinyint NOT NULL                                       DEFAULT '0' COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
    `version`                   bigint  NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `I_trigger_time` (`trigger_time`) USING BTREE,
    KEY `I_handle_code` (`handle_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 519
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_log_report
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log_report`;
CREATE TABLE `xxl_job_log_report`
(
    `id`            int    NOT NULL AUTO_INCREMENT,
    `trigger_day`   datetime        DEFAULT NULL COMMENT '调度-时间',
    `running_count` int    NOT NULL DEFAULT '0' COMMENT '运行中-日志数量',
    `suc_count`     int    NOT NULL DEFAULT '0' COMMENT '执行成功-日志数量',
    `fail_count`    int    NOT NULL DEFAULT '0' COMMENT '执行失败-日志数量',
    `update_time`   datetime        DEFAULT NULL,
    `version`       bigint NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_trigger_day` (`trigger_day`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 142
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_logglue
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_logglue`;
CREATE TABLE `xxl_job_logglue`
(
    `id`          int                                                    NOT NULL AUTO_INCREMENT,
    `job_id`      int                                                    NOT NULL COMMENT '任务，主键ID',
    `glue_type`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'GLUE类型',
    `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT 'GLUE源代码',
    `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'GLUE备注',
    `add_time`    datetime                                              DEFAULT NULL,
    `update_time` datetime                                              DEFAULT NULL,
    `version`     bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_registry
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_registry`;
CREATE TABLE `xxl_job_registry`
(
    `id`             int                                                    NOT NULL AUTO_INCREMENT,
    `registry_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL,
    `registry_key`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `registry_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `update_time`    datetime DEFAULT NULL,
    `version`        bigint                                                 NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `i_g_k_v` (`registry_group`, `registry_key`, `registry_value`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 488
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

-- ----------------------------
-- Table structure for xxl_job_user
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_user`;
CREATE TABLE `xxl_job_user`
(
    `id`         int                                                   NOT NULL AUTO_INCREMENT,
    `username`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '账号',
    `password`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密码',
    `role`       tinyint                                               NOT NULL COMMENT '角色：0-普通用户、1-管理员',
    `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
    `version`    bigint                                                NOT NULL COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_username` (`username`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;

SET FOREIGN_KEY_CHECKS = 1;
