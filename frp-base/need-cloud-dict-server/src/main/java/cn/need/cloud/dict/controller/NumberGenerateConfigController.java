package cn.need.cloud.dict.controller;

import cn.need.cloud.dict.converter.NumberGenerateConfigConverter;
import cn.need.cloud.dict.model.entity.NumberGenerateConfig;
import cn.need.cloud.dict.model.vo.NumberGenerateConfigVO;
import cn.need.cloud.dict.service.NumberGenerateConfigService;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.constant.SortFieldEnum;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 编码生成配置表 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/dict/number-generate-config")
@Tag(name = "编码生成配置表")
@Slf4j
public class NumberGenerateConfigController extends AbstractRestController<NumberGenerateConfigService, NumberGenerateConfig, NumberGenerateConfigConverter, NumberGenerateConfigVO> {

    /**
     * 新增数据
     *
     * @param vo VO对象
     * @return Result<Long> 新增结果，并填充数据id
     */
    @Operation(summary = "新增数据接口", description = "接收数据的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) NumberGenerateConfigVO vo) {
        log.info("====> /api/dict/number-generate-config/insert, vo={}", JsonUtil.toJson(vo));
        return super.insert(vo, null);
    }

    /**
     * 修改数据
     *
     * @param vo VO对象
     * @return Result<Integer> 修改结果，并填充受影响行数
     */
    @Operation(summary = "修改数据接口，返回受影响行数", description = "接收数据的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) NumberGenerateConfigVO vo) {
        log.info("====> /api/dict/number-generate-config/update, vo={}", JsonUtil.toJson(vo));
        return super.update(vo, null);
    }

    /**
     * 根据id删除数据
     *
     * @param id 数据主键
     * @return Result<Integer> 删除结果，并填充受影响行数
     */
    @Operation(summary = "删除数据接口，返回受影响行数", description = "根据数据id，从数据库中删除其对应的数据")
    @GetMapping(value = "remove/{id}")
    public Result<Integer> remove(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/dict/number-generate-config/remove/{id}, id={}", id);
        return super.remove(id, null);
    }

    /**
     * 根据id设置数据有效性
     *
     * @param id 数据主键
     * @return Result<Integer> 设置有效性结果结果，并填充受影响行数
     */
    @Operation(summary = "设置数据有效性接口，返回受影响行数", description = "根据数据id，设置对应数据的有效性")
    @GetMapping(value = "active/{id}")
    public Result<Integer> active(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/dict/number-generate-config/active/{id}, id={}", id);
        return super.active(id, null);
    }

    /**
     * 根据id获取数据详情
     *
     * @param id 数据主键
     * @return Result<NumberGenerateConfigVO> 详情结果
     */
    @Operation(summary = "获取数据详情接口", description = "根据数据id，从数据库中获取其对应的数据详情")
    @GetMapping(value = "detail/{id}")
    public Result<NumberGenerateConfigVO> detail(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/dict/number-generate-config/detail/{id}, id={}", id);
        return super.detail(id, null);
    }

    /**
     * 根据条件参数获取分页后的列表数据
     *
     * @param search 条件参数
     * @return Result<PageData < NumberGenerateConfigVO>> 响应结果，并填充分页后的数据
     */
    @Operation(summary = "获取数据列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    public Result<PageData<NumberGenerateConfigVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<NumberGenerateConfigVO> search) {
        log.info("====> /api/dict/number-generate-config/list, search={}", JsonUtil.toJson(search));
        search.addSort(SortFieldEnum.CREATE_TIME.getCode(), false);
        return super.list(search, null);
    }

    /**
     * 根据excel附件，执行导入操作
     *
     * @param file excel附件
     * @return Result<Integer> 数据导入结果，并填导入数据
     */
    @Operation(summary = "数据导入接口，返回导入成功数量", description = "接口接收一个Excel文件，将数据导入到数据库")
    @PostMapping(value = "import")
    public Result<Integer> importExcel(@RequestParam("file") @Parameter(description = "excel附件", required = true) MultipartFile file) {
        log.info("====> /api/dict/number-generate-config/import, fileName={}", file.getOriginalFilename());
        return super.importExcel(file, super.voClass, null);
    }

    /**
     * 将数据导出为一个excel附件
     */
    @Operation(summary = "数据导出接口", description = "将数据导出成Excel文件")
    @GetMapping(value = "export")
    public void exportExcel() {
        log.info("====> /api/dict/number-generate-config/export");
        super.exportExcel(super.getExportName("编码生成配置表"), super.voClass, null);
    }
}
