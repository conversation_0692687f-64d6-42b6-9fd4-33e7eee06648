package cn.need.cloud.dict.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 编码生成配置表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("number_generate_config")
public class NumberGenerateConfig extends SuperModel {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 编码前缀
     */
    @TableField("prefix_code")
    private String prefixCode;

    /**
     * 时间格式
     */
    @TableField("date_format")
    private String dateFormat;

    /**
     * 流水号长度
     */
    @TableField("stream_length")
    private Integer streamLength;

    /**
     * 当前数
     */
    @TableField("current_num")
    private Long currentNum;

    /**
     * 步长
     */
    @TableField("step_size")
    private Long stepSize;

    /**
     * 是否每天重置
     */
    @TableField("reset_flag")
    private Integer resetFlag;

    /**
     * 备注
     */
    @TableField("description")
    private String description;


    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;
}
