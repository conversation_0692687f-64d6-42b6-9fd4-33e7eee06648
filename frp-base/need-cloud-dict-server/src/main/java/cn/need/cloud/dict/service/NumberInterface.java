package cn.need.cloud.dict.service;

import cn.need.framework.common.core.constant.StringPool;

/**
 * 编码生成接口
 *
 * <AUTHOR>
 */
public interface NumberInterface {

    /**
     * 根据给定的代码生成一个唯一的编号
     * 此方法首先根据代码获取对应的编号生成配置，然后根据配置生成编号
     * 如果配置中指定了日期格式，则生成的编号会包含日期序列；否则，仅包含前缀和流水号
     *
     * @param code 编号生成的代码依据，用于从配置库中获取对应的编号生成配置
     * @return 根据配置生成的唯一编号字符串
     */
    default String generateNumber(String code) {
        return generateNumber(code, null);
    }


    /**
     * 生成编号的实现方法
     * 根据给定的代码和时区生成唯一的编号
     * 主要逻辑包括从配置中获取生成编号的规则，并根据这些规则生成编号
     *
     * @param code   编号生成的代码依据，用于从配置库中获取对应的编号生成配置
     * @param zoneId 指定时区，用于处理日期时间序列
     * @return 根据生成规则生成的编号字符串
     */
    default String generateNumber(String code, String zoneId) {
        return generateNumber(code, null, zoneId);
    }

    /**
     * 生成特定编号的方法
     * 该方法根据提供的前缀代码、仓库代码和时区ID来生成一个唯一的编号
     * 主要通过结合时间序列和流水号来创建最终的编号
     *
     * @param prefixCode    编号的前缀代码，用于识别特定的编号体系
     * @param warehouseCode 仓库代码，表示编号所属的仓库
     * @param zoneId        时区ID，用于计算时间序列
     * @return 返回生成的唯一编号
     */
    default String generateNumber(String prefixCode, String warehouseCode, String zoneId) {
        return generateNumber(prefixCode, warehouseCode, zoneId, StringPool.EMPTY);
    }

    /**
     * 生成特定编号的方法
     * 该方法根据提供的前缀代码、仓库代码和时区ID来生成一个唯一的编号
     * 主要通过结合时间序列和流水号来创建最终的编号
     *
     * @param prefixCode    编号的前缀代码，用于识别特定的编号体系
     * @param warehouseCode 仓库代码，表示编号所属的仓库
     * @param zoneId        时区ID，用于计算时间序列
     * @param delimiter     分隔符
     * @return 返回生成的唯一编号
     */
    String generateNumber(String prefixCode, String warehouseCode, String zoneId, String delimiter);


    String generateNumberWithProduct(String code, String abbrName, String zoneId);
}
