package cn.need.cloud.dict.controller;

import cn.need.cloud.dict.converter.SystemConfigConverter;
import cn.need.cloud.dict.model.entity.SystemConfig;
import cn.need.cloud.dict.model.vo.SystemConfigVO;
import cn.need.cloud.dict.model.vo.req.DictBaseReqVO;
import cn.need.cloud.dict.service.SystemConfigService;
import cn.need.framework.common.annotation.constant.Grouping;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.api.ConfigRepertory;
import cn.need.framework.common.dict.constant.SortFieldEnum;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.util.ConfigUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.need.framework.common.core.lang.ObjectUtil.*;


/**
 * 系统配置 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/dict/config")
@Tag(name = "系统配置")
@ApiSupport(order = 1)
@Slf4j
@RequiredArgsConstructor
public class SystemConfigController extends AbstractRestController<SystemConfigService, SystemConfig, SystemConfigConverter, SystemConfigVO> {

    private final Map<String, Class<?>> classMap = Maps.concurrentMap();

    private final Map<String, String> pathMap = Maps.concurrentMap();

    private final ConfigRepertory repertory;


    /**
     * 新增数据
     *
     * @param vo VO对象
     * @return Result<Long> 新增结果，并填充数据id
     */
    @Operation(summary = "新增数据接口", description = "接收数据的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "insert")
    @ApiOperationSupport(order = 1)
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SystemConfigVO vo) {
        log.info("====> /api/dict/config/insert, vo={}", JsonUtil.toJson(vo));
        return super.insert(vo, null);
    }

    /**
     * 修改数据
     *
     * @param vo VO对象
     * @return Result<Integer> 修改结果，并填充受影响行数
     */
    @Operation(summary = "修改数据接口，返回受影响行数", description = "接收数据的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "update")
    @ApiOperationSupport(order = 2)
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SystemConfigVO vo) {
        log.info("====> /api/dict/config/update, vo={}", JsonUtil.toJson(vo));
        return super.update(vo, null);
    }

    /**
     * 根据id删除数据
     *
     * @param id 数据主键
     * @return Result<Integer> 删除结果，并填充受影响行数
     */
    @Operation(summary = "删除数据接口，返回受影响行数", description = "根据数据id，从数据库中删除其对应的数据")
    @GetMapping(value = "remove/{id}")
    @ApiOperationSupport(order = 3)
    public Result<Integer> remove(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/dict/config/remove/{id}, id={}", id);
        return super.remove(id, service::removeById);
    }

    /**
     * 根据id设置数据有效性
     *
     * @param id 数据主键
     * @return Result<Integer> 设置有效性结果结果，并填充受影响行数
     */
    @Operation(summary = "设置数据有效性接口，返回受影响行数", description = "根据数据id，设置对应数据的有效性")
    @GetMapping(value = "active/{id}")
    @ApiOperationSupport(order = 4)
    public Result<Integer> active(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> ${requestBasePath}/active/{id}, id={}", id);
        return super.active(id, it -> service.active(it, (Integer) transformActiveValue(it.getState())));
    }

    /**
     * 根据id获取数据详情
     *
     * @param id 数据主键
     * @return Result<ConfigVO> 详情结果
     */
    @Operation(summary = "获取数据详情接口", description = "根据数据id，从数据库中获取其对应的数据详情")
    @GetMapping(value = "detail/{id}")
    @ApiOperationSupport(order = 5)
    public Result<SystemConfigVO> detail(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/dict/config/detail/{id}, id={}", id);
        return super.detail(id, null);
    }

    /**
     * 根据条件参数获取分页后的列表数据
     *
     * @param search 条件参数
     * @return Result<PageData < ConfigVO>> 响应结果，并填充分页后的数据
     */
    @Operation(summary = "获取数据列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    @ApiOperationSupport(order = 6)
    public Result<PageData<SystemConfigVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<DictBaseReqVO> search) {
        log.info("====> /api/dict/config/list, search={}", JsonUtil.toJson(search));
        search.addSort(SortFieldEnum.CREATE_TIME.getCode(), false);
        return super.list(search, null);
    }

    /**
     * 根据配置编号，获取配置值
     *
     * @param code     系统配置编号
     * @param grouping 分组信息，默认"DEFAULT"
     * @return Result<String> 系统配置的值
     */
    @Operation(summary = "获取系统配置值接口", description = "根据配置编号、分组获取配置值，分组可以为空，会默认为DEFAULT")
    @GetMapping(value = "get-value")
    @ApiOperationSupport(order = 7)
    public Result<String> getValue(@RequestParam("code") @Parameter(description = "配置编号", required = true) String code,
                                   @RequestParam(value = "grouping", defaultValue = Grouping.DEFAULT) @Parameter(description = "分组") String grouping) {
        log.info("====> /api/dict/config/get-value, code={}, grouping={}", code, grouping);
        String value = ConfigUtil.get(code, grouping);
        if (ObjectUtil.isEmpty(value)) {
            SystemConfig config = this.service.getByConfCode(code, grouping);
            if (isNotEmpty(config)) {
                value = config.getConfValue();
            }
        }
        return success(value);
    }

    /**
     * 将数据导出为一个excel附件
     */
    @Operation(summary = "数据导出接口", description = "将数据导出成Excel文件")
    @GetMapping(value = "export")
    public void exportExcel() {
        log.info("====> /api/dict/config/export");
        super.exportExcel(super.getExportName("配置表单信息"), voClass, null);
    }

    /**
     * 初始化配置
     *
     * @return 成功或失败
     */
    @Operation(summary = "初始化编码配置", description = "初始化编码配置,加载到redis缓存中")
    @GetMapping("initConfig")
    public String initConfig() {
        log.info(">>>>>>>>>>>>>>>>>>>>> controller 初始化系统配置至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        //定义系统配置数据的map集合
        Map<String, Map<String, String>> map = Maps.hashMap();
        //获取所有配置数据并过滤失效的，填充到map中
        List<SystemConfig> list = service.list().stream().filter(it -> equal(it.getState(), DataState.ENABLED)).toList();
        list.forEach(it -> {
            String key = nullToDefault(it.getConfGroup(), StringPool.EMPTY);
            Map<String, String> configMap = nullToDefault(map.get(key), Maps.hashMap());
            configMap.put(it.getConfCode(), it.getConfValue());
            map.put(key, configMap);
        });
        repertory.initialization(map);
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化系统配置至redis【完成】，分组数[{}{}{}{}", map.size(), "]，总数[", list.size(), "] <<<<<<<<<<<<<<<<<<<<<");
        return "success";
    }
}
