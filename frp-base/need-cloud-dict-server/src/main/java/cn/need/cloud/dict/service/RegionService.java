package cn.need.cloud.dict.service;

import cn.need.cloud.dict.client.dto.RegionDTO;
import cn.need.cloud.dict.client.dto.req.RegionReqDTO;
import cn.need.cloud.dict.model.entity.Region;
import cn.need.cloud.dict.model.vo.RegionVO;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.List;

/**
 * 行政区域  服务接口
 *
 * <AUTHOR>
 */
public interface RegionService extends SuperService<Region> {

    /**
     * 批量保存数据
     *
     * @param depth   深度
     * @param dtoList 行政区域数据集合
     * @return int 受影响行数
     */
    int saveBatch(Integer depth, List<RegionDTO> dtoList);

    /**
     * 根据id，物理删除行政区域
     *
     * @param id 行政区域id
     * @return int 受影响行数
     */
    int deleteById(Long id);


    /**
     * 初始化全部行政区域数据至缓存中
     *
     * @return int 缓存数量
     */
    int initialize();

    /**
     * 根据行政区域编码，获取行政区域数据对象
     *
     * @param regionCode 行政区域编码
     * @return Region
     */
    Region getByRegionCode(String regionCode);

    /**
     * 根据行政区域编码集合，获取行政区域数据
     *
     * @param regionCodes 行政区域编码集合
     * @return List<Region>
     */
    List<Region> listByRegionCodes(List<String> regionCodes);

    /**
     * 根据父级id获取其对应的直系子集行政区域数据
     *
     * @param parentId 父级id，如果为null，会被默认设置为0
     * @return List<Region> 父级id对应的直系子集行政区域集合
     */
    List<Region> listByParentId(Long parentId);

    /**
     * 根据父级id，获取其对应的所有子集行政区域，包含子集的子集
     *
     * @param parentId    父级id，必须大于0，否则会抛异常
     * @param containSelf 是否包含参数本身对应的数据
     * @return List<Region> 父级id对应的所有子集行政区域集合
     */
    List<Region> listAllByParentId(Long parentId, boolean containSelf);

    /**
     * 根据行政区域的路径信息，匹配所有符合条件的行政区域集合，使用 like 'path%'来获取数据
     *
     * @param path 行政区域路径，如果路径值为空，会返回空集合数据
     * @return List<Region> 行政区域路径匹配的所有子集行政区域数据集合
     */
    List<Region> listLikePath(String path);

    /**
     * 根据行政区域路径，获取其映射的所有值集信息，取值逻辑：
     * <p>
     * 1. 根据分隔符"."切割路径，得到路径下的第一个id值，也就是一个根级行政区域的id
     * <p>
     * 2. 根据id值获取其所有子集，也包含id映射的行政区域数据
     *
     * @param path 行政区域的路径值
     * @return List<Region> 行政区域路径映射的数据行政区域值集
     */
    List<Region> listValueSet(String path);


    /**
     * 通过条件进行查询行政区域
     *
     * @param condition 条件
     * @return 行政区域
     */
    List<Region> findByCondition(RegionReqDTO condition);

    /**
     * 批量保存区域
     *
     * @param regionList 区域数据
     */
    void batchInsert(List<Region> regionList);

    /**
     * 批量更新区域
     *
     * @param regionList 区域数据
     */
    void batchUpdate(List<Region> regionList);

    /**
     * 获取所有区域
     *
     * @return 所有行政区信息
     */
    List<Region> listAll();

    /**
     * 根据id获取详情
     *
     * @param id 区域详情
     * @return RegionVO
     */
    RegionVO detailById(Long id);
}
