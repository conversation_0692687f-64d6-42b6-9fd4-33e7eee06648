package cn.need.cloud.dict.util;

import cn.need.cloud.dict.client.dto.RegionDTO;
import cn.need.cloud.dict.model.entity.Dictionary;
import cn.need.cloud.dict.model.entity.Region;
import cn.need.framework.common.annotation.enums.DictProperty;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.CollectionUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.UncheckedException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.text.PinyinUtil;
import cn.need.framework.common.core.text.pattern.PinyinPattern;
import cn.need.framework.common.dict.entity.Area;
import cn.need.framework.common.dict.entity.Dict;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * 通用的工具类.
 *
 * <AUTHOR>
 */
public class GeneralUtil {

    public static final int DEFAULT_EXCEPTION_CODE = 400;

    /**
     * 私有化构造函数
     */
    private GeneralUtil() {
        throw new AssertionError("No " + getClass().getName() + " instances for you!");
    }


    /**
     * 获取异常编号
     *
     * @param exception 异常数据对象
     * @return int 异常编码
     */
    public static int getExceptionCode(Exception exception) {
        if (exception instanceof UncheckedException) {
            return ObjectUtil.nullToDefault(((UncheckedException) exception).getCode(), DEFAULT_EXCEPTION_CODE);
        }
        return DEFAULT_EXCEPTION_CODE;
    }

    /**
     * 构建common包的数据字典对象集合
     *
     * @param entityList 字典实体
     * @return Dict common包的数据字典数据对象
     */
    public static List<Dict> buildDict(List<Dictionary> entityList) {
        Map<Long, Dictionary> map = toMap(entityList, Dictionary::getId);
        //迭代字典map集合，并封装成common的字典集合
        return map.values().stream().map(it -> buildDict(it, map)).collect(Collectors.toList());
    }

    /**
     * 构建common包的数据字典对象
     *
     * @param entity 字典实体
     * @param parent 父级字典
     * @param root   值集
     * @return Dict common包的数据字典数据对象
     */
    public static Dict buildDict(Dictionary entity, Dictionary parent, Dictionary root) {
        Dict dict = new Dict();
        dict.setCode(entity.getDictCode());
        dict.setName(entity.getDictName());
        dict.setValue(entity.getDictValue());
        dict.setDescription(entity.getDescription());
        dict.setSorting(entity.getSorting());
        dict.setEnable(equal(entity.getState(), DataState.ENABLED));
        if (isNotNull(parent)) {
            dict.setParent(parent.getDictCode());
        }
        if (isNotNull(root)) {
            dict.setRoot(root.getDictCode());
        }
        return dict;
    }


    /**
     * 构建common的字典对象
     */
    private static Dict buildDict(Dictionary it, Map<Long, Dictionary> map) {
        return buildDict(it, getParent(it, map), getRoot(it, map));
    }

    /**
     * 获取数据字典的父级字典对象
     */
    private static Dictionary getParent(Dictionary it, Map<Long, Dictionary> map) {
        return isNull(it.getParentId()) ? null : map.get(it.getParentId());
    }

    /**
     * 获取数据字典的根级字典对象
     */
    private static Dictionary getRoot(Dictionary it, Map<Long, Dictionary> map) {
        //取path的第一级做为根级字典id
        List<String> list = StringUtil.split(it.getPath(), StringPool.COMMA);
        return list.isEmpty() ? null : map.get(convert(list.get(0), 0L));
    }

    /**
     * 构建数据字典数据的map集合
     *
     * @param list   缓存字典对象
     * @param fields map中包含的字段，默认会包含code、name
     * @return List<Map < String, Object>> 将字典封装成map集合对象
     */
    public static List<Map<String, Object>> buildDictMapList(List<Dict> list, List<String> fields) {
        return isEmpty(list) ? Lists.arrayList() : list.stream().map(it -> buildDictMapList(it, buildKeys(fields))).collect(Collectors.toList());
    }

    /**
     * 将数据字典封装成map对象
     *
     * @param dict   缓存字典对象
     * @param fields map中包含的字段
     * @return Map<String, Object> 封装后的map数据
     */
    private static Map<String, Object> buildDictMapList(Dict dict, List<String> fields) {
        Map<String, Object> map = Maps.hashMap();
        fields.stream().filter(ObjectUtil::isNotEmpty).forEach(it -> map.put(it, BeanUtil.getProperty(dict, it)));
        return map;
    }

    /**
     * 构建字典map的key集，会优先添加code、name作为字典的key
     *
     * @param fields map中包含的字段
     * @return List<String> 字典map的key集
     */
    private static List<String> buildKeys(List<String> fields) {
        return CollectionUtil.contact(Lists.arrayList(DictProperty.CODE.getCode(), DictProperty.NAME.getCode()), fields);
    }

    public static Area buildArea(Region data, Region parent) {
        Area area = new Area();
        area.setCode(data.getRegionCode());
        area.setName(data.getRegionName());
        area.setFullName(data.getFullRegionName());
        area.setFullCode(data.getFullRegionCode());
        area.setShortName(data.getShortName());
        area.setPinyin(data.getRegionNamePy());
        area.setShortPinyin(data.getShortNamePy());
        area.setDepth(data.getDepth());
        area.setSort(data.getSorting());
        area.setPath(data.getPath());
        if (parent != null) {
            area.setParent(parent.getRegionCode());
        }
        return area;
    }


    /**
     * 构建行政区域数据
     */
    public static Region buildRegion(RegionDTO dto, Region parent, Region data) {
        Region region = new Region();
        //设置id
        Long id = isNotNull(data) ? data.getId() : dto.getId();
        region.setId(id);
        region.setRegionCode(dto.getRegionCode());
        region.setRegionName(dto.getRegionName());
        region.setRegionNamePy(PinyinUtil.toPinyin(region.getRegionName(), PinyinPattern.NONE_TONE_FU));
        region.setShortNamePy(PinyinUtil.toShortPinyin(region.getRegionName()).toUpperCase(Locale.ROOT));
        region.setShortName(dto.getShortName());
        region.setLongitude(dto.getLongitude());
        region.setLatitude(dto.getLatitude());
        //设置父级id
        Long parentId = isNotNull(parent) ? parent.getId() : 0L;
        region.setParentId(parentId);
        //设置深度
        Integer depth = isNotNull(parent) ? parent.getDepth() + 1 : 1;
        region.setDepth(depth);
        //设置路径
        String path = isNotNull(parent) ? parent.getPath() : StringPool.EMPTY;
        //历史数据不为空的情况下，连接id值
        if (isNotNull(data)) {
            path = path.concat(StringUtil.toString(data.getId())).concat(StringPool.COMMA);
        }
        region.setPath(path);
        //设置排序值
        if (isNull(region.getSorting())) {
            Double sorting = isNotNull(parent) ? parent.getSorting() + 1 : 1D;
            region.setSorting(sorting);
        }
        return region;
    }

    /**
     * 判断父级编码是否为空
     */
    public static boolean isParentEmpty(RegionDTO dto) {
        return isParentEmpty(dto.getParentCode());
    }

    /**
     * 判断父级编码是否为空
     */
    public static boolean isParentEmpty(String parentCode) {
        return isEmpty(parentCode) || equal(parentCode, "0");
    }
}
