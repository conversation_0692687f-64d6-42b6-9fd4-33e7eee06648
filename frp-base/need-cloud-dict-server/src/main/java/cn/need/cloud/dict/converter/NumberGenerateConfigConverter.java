package cn.need.cloud.dict.converter;

import cn.need.cloud.dict.client.dto.NumberGenerateConfigDTO;
import cn.need.cloud.dict.model.entity.NumberGenerateConfig;
import cn.need.cloud.dict.model.vo.NumberGenerateConfigVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 编码生成配置表  对象转换器
 *
 * <AUTHOR>
 */
public class NumberGenerateConfigConverter extends AbstractModelConverter<NumberGenerateConfig, NumberGenerateConfigVO, NumberGenerateConfigDTO> {

}