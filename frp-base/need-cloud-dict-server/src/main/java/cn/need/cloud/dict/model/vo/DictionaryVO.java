package cn.need.cloud.dict.model.vo;

import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.tree.Node;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "数据字典 VO对象")
public class DictionaryVO implements Node<Long, DictionaryVO> {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 字典编号
     */
    @NotBlank(message = "字典编号不能为空")
    @Schema(description = "dictCode")
    private String dictCode;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @Schema(description = "字典名称")
    private String dictName;

    /**
     * 字典值
     */
    @Schema(description = "字典值")
    private String dictValue;

    /**
     * 字典类型
     */
    @Schema(description = "字典类型")
    private String dictType;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    @Schema(description = "数据状态，0 - 无效，1 - 有效")
    private Integer state;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private Long parentId;

    /**
     * 深度，用来记录树结构的层级关系
     */
    @Schema(description = "深度，用来记录树结构的层级关系")
    private Integer depth;

    /**
     * 路径，用来记录树结构数据id的路径，用','分隔
     */
    @Schema(description = "路径，用来记录树结构数据id的路径，用','分隔")
    private String path;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private Integer sorting;

    /**
     * 数据字典子集集合
     */
    @Schema(description = "数据字典子集集合")
    private List<DictionaryVO> children;

    /**
     * 是否包含子集
     */
    @Schema(description = "是否包含子集")
    private Boolean hasChildren;

    @Override
    public CharSequence getName() {
        return this.dictName;
    }

    @Override
    public Comparable<?> getWeight() {
        return this.sorting;
    }

    @Override
    public List<DictionaryVO> getChildren() {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        return this.children;
    }

    @Override
    public Boolean getHasChildren() {
        return StringUtil.split(this.path, StringPool.COMMA).size() > 1;
    }

}