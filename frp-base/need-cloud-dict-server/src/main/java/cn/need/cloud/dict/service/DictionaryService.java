package cn.need.cloud.dict.service;

import cn.need.cloud.dict.client.dto.DictionaryDTO;
import cn.need.cloud.dict.model.entity.Dictionary;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.List;

/**
 * 数据字典  服务接口
 *
 * <AUTHOR>
 */
public interface DictionaryService extends SuperService<Dictionary> {

    /**
     * 设置数据字典的有效性
     *
     * @param entity 需要设置的数据字典对象
     * @param state  目标状态值
     * @return int 受影响行数
     */
    int active(Dictionary entity, int state);

    /**
     * 根据字典路径，获取根级数据字典对象
     *
     * @param path 字典路径
     * @return Dictionary 路径映射的根级数据字典对象
     */
    Dictionary getRoot(String path);

    /**
     * 根据字典根级编码，获取根级数据字典对象
     *
     * @param rootCode 字典根级编码
     * @return Dictionary 路径映射的根级数据字典对象
     */
    Dictionary getByRootCode(String rootCode);

    /**
     * 根据根级编码，与父级编码，获取字典对象
     *
     * @param rootCode   根级编码
     * @param parentCode 父级编码
     * @return Dictionary
     */
    Dictionary getByDictCode(String rootCode, String parentCode);

    /**
     * 根据根级字典id，与父级编码，获取字典对象
     *
     * @param rootId     根级字典id
     * @param parentCode 父级编码
     * @return Dictionary
     */
    Dictionary getByDictCode(Long rootId, String parentCode);

    /**
     * 根据父级id获取其对应的直系子集字典数据
     *
     * @param parentId 父级id，如果为null，会被默认设置为0
     * @return List<Dictionary> 父级id对应的直系子集字典数据集合
     */
    List<Dictionary> listByParentId(Long parentId);

    /**
     * 根据父级id，获取其对应的所有子集字典数据，包含子集的子集
     *
     * @param parentId 父级id，必须大于0，否则会抛异常
     * @return List<Dictionary> 父级id对应的所有子集字典数据集合
     */
    List<Dictionary> listAllByParentId(Long parentId);

    /**
     * 根据父级id，获取其对应的所有子集字典数据，包含子集的子集
     *
     * @param parentId    父级id，必须大于0，否则会抛异常
     * @param containSelf 是否包含参数本身对应的数据
     * @return List<Dictionary> 父级id对应的所有子集字典数据集合
     */
    List<Dictionary> listAllByParentId(Long parentId, boolean containSelf);

    /**
     * 根据字典的路径信息，匹配所有符合条件的字典数据集合，使用 like 'path%'来获取数据
     *
     * @param path 字典路径，如果路径值为空，会返回空集合数据
     * @return List<Dictionary> 字典路径匹配的所有子集字典数据集合
     */
    List<Dictionary> listLikePath(String path);

    /**
     * 根据字典路径，获取其映射的所有值集信息，取值逻辑：
     * <p>
     * 1. 根据分隔符"."切割路径，得到路径下的第一个id值，也就是一个根级字典的id
     * <p>
     * 2. 根据id值获取其所有子集，也包含id映射的字典数据
     *
     * @param path 字典的路径值
     * @return List<Dictionary> 字典路径映射的数据字典值集
     */
    List<Dictionary> listValueSet(String path);

    /**
     * 查询
     *
     * @param keyWord 关键字
     * @return List<DictionaryVO>
     */
    List<Dictionary> tree(String keyWord);

    /**
     * 根据字典名称获取字典编码
     */
    List<DictionaryDTO> getByName(DictionaryDTO dto);

    /**
     * 根据字典编码获取字典对象
     */
    List<DictionaryDTO> getList(List<String> codes);
}