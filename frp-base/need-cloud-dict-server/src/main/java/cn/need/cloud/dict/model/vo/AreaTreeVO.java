package cn.need.cloud.dict.model.vo;

import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.tree.Node;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * 行政区域 vo对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AreaTreeVO implements Node<Long, AreaTreeVO> {

    @Serial
    private static final long serialVersionUID = 2141222566966420144L;


    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 区域编号
     */
    private String code;


    /**
     * 区域名称
     */
    private String name;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 区域深度，记录字典的层级关系，0/国家，1/省，2/市，3/区县，4/乡镇，5/街道
     */
    private Integer depth;

    /**
     * 区域路径，用来记录当前类别的id路径，用“,”分隔
     */
    private String path;


    /**
     * 区域排序
     */
    private Double sort;


    /**
     * 数据字典子集集合
     */
    @Schema(description = "数据字典子集集合")
    private List<AreaTreeVO> children;

    /**
     * 是否包含子集
     */
    @Schema(description = "是否包含子集")
    private Boolean hasChildren;

    /**
     * 是否可以进行选择 true:可以 false:不可以
     */
    private Boolean checkFlag;


    /**
     * 选中状态:
     * checked 已选
     * half 半选择
     * "" 末选
     */
    private String checkStatus;


    /**
     * 获取排序的时候赋默认值
     *
     * @return Double
     */
    @SuppressWarnings("unused")
    public Integer getDepth() {
        if (this.path == null) {
            this.depth = -1;
        }
        return this.depth;
    }

    @Override
    public Comparable<?> getWeight() {
        return this.getSort();
    }

    @Override
    public List<AreaTreeVO> getChildren() {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        return this.children;
    }

    @Override
    public Boolean getHasChildren() {
        return ObjectUtil.isNotEmpty(children);
    }
}
