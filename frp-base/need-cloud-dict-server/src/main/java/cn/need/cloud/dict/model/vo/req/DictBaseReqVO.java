package cn.need.cloud.dict.model.vo.req;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.enums.NestedType;
import cn.need.framework.common.annotation.param.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 数据字典模块基本查询条件
 *
 * <AUTHOR>
 */
@Schema(description = "数据字典模块基本查询条件，适用于字典管理的列表查询、系统配置的列表查询")
@Data
public class DictBaseReqVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 173768130730302778L;

    /**
     * 关键字，嵌套OR查询条件，可以是字典的code或name，或是系统配置的code或name
     */
    @Schema(description = "关键字，嵌套OR查询条件，可以是字典的code或name，或是系统配置的code或name，")
    @Condition(value = Keyword.LIKE, fields = {"dictCode", "dictName", "confCode", "confName"}, nestedType = NestedType.OR)
    private String keyword;

    /**
     * 数据的状态，取值0/1，1 - 有效，0 - 无效
     */
    @Schema(description = "数据的状态，取值0/1，1表示有效，0表示无效")
    private Integer state;
}
