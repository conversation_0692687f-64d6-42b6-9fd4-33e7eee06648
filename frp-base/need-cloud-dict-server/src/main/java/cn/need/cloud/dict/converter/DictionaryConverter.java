package cn.need.cloud.dict.converter;

import cn.need.cloud.dict.client.dto.DictionaryDTO;
import cn.need.cloud.dict.model.entity.Dictionary;
import cn.need.cloud.dict.model.vo.DictionaryVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 数据字典  对象转换器
 *
 * <AUTHOR>
 */
public class DictionaryConverter extends AbstractModelConverter<Dictionary, DictionaryVO, DictionaryDTO> {

}