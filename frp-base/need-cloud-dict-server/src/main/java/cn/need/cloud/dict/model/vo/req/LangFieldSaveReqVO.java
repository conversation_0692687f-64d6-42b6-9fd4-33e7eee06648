package cn.need.cloud.dict.model.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
@Data
@Schema(description = "平台多语言字段定义 vo对象")
public class LangFieldSaveReqVO implements Serializable {

    /**
     * 主键id
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 语言字段翻译列表
     */
    @Schema(description = "语言字段翻译列表")
    private List<LangFieldTranslationSaveReqVO> langFieldTranslationList;
}
