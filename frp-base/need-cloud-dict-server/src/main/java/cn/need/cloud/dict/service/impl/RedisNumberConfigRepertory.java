package cn.need.cloud.dict.service.impl;

import cn.need.cloud.dict.client.constant.NumberGenerateConfigConstant;
import cn.need.cloud.dict.model.entity.NumberGenerateConfig;
import cn.need.cloud.dict.service.NumberConfigRepertory;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * Class description goes here.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisNumberConfigRepertory implements NumberConfigRepertory {

    /**
     * redisTemplate对象，需要注入
     */
    private final RedisTemplate redisTemplate;

    //////////////////////////////////////// 接口方法实现 /////////////////////////////////////////////////////////////////////
    /**
     * 配置数据在redis中的key值，可以注入
     */
    private final String redisKey;


    /**
     * 带redisTemplate参数的构造函数
     *
     * @param redisTemplate redisTemplate对象
     */
    public RedisNumberConfigRepertory(RedisTemplate redisTemplate) {
        log.info("-------------  >> RedisNumberConfigRepertory is null ? {}", ObjectUtil.isNull(redisTemplate));
        this.redisTemplate = redisTemplate;
        this.redisKey = NumberGenerateConfigConstant.NUMBER_GENERATE_REDIS_KEY;
    }

    @Override
    public NumberGenerateConfig get(String code) {
        return isEmpty(code) ? null : getHash().get(code);
    }

    @Override
    public void add(NumberGenerateConfig config) {
        if (isEmpty(config.getCode())) {
            return;
        }
        getHash().put(config.getCode(), config);
    }

    @Override
    public void del(String key) {
        if (isEmpty(key)) {
            return;
        }
        getHash().delete(key);
    }

    /**
     * 清空所有配置信息
     */
    @Override
    public void clear() {
        TimeMeter meter = new TimeMeter();
        String pattern = this.redisKey.concat(StringPool.COLON).concat(StringPool.ASTERISK);
        Set<String> keys = nullToDefault(redisTemplate.keys(pattern), Lists.hashSet());
        keys.forEach(it -> {
            if (log.isDebugEnabled()) {
                log.debug("----------->> 删除系统配置分组：{}", it);
            }
            if (isNotEmpty(it)) {
                redisTemplate.delete(it);
            }
        });
        if (log.isDebugEnabled()) {
            log.debug("----------->> <<<清空>>>所有【系统配置】数据完成！分组数量：{}，用时：{}ms", keys, meter.sign());
        }
    }

    /// ///////////////////////////////////// 私有属性 /////////////////////////////////////////////////////////////////////

    @Override
    public void initialization(Collection<NumberGenerateConfig> configs) {
        TimeMeter meter = new TimeMeter();
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化缓存【编码配置】数据【开始】，初始化编码配置对象数量：{} ============", isNotNull(configs) ? configs.size() : 0);
        }
        //清空缓存数据
        clear();
        //将数据保存至redis
        if (isNotNull(configs)) {
            configs.forEach(this::add);
        }
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化缓存【编码配置】数据<<<完成>>>，总耗时{}ms ============", meter.sign());
        }
    }

    /**
     * 获取RedisTemplate的BoundHashOperations对象，系统会为行政区域数据集合构建一个hash对象
     */
    @Override
    public BoundHashOperations<String, String, NumberGenerateConfig> getHash() {
        return this.redisTemplate.boundHashOps(NumberGenerateConfigConstant.NUMBER_GENERATE_REDIS_KEY);
    }

}
