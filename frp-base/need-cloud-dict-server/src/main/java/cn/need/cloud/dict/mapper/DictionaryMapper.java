package cn.need.cloud.dict.mapper;

import cn.need.cloud.dict.client.dto.DictionaryDTO;
import cn.need.cloud.dict.model.entity.Dictionary;
import cn.need.framework.common.mybatis.base.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据字典 Mapper接口
 *
 * <AUTHOR>
 */
public interface DictionaryMapper extends SuperMapper<Dictionary> {

    /**
     * 根据parentIdList查询
     *
     * @param parentIdList parentIdList
     * @return List<Dictionary>
     */
    List<Dictionary> listSubByParentIdList(@Param("parentIdList") List<Long> parentIdList);

    /**
     * 根据字典编码获取字典对象
     */
    List<DictionaryDTO> getList(@Param("codes") List<String> codes);
}