package cn.need.cloud.dict.provider;

import cn.need.cloud.dict.client.api.DictionaryClient;
import cn.need.cloud.dict.client.api.path.DictClientPath;
import cn.need.cloud.dict.client.dto.DictionaryDTO;
import cn.need.cloud.dict.client.dto.req.DictionaryDeleteReqDTO;
import cn.need.cloud.dict.client.dto.req.DictionaryReqDTO;
import cn.need.cloud.dict.client.dto.req.SubsetReqDTO;
import cn.need.cloud.dict.converter.DictionaryConverter;
import cn.need.cloud.dict.model.entity.Dictionary;
import cn.need.cloud.dict.service.DictionaryService;
import cn.need.cloud.dict.util.GeneralUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ParameterException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.entity.Dict;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.util.DictUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(DictClientPath.PREFIX)
@RequiredArgsConstructor
public class DictionaryProvider implements DictionaryClient {

    private final DictionaryService dictionaryService;

    @PostMapping(DictClientPath.GET_DICTIONARY)
    @Operation(hidden = true)
    @Override
    public Result<PageData<DictionaryDTO>> getDictionary(@RequestBody PageSearch<DictionaryReqDTO> search) {
        log.info("-----> {}, search={}", DictClientPath.PREFIX.concat(DictClientPath.GET_DICTIONARY), JsonUtil.toJson(search));
        try {
            //构建查询条件，只获取有效的数据
            LambdaQueryWrapper<Dictionary> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Dictionary::getState, 1);
            DictionaryReqDTO param = nullToDefault(search.getCondition(), new DictionaryReqDTO());
            //优先获取根级字典id
            Long rootId = isEmpty(param.getRootCode()) ? null : getRootId(param.getRootCode());
            //根级编码不为空
            if (isNotNull(rootId)) {
                wrapper.likeRight(Dictionary::getPath, StringUtil.toString(rootId).concat(StringPool.COMMA).concat(StringPool.PERCENT));
            }
            //深度不为空
            if (isNotNull(param.getDepth())) {
                wrapper.eq(Dictionary::getDepth, param.getDepth());
            }
            //父级编码不为空
            if (isNotEmpty(param.getParentCode())) {
                wrapper.eq(Dictionary::getParentId, getParentId(param.getParentCode(), rootId));
            }
            //执行分页查询，获取数据字典数据
            IPage<Dictionary> page = dictionaryService.page(new Page<>(search.getCurrent(), search.getSize()), wrapper);
            PageData<DictionaryDTO> pageData = Converters.get(DictionaryConverter.class).toDTOPage(page);
            //填充父级编码
            if (isNotEmpty(pageData.getRecords())) {
                filledParentCode(pageData.getRecords());
            }
            //响应数据
            return Result.ok(pageData);
        } catch (Exception e) {
            log.error("根据分页条件，获取数据字典信息异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping(DictClientPath.SAVE_DICTIONARY)
    @Operation(hidden = true)
    @Override
    public Result<Integer> save(@RequestBody DictionaryDTO dto) {
        log.info("-----> {}, dto={}", DictClientPath.PREFIX.concat(DictClientPath.SAVE_DICTIONARY), JsonUtil.toJson(dto));
        try {
            return Result.ok(dictionaryService.insertOrUpdate(buildDictionary(dto)));
        } catch (Exception e) {
            log.error("保存数据字典异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping(DictClientPath.SAVE_DICTIONARY_BATCH)
    @Operation(hidden = true)
    @Override
    public Result<Integer> saveBatch(@RequestBody List<DictionaryDTO> dtoList) {
        log.info("-----> {}, dtoList={}", DictClientPath.PREFIX.concat(DictClientPath.SAVE_DICTIONARY_BATCH), JsonUtil.toJson(dtoList));
        try {
            List<Dictionary> entityList = dtoList.stream().map(this::buildDictionary).collect(Collectors.toList());
            return Result.ok(dictionaryService.insertOrUpdateBatch(entityList));
        } catch (Exception e) {
            log.error("批量保存数据字典异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping(DictClientPath.DELETE_DICTIONARY_BATCH)
    @Operation(hidden = true)
    @Override
    public Result<Integer> deleteBatch(@RequestBody List<DictionaryDeleteReqDTO> deleteParams) {
        log.info("-----> {}, dtoList={}", DictClientPath.PREFIX.concat(DictClientPath.DELETE_DICTIONARY_BATCH), JsonUtil.toJson(deleteParams));
        try {
            //根据参数条件，获取数据集合
            List<Long> ids = buildDictionaryIds(deleteParams);
            return Result.ok(dictionaryService.removeByIds(ids));
        } catch (Exception e) {
            log.error("批量保存数据字典异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping(value = DictClientPath.SUBSET)
    @Operation(hidden = true)
    @Override
    public Result<List<Map<String, Object>>> subset(@RequestBody SubsetReqDTO param) {
        log.info("====> /api/dict/dictionary/subset, param={}", JsonUtil.toJson(param));
        List<Dict> list = DictUtil.subList(param.getCode(), StringUtil.emptyToDefault(param.getRoot(), param.getCode()), true);
        return Result.ok(GeneralUtil.buildDictMapList(list, param.getExtendFields()));
    }

    /**
     * 根据字典名称模糊查获取字典编码
     *
     * @param dto 数据字典子集接口查询条件
     * @return 有效的字典子集
     */
    @Override
    public Result<List<DictionaryDTO>> getCodeByName(DictionaryDTO dto) {
        List<DictionaryDTO> dictionaryDTOS = dictionaryService.getByName(dto);
        return Result.ok(dictionaryDTOS);
    }

    /**
     * 获取所有字典行
     *
     * @return 有效的字典子集
     */
    @Override
    public Result<List<DictionaryDTO>> getList(List<String> codes) {
        List<DictionaryDTO> list = dictionaryService.getList(codes);
        return Result.ok(ObjectUtil.nullToDefault(list, new ArrayList<>()));
    }

    private List<Long> buildDictionaryIds(List<DictionaryDeleteReqDTO> deleteParams) {
        List<Long> dictionaryIds = Lists.arrayList();
        deleteParams.forEach(it -> {
            Dictionary dictionary = dictionaryService.getByDictCode(it.getRootCode(), it.getDictCode());
            if (isNotNull(dictionary)) {
                dictionaryIds.add(dictionary.getId());
            }
        });
        return dictionaryIds;
    }

    private Dictionary buildDictionary(DictionaryDTO dto) {
        //校验，字典编码不能为空
        if (isEmpty(dto.getDictCode())) {
            throw new ParameterException("字典编码不能为空");
        }
        if (isEmpty(dto.getDictName())) {
            throw new ParameterException("字典名称不能为空");
        }
        //数据转换为实体
        Dictionary entity = Converters.get(DictionaryConverter.class).toEntity(dto);
        //如果父级id为空，设置字典的父级id
        if (isNull(entity.getParentId())) {
            entity.setParentId(getDictionaryParentId(dto));
        }
        return entity;
    }

    private Long getDictionaryParentId(DictionaryDTO dto) {
        if (isNotNull(dto.getParentId())) {
            return dto.getParentId();
        }
        //如果父级编码和根级编码同事为空，直接返回null
        if (isEmpty(dto.getParentCode()) && isEmpty(dto.getRootCode())) {
            return null;
        }
        //如果根级编码为空，设置根级编码与父级编码一致
        if (isEmpty(dto.getRootCode())) {
            dto.setRootCode(dto.getParentCode());
        }
        //如果父级编码为空，设置父级编码与根级编码一致
        if (isEmpty(dto.getParentCode())) {
            dto.setParentCode(dto.getRootCode());
        }
        //根级编码，获取字典对象
        Dictionary entity = dictionaryService.getByDictCode(dto.getRootCode(), dto.getParentCode());
        return isNotNull(entity) ? entity.getId() : null;
    }

    private void filledParentCode(List<DictionaryDTO> records) {
        //根据父级id集合获取数据，并封装成map，key为数据的id，value为分类数据对象
        List<Long> parentIds = records.stream().map(DictionaryDTO::getParentId).filter(ObjectUtil::gtZero).collect(Collectors.toList());
        //追加根级id
        records.forEach(it -> {
            Long rootId = getRootId(it);
            if (isNotNull(rootId) && !parentIds.contains(rootId)) {
                parentIds.add(rootId);
            }
        });
        Map<Long, Dictionary> dictionaryMap = ObjectUtil.toMap(dictionaryService.listByIds(parentIds), Dictionary::getId);
        //循环响应数据，填充父级编码、根级编码
        records.forEach(it -> {
            //设置根级编码
            Dictionary root = dictionaryMap.get(getRootId(it));
            if (isNotNull(root)) {
                it.setRootCode(root.getDictCode());
            }
            //设置父级编码
            if (gtZero(it.getParentId())) {
                Dictionary parent = dictionaryMap.get(it.getParentId());
                if (isNotNull(parent)) {
                    it.setParentCode(parent.getDictCode());
                }
            }
        });
    }

    private Long getRootId(DictionaryDTO it) {
        List<Long> ids = StringUtil.splitToLong(it.getPath(), StringPool.COMMA);
        return ids.isEmpty() ? null : ids.get(0);
    }

    private Long getParentId(String parentCode, Long rootId) {
        if (equal(parentCode, StringPool.ZERO)) {
            return 0L;
        }
        if (isNull(rootId)) {
            return -1L;
        }
        Dictionary parent = dictionaryService.getByDictCode(rootId, parentCode);
        return isNull(parent) ? -1L : parent.getId();
    }

    private Long getRootId(String rootCode) {
        Dictionary root = dictionaryService.getByRootCode(rootCode);
        if (isNull(root)) {
            throw new BusinessException("不能根据根级字典编号[" + rootCode + "]获取到对应的字段数据");
        }
        return root.getId();
    }
}
