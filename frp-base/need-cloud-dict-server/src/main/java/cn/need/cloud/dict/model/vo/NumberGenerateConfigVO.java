package cn.need.cloud.dict.model.vo;


import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 编码生成配置表 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "编码生成配置表 VO对象")
public class NumberGenerateConfigVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 编码前缀
     */
    @NotBlank(message = "编码前缀不为空")
    @Condition(value = Keyword.LIKE)
    @Schema(description = "编码前缀")
    private String prefixCode;


    /**
     * 时间格式
     */
    @Schema(description = "时间格式")
    private String dateFormat;

    /**
     * 流水号长度
     */
    @Schema(description = "流水号长度")
    private Integer streamLength;

    /**
     * 生成总数
     */
    @Schema(description = "生成总数")
    private Long generateNum;

    /**
     * 当前数
     */
    @Schema(description = "当前数")
    private Long currentNum;

    /**
     * 递增最大值步长
     */
    @Schema(description = "递增最大值步长")
    private Long stepSize;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String description;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
