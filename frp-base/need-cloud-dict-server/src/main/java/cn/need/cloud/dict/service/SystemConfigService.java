package cn.need.cloud.dict.service;

import cn.need.cloud.dict.model.entity.SystemConfig;
import cn.need.framework.common.mybatis.base.SuperService;

/**
 * 系统配置  服务接口
 *
 * <AUTHOR>
 */
public interface SystemConfigService extends SuperService<SystemConfig> {

    /**
     * 设置系统配置的有效性
     *
     * @param entity 需要设置的系统配置对象
     * @param state  目标状态值
     * @return int 受影响行数
     */
    int active(SystemConfig entity, int state);

    /**
     * 根据配置编码，获取系统配置数据信息
     *
     * @param confCode  配置编码
     * @param confGroup 分组信息
     * @return SystemConfig
     */
    SystemConfig getByConfCode(String confCode, String confGroup);
}