package cn.need.cloud.dict.init;

import cn.need.cloud.dict.client.constant.LangFieldConstant;
import cn.need.cloud.dict.client.constant.enums.FieldShowTypeEnum;
import cn.need.cloud.dict.service.LangFieldService;
import cn.need.framework.common.core.map.MapUtil;
import cn.need.framework.common.dict.api.ExceptionLangRepertory;
import cn.need.framework.common.dict.api.LangDictRepertory;
import cn.need.framework.common.dict.api.LangFieldRepertory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * package cn.need.cloud.dict.init;
 * <p>
 * <p>
 * /**
 *
 * <AUTHOR>
 * @since 2024/1/25
 */
@Slf4j
@Component
public class LangFieldInitialize implements InitializingBean {

    private final LangFieldRepertory langFieldRepertory;

    private final LangFieldService langFieldService;

    private final ExceptionLangRepertory exceptionLangRepertory;

    private final LangDictRepertory langDictRepertory;

    public LangFieldInitialize(LangFieldRepertory langFieldRepertory, LangFieldService langFieldService, ExceptionLangRepertory exceptionLangRepertory,
                               LangDictRepertory langDictRepertory) {
        this.langFieldRepertory = langFieldRepertory;
        this.langFieldService = langFieldService;
        this.exceptionLangRepertory = exceptionLangRepertory;
        this.langDictRepertory = langDictRepertory;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化多语言配置至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        //构建common的字典集合
        Map<String, Map<String, String>> allLang = getLangField();
        langFieldRepertory.initialization(allLang);
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化多语言配置至redis【完成】，总数[{}{}", allLang.size(), "] <<<<<<<<<<<<<<<<<<<<<");

        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化多语言异常信息配置至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        Map<String, Map<String, String>> allExceptionLang = langFieldService.loadLangFieldResult(LangFieldConstant.EXCEPTION, null);
        exceptionLangRepertory.initialization(allExceptionLang);
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化多语言异常信息配置至redis【完成】，总数[{}{}", allExceptionLang.size(), "] <<<<<<<<<<<<<<<<<<<<<");

        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化多语言字典信息配置至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        Map<String, Map<String, String>> allDictLang = langFieldService.loadLangDictResult(LangFieldConstant.DICT, null);
        langDictRepertory.initialization(allDictLang);
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化多语言字典信息配置至redis【完成】，总数[{}{}", allDictLang.size(), "] <<<<<<<<<<<<<<<<<<<<<");
    }


    private Map<String, Map<String, String>> getLangField() {
        Map<String, Map<String, String>> allLang = langFieldService.loadLangFieldResult(LangFieldConstant.FIELD, null);
        Map<String, Map<String, String>> dictLang = langFieldService.loadLangFieldResult(LangFieldConstant.DICT, null);
        Map<String, Map<String, String>> allTipsLang = langFieldService.loadLangFieldTipsResult(null, FieldShowTypeEnum.TIPS.getCode());
        for (Map.Entry<String, Map<String, String>> entry : allTipsLang.entrySet()) {
            String key = entry.getKey();
            Map<String, String> tipsValue = entry.getValue();

            if (allLang.containsKey(key)) {
                Map<String, String> langValue = allLang.get(key);
                langValue.putAll(tipsValue);
                allLang.put(key, langValue);
            }
        }
        if (MapUtil.isNotEmpty(dictLang)) {
            for (Map.Entry<String, Map<String, String>> entry : dictLang.entrySet()) {
                String key = entry.getKey();
                Map<String, String> dictMap = entry.getValue();
                if (allLang.containsKey(key)) {
                    Map<String, String> langValue = allLang.get(key);
                    langValue.putAll(dictMap);
                    allLang.put(key, langValue);
                }
            }
        }
        return allLang;
    }
}
