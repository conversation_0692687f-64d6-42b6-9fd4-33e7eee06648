package cn.need.cloud.dict.model.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/8/6 15:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "系统版本发布 创建或编辑请求 VO 对象")
public class VersionReleaseRemoveReqVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 281632834135717428L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    @Schema(description = "用户id", hidden = true)
    private Long userId;

}
