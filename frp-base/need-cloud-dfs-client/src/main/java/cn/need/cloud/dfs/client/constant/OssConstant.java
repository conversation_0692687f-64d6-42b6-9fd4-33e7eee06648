package cn.need.cloud.dfs.client.constant;

import java.util.Arrays;
import java.util.List;

/**
 * oss对象存储常量
 *
 * <AUTHOR>
 */
public class OssConstant {

    /**
     * 文件图片类型
     */
    public static final List<String> IMAGE_TYPE = Arrays.asList("image/jpeg", "image/jpg", "image/png", "image/gif");

    /**
     * 文件图片类型
     */
    public static final List<String> DOC_TYPE = Arrays.asList("image/jpeg", "image/jpg", "image/png", "image/gif", "application/pdf", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/msword", "application/vnd.ms-excel", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "application/zip", "application/rar", "application/x-rar-compressed; version=5", "application/x-rar-compressed", "application/x-tika-ooxml");

    /**
     * 文件图片类型
     */
    public static final List<String> DOC_TYPE_MSG = Arrays.asList("jpeg", "jpg", "png", "gif", "pdf", "word", "doc", "docx", "xls", "xlsx", "zip", "rar");
}
