package cn.need.cloud.log.client.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RecordTypeEnum {

    /**
     * 请求信息
     */
    REQUEST("REQUEST", 1, "请求信息"),

    /**
     * 响应信息
     */
    RESPONSE("RESPONSE", 2, "响应信息");

    /**
     * 编号
     */
    private final String code;

    /**
     * 值
     */
    private final Integer value;

    /**
     * 描述
     */
    private final String description;
}
