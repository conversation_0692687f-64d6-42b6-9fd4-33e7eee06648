package cn.need.cloud.log.client.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志记录表 dto对象
 *
 * <AUTHOR>
 */
@Data
public class OperationRecordDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    private String recordId;

    /**
     * 操作用户
     */
    private String operationUser;

    /**
     * 操作用户角色
     */
    private String operationUserRole;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作URL
     */
    private String operationUrl;

    /**
     * 操作ip
     */
    private String operationIp;

    /**
     * 操作来源，用来标记是PC端还是小程序
     */
    private String operationSource;

    /**
     * 操作模块
     */
    private String operationModule;

    /**
     * 操作功能
     */
    private String operationFunction;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 状态，成功或失败
     */
    private String status;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

}