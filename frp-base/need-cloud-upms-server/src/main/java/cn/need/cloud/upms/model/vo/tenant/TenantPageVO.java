package cn.need.cloud.upms.model.vo.tenant;

import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 租户 vo对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "租户 VO对象")
public class TenantPageVO extends SuperVO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * id
     */
    @Schema(description = "业务id")
    private Long businessId;

    /**
     * 租户编码
     */
    @Schema(description = "租户编码")
    private String tenantCode;

    /**
     * 状态 1:有效 0:无效
     */
    @Schema(description = "状态 1:有效 0:无效")
    private Integer state;

    /**
     * 租户logo
     */
    @Schema(description = "租户logo")
    private String tenantLogo;

    /**
     * 合作伙伴名称
     */
    @Schema(description = "合作伙伴名称")
    private String name;

    /**
     * 合作伙伴简称
     */
    @Schema(description = "合作伙伴简称")
    private String abbrName;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactName;

    /**
     * 联系人邮箱
     */
    @Schema(description = "联系人邮箱")
    private String contactEmail;

    /**
     * 合作伙伴类型
     */
    @Schema(description = "合作伙伴类型")
    private List<String> partnerType;

    /**
     * 租户状态 1:有效 0:无效
     */
    @Schema(description = "状态 1:有效 0:无效")
    private Integer userActiveFlag;

}
