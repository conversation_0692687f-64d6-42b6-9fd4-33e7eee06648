package cn.need.cloud.upms.converter.permissions;

import cn.need.cloud.upms.client.dto.PermissionsDTO;
import cn.need.cloud.upms.model.entity.permissions.Permissions;
import cn.need.cloud.upms.model.vo.permissions.PermissionsVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 系统权限信息  对象转换器
 *
 * <AUTHOR>
 */
public class PermissionsConverter extends AbstractModelConverter<Permissions, PermissionsVO, PermissionsDTO> {
}
