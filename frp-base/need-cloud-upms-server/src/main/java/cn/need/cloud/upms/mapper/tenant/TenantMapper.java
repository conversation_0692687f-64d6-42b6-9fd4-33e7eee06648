package cn.need.cloud.upms.mapper.tenant;


import cn.need.cloud.upms.model.entity.tenant.Tenant;
import cn.need.cloud.upms.model.query.tenant.TenantQuery;
import cn.need.cloud.upms.model.vo.tenant.TenantPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户 Mapper接口
 *
 * <AUTHOR>
 */
public interface TenantMapper extends SuperMapper<Tenant> {

    /**
     * 根据条件获取租户列表
     *
     * @param query 查询条件
     * @return 租户集合
     */
    default List<TenantPageVO> listByQuery(TenantQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取租户分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 租户集合
     */
    List<TenantPageVO> listByQuery(@Param("qo") TenantQuery query, @Param("page") Page<?> page);

    /**
     * 根据用户id获取租户列表（不包含禁用的租户）
     *
     * @param userId 用户id
     * @return 租户集合
     */
    List<TenantPageVO> listTenantByUserId(@Param("userId") Long userId);
}
