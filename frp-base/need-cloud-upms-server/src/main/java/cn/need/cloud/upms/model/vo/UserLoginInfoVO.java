package cn.need.cloud.upms.model.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/28
 */
@Data
public class UserLoginInfoVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 7571737346434302903L;

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 所属区域id
     */
    private Long regionId;

    /**
     * 所属区域
     */
    private String region;

    /**
     * 关联企业id
     */
    private Long relatedCompanyId;

    /**
     * 关联企业
     */
    private String relatedCompanyName;

    /**
     * 关联企业id
     */
    private Long baseId;

    /**
     * 所属基地
     */
    private String baseName;

    /**
     * 角色
     */
    private List<String> roleNameList;

    private Long siteId;

    private Long tenantId;

    private String userType;

    /**
     * 供应商id
     */
    private Long supplierId;
}
