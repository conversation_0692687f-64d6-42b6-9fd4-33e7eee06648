package cn.need.cloud.upms.converter.permissions;

import cn.need.cloud.upms.client.dto.BusinessRoleDTO;
import cn.need.cloud.upms.model.entity.permissions.BusinessRole;
import cn.need.cloud.upms.model.vo.permissions.BusinessRoleVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 业务角色信息  对象转换器
 *
 * <AUTHOR>
 */
public class BusinessRoleConverter extends AbstractModelConverter<BusinessRole, BusinessRoleVO, BusinessRoleDTO> {

}