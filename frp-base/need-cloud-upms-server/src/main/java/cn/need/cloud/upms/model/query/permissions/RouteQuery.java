package cn.need.cloud.upms.model.query.permissions;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 接口权限路由 UpdateParam对象
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "接口权限路由 Query对象")
public class RouteQuery extends SuperQuery {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 所属服务
     */
    @Schema(description = "所属服务")
    private String serverName;

    /**
     * 请求方式
     */
    @Schema(description = "请求方式")
    private String requestMethod;


    /**
     * 路径
     */
    @Schema(description = "路径")
    private String path;

    /**
     * 是否忽略权限控制标识
     */
    @Schema(description = "是否忽略权限控制标识")
    private Boolean ignoreFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


}