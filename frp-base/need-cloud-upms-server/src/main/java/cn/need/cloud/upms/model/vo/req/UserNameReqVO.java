package cn.need.cloud.upms.model.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 根据用户名查询非供应商角色列表条件
 *
 * <AUTHOR>
 */
@Schema(description = "根据用户名查询非供应商角色列表条件")
@Data
public class UserNameReqVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * id集合
     */
    @Schema(description = "id集合")
    private List<Long> ids;
}
