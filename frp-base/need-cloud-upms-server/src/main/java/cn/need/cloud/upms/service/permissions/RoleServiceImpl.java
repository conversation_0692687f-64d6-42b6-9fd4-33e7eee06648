package cn.need.cloud.upms.service.permissions;

import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.converter.permissions.RoleConverter;
import cn.need.cloud.upms.mapper.permissions.RoleMapper;
import cn.need.cloud.upms.model.entity.permissions.Permissions;
import cn.need.cloud.upms.model.entity.permissions.Role;
import cn.need.cloud.upms.model.entity.permissions.RolePermissions;
import cn.need.cloud.upms.model.param.permissions.RoleCreateParam;
import cn.need.cloud.upms.model.param.permissions.RoleUpdateParam;
import cn.need.cloud.upms.model.query.role.RoleQuery;
import cn.need.cloud.upms.model.vo.permissions.RolePageVO;
import cn.need.cloud.upms.model.vo.permissions.RoleSimpleVO;
import cn.need.cloud.upms.model.vo.permissions.RoleVO;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotEmpty;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;
import static cn.need.framework.starter.security.constant.SecurityConstants.CACHE_ROLE_PERMISSIONS_PREFIX;

/**
 * 系统角色信息 服务实现
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends SuperServiceImpl<RoleMapper, Role> implements RoleService {

    @Resource
    private TenantCacheService tenantCacheService;

    @Resource
    private RolePermissionsService rolePermissionsService;

    @Resource
    private PermissionsService permissionsService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(RoleCreateParam createParam) {
        // 检查传入系统角色信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取系统角色信息转换器实例，用于将系统角色信息参数对象转换为实体对象
        RoleConverter converter = Converters.get(RoleConverter.class);

        // 将系统角色信息参数对象转换为实体对象并初始化
        Role entity = initRole(converter.toEntity(createParam));

        // 插入系统角色信息实体对象到数据库
        super.insert(entity);

        // 返回系统角色信息ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(RoleUpdateParam updateParam) {
        // 检查传入系统角色信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取系统角色信息转换器实例，用于将系统角色信息参数对象转换为实体对象
        RoleConverter converter = Converters.get(RoleConverter.class);

        // 将系统角色信息参数对象转换为实体对象
        Role entity = converter.toEntity(updateParam);

        // 更新角色信息到数据库 返回影响数据条数
        return super.update(entity);

    }

    @Override
    public List<RolePageVO> listByQuery(RoleQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<RolePageVO> pageByQuery(PageSearch<RoleQuery> search) {
        Page<Role> page = Conditions.page(search, entityClass);
        return new PageData<>(mapper.listByQuery(search.getCondition(), page), page);
    }

    @Override
    public RoleVO detailById(Long id) {
        Role entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in Role");
        }
        return buildRoleVO(entity);
    }

    @Override
    public List<RoleSimpleVO> listSimple() {

        // 获取当前用户所属租户类型
        TenantCache tenant = tenantCacheService.getById(TenantContextHolder.getTenantId());

        // 如果租户类型信息不存在，抛出权限拒绝异常
        if (ObjectUtil.isEmpty(tenant)) {
            throw new BusinessException("Permission denied");
        }

        // 查询并返回符合当前租户类型且状态为启用的角色的简单信息视图列表
        return BeanUtil.copyNew(listByGroupType(tenant.getPartnerType()), RoleSimpleVO.class);
    }

    @Override
    public List<Role> listByGroupType(Integer state, Collection<String> partnerTypes) {
        return lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(state), Role::getState, state)
                .in(Role::getRoleGroup, partnerTypes)
                .list();
    }

    /**
     * 初始化角色权限数据至Redis缓存
     * 该方法用于将角色权限数据加载到Redis缓存中，以提高权限数据的访问速度
     */
    @Override
    public void initRoleCache() {
        // 创建时间测量对象，用于记录方法执行时间
        TimeMeter meter = new TimeMeter();
        // 日志记录：初始化角色权限数据至Redis开始
        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化角色权限数据至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");

        // 获取所有角色权限数据
        List<RolePermissions> rolePermissionsList = rolePermissionsService.list();
        // 检查角色权限列表是否为空，如果为空则记录警告日志并返回
        if (rolePermissionsList.isEmpty()) {
            log.warn("角色权限列表为空，无法初始化角色权限数据");
            return;
        }

        // 获取所有权限数据
        List<Permissions> permissionsList = permissionsService.list();
        // 检查权限列表是否为空，如果为空则记录警告日志并返回
        if (permissionsList.isEmpty()) {
            log.warn("权限列表为空，无法初始化角色权限数据");
            return;
        }

        // 将角色权限数据根据roleId分组
        Map<Long, List<RolePermissions>> rolePermissionsMap = rolePermissionsList.stream()
                .collect(Collectors.groupingBy(RolePermissions::getRoleId));

        // 将权限数据转换为Map，便于后续查询
        Map<Long, Permissions> permissionsMap = ObjectUtil.toMap(permissionsList, Permissions::getId);

        // 遍历角色权限Map，初始化每个角色的权限数据到Redis
        rolePermissionsMap.forEach((roleId, rolePermissionsListTemp) -> {

            // 创建集合，用于存储角色的所有路由路径
            Set<String> routePathSet = Sets.newHashSet();

            // 遍历角色的权限列表，收集所有路由路径
            rolePermissionsListTemp.forEach(rolePermissions -> {
                Permissions permissions = permissionsMap.get(rolePermissions.getPermissionsId());

                // 检查权限对象和路由路径是否非空，如果非空则添加路由路径到集合中
                if (ObjectUtil.isNotEmpty(permissions) && ObjectUtil.isNotEmpty(permissions.getRoutePath())) {
                    routePathSet.addAll(permissions.getRoutePath());
                }

            });
            // 将角色的路由路径集合更新到Redis中，并记录日志
            try {
                redisTemplate.opsForValue().set(CACHE_ROLE_PERMISSIONS_PREFIX + roleId, routePathSet);
                log.info("成功更新角色 {} 的权限缓存", roleId);
            } catch (Exception e) {
                // 如果更新Redis失败，则记录错误日志
                log.error("Failed to update role permissions in Redis for roleId: {}", roleId, e);
            }

        });

        // 日志记录：初始化角色权限数据至Redis完成，并记录总耗时
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化角色权限数据至redis【完成】，总耗时：{}ms <<<<<<<<<<<<<<<<<<<<<", meter.sign());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAndNote(Long id, String note) {
        // 根据id返回影响数据条数
        return super.removeAndNote(id, note);
    }


    //********************************************* 私有方法 *********************************************/

    /**
     * 初始化系统角色信息对象
     * 此方法用于设置系统角色信息对象的必要参数，确保其处于有效状态
     *
     * @param entity 系统角色信息对象，不应为空
     * @return 返回初始化后的系统角色信息
     * @throws BusinessException 如果传入的系统角色信息为空，则抛出此异常
     */
    private Role initRole(Role entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("Role cannot be empty");
        }

        //  初始化基础信息
        setBasicInfo(entity);

        // 返回初始化后的配置对象
        return entity;
    }


    /**
     * 构建系统角色信息VO对象
     *
     * @param entity 系统角色信息对象
     * @return 返回包含详细信息的系统角色信息VO对象
     */
    private RoleVO buildRoleVO(Role entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的系统角色信息VO对象
        return Converters.get(RoleConverter.class).toVO(entity);
    }

    /**
     * 新增或是修改时，设置基本信息
     *
     * @param entity 需要设置的角色对象
     */
    private void setBasicInfo(Role entity) {
        //1. 设置排序值
        setSorting(entity);
        //2. 设置状态
        setState(entity);
    }

    /**
     * 设置角色排序值
     */
    private void setSorting(Role entity) {
        //1. 情况一，当前设值对象的排序值不为空，直接退出
        if (isNotNull(entity.getSorting())) {
            return;
        }
        //2. 其他情况，设置为默认排序值
        entity.setSorting(getDefaultSorting());
    }

    /**
     * 获取角色的最大排序值
     *
     * @return int 最大排序值
     */
    private int getDefaultSorting() {
        return list().stream().filter(it -> isNotNull(it.getSorting())).mapToInt(Role::getSorting).max().orElse(0) + 1;
    }

    /**
     * 设置角色的状态
     */
    private void setState(Role entity) {
        //1. 情况一，当前设值对象的状态不为空，直接退出
        if (isNotEmpty(entity.getState())) {
            return;
        }
        //2. 其他情况，设置为默认状态
        entity.setState(DataState.ENABLED);
    }


}
