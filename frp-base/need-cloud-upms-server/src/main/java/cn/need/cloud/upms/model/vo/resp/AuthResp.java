package cn.need.cloud.upms.model.vo.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AuthResp implements Serializable {

    private String accessToken;

    private Long userId;

    private String nickName;

    private String email;

    private String name;

    private String userType;

    private List<String> roleCodes;

    private List<Long> roleIds;

    private Long tenantId;

    private String tenantName;

    private Long siteId;

    private String siteName;

    private String avatar;

    private String abbrName;

    /**
     * 供应商id
     */
    private Long supplierId;
}
