package cn.need.cloud.upms.controller.setting;


import cn.need.cloud.upms.cache.util.UserCacheUtil;
import cn.need.cloud.upms.converter.setting.DuoWhiteListConverter;
import cn.need.cloud.upms.model.entity.setting.DuoWhiteList;
import cn.need.cloud.upms.model.param.DuoWhiteListCreateParam;
import cn.need.cloud.upms.model.param.update.DuoWhiteListUpdateParam;
import cn.need.cloud.upms.model.query.DuoWhiteListQuery;
import cn.need.cloud.upms.model.vo.setting.DuoWhiteListPageVO;
import cn.need.cloud.upms.model.vo.setting.DuoWhiteListVO;
import cn.need.cloud.upms.service.setting.DuoWhiteListService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 二次验证白名单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@RestController
@RequestMapping("/api/upms/duo-white-list")
@Tag(name = "二次验证白名单")
public class DuoWhiteListController extends AbstractRestController<DuoWhiteListService, DuoWhiteList, DuoWhiteListConverter, DuoWhiteListVO> {


    @Operation(summary = "新增二次验证白名单", description = "接收二次验证白名单的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) DuoWhiteListCreateParam insertParam) {
        log.info("====> /api/upms/duo-white-list/insert, insertParam={}", JsonUtil.toJson(insertParam));
        // 返回结果
        return success(service.insertByParam(insertParam));
    }


    @Operation(summary = "修改二次验证白名单", description = "接收二次验证白名单的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) DuoWhiteListUpdateParam updateParam) {
        log.info("====> /api/upms/duo-white-list/update, updateParam={}", JsonUtil.toJson(updateParam));
        // 返回结果
        return success(service.updateByParam(updateParam));
    }


    @Operation(summary = "根据id删除二次验证白名单", description = "根据数据主键id，从数据库中删除其对应的数据，并记录原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        log.info("====> /api/upms/duo-white-list/remove, Param={}", JsonUtil.toJson(deletedNoteParam));
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }


    @Operation(summary = "根据id获取二次验证白名单详情", description = "根据数据主键id，从数据库中获取其对应的数据详情")
    @GetMapping(value = "/detail/{id}")
    public Result<DuoWhiteListVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        log.info("====> /api/upms/duo-white-list/detail, id={}", JsonUtil.toJson(id));
        // 获取详情
        DuoWhiteListVO detailVo = service.detailById(id);
        // 填充创建人
        UserCacheUtil.filledCreateBy(detailVo);
        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取二次验证白名单列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @PostMapping(value = "/list")
    public Result<PageData<DuoWhiteListPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<DuoWhiteListQuery> search) {
        log.info("====> /api/upms/duo-white-list/list, search={}", JsonUtil.toJson(search));
        // 获取分页
        PageData<DuoWhiteListPageVO> resultPage = service.pageByQuery(search);
        // 填充创建人
        UserCacheUtil.filledCreateBy(resultPage.getRecords());
        // 返回结果
        return success(resultPage);
    }
}
