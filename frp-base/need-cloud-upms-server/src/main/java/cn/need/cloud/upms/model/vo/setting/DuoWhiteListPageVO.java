package cn.need.cloud.upms.model.vo.setting;

import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 二次验证白名单 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "二次验证白名单 vo对象")
public class DuoWhiteListPageVO extends SuperVO {


    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ipAddress;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;

    /**
     * 删除备注
     */
    @Schema(description = "删除备注")
    private String deletedNote;

}