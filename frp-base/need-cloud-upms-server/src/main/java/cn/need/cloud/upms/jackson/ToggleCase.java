package cn.need.cloud.upms.jackson;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.lang.annotation.*;

/**
 * BigDecimalFormat
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
@JacksonAnnotationsInside
@JsonDeserialize(using = ToggleCaseDeserializer.class)
public @interface ToggleCase {

    /**
     * 是否大写， 默认大写
     */
    boolean upper() default true;
}
