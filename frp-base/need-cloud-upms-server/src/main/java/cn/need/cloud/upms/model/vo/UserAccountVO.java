package cn.need.cloud.upms.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户账号信息 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "用户账号信息 VO对象")
public class UserAccountVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String userAccount;

    /**
     * 账号类型
     */
    @Schema(description = "账号类型")
    private String accountType;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    @Schema(description = "数据状态，0 - 无效，1 - 有效")
    private Integer state;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

}