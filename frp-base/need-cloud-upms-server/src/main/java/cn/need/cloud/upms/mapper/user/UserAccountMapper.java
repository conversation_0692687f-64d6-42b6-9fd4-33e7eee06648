package cn.need.cloud.upms.mapper.user;

import cn.need.cloud.upms.model.entity.user.UserAccount;
import cn.need.framework.common.mybatis.base.SuperMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户账号信息 Mapper接口
 *
 * <AUTHOR>
 */
public interface UserAccountMapper extends SuperMapper<UserAccount> {

    /**
     * 支持多租户账号获取
     *
     * @param account     账号
     * @param accountType 账号类型
     * @param tenantId    租户id
     * @return 用户账号信息
     */
    UserAccount getByAccountAndUser(@Param("account") String account, @Param("accountType") String accountType, @Param("tenantId") Long tenantId, @Param("userId") Long userId);

}