package cn.need.cloud.upms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统用户信息 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "系统用户信息 VO对象")
public class UserSelectVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;
    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;
}
