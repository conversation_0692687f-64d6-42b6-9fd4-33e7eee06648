package cn.need.cloud.upms.model.param.update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 企业运输 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "企业运输 vo对象")
public class PartnerShipUpdateParam implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull
    private Long id;

    /**
     * 提供者合作伙伴ID
     */
    @Schema(description = "提供者合作伙伴ID")
    private Long providerPartnerId;

    /**
     * 客户合作伙伴ID
     */
    @Schema(description = "客户合作伙伴ID")
    private Long clientPartnerId;

    /**
     * 提供者类型
     */
    @Schema(description = "提供者类型")
    private String providerType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;

}