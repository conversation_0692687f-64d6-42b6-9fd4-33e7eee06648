package cn.need.cloud.upms.provider.permissions;

import cn.need.cloud.upms.client.api.RoleClient;
import cn.need.cloud.upms.client.api.path.RolePath;
import cn.need.cloud.upms.client.constant.BusinessRoleType;
import cn.need.cloud.upms.client.dto.RoleDTO;
import cn.need.cloud.upms.converter.permissions.RoleConverter;
import cn.need.cloud.upms.model.entity.permissions.Role;
import cn.need.cloud.upms.service.permissions.BusinessRoleService;
import cn.need.cloud.upms.service.permissions.RoleService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.convert.Converters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.need.framework.common.core.lang.ObjectUtil.isEmpty;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;


/**
 * 角色信息  Feign 提供者
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(RolePath.PREFIX)
public class RoleProvider implements RoleClient {

    @Autowired
    private RoleService roleService;

    @Autowired
    private BusinessRoleService businessRoleService;

    @Override
    @GetMapping(RolePath.GET_ROLE_LIST + "/{id}")
    @Operation(hidden = true)
    @IgnoreAuth
    public Result<List<RoleDTO>> getRoleList(@PathVariable("id") Long id) {
        // return Result.ok(roleService.getRoleList(id));

        return Result.ok();
    }

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return RoleDTO
     */
    @Override
    @GetMapping(RolePath.LIST_BY_USER_ID + "/{userId}")
    @Operation(hidden = true)
//    @IgnoreAuth
    public Result<List<RoleDTO>> listByUserId(@PathVariable("userId") Long userId) {
        List<Long> roleIds = businessRoleService.roleIdsByBusinessId(BusinessRoleType.USER, userId);
        if (isEmpty(roleIds)) {
            return Result.ok(Lists.arrayList());
        }
        List<Role> roles = roleService.listByIds(roleIds);
        return isNotNull(roles) ? Result.ok(Converters.get(RoleConverter.class).toDTO(roles)) : Result.ok(Lists.arrayList());
    }


    /**
     * 通过ids查询角色
     *
     * @return 角色列表
     */
//    @IgnoreAuth
    @Override
    @PostMapping(RolePath.LIST_BY_IDS)
    @Operation(hidden = true)
    public Result<List<RoleDTO>> listByIds(@RequestBody List<Long> ids) {
        List<Role> roles = roleService.listByIds(ids);
        return isEmpty(roles) ? Result.ok(Lists.arrayList()) : Result.ok(BeanUtil.copyNew(roles, RoleDTO.class));
    }

    @Override
    @GetMapping(RolePath.GET_BY_ROLE_NAME)
    @Operation(hidden = true)
    public Result<RoleDTO> getByRoleName(@RequestParam("roleName") String roleName) {
        // Role role = roleService.getByRoleName(roleName);
        // return ObjectUtil.isEmpty(role) ? Result.ok(null) : Result.ok(Converters.get(RoleConverter.class).toDTO(role));
        return Result.ok();
    }

    // /**
    //  * 根据角色编码查询角色
    //  *
    //  * @param roleList 角色编码
    //  * @return 角色列表
    //  */
    // @Override
    // public Result<List<RoleDTO>> listByRoleCode(List<String> roleList) {
    //     // List<Role> roles = roleService.listByCodes(roleList);
    //     // if (ObjectUtil.isEmpty(roles)) {
    //     //     return Result.ok();
    //     // }
    //     // return Result.ok(Converters.get(RoleConverter.class).toDTO(roles));
    //     return Result.ok();
    // }

}
