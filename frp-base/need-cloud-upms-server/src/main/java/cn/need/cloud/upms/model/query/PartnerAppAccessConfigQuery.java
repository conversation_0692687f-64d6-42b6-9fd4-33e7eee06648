package cn.need.cloud.upms.model.query;

import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 企业伙伴app权限配置 query对象
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "企业伙伴app权限配置 query对象")
public class PartnerAppAccessConfigQuery extends SuperQuery {

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String name;

    /**
     * 合作伙伴用户ID
     */
    @Schema(description = "合作伙伴用户ID")
    private Long partnerUserId;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 应用密钥
     */
    @Schema(description = "应用密钥")
    private String appSecret;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型集合")
    @Condition(value = Keyword.IN, fields = {"serviceType"})
    private List<String> serviceTypeList;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 删除备注
     */
    @Schema(description = "删除备注")
    private String deletedNote;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;


}