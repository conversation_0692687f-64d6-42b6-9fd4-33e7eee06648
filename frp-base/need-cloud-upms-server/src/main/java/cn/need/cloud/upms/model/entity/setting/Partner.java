package cn.need.cloud.upms.model.entity.setting;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 企业伙伴
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("partner")
public class Partner extends SuperModel {


    /**
     * 合作伙伴名称
     */
    @TableField("name")
    private String name;

    /**
     * 合作伙伴简称
     */
    @TableField("abbr_name")
    private String abbrName;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 地址名称
     */
    @TableField("address_name")
    private String addressName;

    /**
     * 公司地址
     */
    @TableField("address_company")
    private String addressCompany;

    /**
     * 国家
     */
    @TableField("address_country")
    private String addressCountry;

    /**
     * 州/省
     */
    @TableField("address_state")
    private String addressState;

    /**
     * 城市
     */
    @TableField("address_city")
    private String addressCity;

    /**
     * 邮编
     */
    @TableField("address_zip_code")
    private String addressZipCode;

    /**
     * 地址1
     */
    @TableField("address_addr1")
    private String addressAddr1;

    /**
     * 地址2
     */
    @TableField("address_addr2")
    private String addressAddr2;

    /**
     * 地址3
     */
    @TableField("address_addr3")
    private String addressAddr3;

    /**
     * 地址邮箱
     */
    @TableField("address_email")
    private String addressEmail;

    /**
     * 地址电话
     */
    @TableField("address_phone")
    private String addressPhone;

    /**
     * 地址备注
     */
    @TableField("address_note")
    private String addressNote;

    /**
     * 合作伙伴类型
     */
    @TableField("partner_type")
    private String partnerType;

    /**
     * 是否激活
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 版本
     */
    @TableField("version")
    @Version
    private Long version;

    /**
     * 删除备注
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 是否住宅地址
     */
    @TableField("address_is_residential")
    private Integer addressIsResidential;

}
