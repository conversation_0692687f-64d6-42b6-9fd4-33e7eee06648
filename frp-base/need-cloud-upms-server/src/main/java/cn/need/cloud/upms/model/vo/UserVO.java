package cn.need.cloud.upms.model.vo;


import cn.need.cloud.upms.model.vo.resp.UserTenantListVO;
import cn.need.framework.common.annotation.dict.DictField;
import cn.need.framework.common.annotation.enums.Keyword;
import cn.need.framework.common.annotation.param.Condition;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统用户信息 vo对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "系统用户信息 VO对象")
public class UserVO extends BaseTenantUserVO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 用户类型
     */
    @Schema(description = "用户类型")
    private String userType;

    /**
     * 用户类型名称
     */
    @Schema(description = "用户类型名称")
    @DictField("USER_CONTROL")
    private String userTypeName;

    /**
     * 终端类型
     */
    @Schema(description = "终端类型")
    private String terminalType;

    /**
     * 终端类型名称
     */
    @Schema(description = "终端类型名称")
    private String terminalTypeName;

    /**
     * 用户的姓名
     */
    @Schema(description = "用户的姓名")
    private String name;

    /**
     * 用户的名字
     */
    @Schema(description = "用户的名字")
    private String firstName;

    /**
     * 用户的姓氏
     */
    @Schema(description = "用户的姓氏")
    private String lastName;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    @Condition(value = Keyword.LIKE)
    private String nickName;

    /**
     * 用户密码
     */
    @Schema(description = "用户密码")
    private String password;

    /**
     * 加密盐值
     */
    @Schema(description = "加密盐值")
    private String salt;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    @Schema(description = "数据状态，0 - 无效，1 - 有效")
    private Integer state;

    /**
     * email
     */
    @Schema(description = "email")
    private String email;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String avatar;

    /**
     * 个性签名
     */
    @Schema(description = "个性签名")
    private String signature;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    @Size(max = 64, message = "用户账号不能超过64个字符")
    private String userAccount;

    /**
     * 账号类型
     */
    @Schema(description = "账号类型")
    @NotBlank(message = "账号类型不能为空")
    private String accountType;

    @Schema(description = "用户岗位id集合")
    private List<Long> positionIds;


    @Schema(description = "用户角色id集合")
    @NotEmpty(message = "用户角色id集合")
    private List<Long> roleIds;

    @Schema(description = "用户角色名称集合")
    private List<String> roleNames;

    /**
     * 新密码
     */
    @Schema(description = "新密码")
    private String newPassword;

    /**
     * 确认密码
     */
    @Schema(description = "确认密码")
    private String verifyPassword;


    /**
     * 支持多名称搜索如 [name1.name2]
     */
    @Schema(description = "支持多名称搜索")
    private List<String> searchNames;

    /**
     * 供应商编码
     */
    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "工号")
    private String jobNumber;


    /**
     * 数据来源系统
     */
    @Schema(description = "数据来源系统")
    private String thirdSystem;

    /**
     * 数据来源
     */
    @Schema(description = "数据来源系统")
    private String dataSource;

    @Schema(description = "数据来源名称")
    @DictField("DATA_SOURCE")
    private String dataSourceName;

    @Schema(description = "当前租户id")
    private String currentTenantId;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "会员等级")
    private String memberLevel;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 用户所属的租户信息
     */
    @Schema(description = "用户所属的租户信息")
    private List<UserTenantListVO> tenantList;

    /**
     * 账号设置到期标记
     */
    @Schema(description = "账号设置到期标记")
    private Integer expiringFlag;

    /**
     * 账号到期日期
     */
    @Schema(description = "账号到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiringDate;

    /**
     * 采购组编码
     */
    @Schema(description = "采购组编码")
    private String purchasingGroupCode;


    /**
     * 所属组织id
     */
    @Schema(description = "id主键")
    private Long organizationId;

    /**
     * 角色名称字符拼接
     */
    @Schema(description = "角色名称字符拼接")
    private String roleNamesStr;

    /**
     * 租户名称字符拼接
     */
    @Schema(description = "租户名称字符拼接")
    private String tenantNamesStr;

    /**
     * 组织名称字符拼接
     */
    @Schema(description = "组织名称字符拼接")
    private String orgNamesStr;


    /**
     * 所属组织id集合
     */
    @Schema(description = "id主键")
    private List<Long> orgIds;

    /**
     * 可查看的数据权限
     */
    @Schema(description = "可查看的数据权限")
    private String dataScope;

    /**
     * 租户id集合
     */
    @Schema(description = "租户id集合")
    private List<Long> tenantIds;


}
