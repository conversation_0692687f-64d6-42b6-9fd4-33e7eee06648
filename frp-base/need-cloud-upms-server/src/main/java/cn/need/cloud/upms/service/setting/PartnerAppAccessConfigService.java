package cn.need.cloud.upms.service.setting;

import cn.need.cloud.upms.model.entity.setting.PartnerAppAccessConfig;
import cn.need.cloud.upms.model.param.PartnerAppAccessConfigCreateParam;
import cn.need.cloud.upms.model.param.update.PartnerAppAccessConfigUpdateParam;
import cn.need.cloud.upms.model.query.PartnerAppAccessConfigQuery;
import cn.need.cloud.upms.model.vo.setting.PartnerAppAccessConfigPageVO;
import cn.need.cloud.upms.model.vo.setting.PartnerAppAccessConfigVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 企业伙伴app权限配置 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface PartnerAppAccessConfigService extends SuperService<PartnerAppAccessConfig> {

    /**
     * 根据参数新增企业伙伴app权限配置
     *
     * @param createParam 请求创建参数，包含需要插入的企业伙伴app权限配置的相关信息
     * @return 企业伙伴app权限配置ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(PartnerAppAccessConfigCreateParam createParam);


    /**
     * 根据参数更新企业伙伴app权限配置
     *
     * @param updateParam 请求创建参数，包含需要更新的企业伙伴app权限配置的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(PartnerAppAccessConfigUpdateParam updateParam);

    /**
     * 根据查询条件获取企业伙伴app权限配置列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个企业伙伴app权限配置对象的列表(分页)
     */
    List<PartnerAppAccessConfigPageVO> listByQuery(PartnerAppAccessConfigQuery query);

    /**
     * 根据查询条件获取企业伙伴app权限配置列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个企业伙伴app权限配置对象的列表(分页)
     */
    PageData<PartnerAppAccessConfigPageVO> pageByQuery(PageSearch<PartnerAppAccessConfigQuery> search);

    /**
     * 根据ID获取企业伙伴app权限配置
     *
     * @param id 企业伙伴app权限配置ID
     * @return 返回企业伙伴app权限配置VO对象
     */
    PartnerAppAccessConfigVO detailById(Long id);

    /**
     * 根据ID更新合作伙伴应用访问配置的激活状态
     * 此方法使用事务管理，当方法执行过程中抛出Exception类及其子类时回滚事务
     *
     * @param id 合作伙伴应用访问配置的ID
     */
    void updateStateById(Long id);
}