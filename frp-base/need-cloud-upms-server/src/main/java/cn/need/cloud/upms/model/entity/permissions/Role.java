package cn.need.cloud.upms.model.entity.permissions;

import cn.need.framework.common.annotation.validation.Unique;
import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统角色信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upms_role")
public class Role extends SuperModel {

    /**
     * 角色编号
     */
    @TableField("role_code")
    @Unique("The role code is not unique")
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色名称
     */
    @TableField("role_name_en")
    private String roleNameEn;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    @TableField("state")
    private Integer state;

    /**
     * 分组信息，同一分组下的配置编号唯一
     */
    @TableField("role_group")
    private String roleGroup;

    /**
     * 排序字段
     */
    @TableField("sorting")
    private Integer sorting;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;
}
