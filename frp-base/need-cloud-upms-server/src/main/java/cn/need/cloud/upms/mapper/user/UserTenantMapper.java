package cn.need.cloud.upms.mapper.user;

import cn.need.cloud.upms.model.entity.user.UserTenant;
import cn.need.cloud.upms.model.query.tenant.UserTenantQuery;
import cn.need.cloud.upms.model.vo.tenant.UserTenantPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户租户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Mapper
public interface UserTenantMapper extends SuperMapper<UserTenant> {

    /**
     * 根据条件获取用户租户信息列表
     *
     * @param query 查询条件
     * @return 用户租户信息集合
     */
    default List<UserTenantPageVO> listByQuery(UserTenantQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取用户租户信息分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 用户租户信息集合
     */
    List<UserTenantPageVO> listByQuery(@Param("qo") UserTenantQuery query, @Param("page") Page<?> page);

}