package cn.need.cloud.upms.model.query;

import cn.need.framework.common.mybatis.model.SuperQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 企业运输 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业运输 query对象")
public class PartnerShipQuery extends SuperQuery {

    /**
     * appId
     */
    @Schema(description = "appId")
    private String appId;

    /**
     * 提供者合作伙伴ID
     */
    @Schema(description = "提供者合作伙伴ID")
    private Long providerPartnerId;

    /**
     * 客户合作伙伴ID
     */
    @Schema(description = "客户合作伙伴ID")
    private Long clientPartnerId;

    /**
     * 提供者类型
     */
    @Schema(description = "提供者类型")
    private String providerType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


}