package cn.need.cloud.upms.model.vo.setting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = " vo对象")
public class BaseUserVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "名字")
    private String firstName;

    @Schema(description = "姓氏")
    private String lastName;
}
