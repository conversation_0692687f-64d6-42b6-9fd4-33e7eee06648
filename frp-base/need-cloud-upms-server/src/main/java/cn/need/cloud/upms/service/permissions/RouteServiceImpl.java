package cn.need.cloud.upms.service.permissions;


import cn.need.cloud.upms.converter.permissions.RouteConverter;
import cn.need.cloud.upms.mapper.permissions.RouteMapper;
import cn.need.cloud.upms.model.entity.permissions.Route;
import cn.need.cloud.upms.model.param.permissions.RouteCreateParam;
import cn.need.cloud.upms.model.param.permissions.RouteUpdateParam;
import cn.need.cloud.upms.model.query.permissions.RouteQuery;
import cn.need.cloud.upms.model.vo.permissions.RoutePageVO;
import cn.need.cloud.upms.model.vo.permissions.RouteVO;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 接口权限路由 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Service
public class RouteServiceImpl extends SuperServiceImpl<RouteMapper, Route> implements RouteService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(RouteCreateParam createParam) {
        // 检查传入接口权限路由参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取接口权限路由转换器实例，用于将接口权限路由参数对象转换为实体对象
        RouteConverter converter = Converters.get(RouteConverter.class);

        // 将接口权限路由参数对象转换为实体对象并初始化
        Route entity = initRoute(converter.toEntity(createParam));

        // 插入接口权限路由实体对象到数据库
        super.insert(entity);

        // 返回接口权限路由ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(RouteUpdateParam updateParam) {
        // 检查传入接口权限路由参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取接口权限路由转换器实例，用于将接口权限路由参数对象转换为实体对象
        RouteConverter converter = Converters.get(RouteConverter.class);

        // 将接口权限路由参数对象转换为实体对象
        Route entity = converter.toEntity(updateParam);

        // 执行更新接口权限路由操作
        return super.update(entity);

    }

    @Override
    public List<RoutePageVO> listByQuery(RouteQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<RoutePageVO> pageByQuery(PageSearch<RouteQuery> search) {
        Page<Route> page = Conditions.page(search, entityClass);
        List<RoutePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public RouteVO detailById(Long id) {
        Route entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in Route");
        }
        return buildRouteVO(entity);
    }

    /**
     * 初始化接口权限路由对象
     * 此方法用于设置接口权限路由对象的必要参数，确保其处于有效状态
     *
     * @param entity 接口权限路由对象，不应为空
     * @return 返回初始化后的接口权限路由
     * @throws BusinessException 如果传入的接口权限路由为空，则抛出此异常
     */
    private Route initRoute(Route entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("Route cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建接口权限路由VO对象
     *
     * @param entity 接口权限路由对象
     * @return 返回包含详细信息的接口权限路由VO对象
     */
    private RouteVO buildRouteVO(Route entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的接口权限路由VO对象
        return Converters.get(RouteConverter.class).toVO(entity);
    }

}
