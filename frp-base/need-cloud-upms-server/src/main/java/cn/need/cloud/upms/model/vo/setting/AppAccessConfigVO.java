package cn.need.cloud.upms.model.vo.setting;

import cn.need.framework.common.support.api.SuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * app权限配置 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "app权限配置 vo对象")
public class AppAccessConfigVO extends SuperVO {


    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 应用密钥
     */
    @Schema(description = "应用密钥")
    private String appSecret;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;

    /**
     * 删除备注
     */
    @Schema(description = "删除备注")
    private String deletedNote;

    /**
     * 默认操作员ID
     */
    @Schema(description = "默认操作员ID")
    private Long defaultOperatorId;

    /**
     * 默认租户ID
     */
    @Schema(description = "默认租户ID")
    private Long defaultTenantId;

}