package cn.need.cloud.upms.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * PartnerInfoVO vo对象
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@Schema(description = "应用权限配置新建参数")
public class AppAccessConfigCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 应用密钥
     */
    @Schema(description = "应用密钥")
    private String appSecret;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serviceType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;

    /**
     * 删除备注
     */
    @Schema(description = "删除备注")
    private String deletedNote;

    /**
     * 默认操作员ID
     */
    @Schema(description = "默认操作员ID")
    private Long defaultOperatorId;

    /**
     * 默认租户ID
     */
    @Schema(description = "默认租户ID")
    private Long defaultTenantId;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID")
    private Long createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者ID
     */
    @Schema(description = "更新者ID")
    private Long updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志
     */
    @Schema(description = "删除标志")
    private Integer removeFlag;

}