package cn.need.cloud.upms.model.entity.permissions;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 接口权限路由
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upms_route")
public class Route extends SuperModel {

    /**
     * 所属服务
     */
    @TableField("server_name")
    private String serverName;

    /**
     * 请求方式
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 路径
     */
    @TableField("path")
    private String path;

    /**
     * 是否忽略权限控制标识
     */
    @TableField("ignore_flag")
    private Boolean ignoreFlag;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
