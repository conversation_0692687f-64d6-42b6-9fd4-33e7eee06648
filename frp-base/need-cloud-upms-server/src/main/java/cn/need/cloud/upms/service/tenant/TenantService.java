package cn.need.cloud.upms.service.tenant;

import cn.need.cloud.upms.client.dto.TenantQueryDTO;
import cn.need.cloud.upms.model.entity.tenant.Tenant;
import cn.need.cloud.upms.model.param.tenant.TenantCreateParam;
import cn.need.cloud.upms.model.param.tenant.TenantUpdateParam;
import cn.need.cloud.upms.model.query.tenant.TenantQuery;
import cn.need.cloud.upms.model.vo.tenant.TenantPageVO;
import cn.need.cloud.upms.model.vo.tenant.TenantVO;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 租户 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
public interface TenantService extends SuperService<Tenant> {

    /**
     * 初始化租户缓存方法
     * <p>
     * 本方法旨在从数据库中加载所有租户信息，并将其复制到缓存中，以优化系统性能和减少数据库访问次数
     * 它首先检查从数据库中获取的租户列表是否不为空，然后将每个租户信息转换为缓存所需的格式，最后批量初始化缓存
     */
    void initTenantCache();

    /**
     * 根据参数新增租户
     *
     * @param createParam 请求创建参数，包含需要插入的租户的相关信息
     * @return 租户ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(TenantCreateParam createParam);


    /**
     * 根据参数更新租户
     *
     * @param updateParam 请求创建参数，包含需要更新的租户的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(TenantUpdateParam updateParam);

    /**
     * 根据查询条件获取租户列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个租户对象的列表(分页)
     */
    List<TenantPageVO> listByQuery(TenantQuery query);

    /**
     * 根据查询条件获取租户列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个租户对象的列表(分页)
     */
    PageData<TenantPageVO> pageByQuery(PageSearch<TenantQuery> search);


    /**
     * 根据租户ID获取租户详细信息
     * <p>
     * 此方法首先通过调用getById方法从数据库中查询指定ID的租户实体如果未找到对应的租户实体，
     * 则抛出业务异常，指示ID在租户表中不存在如果找到实体，则将其转换为TenantVO对象并返回
     *
     * @param id 租户ID，用于查询租户详细信息
     * @return TenantVO对象，包含租户详细信息
     * @throws BusinessException 当指定ID的租户不存在时抛出
     */
    TenantVO detailById(Long id);


    /**
     * 根据用户ID获取租户（不包含禁用的租户）
     *
     * @param userId 用户ID
     * @return 返回租户VO对象
     */
    List<TenantPageVO> listTenantByUserId(Long userId);

    /**
     * 根据用户ID获取租户（不包含禁用的租户, 存在角色）
     *
     * @param userId 用户ID
     * @return 返回租户VO对象
     */
    List<TenantPageVO> listTenantByUserIdHasRole(Long userId);

    /**
     * 校验默认租户
     * <p>
     * 此方法的目的是确保指定的租户存在并且不是默认租户默认租户具有特殊的业务逻辑意义，
     * 因此不允许被删除通过此方法可以防止对默认租户进行不合法的操作
     *
     * @param id 租户ID，用于识别特定的租户
     * @throws BusinessException 如果租户不存在或尝试删除默认租户时抛出业务异常
     */
    void verifyDefault(Long id);

    /**
     * 根据租户编码获取租户信息
     *
     * @param codeList 租户编码列表
     * @return 返回一个包含指定编码的租户信息的列表
     */
    List<Tenant> listByCode(Collection<String> codeList);

    /**
     * 根据租户编码获取租户信息
     *
     * @param code 租户编码
     * @return 返回一个包含指定编码的租户信息的列表
     */
    Tenant getByCode(String code);

    /**
     * 根据查询条件获取租户列表
     *
     * @param tenantQueryDTO 查询条件对象，包含了用于筛选租户的各种条件
     * @return 返回一个租户对象的列表
     */
    List<Tenant> list(TenantQueryDTO tenantQueryDTO);
}