<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.permissions.RouteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.upms.model.entity.permissions.Route">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="version" property="version"/>
        <result column="server_name" property="serverName"/>
        <result column="request_method" property="requestMethod"/>
        <result column="path" property="path"/>
        <result column="ignore_flag" property="ignoreFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.version,
        t.server_name,
        t.request_method,
        t.path,
        t.ignore_flag,
        t.remark
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.upms.model.vo.permissions.RoutePageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        upms_route t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.serverName != null and qo.serverName != ''">
            AND t.server_name = #{qo.serverName}
        </if>
        <if test="qo.path != null">
            AND t.path = #{qo.path}
        </if>
        <if test="qo.requestMethod != null">
            AND t.request_method = #{qo.requestMethod}
        </if>
        <if test="qo.ignoreFlag != null">
            AND t.ignore_flag = #{qo.ignoreFlag}
        </if>
        <if test="qo.remark != null and qo.remark != ''">
            AND t.remark = #{qo.remark}
        </if>
    </sql>

</mapper>