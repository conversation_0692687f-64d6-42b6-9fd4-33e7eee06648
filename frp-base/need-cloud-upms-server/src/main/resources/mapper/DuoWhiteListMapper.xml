<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.upms.mapper.setting.DuoWhiteListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.upms.model.entity.setting.DuoWhiteList">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="ipaddress" property="ipAddress"/>
        <result column="note" property="note"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.name,
        t.ipaddress,
        t.note,
        t.version,
        t.deleted_note,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.name != null and qo.name != ''">
            AND t.name = #{qo.name}
        </if>
        <if test="qo.ipaddress != null and qo.ipaddress != ''">
            AND t.ipaddress = #{qo.ipaddress}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time >= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time >= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.upms.model.vo.setting.DuoWhiteListPageVO">
        select
        <include refid="Base_Column_List"/>
        from
        duo_white_list t
        where
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
    </select>

</mapper>
