package cn.need.cloud.upms.client.api;

import cn.need.cloud.upms.client.api.path.RolePath;
import cn.need.cloud.upms.client.dto.RoleDTO;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统角色信息  Feign 客户端
 *
 * <AUTHOR>
 */
@FeignClient(value = "${serviceId.frp-base}", contextId = "RoleClient")
public interface RoleClient {

    /**
     * 根据用户id获取角色名称，供应商用1
     *
     * @param id 用户id
     * @return 角色DTO集合
     */
    @GetMapping(RolePath.GET_ROLE_LIST + "/{id}")
    Result<List<RoleDTO>> getRoleList(@PathVariable("id") Long id);

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return RoleDTO
     */
    @GetMapping(RolePath.LIST_BY_USER_ID + "/{userId}")
    Result<List<RoleDTO>> listByUserId(@PathVariable("userId") Long userId);

    /**
     * 通过ids查询角色
     *
     * @param ids 角色id集合
     * @return 角色列表
     */
    @PostMapping(RolePath.LIST_BY_IDS)
    Result<List<RoleDTO>> listByIds(@RequestBody List<Long> ids);

    /**
     * 根据角色名称获取信息
     *
     * @param roleName 角色名称
     * @return RoleDTO 角色信息
     */
    @GetMapping(RolePath.GET_BY_ROLE_NAME)
    Result<RoleDTO> getByRoleName(@RequestParam("roleName") String roleName);

    // /**
    //  * 根据角色code获取角色信息
    //  *
    //  * @param roleList 角色code集合
    //  * @return 角色信息
    //  */
    // Result<List<RoleDTO>> listByRoleCode(List<String> roleList);

}
