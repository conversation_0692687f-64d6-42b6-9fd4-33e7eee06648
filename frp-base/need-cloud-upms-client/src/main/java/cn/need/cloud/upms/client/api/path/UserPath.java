package cn.need.cloud.upms.client.api.path;

/**
 * 系统用户信息路径类型
 *
 * <AUTHOR>
 */
public class UserPath {

    /**
     * 用户权限管理服务前缀
     */
    public static final String PREFIX = "/client/upms/user";

    /**
     * 通过用户账号进行查询登录
     */
    public static final String LOGIN_USER_NAME = "/loginUsername";

    /**
     * 通过用户账号进行查询登录
     */
    public static final String LOGIN_USER_ID = "/loginUserId";

    /**
     * 根据mts系统token 获取登录用户
     */
    public static final String GET_USER_BY_SIMULATION_TOKEN = "/getUserBySimulationToken";

    /**
     * 根据用户id获取user对象
     */
    public static final String DETAIL = "/detail";

    /**
     * 根据用户id集合获取用户集合
     */
    public static final String LIST_IDS = "/list/ids";

    /**
     * 根据用户类型和用户id集合查询用户信息
     */
    public static final String LIST_BY_TYPE_IDS = "/list-by-type-ids";

    /**
     * 根据用户名集合获取用户集合
     */
    public static final String LIST_BY_USERNAME = "/list-by-usernames";

    /**
     * 传入信息新增用户，供应商用
     */
    public static final String INSERT_SUPPLIER_USER = "/insert-supplier-user";

    /**
     * 传入信息更新用户，供应商用
     */
    public static final String UPDATE_SUPPLIER_USER = "/update-supplier-user";

    /**
     * 根据id逻辑删除对应用户/角色信息，供应商用
     */
    public static final String REMOVE_SUPPLIER_USER = "/remove-supplier-user";

    /**
     * 根据用户账号获取用户账户信息
     */
    public static final String GET_BY_USER_NAME = "get-by-user-name";

    /**
     * 根据用户id获取用户账户信息
     */
    public static final String GET_BY_USER_ID = "get-by-user-id";

    /**
     * 查询所有管理员信息
     */
    public static final String LIST_USER = "/list-user";

    /**
     * 传入信息新增用户,账号，学生关联组织，SAPP用
     */
    public static final String INSERT_USER_ACCOUNT = "insert-user-account";

    /**
     * 获取当前账号数据权限 ALL可看所有
     */
    public static final String GET_CURRENT_DATA_ROLE = "get-current-data-role";

    /**
     * 根据用户名，获取用户id集合
     */
    public static final String LIST_ID_LIKE_USER_NAME = "list-id-like-user-name";

    /**
     * 同步用户微信openId
     */
    public static final String SYNC_USER_OPEN_ID = "sync-user-open-id";

    /**
     * 修改用户信息
     */
    public static final String UPDATE_USER_INFO = "update-user-info";

    /**
     * 批量新增或修改用户方法(此方法会生成，登入信息，供应商管理员角色关系)
     */
    public static final String SAVE = "save";

    /**
     * 注册账号
     */
    public static final String REGISTER = "register";

    /**
     * 删除供应商账号
     */
    public static final String REMOVE_SUPPLIER = "removeSupplier";

    /**
     * 更新用户状态
     */
    public static final String UPDATE_USER_STATE = "updateUserState";

    /**
     * 根据用户名称集合和账号类型查询账号
     */
    public static final String LIST_BY_USER_ACCOUNT = "list-by-userAccount";

    /**
     * 根据供应商
     */
    public static final String LIST_BY_USERIDS = "list-by-userIds";

    /**
     * 新增用户账号方法（为账号绑定openId）
     */
    public static final String INSERT_USER_ACCOUNT_OPENID = "insert-userAccount-openId";

    /**
     * 根据角色编码查询用户ids
     */
    public static final String LIST_USER_ID_BY_ROLE_CODE = "list-user-id-by-role-code";

    /**
     * 根据角色ids查询用户
     */
    public static final String LIST_USER_BY_ROLE_IDS = "/list-user-by-role-ids";

    /**
     * 根据用户ids查询用户
     */
    public static final String USERS = "/list-user-by-user-ids";

    /**
     * 根据用户id或者账号更新用户账号角色信息
     */
    public static final String SAVE_USER_ROLE = "saveUserRole";

    /**
     * 更新用户状态失效
     */
    public static final String UPDATE_USER_UN_READ_BY_USER_ID = "/update-user-un-read-by-user-id";

    /**
     * 获取mts系统员工id与erp系统用户id关联关系
     */
    public static final String MTS_STAFF_MAP = "/mts-staff-map";

    /**
     * 根据角色编码查询用户ids
     */
    public static final String LIST_USER_ID_BY_ROLE_ID = "/list-user-id-by-role-id";

    /**
     * 获取所有有效的用户
     */
    public static final String LIST_ALL_USER_IDS = "/list-all-user-ids";

    /**
     * 获取所有有效的用户
     */
    public static final String GET_USER_ALL_INFO_BY_ID = "/get-user-all-info-by-id";

    /**
     * 获取用户绑定微信信息
     */
    public static final String GET_USER_BIND_WX = "/get-user-bind-wx";

    /**
     * 获取用户组织信息
     */
    public static final String GET_USER_BASIC = "/get-user-basic";

    /**
     * 修改用户角色
     */
    public static final String UPDATE_USER_ROLE = "/updateUserRole";

    /**
     * 按照手机号查询
     */
    public static final String LIST_BY_PHONES = "/listByPhones";
}
