package cn.need.cloud.upms.client.api;

import cn.need.cloud.upms.client.api.path.PermissionPath;
import cn.need.cloud.upms.client.dto.PermissionsDTO;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "${serviceId.frp-base}", contextId = "PermissionClient")
public interface PermissionClient {

    /**
     * 获取路由接口
     *
     * @return List<PermissionsDTO> 路由列表
     */
    @GetMapping(value = PermissionPath.ROUTES)
    Result<List<PermissionsDTO>> routes(String terminal);
}
