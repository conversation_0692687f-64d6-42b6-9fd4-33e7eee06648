package cn.need.cloud.upms.client.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 用户类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    /**
     * 手机登录
     */
    PHONE("PHONE", "手机登录"),
    /**
     * 邮箱登录
     */
    EMAIL("EMAIL", "邮箱登录"),
    /**
     * 用户名
     */
    USER_NAME("USER_NAME", "用户名"),
    /**
     * 微信开放id
     */
    WX_OPEN_ID("WX_OPEN_ID", "微信开放id");


    private final String code;

    private final String desc;
}
