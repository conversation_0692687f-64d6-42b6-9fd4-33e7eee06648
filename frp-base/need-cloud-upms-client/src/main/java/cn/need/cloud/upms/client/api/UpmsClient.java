package cn.need.cloud.upms.client.api;

import cn.need.cloud.upms.client.api.path.UpmsPath;
import cn.need.cloud.upms.client.dto.PermissionsDTO;
import cn.need.cloud.upms.client.dto.RoleDTO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户权限管理 Feign 客户端
 *
 * <AUTHOR>
 **/
@FeignClient(value = "${serviceId.frp-base}", contextId = "UpmsClient")
public interface UpmsClient {


    /**
     * 获取角色列表
     *
     * @param search 筛选条件
     * @return 角色列表
     */
    @PostMapping(UpmsPath.ROLE_LIST)
    Result<PageData<RoleDTO>> roleList(@RequestBody PageSearch<RoleDTO> search);


    /**
     * 获取权限资源列表
     *
     * @param search 筛选条件
     * @return 权限资源列表
     */
    @PostMapping(UpmsPath.PERMISSIONS_LIST)
    Result<PageData<PermissionsDTO>> permissionsList(@RequestBody PageSearch<PermissionsDTO> search);

}
