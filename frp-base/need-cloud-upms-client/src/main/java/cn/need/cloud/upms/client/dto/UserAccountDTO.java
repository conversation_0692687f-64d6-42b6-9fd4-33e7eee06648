package cn.need.cloud.upms.client.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户账号信息 dto对象
 *
 * <AUTHOR>
 */
@Data
public class UserAccountDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 数据状态，0 - 无效，1 - 有效
     */
    private Integer state;

    /**
     * 描述
     */
    private String description;

}