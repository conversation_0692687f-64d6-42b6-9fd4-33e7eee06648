#!/bin/bash
source /etc/profile

# create directory if not exist
if [ ! -d "/app/frp-base" ];then
  mkdir -p /app/frp-base
fi

# clear application log
echo Clearing application.log ...
rm -rf logs
mkdir -p logs/frp-base
touch logs/frp-base/application.log

# stop service
echo Stopping frp-base ...
docker compose -f frp-base.yaml down

# start service
echo Starting frp-base ...
docker compose -f frp-base.yaml up -d

# print log, auto abort when 'JVM running for' is encountered(start successfully)
tail -200f logs/frp-base/application.log | sed '/JVM running for/ q'
