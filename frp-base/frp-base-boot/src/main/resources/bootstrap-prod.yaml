spring:
    # 服务名称
    application:
        name: frp-base-@NACOS_GROUP@
    # 配置spring cloud的配置中心，采用alibaba的nacos做为配置中心
    cloud:
        nacos:
            config:
                server-addr: @NACOS_HOST@:@NACOS_PORT@
                file-extension: yaml
                # nacos命名空间，这儿配置的是namespace id，默认namespace public的id没有，所以下面要注释掉
                #        namespace: public
                namespace: prod
                username: @NACOS_USERNAME@
                password: @NACOS_PASSWORD@
                # 分组
                group: @NACOS_GROUP@
                # nacos共享配置
                shared-configs:
                    -   data-id: frp-database.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-redis.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-feign.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-security.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-xxl-job.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-tenant.yaml
                        group: @SHARE_GROUP@
                    -   data-id: frp-warehouse.yaml
                        group: @SHARE_GROUP@
            discovery:
                server-addr: @NACOS_HOST@:@NACOS_PORT@
                # 命名空间
                #        namespace: public
                namespace: prod
                username: @NACOS_USERNAME@
                password: @NACOS_PASSWORD@