package cn.need.cloud.upms.cache.impl;

import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.TenantRedisTemplate;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.framework.common.core.convert.Convert;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Collection;
import java.util.List;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisTenantCacheServiceImpl implements TenantCacheService {

    /**
     * redisTemplate对象，需要子类注入
     */
    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * 租户Template对象
     */
    private final TenantRedisTemplate template;

    public RedisTenantCacheServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        Validate.notNull(redisTemplate, "redisTemplate can't be null!");
        this.redisTemplate = redisTemplate;
        this.template = new TenantRedisTemplate(this.redisTemplate);
    }

    @Override
    public TenantCache getById(Long tenantId) {
        return this.template.hash().get(StringUtil.toString(tenantId));
    }

    @Override
    public List<TenantCache> listByIds(Collection<Long> tenantIds) {
        return this.template.hash().multiGet(Convert.toList(String.class, tenantIds));
    }


}
