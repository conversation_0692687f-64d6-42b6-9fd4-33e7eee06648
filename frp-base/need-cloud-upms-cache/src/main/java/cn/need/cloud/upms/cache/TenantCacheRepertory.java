package cn.need.cloud.upms.cache;

import cn.need.cloud.upms.cache.bean.TenantCache;

import java.util.List;
import java.util.Set;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public interface TenantCacheRepertory {

    /**
     * 缓存中添加租户缓存数据对象
     *
     * @param cache 租户数据对象
     */
    void addTenant(TenantCache cache);

    /**
     * 缓存中添加租户缓存数据对象集合
     *
     * @param caches 租户数据集合集合
     */
    void addTenant(List<TenantCache> caches);

    /**
     * 根据租户，删除缓存中的租户数据对象
     *
     * @param tenant 租户
     */
    void delTenant(String tenant);

    /**
     * 根据租户集合，删除缓存中的租户数据对象集合
     *
     * @param tenants 租户集合
     */
    void delTenant(List<String> tenants);

    /**
     * 清空全部租户缓存数据
     */
    void clearTenant();

    /**
     * 初始化租户缓存数据
     *
     * @param caches 租户数据集合
     */
    void initTenant(List<TenantCache> caches);

    /**
     * 获取全部租户id集合
     *
     * @return 全部租户id集合
     */
    Set<Long> list();
}
