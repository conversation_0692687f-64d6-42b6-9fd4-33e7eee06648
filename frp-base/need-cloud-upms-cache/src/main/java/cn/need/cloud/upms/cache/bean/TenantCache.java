package cn.need.cloud.upms.cache.bean;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 租户缓存
 *
 * <AUTHOR>
 */
@Data
public class TenantCache implements Serializable {

    @Serial
    private static final long serialVersionUID = 194211791495435145L;

    /**
     * id
     */
    private Long id;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 状态 1:有效 0:无效
     */
    private Integer state;

    /**
     * 租户logo
     */
    private String tenantLogo;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 合作伙伴名称
     */
    private String name;

    /**
     * 合作伙伴简称
     */
    private String abbrName;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 地址名称
     */
    private String addressName;

    /**
     * 公司地址
     */
    private String addressCompany;

    /**
     * 国家
     */
    private String addressCountry;

    /**
     * 州/省
     */
    private String addressState;

    /**
     * 城市
     */
    private String addressCity;

    /**
     * 邮编
     */
    private String addressZipCode;

    /**
     * 地址1
     */
    private String addressAddr1;

    /**
     * 地址2
     */
    private String addressAddr2;

    /**
     * 地址3
     */
    private String addressAddr3;

    /**
     * 地址邮箱
     */
    private String addressEmail;

    /**
     * 地址电话
     */
    private String addressPhone;

    /**
     * 地址备注
     */
    private String addressNote;

    /**
     * 合作伙伴类型
     */
    private List<String> partnerType;

    /**
     * 删除备注
     */
    private String deletedNote;

    /**
     * 是否住宅地址
     */
    private Integer addressIsResidential;

}
