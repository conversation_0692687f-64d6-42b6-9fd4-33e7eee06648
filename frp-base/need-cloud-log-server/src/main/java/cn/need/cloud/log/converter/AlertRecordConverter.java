package cn.need.cloud.log.converter;

import cn.need.cloud.log.client.dto.AlertRecordDTO;
import cn.need.cloud.log.model.entity.AlertRecord;
import cn.need.cloud.log.model.vo.AlertRecordVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 预警日志数据  对象转换器
 *
 * <AUTHOR>
 */
public class AlertRecordConverter extends AbstractModelConverter<AlertRecord, AlertRecordVO, AlertRecordDTO> {

}