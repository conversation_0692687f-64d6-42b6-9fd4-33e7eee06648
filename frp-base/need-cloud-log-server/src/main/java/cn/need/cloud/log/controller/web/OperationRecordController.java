package cn.need.cloud.log.controller.web;

import cn.need.cloud.log.client.constant.OperationFunctionConstant;
import cn.need.cloud.log.client.constant.OperationModuleConstant;
import cn.need.cloud.log.client.constant.OperationTypeConstant;
import cn.need.cloud.log.client.constant.RequestSourceConstant;
import cn.need.cloud.log.converter.OperationRecordConverter;
import cn.need.cloud.log.model.entity.OperationRecord;
import cn.need.cloud.log.model.vo.OperationRecordVO;
import cn.need.cloud.log.model.vo.req.LoggerReqVO;
import cn.need.cloud.log.service.OperationRecordService;
import cn.need.cloud.log.util.LoggerRecordQuery;
import cn.need.cloud.log.util.LoggerRecordValidate;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.dict.api.DictRepertory;
import cn.need.framework.common.dict.entity.Dict;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.util.DictUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static cn.need.cloud.log.util.LoggerRecordQuery.getDictMap;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * 操作日志记录表 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/log/operation-record")
@Tag(name = "操作日志记录管理")
@Slf4j
public class OperationRecordController extends AbstractRestController<OperationRecordService, OperationRecord, OperationRecordConverter, OperationRecordVO> {

    @Autowired
    private DictRepertory repertory;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取操作日志列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    public Result<PageData<OperationRecordVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<LoggerReqVO> search) {
        log.info("====> /api/log/operation-record/list, search={}", JsonUtil.toJson(search));
        LoggerReqVO condition = isNotNull(search.getCondition()) ? search.getCondition() : new LoggerReqVO();
        // 1. 校验参数
        LoggerRecordValidate.queryDate(condition);
        // 2. 缓存查询条件
        setParameterCache(condition);
        // 3. 构建分页条件
        Page<OperationRecord> page = Conditions.page(search, OperationRecord.class);
        // 4. 构建查询条件
        Map<String, List<Dict>> dictMap = getDictMap(repertory, RequestSourceConstant.ROOT, OperationModuleConstant.ROOT, OperationFunctionConstant.ROOT, OperationTypeConstant.ROOT);
        QueryWrapper<OperationRecord> wrapper = LoggerRecordQuery.buildOperationWrapper(condition, dictMap);
        // 5. 获取数据
        page = service.page(page, wrapper);
        // 6. 转换成voList，并设置字典数据
        List<OperationRecordVO> voList = toVO(page.getRecords());
        DictUtil.set(voList);
        return success(new PageData<>(voList, page));
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "清理操作日志接口", description = "根据条件，清理操作日志明细")
    @PostMapping({"operation-log-clear"})
    public Result operationLogClear(@RequestBody LoggerReqVO condition) {
        log.info("====> /api/log/log-record/operation-log-clear, condition={}", JsonUtil.toJson(condition));
        // 1. 校验参数
        LoggerRecordValidate.queryDate(condition);
        // 2. 构建查询条件
        Map<String, List<Dict>> dictMap = getDictMap(repertory, RequestSourceConstant.ROOT, OperationModuleConstant.ROOT, OperationFunctionConstant.ROOT, OperationTypeConstant.ROOT);
        QueryWrapper<OperationRecord> wrapper = LoggerRecordQuery.buildOperationWrapper(condition, dictMap);
        // 3. 根据条件清理日志
        service.clear(wrapper);
        return success();
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "操作日志数据导出接口", description = "将数据导出成Excel文件")
    @GetMapping(value = "export")
    public void exportExcel() {
        log.info("====> /api/log/operation-record/export");
        super.exportExcel(super.getExportName("操作日志记录表"), super.voClass, this::exportExcelData);
    }

    //************************************************** 私有方法 *****************************************************/

    /**
     * 导出
     */
    private List<OperationRecordVO> exportExcelData() {
        LoggerReqVO condition = getParameterCache();
        log.info("=====> OperationRecord ExportExcelData Condition Is {} <======", JsonUtil.toJson(condition));
        // 1. 构建查询条件
        Map<String, List<Dict>> dictMap = getDictMap(repertory, RequestSourceConstant.ROOT, OperationModuleConstant.ROOT, OperationFunctionConstant.ROOT, OperationTypeConstant.ROOT);
        QueryWrapper<OperationRecord> wrapper = isNotNull(condition) ? LoggerRecordQuery.buildOperationWrapper(condition, dictMap) : null;
        // 2. 获取数据
        List<OperationRecord> list = isNotNull(wrapper) ? service.list(wrapper) : Lists.arrayList();
        // 3. 转换成voList，并设置字典数据
        List<OperationRecordVO> voList = toVO(list);
        DictUtil.set(voList);
        return voList;
    }

}
