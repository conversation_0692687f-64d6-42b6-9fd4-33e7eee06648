package cn.need.cloud.log.service.impl;

import cn.need.cloud.log.mapper.RequestPathPatternMapper;
import cn.need.cloud.log.model.entity.RequestPathPattern;
import cn.need.cloud.log.service.RequestPathPatternService;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 请求路径匹配规则 服务实现
 *
 * <AUTHOR>
 */
@Service
public class RequestPathPatternServiceImpl extends SuperServiceImpl<RequestPathPatternMapper, RequestPathPattern> implements RequestPathPatternService {


}
