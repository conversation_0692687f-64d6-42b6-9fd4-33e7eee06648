package cn.need.cloud.log.controller.web;

import cn.need.cloud.log.converter.RequestUsedTimeConverter;
import cn.need.cloud.log.model.entity.RequestUsedTime;
import cn.need.cloud.log.model.vo.RequestUsedTimeVO;
import cn.need.cloud.log.model.vo.req.LoggerReqVO;
import cn.need.cloud.log.service.RequestUsedTimeService;
import cn.need.cloud.log.util.LoggerRecordQuery;
import cn.need.cloud.log.util.LoggerRecordValidate;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.util.DictUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * 请求时长日志信息 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/log/request-used-time")
@Tag(name = "请求时长日志信息管理")
@Slf4j
public class RequestUsedTimeController extends AbstractRestController<RequestUsedTimeService, RequestUsedTime, RequestUsedTimeConverter, RequestUsedTimeVO> {

    @ApiOperationSupport(order = 1)
    @Operation(summary = "请求时长日志数据列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    public Result<PageData<RequestUsedTimeVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<LoggerReqVO> search) {
        log.info("====> /api/log/request-used-time/list, search={}", JsonUtil.toJson(search));
        LoggerReqVO condition = isNotNull(search.getCondition()) ? search.getCondition() : new LoggerReqVO();
        // 1. 校验参数
        LoggerRecordValidate.queryDate(condition);
        // 2. 缓存查询条件
        setParameterCache(condition);
        // 3. 构建分页条件
        Page<RequestUsedTime> page = Conditions.page(search, RequestUsedTime.class);
        // 4. 构建查询条件
        QueryWrapper<RequestUsedTime> requestUsedTimeQueryWrapper = LoggerRecordQuery.buildUsedTimeWrapper(search.getCondition());
        // 5. 获取数据
        page = service.page(page, requestUsedTimeQueryWrapper);
        // 6. 转换成voList，并设置字典数据
        List<RequestUsedTimeVO> voList = toVO(page.getRecords());
        DictUtil.set(voList);
        return success(new PageData<>(voList, page));
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "清理请求时长日志接口", description = "根据条件，清理请求时长日志明细")
    @PostMapping({"request-used-time-clear"})
    public Result requestUsedTimeClear(@RequestBody LoggerReqVO condition) {
        log.info("====> /api/log/log-record/request-used-time-clear, condition={}", JsonUtil.toJson(condition));
        // 1. 校验参数
        LoggerRecordValidate.queryDate(condition);
        // 2. 构建查询条件
        QueryWrapper<RequestUsedTime> wrapper = LoggerRecordQuery.buildUsedTimeWrapper(condition);
        // 3. 根据条件清理日志
        service.clear(wrapper);
        return success();
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "数据导出接口", description = "将数据导出成Excel文件")
    @GetMapping(value = "export")
    public void exportExcel() {
        log.info("====> /api/log/request-used-time/export");
        super.exportExcel(super.getExportName("请求时长日志信息"), super.voClass, this::exportExcelData);
    }

    /**
     * 导出
     */
    private List<RequestUsedTimeVO> exportExcelData() {
        LoggerReqVO condition = getParameterCache();
        log.info("=====> RequestUsedTime ExportExcelData Condition Is {} <======", JsonUtil.toJson(condition));
        // 1. 构建查询条件
        QueryWrapper<RequestUsedTime> wrapper = isNotNull(condition) ? LoggerRecordQuery.buildUsedTimeWrapper(condition) : null;
        // 2. 获取数据
        List<RequestUsedTime> list = isNotNull(wrapper) ? service.list(wrapper) : Lists.arrayList();
        // 3. 转换成voList，并设置字典数据
        List<RequestUsedTimeVO> voList = toVO(list);
        DictUtil.set(voList);
        return voList;
    }

}
