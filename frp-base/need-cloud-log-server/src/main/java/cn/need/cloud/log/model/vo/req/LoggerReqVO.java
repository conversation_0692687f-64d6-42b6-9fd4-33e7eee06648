package cn.need.cloud.log.model.vo.req;

import cn.need.framework.common.core.map.Maps;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import static cn.need.framework.common.core.lang.ObjectUtil.equal;

/**
 * <p>
 * 日志查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Data
@Schema(description = "日志查询条件")
public class LoggerReqVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4173190745694307201L;

    @Schema(description = "查询关键字")
    private String keyword;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date start;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date end;

    @Schema(description = "功能日志条件，空：所有操作日志;USER：用户管理;ROLE:角色管理;WORK_TABLE：排班管理;ORDER_TABLE：排单管理")
    private String function;

    @Schema(description = "请求来源")
    private String source;

    @Schema(description = "请求用时(秒)")
    private Long usedTime;

    public Map<String, Object> getConditionMap(String... columns) {
        Map<String, Object> map = Maps.hashMap();
        for (String column : columns) {
            if (equal(column, "status")) {
                map.put(column, this.status);
                continue;
            }
            if (equal(column, "login_source") || equal(column, "operation_source") || equal(column, "request_source")) {
                map.put(column, this.source);
                continue;
            }
            if (equal(column, "operation_function")) {
                map.put(column, this.function);
            }
        }
        return map;
    }
}
