package cn.need.cloud.log.service;

import cn.need.cloud.log.model.entity.ThirdRecord;
import cn.need.cloud.log.model.vo.ThirdRecordVO;
import cn.need.cloud.log.model.vo.req.ThirdRecordReqVO;
import cn.need.framework.common.mybatis.base.SuperService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 第三方请求日志记录  服务接口
 *
 * <AUTHOR>
 */
public interface ThirdRecordService extends SuperService<ThirdRecord> {

    /**
     * 根据查询条件，物理删除第三方请求日志信息集合
     *
     * @param condition 查询条件
     * @return int 受影响行数
     */
    int deleteByThirdRecordCondition(ThirdRecordReqVO condition);

    /**
     * 根据分页条件，查询条件从数据库中获取第三方请求日志信息集合
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return Page<ThirdRecord> 分页后的第三方请求日志
     */
    List<ThirdRecordVO> listByThirdRecordCondition(Page<ThirdRecord> page, ThirdRecordReqVO condition);
}