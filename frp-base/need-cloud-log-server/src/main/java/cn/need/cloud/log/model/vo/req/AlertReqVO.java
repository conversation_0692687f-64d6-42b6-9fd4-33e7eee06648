package cn.need.cloud.log.model.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 日志查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Data
@Schema(description = "日志查询条件")
public class AlertReqVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4173190745694307201L;


    /**
     * 来源
     */
    @Schema(description = "来源")
    private String source;

    /**
     * 查询关键字
     */
    @Schema(description = "查询关键字")
    private String keyword;

    /**
     * 功能模块
     */
    @Schema(description = "功能模块")
    private String module;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String endTime;

}
