package cn.need.cloud.log.controller.provider;

import cn.need.cloud.log.client.api.AlertRecordClient;
import cn.need.cloud.log.client.api.path.AlertRecordPath;
import cn.need.cloud.log.client.dto.AlertRecordDTO;
import cn.need.cloud.log.model.entity.AlertRecord;
import cn.need.cloud.log.service.AlertRecordService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.api.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预警日志数据  Feign 提供者
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping(AlertRecordPath.PREFIX)
public class AlertRecordProvider implements AlertRecordClient {

    private final AlertRecordService alertRecordService;

    /**
     * 写入日志信息
     *
     * @param info 日志信息DTO对象，包含了请求数据对象、响应数据对象
     */
    @PostMapping(AlertRecordPath.WRITE)
    @Override
    public Result<Integer> write(@RequestBody AlertRecordDTO info) {
        log.info("---------------->POST /client/log/log-record/write, info={}", JsonUtil.toJson(info));
        try {
            return Result.ok(alertRecordService.insert(BeanUtil.copyNew(info, AlertRecord.class)));
        } catch (Exception e) {
            log.error("写入日志信息异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

}
