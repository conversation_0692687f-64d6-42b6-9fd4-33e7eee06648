package cn.need.cloud.log.service.impl;

import cn.need.cloud.log.mapper.RequestDataMapper;
import cn.need.cloud.log.model.entity.RequestData;
import cn.need.cloud.log.service.RequestDataService;
import cn.need.cloud.log.util.LoggerRecordUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 请求明细数据 服务实现
 *
 * <AUTHOR>
 */
@Service
public class RequestDataServiceImpl extends SuperServiceImpl<RequestDataMapper, RequestData> implements RequestDataService {

    @Override
    public void deleteByIds(List<Long> ids) {
        LoggerRecordUtil.deleteRecord(ids, this.mapper);
    }

    @Override
    public void clear(QueryWrapper<RequestData> wrapper) {
        this.mapper.truncateTable();
      /*  if (isNull(wrapper)) {
            return;
        }
        //根据条件获取数据
        List<RequestData> records = super.list(wrapper);
        //删除错误日志记录
        this.deleteByIds(getPropertyList(records, RequestData::getId));
        //删除对应的响应数据
        responseDataService.deleteByRecordIds(getPropertyList(records, RequestData::getRecordId));*/
    }

    @Override
    public RequestData getByRecordId(String recordId) {
        return lambdaQuery().eq(RequestData::getRecordId, recordId).one();
    }

    @Override
    public List<RequestData> listByRecordIds(List<String> recordIds) {
        return LoggerRecordUtil.queryRecord(recordIds, this);
    }

}
