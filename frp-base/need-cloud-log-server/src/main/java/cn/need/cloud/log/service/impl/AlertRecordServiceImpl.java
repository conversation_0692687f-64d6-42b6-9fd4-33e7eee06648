package cn.need.cloud.log.service.impl;

import cn.need.cloud.log.mapper.AlertRecordMapper;
import cn.need.cloud.log.model.entity.AlertRecord;
import cn.need.cloud.log.model.vo.AlertRecordVO;
import cn.need.cloud.log.model.vo.req.AlertReqVO;
import cn.need.cloud.log.service.AlertRecordService;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预警日志数据 服务实现
 *
 * <AUTHOR>
 */
@Service
public class AlertRecordServiceImpl extends SuperServiceImpl<AlertRecordMapper, AlertRecord> implements AlertRecordService {

    @Override
    public List<AlertRecordVO> page(Page<AlertReqVO> page, AlertReqVO condition) {
        long offset = (page.getCurrent() - 1) * page.getSize();
        if (ObjectUtil.isNotEmpty(condition.getEndTime())) {
            condition.setEndTime(condition.getEndTime() + " 23:59:59");
        }
        List<AlertRecordVO> list = mapper.pageList(condition, offset, page.getSize());
        if (ObjectUtil.isEmpty(list)) {
            return Lists.arrayList();
        }
        page.setTotal(mapper.pageCount(condition));
        return list;
    }

    @Override
    public void clear() {
        this.mapper.truncateTable();
    }


}
