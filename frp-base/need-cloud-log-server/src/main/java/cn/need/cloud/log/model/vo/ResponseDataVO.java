package cn.need.cloud.log.model.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 响应明细数据 vo对象
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
@Schema(description = "响应明细数据 VO对象")
public class ResponseDataVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据项id，主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    @Schema(description = "日志记录id，UUID，用来标记记录、请求、响应的关联关系")
    private String recordId;

    /**
     * 响应时间
     */
    @Schema(description = "响应时间")
    private LocalDateTime responseTime;

    /**
     * 响应状态
     */
    @Schema(description = "响应状态")
    private String responseStatus;

    /**
     * 响应编号
     */
    @Schema(description = "响应编号")
    private String responseCode;

    /**
     * 响应描述简语
     */
    @Schema(description = "响应描述简语")
    private String responseReasonPhrase;

    /**
     * 响应信息
     */
    @Schema(description = "响应信息")
    private String responseMessage;

    /**
     * 响应头信息
     */
    @Schema(description = "响应头信息")
    private String responseHeaders;

    /**
     * 响应数据内容
     */
    @Schema(description = "响应数据内容")
    private String responseBody;

}