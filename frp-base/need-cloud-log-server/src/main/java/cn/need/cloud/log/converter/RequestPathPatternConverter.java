package cn.need.cloud.log.converter;

import cn.need.cloud.log.client.dto.RequestPathPatternDTO;
import cn.need.cloud.log.model.entity.RequestPathPattern;
import cn.need.cloud.log.model.vo.RequestPathPatternVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 请求路径匹配规则  对象转换器
 *
 * <AUTHOR>
 */
public class RequestPathPatternConverter extends AbstractModelConverter<RequestPathPattern, RequestPathPatternVO, RequestPathPatternDTO> {

    @Override
    protected boolean isFillDict() {
        return true;
    }

}