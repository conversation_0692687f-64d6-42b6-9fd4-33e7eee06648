package cn.need.cloud.log.model.entity;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 操作日志记录表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("log_operation_record")
public class OperationRecord extends SuperModel {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    @TableField("record_id")
    private String recordId;

    /**
     * 操作用户
     */
    @TableField("operation_user")
    private String operationUser;

    /**
     * 操作用户角色
     */
    @TableField("operation_user_role")
    private String operationUserRole;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    private LocalDateTime operationTime;

    /**
     * 操作URL
     */
    @TableField("operation_url")
    private String operationUrl;

    /**
     * 操作ip
     */
    @TableField("operation_ip")
    private String operationIp;

    /**
     * 操作来源，用来标记是PC端还是小程序
     */
    @TableField("operation_source")
    private String operationSource;

    /**
     * 操作模块
     */
    @TableField("operation_module")
    private String operationModule;

    /**
     * 操作功能
     */
    @TableField("operation_function")
    private String operationFunction;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 状态，成功或失败
     */
    @TableField("status")
    private String status;

    /**
     * 错误编码
     */
    @TableField("error_code")
    private String errorCode;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
}
