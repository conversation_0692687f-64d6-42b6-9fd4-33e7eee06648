package cn.need.cloud.log.model.vo;

import cn.need.cloud.log.client.constant.RequestSourceConstant;
import cn.need.cloud.log.client.constant.ResponseStatusConstant;
import cn.need.framework.common.annotation.dict.DictField;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 登录日志记录 vo对象
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
@Schema(description = "登录日志记录 VO对象")
public class LoginRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据项id，主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    @Schema(description = "日志记录id，UUID，用来标记记录、请求、响应的关联关系")
    private String recordId;

    /**
     * 登录用户
     */
    @ExcelProperty(value = "登录用户", index = 0)
    @Schema(description = "登录用户")
    private String loginUser;

    /**
     * 用户角色信息
     */
    @ExcelProperty(value = "用户角色", index = 1)
    @Schema(description = "用户角色信息")
    private String loginUserRole;

    /**
     * 登录时间
     */
    @ExcelProperty(value = "登录时间", index = 7)
    @Schema(description = "登录时间")
    private LocalDateTime loginTime;

    /**
     * 请求url
     */
    @ExcelProperty(value = "请求URL", index = 3)
    @Schema(description = "请求url")
    private String loginUrl;

    /**
     * 登录ip
     */
    @ExcelProperty(value = "登录IP", index = 6)
    @Schema(description = "登录ip")
    private String loginIp;

    /**
     * 登录来源，用来标记是PC端还是小程序
     */
    @Schema(description = "登录来源，用来标记是PC端还是小程序")
    private String loginSource;

    /**
     * 状态，成功或失败
     */
    @Schema(description = "状态，成功或失败")
    private String status;

    /**
     * 错误编码
     */
    @Schema(description = "错误编码")
    private String errorCode;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息", index = 5)
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 登录来源名称
     */
    @ExcelProperty(value = "来源", index = 2)
    @DictField(RequestSourceConstant.ROOT)
    @Schema(description = "登录来源名称")
    private String loginSourceName;

    /**
     * 状态名称
     */
    @ExcelProperty(value = "结果", index = 4)
    @DictField(ResponseStatusConstant.ROOT)
    @Schema(description = "状态名称")
    private String statusName;

}