package cn.need.cloud.log.converter;

import cn.need.cloud.log.client.dto.ResponseDataDTO;
import cn.need.cloud.log.model.entity.ResponseData;
import cn.need.cloud.log.model.vo.ResponseDataVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * 响应明细数据  对象转换器
 *
 * <AUTHOR>
 */
public class ResponseDataConverter extends AbstractModelConverter<ResponseData, ResponseDataVO, ResponseDataDTO> {

}