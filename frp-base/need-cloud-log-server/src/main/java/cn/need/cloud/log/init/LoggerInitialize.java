package cn.need.cloud.log.init;

import cn.need.cloud.log.cache.LoggerPatternCacheRepertory;
import cn.need.cloud.log.cache.bean.LoggerPatternCache;
import cn.need.cloud.log.model.entity.RequestPathPattern;
import cn.need.cloud.log.service.RequestPathPatternService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类缓存初始化.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoggerInitialize implements InitializingBean {

    private final RequestPathPatternService patternService;

    private final LoggerPatternCacheRepertory cacheRepertory;

    @Override
    public void afterPropertiesSet() {
        TimeMeter meter = new TimeMeter();
        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化日志规则数据至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        //过滤掉失效的数据
        List<RequestPathPattern> list = patternService.list().stream().filter(it -> ObjectUtil.equal(it.getState(), 1)).collect(Collectors.toList());
        cacheRepertory.initLoggerPattern(BeanUtil.copyNew(list, LoggerPatternCache.class));
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化日志规则数据至redis【完成】，总耗时：{}ms <<<<<<<<<<<<<<<<<<<<<", meter.sign());
    }
}
