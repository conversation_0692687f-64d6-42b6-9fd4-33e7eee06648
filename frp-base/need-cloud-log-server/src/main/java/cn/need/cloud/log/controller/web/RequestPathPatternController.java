package cn.need.cloud.log.controller.web;

import cn.need.cloud.log.cache.LoggerPatternCacheRepertory;
import cn.need.cloud.log.cache.bean.LoggerPatternCache;
import cn.need.cloud.log.converter.RequestPathPatternConverter;
import cn.need.cloud.log.model.entity.RequestPathPattern;
import cn.need.cloud.log.model.vo.RequestPathPatternVO;
import cn.need.cloud.log.model.vo.req.PatternReqVO;
import cn.need.cloud.log.service.RequestPathPatternService;
import cn.need.cloud.log.util.LoggerRecordQuery;
import cn.need.cloud.log.util.LoggerRecordValidate;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ParameterException;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.util.DictUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static cn.need.framework.common.core.lang.ObjectUtil.equal;
import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * 请求路径匹配规则 前端控制器
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/log/request-path-pattern")
@Tag(name = "请求路径匹配规则管理")
@Slf4j
public class RequestPathPatternController extends AbstractRestController<RequestPathPatternService, RequestPathPattern, RequestPathPatternConverter, RequestPathPatternVO> {

    private final LoggerPatternCacheRepertory cacheRepertory;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "新增请求路径规则配置接口", description = "新增请求路径规则配置接口")
    @PostMapping(value = "insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) RequestPathPatternVO vo) {
        log.info("====> /api/log/request-path-pattern/insert, vo={}", JsonUtil.toJson(vo));
        LoggerRecordValidate.pattern(vo);
        return super.insert(vo, it -> service.insert(it, pattern -> {
            //将数据添加到缓存中
            if (equal(it.getState(), 1)) {
                LoggerPatternCache cache = BeanUtil.copyNew(pattern, LoggerPatternCache.class);
                cacheRepertory.addLoggerPattern(cache);
            }
        }));
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "修改请求路径规则配置接口", description = "修改请求路径规则配置接口")
    @PostMapping(value = "update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) RequestPathPatternVO vo) {
        log.info("====> /api/log/request-path-pattern/update, vo={}", JsonUtil.toJson(vo));
        //1. 校验入参数据
        LoggerRecordValidate.pattern(vo);
        //2. 根据id获取已存在的数据
        RequestPathPattern pattern = Validate.notNull(service.getById(vo.getId()), "不能根据id[" + vo.getId() + "]获取到对应的路径规则配置数据");
        //3. 校验请求路径不可以修改
        if (!equal(vo.getRequestPath(), pattern.getRequestPath())) {
            throw new ParameterException("请求路径不可以修改");
        }
        //4. 执行修改操作
        RequestPathPattern entity = converter.toEntity(vo);
        int result = service.update(entity);
        if (result < 1) {
            throw new BusinessException("修改请求路径规则配置失败，请稍后再试");
        }
        //5. 添加数据至缓存中
        if (equal(entity.getState(), 1)) {
            LoggerPatternCache cache = BeanUtil.copyNew(pattern, LoggerPatternCache.class);
            cacheRepertory.addLoggerPattern(cache);
        } else {
            //如果数据为失效状态，并且历史记录为有效状态，将数据从缓存中删除
            if (equal(pattern.getState(), 1)) {
                cacheRepertory.delLoggerPattern(pattern.getRequestPath());
            }
        }
        //6. 响应结果
        return success(result);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "失效或生效请求路径规则配置接口", description = "失效或生效请求路径规则配置接口")
    @GetMapping(value = "active/{id}")
    public Result<Integer> active(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/log/request-path-pattern/active/{id}, id={}", id);
        //1. 根据id获取数据
        RequestPathPattern pattern = Validate.notNull(service.getById(id), "不能根据id[" + id + "]获取到日志规则数据");
        //2. 获取值，并更新到数据库中
        int state = equal(pattern.getState(), 1) ? 0 : 1;
        RequestPathPattern update = new RequestPathPattern();
        update.setId(pattern.getId());
        update.setState(state);
        int result = service.update(update);
        //3. 如果是失效，删除数据，有效就增加数据
        if (equal(state, 0)) {
            cacheRepertory.delLoggerPattern(pattern.getRequestPath());
        } else {
            LoggerPatternCache cache = BeanUtil.copyNew(service.getById(id), LoggerPatternCache.class);
            cacheRepertory.addLoggerPattern(cache);
        }
        //4. 响应结果
        return success(result);
    }

    @ApiOperationSupport(order = 4)
    @Operation(summary = "删除请求路径规则配置接口", description = "删除请求路径规则配置接口")
    @GetMapping(value = "remove/{id}")
    public Result<Integer> remove(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/log/request-path-pattern/remove/{id}, id={}", id);
        //1. 根据id获取数据
        RequestPathPattern pattern = Validate.notNull(service.getById(id), "不能根据id[" + id + "]获取到日志规则数据");
        //2. 执行删除数据
        int result = service.removeById(id);
        //3. 删除缓存数据
        cacheRepertory.delLoggerPattern(pattern.getRequestPath());
        //4. 响应结果
        return success(result);
    }

    /**
     * 根据id获取数据详情
     *
     * @param id 数据主键
     * @return Result<GroupVO> 详情结果
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "获取数据详情接口", description = "根据数据id，从数据库中获取其对应的数据详情")
    @GetMapping(value = "detail/{id}")
    public Result<RequestPathPatternVO> detail(@PathVariable("id") @Parameter(description = "数据id", required = true) Long id) {
        log.info("====> /api/upms/group/detail/{id}, id={}", id);
        return super.detail(id, null);
    }

    @ApiOperationSupport(order = 6)
    @Operation(summary = "请求路径匹配规则获取数据列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    public Result<PageData<RequestPathPatternVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<PatternReqVO> search) {
        log.info("====> /api/log/request-path-pattern/list, search={}", JsonUtil.toJson(search));
        PatternReqVO condition = isNotNull(search.getCondition()) ? search.getCondition() : new PatternReqVO();
        // 1. 缓存查询条件
        setParameterCache(condition);
        Page<RequestPathPattern> page = Conditions.page(search, RequestPathPattern.class);
        // 2. 构建查询条件
        QueryWrapper<RequestPathPattern> wrapper = LoggerRecordQuery.buildPathPatternWrapper(search.getCondition());
        page = service.page(page, wrapper);
        // 3. 转换成voList，并设置字典数据
        List<RequestPathPatternVO> voList = toVO(page.getRecords());
        return success(new PageData<>(voList, page));
    }

    @ApiOperationSupport(order = 7)
    @Operation(summary = "数据导出接口", description = "将数据导出成Excel文件")
    @GetMapping(value = "export")
    public void exportExcel() {
        log.info("====> /api/log/request-path-pattern/export");
        super.exportExcel(super.getExportName("请求路径匹配规则"), super.voClass, this::exportExcelData);
    }

    //************************************************** 私有方法 *****************************************************/

    /**
     * 导出
     */
    private List<RequestPathPatternVO> exportExcelData() {
        PatternReqVO condition = getParameterCache();
        log.info("=====> RequestPathPattern ExportExcelData Condition Is {} <======", JsonUtil.toJson(condition));
        // 1. 构建查询条件
        QueryWrapper<RequestPathPattern> wrapper = isNotNull(condition) ? LoggerRecordQuery.buildPathPatternWrapper(condition) : null;
        // 2. 获取数据
        List<RequestPathPattern> list = isNotNull(wrapper) ? service.list(wrapper) : Lists.arrayList();
        // 3. 转换成voList，并设置字典数据
        List<RequestPathPatternVO> voList = toVO(list);
        DictUtil.set(voList);
        return voList;
    }

}
