package cn.need.cloud.log.controller.web;

import cn.need.cloud.log.converter.ErrorRecordConverter;
import cn.need.cloud.log.model.entity.ErrorRecord;
import cn.need.cloud.log.model.vo.ErrorRecordVO;
import cn.need.cloud.log.model.vo.req.LoggerReqVO;
import cn.need.cloud.log.service.ErrorRecordService;
import cn.need.cloud.log.util.LoggerRecordQuery;
import cn.need.cloud.log.util.LoggerRecordValidate;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.util.DictUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.need.framework.common.core.lang.ObjectUtil.isNotNull;

/**
 * 错误日志记录信息 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/log/error-record")
@Tag(name = "错误日志记录信息管理")
@Slf4j
public class ErrorRecordController extends AbstractRestController<ErrorRecordService, ErrorRecord, ErrorRecordConverter, ErrorRecordVO> {

    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取数据列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    public Result<PageData<ErrorRecordVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<LoggerReqVO> search) {
        log.info("====> /api/log/error-record/list, search={}", JsonUtil.toJson(search));
        // 1. 缓存参数
        LoggerReqVO condition = isNotNull(search.getCondition()) ? search.getCondition() : new LoggerReqVO();
        setParameterCache(condition);
        // 2. 根据分页条件参数、实体类类型，构建分页对象
        LoggerRecordValidate.queryDate(condition);
        Page<ErrorRecord> page = Conditions.page(search, ErrorRecord.class);
        // 3. 构建查询条件
        QueryWrapper<ErrorRecord> errorRecordQueryWrapper = LoggerRecordQuery.buildErrorWrapper(search.getCondition());
        page = service.page(page, errorRecordQueryWrapper);
        // 4. 转换成voList，并设置字典数据
        List<ErrorRecordVO> voList = toVO(page.getRecords());
        DictUtil.set(voList);
        return success(new PageData<>(voList, page));
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "清理错误日志接口", description = "根据条件，清理错误日志明细")
    @PostMapping({"error-log-clear"})
    public Result errorLogClear(@RequestBody LoggerReqVO condition) {
        log.info("====> /api/log/log-record/error-log-clear, condition={}", JsonUtil.toJson(condition));
        // 1. 校验参数
        LoggerRecordValidate.queryDate(condition);
        // 2. 构建查询条件
        QueryWrapper<ErrorRecord> wrapper = LoggerRecordQuery.buildErrorWrapper(condition);
        // 3. 根据条件清理日志
        service.clear(wrapper);
        return success();
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "数据导出接口", description = "将数据导出成Excel文件")
    @GetMapping(value = "export")
    public void exportExcel() {
        log.info("====> /api/log/error-record/export");
        super.exportExcel(super.getExportName("错误日志记录信息"), super.voClass, this::exportExcelData);
    }

    //************************************************** 私有方法 *****************************************************/

    /**
     * 导出
     */
    private List<ErrorRecordVO> exportExcelData() {
        LoggerReqVO condition = getParameterCache();
        log.info("=====> ErrorRecord ExportExcelData Condition Is {} <======", JsonUtil.toJson(condition));
        // 1. 构建查询条件
        QueryWrapper<ErrorRecord> wrapper = isNotNull(condition) ? LoggerRecordQuery.buildErrorWrapper(condition) : null;
        // 2. 获取数据
        List<ErrorRecord> list = isNotNull(wrapper) ? service.list(wrapper) : Lists.arrayList();
        // 3. 转换成voList，并设置字典数据
        List<ErrorRecordVO> voList = toVO(list);
        DictUtil.set(voList);
        return voList;
    }

}
