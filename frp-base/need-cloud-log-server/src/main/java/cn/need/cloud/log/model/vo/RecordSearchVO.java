package cn.need.cloud.log.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 日志记录查询信息 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "日志记录查询信息 VO对象")
public class RecordSearchVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 记录类型：REQUEST,RESPONSE
     */
    @Schema(description = "记录类型：REQUEST,RESPONSE")
    private String recordType;

    /**
     * 操作功能
     */
    @Schema(description = "操作功能")
    private String operationFunction;

    /**
     * 操作模块
     */
    @Schema(description = "操作模块")
    private String operationModule;

    /**
     * 请求路径
     */
    @Schema(description = "请求路径")
    private String requestPath;

    /**
     * 查询关键字
     */
    @Schema(description = "查询关键字")
    private String keyword;

    /**
     * 日志记录id
     */
    @Schema(description = "日志记录id")
    private String recordId;

}