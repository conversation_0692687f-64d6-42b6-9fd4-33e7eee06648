package cn.need.cloud.log.model.vo;

import cn.need.cloud.log.client.constant.OperationModuleConstant;
import cn.need.framework.common.annotation.dict.DictField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 第三方请求日志记录 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "第三方请求日志记录 VO对象")
public class ThirdRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @Schema(description = "id主键")
    private Long id;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    @Schema(description = "日志记录id，UUID，用来标记记录、请求、响应的关联关系")
    private String recordId;

    /**
     * 操作模块，数据字典动态值：OPERATION_MODULE
     */
    @Schema(description = "业务模块，数据字典动态值：OPERATION_MODULE")
    private String operationModule;

    /**
     * 请求路径
     */
    @Schema(description = "请求路径")
    private String requestPath;

    /**
     * 请求描述
     */
    @Schema(description = "接口描述")
    private String requestDescription;

    /**
     * 请求用户
     */
    @Schema(description = "业务系统")
    private String requestUser;

    /**
     * 请求ip
     */
    @Schema(description = "请求ip")
    private String requestIp;

    /**
     * 请求时间
     */
    @Schema(description = "请求时间")
    private String requestTime;

    /**
     * 用时
     */
    @Schema(description = "用时")
    private Long usedTime;

    /**
     * 状态，成功或失败
     */
    @Schema(description = "响应状态")
    private String responseStatus;

    /**
     * 响应编码
     */
    @Schema(description = "响应编码")
    private String responseCode;

    /**
     * 操作模块，数据字典动态值：OPERATION_MODULE
     */
    @Schema(description = "业务模块名称，数据字典动态值：OPERATION_MODULE")
    @DictField(OperationModuleConstant.ROOT)
    private String operationModuleName;

    /**
     * 请求参数
     */
    @Schema(description = "请求参数")
    private String requestBody;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private String responseBody;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String responseMessage;

    /**
     * 关键字
     */
    @Schema(description = "关键字")
    private String keyword;

}