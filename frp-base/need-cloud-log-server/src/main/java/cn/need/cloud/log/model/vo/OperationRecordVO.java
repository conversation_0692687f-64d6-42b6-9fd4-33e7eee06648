package cn.need.cloud.log.model.vo;

import cn.need.cloud.log.client.constant.*;
import cn.need.framework.common.annotation.dict.DictField;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志记录表 vo对象
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
@Schema(description = "操作日志记录表 VO对象")
public class OperationRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 数据项id，主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 日志记录id，UUID，用来标记记录、请求、响应的关联关系
     */
    @Schema(description = "日志记录id，UUID，用来标记记录、请求、响应的关联关系")
    private String recordId;

    /**
     * 操作用户
     */
    @ExcelProperty(value = "操作用户", index = 0)
    @Schema(description = "操作用户")
    private String operationUser;

    /**
     * 操作用户角色
     */
    @ExcelProperty(value = "用户角色", index = 1)
    @Schema(description = "操作用户角色")
    private String operationUserRole;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间", index = 8)
    @Schema(description = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作URL
     */
    @ExcelProperty(value = "操作URL", index = 4)
    @Schema(description = "操作URL")
    private String operationUrl;

    /**
     * 操作ip
     */
    @ExcelProperty(value = "操作IP", index = 7)
    @Schema(description = "操作ip")
    private String operationIp;

    /**
     * 操作来源，用来标记是PC端还是小程序
     */
    @Schema(description = "操作来源，用来标记是PC端还是小程序")
    private String operationSource;

    /**
     * 操作模块
     */
    @Schema(description = "操作模块")
    private String operationModule;

    /**
     * 操作功能
     */
    @Schema(description = "操作功能")
    private String operationFunction;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型")
    private String operationType;

    /**
     * 状态，成功或失败
     */
    @Schema(description = "状态，成功或失败")
    private String status;

    /**
     * 错误编码
     */
    @Schema(description = "错误编码")
    private String errorCode;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息", index = 6)
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 操作来源名称
     */
    @ExcelProperty(value = "来源", index = 2)
    @DictField(RequestSourceConstant.ROOT)
    @Schema(description = "操作来源名称")
    private String operationSourceName;

    /**
     * 操作模块名称
     */
    @Schema(description = "操作模块名称")
    @DictField(OperationModuleConstant.ROOT)
    private String operationModuleName;

    /**
     * 操作功能名称
     */
    @Schema(description = "操作功能名称")
    @DictField(OperationFunctionConstant.ROOT)
    private String operationFunctionName;

    /**
     * 操作类型名称
     */
    @ExcelProperty(value = "操作类型", index = 3)
    @Schema(description = "操作类型名称")
    @DictField(OperationTypeConstant.ROOT)
    private String operationTypeName;

    /**
     * 状态名称
     */
    @ExcelProperty(value = "结果", index = 5)
    @DictField(ResponseStatusConstant.ROOT)
    @Schema(description = "状态名称")
    private String statusName;

}