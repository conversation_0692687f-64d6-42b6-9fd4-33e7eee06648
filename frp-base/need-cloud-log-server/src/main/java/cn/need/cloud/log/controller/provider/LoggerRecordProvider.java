package cn.need.cloud.log.controller.provider;

import cn.need.cloud.log.cache.LoggerPatternCacheService;
import cn.need.cloud.log.cache.bean.LoggerPatternCache;
import cn.need.cloud.log.client.api.LoggerRecordClient;
import cn.need.cloud.log.client.api.path.LoggerRecordPath;
import cn.need.cloud.log.client.constant.enums.RecordTypeEnum;
import cn.need.cloud.log.client.dto.LoggerInfoDTO;
import cn.need.cloud.log.client.dto.RequestDataDTO;
import cn.need.cloud.log.client.dto.ResponseDataDTO;
import cn.need.cloud.log.model.entity.*;
import cn.need.cloud.log.service.*;
import cn.need.cloud.log.util.LogUtil;
import cn.need.cloud.upms.client.api.RoleClient;
import cn.need.cloud.upms.client.api.UserClient;
import cn.need.cloud.upms.client.dto.RoleDTO;
import cn.need.cloud.upms.client.dto.UserAccountDTO;
import cn.need.cloud.upms.client.dto.UserDTO;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.util.ApiUtil;
import cn.need.framework.starter.security.util.JwtHelper;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.jsonwebtoken.Claims;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.need.cloud.log.client.constant.LoggerConstant.*;
import static cn.need.framework.common.core.lang.ObjectUtil.*;
import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping(LoggerRecordPath.PREFIX)
public class LoggerRecordProvider implements LoggerRecordClient {

    private final RequestDataService requestDataService;
    private final ResponseDataService responseDataService;
    private final RequestUsedTimeService requestUsedTimeService;
    private final LoginRecordService loginRecordService;
    private final ThirdRecordService thirdRecordService;
    private final OperationRecordService operationRecordService;
    private final RecordSearchService recordSearchService;

    private final UserClient userClient;
    private final RoleClient roleClient;

    private final LoggerPatternCacheService loggerPatternCacheService;

    private final JwtHelper jwtHelper;

    private final Map<String, String> userNameMap = Maps.hashMap();

    /**
     * 写入日志信息
     *
     * @param info 日志信息DTO对象，包含了请求数据对象、响应数据对象
     */
    @PostMapping(LoggerRecordPath.WRITE)
    @Override
    public Result<Integer> write(@RequestBody LoggerInfoDTO info) {
        log.info("---------------->POST /client/log/log-record/write, info={}", JsonUtil.toJson(info));
        try {
            Validate.notNull(info.getRequestData(), "请求信息不能为空");
            Validate.notNull(info.getResponseData(), "响应信息不能为空");
            LoggerPatternCache cache = loggerPatternCacheService.getByPattern(info.getRequestData().getRequestPath());
            //1. 填充请求来源
            filledRequestSource(info.getRequestData(), cache);
            //2. 填充请求用户、用户角色
            filledRequestUser(info.getToken(), info.getRequestData(), cache);
            //3. 构建请求对象
            RequestData requestData = createRequestData(info.getRequestData());
            //4. 构建响应对象
            ResponseData responseData = createResponseData(info.getResponseData(), cache);
            //5. 保存请求、响应对象
            int result = requestDataService.insert(requestData, it -> {
                //保存响应信息
                responseDataService.insert(responseData);
                //保存响应时长
                requestUsedTimeService.insert(buildRequestUsedTime(info, requestData, responseData));
            });
            Validate.isTrue(result > 0, "保存请求、响应日志异常");
            //6. 构建日志搜索信息，并保存
            List<RecordSearch> searches = buildRecordSearch(requestData, responseData, cache);
            if (isNotEmpty(searches)) {
                recordSearchService.insertBatch(searches);
            }
            //7. 判断如果是登录功能
            if (LogUtil.isLogin(cache)) {
                loginRecordService.insert(buildLoginRecord(info, requestData, responseData));
                return null;
            }
            //8. 判断如果是接口中心
            if (LogUtil.isApiCenter(cache)) {
                thirdRecordService.insert(buildThirdRecord(info, requestData, responseData, cache));
                return null;
            }
            //9. 其他情况，保存操作日志
            return Result.ok(operationRecordService.insert(buildOperationRecord(info, requestData, responseData, cache)));
        } catch (Exception e) {
            log.error("写入日志信息异常：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }

    }

    /**
     * 根据请求信息、响应信息、配置信息，构建日志记录搜索信息集合
     */
    private List<RecordSearch> buildRecordSearch(RequestData request, ResponseData response, LoggerPatternCache cache) {
        List<RecordSearch> list = Lists.arrayList();
        String requestKey = isNotNull(cache) ? cache.getRequestKey() : null;
        if (isNotEmpty(requestKey) && isNotEmpty(request.getRequestBody())) {
            list.addAll(buildRecordSearch(requestKey, request.getRequestBody(), request, cache, RecordTypeEnum.REQUEST));
        }
        String responseKey = isNotNull(cache) ? cache.getResponseKey() : null;
        if (isNotEmpty(responseKey) && isNotEmpty(response.getResponseBody())) {
            list.addAll(buildRecordSearch(responseKey, response.getResponseBody(), request, cache, RecordTypeEnum.RESPONSE));
        }
        return list;
    }

    private List<RecordSearch> buildRecordSearch(String search, String body, RequestData request, LoggerPatternCache cache, RecordTypeEnum RecordTypeEnum) {
        List<RecordSearch> list = Lists.arrayList();
        LogUtil.parseKeywordMap(StringUtil.split(search, StringPool.COMMA), body).forEach((key, values) -> {
            if (isNotEmpty(values)) {
                values.forEach(it -> {
                    String keyword = StringUtil.toString(it);
                    if (isNotEmpty(keyword)) {
                        list.add(buildRecordSearch(keyword, request, cache, RecordTypeEnum));
                    }
                });
            }
        });
        return list;
    }

    private RecordSearch buildRecordSearch(String keyword, RequestData request, LoggerPatternCache cache, RecordTypeEnum RecordTypeEnum) {
        RecordSearch search = new RecordSearch();
        search.setRecordType(RecordTypeEnum.getCode());
        search.setOperationFunction(cache.getOperationFunction());
        search.setOperationModule(cache.getOperationModule());
        search.setRequestPath(request.getRequestPath());
        search.setKeyword(keyword);
        search.setRecordId(request.getRecordId());
        return search;
    }

    /**
     * 创建响应对象
     */
    private ResponseData createResponseData(ResponseDataDTO dataDTO, LoggerPatternCache cache) {
        //直接拷贝响应对象，并过滤响应信息
        ResponseData data = BeanUtil.copyNew(dataDTO, ResponseData.class, "responseBody");
        //如果需要登记响应信息
        if (LogUtil.isRecordResponseData(cache)) {
            data.setResponseBody(dataDTO.getResponseBody());
        }
        return data;
    }

    /**
     * 创建请求对象
     */
    private RequestData createRequestData(RequestDataDTO dataDTO) {
        RequestData data = BeanUtil.copyNew(dataDTO, RequestData.class);
        if (LogUtil.isNeedConvertJson(data)) {
            //获取请求body参数，requestBody为空的情况下，尝试获取url路径"?"后面的参数
            String requestBody = StringUtil.emptyToDefault(data.getRequestBody(), getRequestBodyFromUrl(data.getRequestUrl()));
            if (isNotEmpty(requestBody)) {
                data.setRequestBody(LogUtil.convertRequestBodyToJson(requestBody));
            }
        }
        return data;
    }

    /**
     * 从url上获取请求的参数内容，取"?"之后的参数集
     */
    private String getRequestBodyFromUrl(String url) {
        return StringUtil.substringAfter(url, StringPool.QUESTION_MARK);
    }

    private OperationRecord buildOperationRecord(LoggerInfoDTO info, RequestData request, ResponseData response, LoggerPatternCache cache) {
        OperationRecord record = new OperationRecord();
        record.setRecordId(info.getRecordId());
        record.setOperationUrl(request.getRequestUrl());
        record.setOperationUser(request.getRequestUser());
        record.setOperationUserRole(request.getRequestUserRole());
        record.setOperationTime(request.getRequestTime());
        record.setOperationIp(request.getRequestIp());
        record.setOperationSource(request.getRequestSource());
        record.setStatus(response.getResponseStatus());
        record.setErrorCode(response.getResponseCode());
        record.setErrorMessage(response.getResponseMessage());
        //设置模块、功能、操作类型
        if (ObjectUtil.isNotNull(cache)) {
            record.setOperationModule(cache.getOperationModule());
            record.setOperationFunction(cache.getOperationFunction());
            record.setOperationType(cache.getOperationType());
        }
        return record;
    }

    private ThirdRecord buildThirdRecord(LoggerInfoDTO info, RequestData request, ResponseData response, LoggerPatternCache cache) {
        ThirdRecord record = new ThirdRecord();
        record.setRecordId(info.getRecordId());
        record.setUsedTime(info.getUsedTime());
        record.setOperationModule(cache.getOperationModule());
        record.setRequestDescription(cache.getRequestDescription());
        record.setRequestPath(request.getRequestPath());
        if (isNotEmpty(request.getRequestUser())) {
            record.setRequestUser(StringUtil.emptyToDefault(userNameMap.get(request.getRequestUser()), request.getRequestUser()));
        }
        record.setRequestIp(request.getRequestIp());
        record.setRequestTime(request.getRequestTime());
        record.setResponseStatus(response.getResponseStatus());
        record.setResponseCode(response.getResponseCode());
        return record;
    }

    private LoginRecord buildLoginRecord(LoggerInfoDTO info, RequestData request, ResponseData response) {
        LoginRecord record = new LoginRecord();
        record.setRecordId(info.getRecordId());
        record.setLoginUrl(request.getRequestUrl());
        record.setLoginTime(request.getRequestTime());
        record.setLoginIp(request.getRequestIp());
        record.setLoginSource(request.getRequestSource());
        record.setLoginUser(request.getRequestUser());
        record.setLoginUserRole(request.getRequestUserRole());
        record.setStatus(response.getResponseStatus());
        record.setErrorCode(response.getResponseCode());
        record.setErrorMessage(response.getResponseMessage());
        return record;
    }

    private void filledRequestUser(String token, RequestDataDTO requestData, LoggerPatternCache cache) {
        //1. 判断如果功能是登录，从请求参数中获取
        if (LogUtil.isLogin(cache)) {
            setRequestUserForBody(requestData);
        }
        //2. 从token中解析请求用户
        else {
            setRequestUserForToken(token, requestData);
        }
        //3. 填充用户角色
        filledRequestUserRole(requestData, cache);
    }

    private void filledRequestUserRole(RequestDataDTO requestData, LoggerPatternCache cache) {
        //1. 过滤用户名为空的情况
        if (isEmpty(requestData.getUserId())) {
            return;
        }
        UserAccountDTO userAccountDTO = ApiUtil.getResultData(userClient.getByUserId(requestData.getUserId()));
        if (isEmpty(userAccountDTO)) {
            return;
        }
        //2. 判断如果功能是第三方接口，将用户名称填充到map集合中
        if (LogUtil.isApiCenter(cache)) {
            filledUserNameMap(userAccountDTO.getUserAccount());
        }
        //3. 其他情况，设置用户角色信息
        else {
            // 根据用户名获取用户信息
            if (isNotNull(userAccountDTO)) {
                List<RoleDTO> roles = nullToDefault(roleClient.listByUserId(userAccountDTO.getUserId()).getData(), Lists.arrayList());
                List<String> names = roles.stream().map(RoleDTO::getRoleName).collect(Collectors.toList());
                requestData.setRequestUserRole(String.join(StringPool.COMMA, names));
            }
        }
    }

    private void filledUserNameMap(String userName) {
        if (userNameMap.containsKey(userName)) {
            return;
        }
        // 根据用户名获取用户信息
        Result<UserAccountDTO> userAccountResult = userClient.getByUsername(userName);
        UserAccountDTO accountDTO = userAccountResult.isSuccess() ? userAccountResult.getData() : null;
        if (isNotNull(accountDTO)) {
            Result<UserDTO> userResult = userClient.detail(accountDTO.getUserId());
            UserDTO userDTO = userResult.isSuccess() ? userResult.getData() : null;
            if (isNotNull(userDTO)) {
                userNameMap.put(userName, userDTO.getName());
            }
        }
    }

    @SuppressWarnings("AlibabaUndefineMagicConstant")
    private void setRequestUserForToken(String token, RequestDataDTO requestData) {
        if (isEmpty(token)) {
            return;
        }
        try {
            Claims claims = jwtHelper.parseToken(token);
            if (isNotNull(claims)) {
                String name = ObjectUtil.emptyToDefault(claims.get(Users.KEY_USER_NAME, String.class), claims.get(Users.KEY_NAME, String.class));
                requestData.setRequestUser(name);
                requestData.setUserId(claims.get(Users.KEY_USER_ID, Long.class));
            }
        } catch (Exception e) {
            log.error("------->> jwt 解析用户信息异常：{}", e.getMessage(), e);
        }
    }

    private void setRequestUserForBody(RequestDataDTO request) {
        //1. 优先从requestBody参数中获取
        if (isNotEmpty(request.getRequestBody())) {
            JSONObject json = JSON.parseObject(request.getRequestBody());
            if (isNotNull(json) && json.containsKey(REQUEST_BODY_LOGIN_NAME)) {
                request.setRequestUser(json.getString(REQUEST_BODY_LOGIN_NAME));
            }
        }
        //2. 如果用户还是为空的情况下，从url中获取
        if (isEmpty(request.getRequestUser())) {
            String requestUrl = nullToDefault(request.getRequestUrl(), StringPool.EMPTY);
            int start = requestUrl.indexOf(REQUEST_URL_LOGIN_NAME);
            if (start < 0) {
                return;
            }
            String loginName = requestUrl.substring(start + REQUEST_URL_LOGIN_NAME.length());
            //获取结束位置
            int end = loginName.indexOf(REQUEST_URL_PASSWORD);
            if (end > -1) {
                loginName = loginName.substring(0, end);
            }
            request.setRequestUser(loginName);
        }
    }

    private void filledRequestSource(RequestDataDTO request, LoggerPatternCache cache) {
        if (isNotNull(cache)) {
            request.setRequestSource(cache.getRequestSource());
        }
    }

    private RequestUsedTime buildRequestUsedTime(LoggerInfoDTO info, RequestData request, ResponseData response) {
        RequestUsedTime record = new RequestUsedTime();
        //设置用时
        record.setRecordId(info.getRecordId());
        record.setUsedTime(info.getUsedTime());
        //设置请求信息
        record.setRequestUrl(request.getRequestUrl());
        record.setRequestIp(request.getRequestIp());
        record.setRequestSource(request.getRequestSource());
        record.setRequestUser(request.getRequestUser());
        record.setRequestUserRole(request.getRequestUserRole());
        record.setRequestTime(request.getRequestTime());
        //设置响应信息
        record.setStatus(response.getResponseStatus());
        record.setErrorCode(response.getResponseCode());
        record.setErrorMessage(response.getResponseMessage());
        record.setResponseTime(response.getResponseTime());
        return record;
    }
}
