<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.log.mapper.RequestDataMapper">

    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE
        FROM
        log_request_data
        WHERE
        id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="truncateTable">
        truncate log_request_data
    </update>

</mapper>
