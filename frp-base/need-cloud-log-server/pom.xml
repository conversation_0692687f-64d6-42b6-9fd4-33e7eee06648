<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.need.cloud</groupId>
        <artifactId>frp-base</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>
    <artifactId>need-cloud-log-server</artifactId>
    <packaging>jar</packaging>
    <name>need-cloud-log-server</name>
    <description>the log server Center for uneed need-cloud</description>

    <!-- 参数配置 -->
    <properties>

    </properties>

    <dependencies>
        <!-- 日志client、cache依赖，版本与当前项目保持一致-->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-log-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- log cache 依赖-->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-log-cache</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- uneed common 依赖-->
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-swagger</artifactId>
        </dependency>

        <!-- need-cloud-upms-client -->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-upms-client</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-security</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
