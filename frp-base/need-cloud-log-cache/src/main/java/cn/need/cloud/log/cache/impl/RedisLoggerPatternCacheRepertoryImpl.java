package cn.need.cloud.log.cache.impl;

import cn.need.cloud.log.cache.LoggerPatternCacheRepertory;
import cn.need.cloud.log.cache.LoggerPatternRedisTemplate;
import cn.need.cloud.log.cache.bean.LoggerPatternCache;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.stream.Collectors;

import static cn.need.cloud.log.cache.LoggerPatternRedisTemplate.REDIS_LOGGER_KEY;
import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisLoggerPatternCacheRepertoryImpl implements LoggerPatternCacheRepertory {

    /**
     * redisTemplate对象，需要子类注入
     */
    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * 日志规则Template对象
     */
    private final LoggerPatternRedisTemplate template;

    public RedisLoggerPatternCacheRepertoryImpl(RedisTemplate<String, Object> redisTemplate) {
        Validate.notNull(redisTemplate, "redisTemplate can't be null!");
        this.redisTemplate = redisTemplate;
        this.template = new LoggerPatternRedisTemplate(this.redisTemplate);
    }

    @Override
    public void addLoggerPattern(LoggerPatternCache cache) {
        if (isCompliance(cache)) {
            this.template.hash().put(cache.getRequestPath(), cache);
        }
    }

    @Override
    public void addLoggerPattern(List<LoggerPatternCache> caches) {
        if (isEmpty(caches)) {
            return;
        }
        List<LoggerPatternCache> list = caches.stream().filter(this::isCompliance).collect(Collectors.toList());
        this.template.hash().putAll(ObjectUtil.toMap(list, LoggerPatternCache::getRequestPath));
    }

    @Override
    public void delLoggerPattern(String pattern) {
        if (isEmpty(pattern)) {
            return;
        }
        if (equal(this.template.hash().hasKey(pattern), Boolean.TRUE)) {
            this.template.hash().delete(pattern);
        }
    }

    @Override
    public void delLoggerPattern(List<String> patterns) {
        if (isEmpty(patterns)) {
            return;
        }
        this.template.hash().delete(patterns.stream().filter(ObjectUtil::isNotEmpty).distinct().toArray());
    }

    @Override
    public void clearLoggerPattern() {
        if (equal(redisTemplate.hasKey(REDIS_LOGGER_KEY), Boolean.TRUE)) {
            redisTemplate.delete(REDIS_LOGGER_KEY);
        }
    }

    @Override
    public void initLoggerPattern(List<LoggerPatternCache> caches) {
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化【日志规则】缓存数据开始，数据数量：{} ============", isNull(caches) ? 0 : caches.size());
        }
        TimeMeter meter = new TimeMeter();
        clearLoggerPattern();
        addLoggerPattern(caches);
        if (log.isDebugEnabled()) {
            log.debug("============ 初始化【日志规则】缓存数据完成，用时：{}ms，缓存数量：{} ============", meter.sign(),
                    nullToDefault(this.template.hash().size(), 0L));
        }
    }

    /**
     * 判断缓存数据是否合规
     */
    private boolean isCompliance(LoggerPatternCache cache) {
        return isNotNull(cache) && isNotEmpty(cache.getRequestPath()) && equal(cache.getState(), DataState.ENABLED);
    }
}
