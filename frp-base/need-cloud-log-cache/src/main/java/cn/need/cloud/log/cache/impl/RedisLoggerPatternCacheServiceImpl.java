package cn.need.cloud.log.cache.impl;

import cn.need.cloud.log.cache.LoggerPatternCacheService;
import cn.need.cloud.log.cache.LoggerPatternRedisTemplate;
import cn.need.cloud.log.cache.bean.LoggerPatternCache;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
public class RedisLoggerPatternCacheServiceImpl implements LoggerPatternCacheService {

    /**
     * 日志规则Template对象
     */
    private final LoggerPatternRedisTemplate template;

    public RedisLoggerPatternCacheServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        Validate.notNull(redisTemplate, "redisTemplate can't be null!");
        this.template = new LoggerPatternRedisTemplate(redisTemplate);
    }

    @Override
    public LoggerPatternCache getByPattern(@NonNull String pattern) {
        return this.template.hash().get(pattern);
    }

    @Override
    public List<LoggerPatternCache> listByPattern(@NonNull List<String> patterns) {
        return this.template.hash().multiGet(patterns);
    }

    @Override
    public boolean hasPattern(@NonNull String pattern) {
        return ObjectUtil.nullToDefault(this.template.hash().hasKey(pattern), Boolean.FALSE);
    }
}
